import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../authContext';
import { useNavigate } from 'react-router-dom';
import '../styles/StudyPlan.scss';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import AddIcon from '@mui/icons-material/Add';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import TodayIcon from '@mui/icons-material/Today';

const MessagePopup = ({ message, isError = false, onClose }) => {
    useEffect(() => {
        let timer;
        if (!isError) {
            timer = setTimeout(() => {
                onClose();
            }, 2000); // Auto-close after 2 seconds for success messages
        }
        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [isError, onClose]);

    return (
        <div className="message-popup-overlay">
            <div className="message-popup-container">
                <div className="popup-header">
                    <h2>{isError ? 'Error' : 'Success'}</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>
                <div className="popup-content">
                    <p>{message}</p>
                </div>
            </div>
        </div>
    );
};

const StudyPlanCreator = ({ onClose, curriculumId, curriculumTitle }) => {
    const { user, token } = useAuth();
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        name: `Study Plan for ${curriculumTitle || 'Curriculum'}`,
        start_date: new Date(),
        end_date: new Date(new Date().setDate(new Date().getDate() + 30)), // Default to 30 days
        daily_study_time_minutes: 60
    });

    // Days of week: 0 = Monday, 6 = Sunday (following JavaScript Date.getDay() convention)
    const [preferredDays, setPreferredDays] = useState([0, 1, 2, 3, 4, 5, 6]); // Default to all days
    const [unavailablePeriods, setUnavailablePeriods] = useState([]);
    const [showUnavailablePeriods, setShowUnavailablePeriods] = useState(false);

    const [messagePopup, setMessagePopup] = useState({ show: false, message: '', isError: false });
    const [isCreating, setIsCreating] = useState(false);
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

    // Days of the week for UI
    const daysOfWeek = [
        { value: 0, label: 'Monday' },
        { value: 1, label: 'Tuesday' },
        { value: 2, label: 'Wednesday' },
        { value: 3, label: 'Thursday' },
        { value: 4, label: 'Friday' },
        { value: 5, label: 'Saturday' },
        { value: 6, label: 'Sunday' }
    ];

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleDateChange = (date, field) => {
        setFormData(prev => ({ ...prev, [field]: date }));
    };

    const handleMessagePopupClose = () => {
        setMessagePopup({ show: false, message: '', isError: false });
        if (!messagePopup.isError) {
            onClose();
            navigate('/study-plans');
        }
    };

    // Handle preferred days selection
    const togglePreferredDay = (dayValue) => {
        setPreferredDays(prev => {
            if (prev.includes(dayValue)) {
                // Remove day if already selected
                return prev.filter(day => day !== dayValue);
            } else {
                // Add day if not selected
                return [...prev, dayValue].sort();
            }
        });
    };

    // Handle unavailable periods
    const addUnavailablePeriod = () => {
        const newPeriod = {
            id: Date.now(), // Unique ID for the period
            start_date: new Date(),
            end_date: new Date(new Date().setDate(new Date().getDate() + 7))
        };
        setUnavailablePeriods(prev => [...prev, newPeriod]);
    };

    const removeUnavailablePeriod = (id) => {
        setUnavailablePeriods(prev => prev.filter(period => period.id !== id));
    };

    const updateUnavailablePeriod = (id, field, value) => {
        setUnavailablePeriods(prev =>
            prev.map(period =>
                period.id === id ? { ...period, [field]: value } : period
            )
        );
    };

    // Validate unavailable periods
    const validateUnavailablePeriods = () => {
        for (const period of unavailablePeriods) {
            if (period.end_date < period.start_date) {
                return "End date must be after start date in unavailable periods";
            }
        }
        return null;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.name || !formData.start_date || !formData.end_date || !formData.daily_study_time_minutes) {
            setMessagePopup({
                show: true,
                message: 'Please fill in all required fields',
                isError: true
            });
            return;
        }

        // Validate dates
        if (formData.end_date < formData.start_date) {
            setMessagePopup({
                show: true,
                message: 'End date must be after start date',
                isError: true
            });
            return;
        }

        // Validate preferred days
        if (preferredDays.length === 0) {
            setMessagePopup({
                show: true,
                message: 'Please select at least one preferred study day',
                isError: true
            });
            return;
        }

        // Validate unavailable periods
        const unavailablePeriodsError = validateUnavailablePeriods();
        if (unavailablePeriodsError) {
            setMessagePopup({
                show: true,
                message: unavailablePeriodsError,
                isError: true
            });
            return;
        }

        setIsCreating(true);

        try {
            console.log('Creating study plan with data:', formData);
            console.log('Preferred days:', preferredDays);
            console.log('Unavailable periods:', unavailablePeriods);

            // Format dates for API
            const formatDate = (date) => {
                const d = new Date(date);
                return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
            };

            // Format unavailable periods for API
            const formattedUnavailablePeriods = unavailablePeriods.map(period => ({
                start_date: formatDate(period.start_date),
                end_date: formatDate(period.end_date)
            }));

            // Prepare request payload
            const payload = {
                subject_id: curriculumId, // Use the curriculumId prop instead of documentId
                curriculum_id: curriculumId, // Add curriculum_id for the backend
                user_id: user.id, // Add user_id for the backend
                name: formData.name,
                start_date: formatDate(formData.start_date),
                end_date: formatDate(formData.end_date),
                daily_study_time_minutes: parseInt(formData.daily_study_time_minutes),
                preferred_days: preferredDays.length === 7 ? null : preferredDays, // Send null if all days are selected
                unavailable_periods: unavailablePeriods.length > 0 ? formattedUnavailablePeriods : null
            };

            console.log('Sending study plan request:', payload);
            console.log('Using API URL:', API_URL);
            console.log('Token available:', !!token);
            console.log('User ID:', user.id);

            // Make API call to create study plan
            const response = await axios.post(`${API_URL}/api/create_study_plan`, payload, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Study plan creation response:', response.data);

            if (response.data.success) {
                // Check if the timeframe is realistic
                if (response.data.is_realistic === false && response.data.suggestions) {
                    // Show suggestions to the user
                    setMessagePopup({
                        show: true,
                        message: `Study plan created, but ${response.data.suggestions.message}`,
                        isError: false
                    });
                } else {
                    // Show success message
                    setMessagePopup({
                        show: true,
                        message: 'Study plan created successfully!',
                        isError: false
                    });
                }
            } else {
                throw new Error(response.data.message || 'Failed to create study plan');
            }
        } catch (error) {
            console.error('Error creating study plan:', error);
            console.error('Error details:', error.response?.data || error.message);
            console.error('Status code:', error.response?.status);
            console.error('Headers:', error.response?.headers);

            setMessagePopup({
                show: true,
                message: `Error creating study plan: ${error.response?.data?.message || error.message}`,
                isError: true
            });
        } finally {
            setIsCreating(false);
        }
    };

    return (
        <div className="study-plan-creator-overlay">
            <div className="study-plan-creator-container">
                <div className="popup-header-main">
                    <h2>Create Study Plan</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>
                <div className="popup-content">
                    <form onSubmit={handleSubmit}>
                        <div className="form-group">
                            <label>
                                <MenuBookIcon /> Study Plan Name
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleInputChange}
                                className="macos-input"
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <CalendarTodayIcon /> Start Date
                            </label>
                            <DatePicker
                                selected={formData.start_date}
                                onChange={(date) => handleDateChange(date, 'start_date')}
                                className="macos-input"
                                dateFormat="yyyy-MM-dd"
                                minDate={new Date()}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <CalendarTodayIcon /> End Date
                            </label>
                            <DatePicker
                                selected={formData.end_date}
                                onChange={(date) => handleDateChange(date, 'end_date')}
                                className="macos-input"
                                dateFormat="yyyy-MM-dd"
                                minDate={formData.start_date}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <AccessTimeIcon /> Daily Study Time (minutes)
                            </label>
                            <input
                                type="number"
                                name="daily_study_time_minutes"
                                value={formData.daily_study_time_minutes}
                                onChange={handleInputChange}
                                className="macos-input"
                                min="15"
                                max="480"
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <TodayIcon /> Preferred Study Days
                            </label>
                            <div className="days-of-week-selector">
                                {daysOfWeek.map(day => (
                                    <div
                                        key={day.value}
                                        className={`day-checkbox ${preferredDays.includes(day.value) ? 'selected' : ''}`}
                                        onClick={() => togglePreferredDay(day.value)}
                                    >
                                        {preferredDays.includes(day.value) ?
                                            <CheckBoxIcon className="checkbox-icon" /> :
                                            <CheckBoxOutlineBlankIcon className="checkbox-icon" />
                                        }
                                        <span>{day.label}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="form-group">
                            <div className="unavailable-periods-header">
                                <label>
                                    <EventBusyIcon /> Unavailable Periods
                                </label>
                                <button
                                    type="button"
                                    className="macos-button small"
                                    onClick={() => setShowUnavailablePeriods(!showUnavailablePeriods)}
                                >
                                    {showUnavailablePeriods ? 'Hide' : 'Show'}
                                </button>
                            </div>

                            {showUnavailablePeriods && (
                                <div className="unavailable-periods-container">
                                    {unavailablePeriods.length === 0 ? (
                                        <p className="no-periods-message">No unavailable periods specified.</p>
                                    ) : (
                                        unavailablePeriods.map(period => (
                                            <div key={period.id} className="unavailable-period">
                                                <div className="period-dates">
                                                    <div className="period-date">
                                                        <label>From:</label>
                                                        <DatePicker
                                                            selected={period.start_date}
                                                            onChange={(date) => updateUnavailablePeriod(period.id, 'start_date', date)}
                                                            className="macos-input"
                                                            dateFormat="yyyy-MM-dd"
                                                            minDate={formData.start_date}
                                                            maxDate={formData.end_date}
                                                        />
                                                    </div>
                                                    <div className="period-date">
                                                        <label>To:</label>
                                                        <DatePicker
                                                            selected={period.end_date}
                                                            onChange={(date) => updateUnavailablePeriod(period.id, 'end_date', date)}
                                                            className="macos-input"
                                                            dateFormat="yyyy-MM-dd"
                                                            minDate={period.start_date}
                                                            maxDate={formData.end_date}
                                                        />
                                                    </div>
                                                </div>
                                                <button
                                                    type="button"
                                                    className="remove-period-button"
                                                    onClick={() => removeUnavailablePeriod(period.id)}
                                                >
                                                    <DeleteOutlineIcon />
                                                </button>
                                            </div>
                                        ))
                                    )}

                                    <button
                                        type="button"
                                        className="macos-button small add-period-button"
                                        onClick={addUnavailablePeriod}
                                    >
                                        <AddIcon /> Add Period
                                    </button>
                                </div>
                            )}
                        </div>

                        <div className="curriculum-info">
                            <h3>Curriculum</h3>
                            <p>{curriculumTitle}</p>
                        </div>

                        <button
                            type="submit"
                            className="macos-button primary"
                            disabled={isCreating}
                        >
                            {isCreating ? (
                                <>
                                    <span className="spinner"></span> Creating...
                                </>
                            ) : (
                                <>Create Study Plan <ArrowForwardIcon /></>
                            )}
                        </button>
                    </form>
                </div>
            </div>
            {messagePopup.show && (
                <MessagePopup
                    message={messagePopup.message}
                    isError={messagePopup.isError}
                    onClose={handleMessagePopupClose}
                />
            )}
        </div>
    );
};

export default StudyPlanCreator;
