import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../authContext';
import { useNavigate } from 'react-router-dom';
import '../styles/Subject.scss';
import DeleteIcon from '@mui/icons-material/Delete';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const AddSubject = ({ onSubjectAdded }) => {
    const { user, token, logout } = useAuth();
    const navigate = useNavigate();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);
    const [files, setFiles] = useState([]);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [availableTools, setAvailableTools] = useState([]);
    const [formData, setFormData] = useState({
        name: '',
        fileType: '',
        studyTools: [],
        fileName: ''
    });
    const [subjectExistsError, setSubjectExistsError] = useState('');
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

    const steps = [
        {
            id: 'subject-info',
            title: 'Subject Information',
            fields: [
                {
                    type: 'text-input',
                    name: 'name',
                    label: 'Subject Name',
                    placeholder: 'Enter subject name',
                    required: true
                }
            ],
            validate: () => formData.name.trim() !== '' && !subjectExistsError
        },
        {
            id: 'file-selection',
            title: 'File Selection',
            fields: [
                {
                    type: 'file-type-selector',
                    options: [
                        { value: 'pdf', label: 'PDF', icon: '📄' },
                        { value: 'doc', label: 'Document', icon: '📝' },
                        { value: 'audio', label: 'Audio', icon: '🎵' },
                        { value: 'video', label: 'Video', icon: '🎥' },
                        { value: 'youtube', label: 'YouTube Link', icon: '▶️' }
                    ],
                    required: true
                },
                {
                    type: 'file-upload',
                    required: true
                }
            ],
            validate: () => formData.fileType && (formData.fileType === 'youtube' ? formData.fileName : files.length > 0)
        },
        {
            id: 'processing-options',
            title: 'Processing Options',
            fields: [
                {
                    type: 'study-tools',
                    options: availableTools,
                    required: false
                }
            ],
            validate: () => true
        },
        {
            id: 'confirmation',
            title: 'Confirmation',
            fields: [
                {
                    type: 'summary',
                    fields: [
                        { label: 'Subject Name', value: formData.name },
                        { label: 'File Type', value: formData.fileType },
                        { label: 'File/Link', value: files[0]?.name || formData.fileName || 'No file selected' },
                        { label: 'Processing Options', value: formData.studyTools.join(', ') || 'None' }
                    ]
                },
                {
                    type: 'progress',
                    show: isLoading
                }
            ],
            validate: () => true
        }
    ];

    useEffect(() => {
        if (!user || !token) {
            console.error('AddSubject.jsx: No user or token. Redirecting to login.');
            navigate('/login', { replace: true });
            return;
        }

        const fetchStudyTools = async () => {
            try {
                const response = await axios.get(`${API_URL}/api/study-tools`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                if (response.data.success) {
                    setAvailableTools(response.data.tools.map(tool => ({
                        value: tool.id.toString(),
                        label: tool.name
                    })));
                    console.log('AddSubject.jsx: Fetched study tools:', response.data.tools);
                }
            } catch (error) {
                console.error('AddSubject.jsx: Error fetching study tools:', {
                    message: error.message,
                    response: error.response?.data,
                    status: error.response?.status
                });
                if (error.response?.status === 403) {
                    console.error('AddSubject.jsx: Invalid token. Redirecting to login.');
                    logout();
                    navigate('/login', { replace: true });
                } else {
                    setError('Failed to load study tools. Please try again.');
                }
            }
        };

        fetchStudyTools();
    }, [user, token, navigate, logout]);

    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage(null);
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    const handleDragOver = (e) => {
        e.preventDefault();
        e.currentTarget.classList.add('drag-active');
    };

    const handleDragLeave = (e) => {
        e.currentTarget.classList.remove('drag-active');
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-active');
        const droppedFiles = Array.from(e.dataTransfer.files);
        setFiles(droppedFiles);
        setFormData(prev => ({ ...prev, fileName: droppedFiles[0]?.name || '' }));
    };

    const handleFileChange = (e) => {
        const selectedFiles = Array.from(e.target.files);
        setFiles(selectedFiles);
        setFormData(prev => ({ ...prev, fileName: selectedFiles[0]?.name || '' }));
    };

    const handleYouTubeLinkChange = (e) => {
        setFormData(prev => ({ ...prev, fileName: e.target.value }));
    };

    const removeFile = () => {
        setFiles([]);
        setFormData(prev => ({ ...prev, fileName: '' }));
    };

    const handleFieldChange = (field, value) => {
        setFormData(prev => {
            if (field === 'studyTools') {
                const newTools = prev.studyTools.includes(value)
                    ? prev.studyTools.filter(t => t !== value)
                    : [...prev.studyTools, value];
                return { ...prev, [field]: newTools };
            }
            return { ...prev, [field]: value };
        });

        if (field === 'name') {
            setSubjectExistsError('');
            if (value.trim()) {
                console.log(`[FRONTEND] Checking subject existence for: "${value}"`);
                axios.get(`${API_URL}/api/subjects/by-name`, {
                    params: { name: value },
                    headers: { Authorization: `Bearer ${token}` }
                })
                    .then(response => {
                        console.log(`[FRONTEND] Subject check response:`, response.data);
                        if (response.data.success && response.data.subject) {
                            console.log(`[FRONTEND] Subject "${value}" already exists, showing error`);
                            const existingName = response.data.subject.name;
                            if (existingName.toLowerCase() === value.toLowerCase() && existingName !== value) {
                                setSubjectExistsError(`Subject "${existingName}" already exists (case-insensitive match). Please choose a different name.`);
                            } else {
                                setSubjectExistsError(`Subject "${value}" already exists. Please choose a different name.`);
                            }
                        } else {
                            console.log(`[FRONTEND] Subject "${value}" is available`);
                        }
                    })
                    .catch(error => {
                        console.error('AddSubject.jsx: Error checking subject existence:', error);
                        console.error('AddSubject.jsx: Error response:', error.response?.data);
                        setSubjectExistsError('Failed to check subject existence. Please try again.');
                    });
            }
        }
    };

    const simulateDelayedProgress = () => {
        const totalDuration = 20000; // 20 seconds
        const startTime = performance.now();

        const updateProgress = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min((elapsed / totalDuration) * 100, 100);
            setUploadProgress(progress);

            if (progress < 100) {
                requestAnimationFrame(updateProgress);
            }
        };

        requestAnimationFrame(updateProgress);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (currentStep !== steps.length - 1) {
            console.warn('AddSubject.jsx: handleSubmit called on non-confirmation step:', currentStep);
            return;
        }

        setIsLoading(true);
        setError(null);
        setUploadProgress(0);
        simulateDelayedProgress();

        try {
            // Create new subject
            const subjectResponse = await axios.post(`${API_URL}/api/subjects`, { name: formData.name }, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (!subjectResponse.data.success) {
                throw new Error(subjectResponse.data.message || 'Failed to create subject');
            }

            const subject = subjectResponse.data.subject;
            const subjectId = subject.id.toString();
            console.log('AddSubject.jsx: Created new subject:', { id: subjectId, name: subject.name });

            // Upload file or YouTube link
            let fileResponse;
            if (formData.fileType === 'youtube') {
                const uploadData = {
                    subject_id: subjectId,
                    file_type: 'youtube',
                    youtube_url: formData.fileName,
                    study_tools: JSON.stringify(formData.studyTools)
                };
                console.log('AddSubject.jsx: Uploading YouTube link:', uploadData);
                fileResponse = await axios.post(`${API_URL}/api/files/upload`, uploadData, {
                    headers: { Authorization: `Bearer ${token}` }
                });
            } else {
                if (!files[0]) {
                    throw new Error('No file selected for upload');
                }

                const formDataObj = new FormData();
                formDataObj.append('file', files[0]);
                formDataObj.append('subject_id', subjectId);
                formDataObj.append('file_type', formData.fileType);
                formDataObj.append('study_tools', JSON.stringify(formData.studyTools));

                console.log('AddSubject.jsx: Uploading file:', {
                    subject_id: subjectId,
                    file_type: formData.fileType,
                    study_tools: formData.studyTools,
                    file_name: files[0]?.name
                });

                fileResponse = await axios.post(`${API_URL}/api/files/upload`, formDataObj, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'multipart/form-data'
                    }
                });
            }

            console.log('AddSubject.jsx: File upload response:', fileResponse.data);

            // Wait for the progress bar to complete (20 seconds) for UX
            await new Promise(resolve => setTimeout(resolve, 20000));
            setIsLoading(false);

            // Handle all success cases
            if (fileResponse.data.success) {
                // Check if onSubjectAdded is a function before calling
                if (typeof onSubjectAdded === 'function') {
                    onSubjectAdded();
                } else {
                    console.warn('AddSubject.jsx: onSubjectAdded is not a function, skipping callback:', onSubjectAdded);
                }
                setSuccessMessage(fileResponse.data.message || 'Subject and file uploaded successfully!');
                resetForm();
            } else {
                throw new Error(fileResponse.data.message || 'File upload failed');
            }
        } catch (err) {
            console.error('AddSubject.jsx: Subject creation/upload failed:', {
                message: err.message,
                response: err.response?.data,
                status: err.response?.status,
                request: {
                    url: err.config?.url,
                    method: err.config?.method,
                    headers: err.config?.headers,
                    data: err.config?.data instanceof FormData
                        ? [...err.config.data.entries()].map(([key, value]) => ({
                            key,
                            value: value instanceof File ? { name: value.name, size: value.size } : value
                        }))
                        : err.config?.data
                }
            });
            setIsLoading(false);
            if (err.response?.status === 403) {
                console.error('AddSubject.jsx: Invalid token. Redirecting to login.');
                logout();
                navigate('/login', { replace: true });
            } else {
                setError(err.response?.data?.message || 'Failed to add subject or upload file. Please try again.');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            name: '',
            fileType: '',
            studyTools: [],
            fileName: ''
        });
        setFiles([]);
        setCurrentStep(0);
        setIsModalOpen(false);
        setError(null);
        setSubjectExistsError('');
        setUploadProgress(0);
    };

    const formatFileSize = (bytes) => {
        if (!bytes || bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const nextStep = () => {
        if (!steps[currentStep].validate()) {
            setError('Please complete all required fields and ensure the subject name is unique.');
            return;
        }
        setError(null);
        if (currentStep < steps.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        setError(null);
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const renderField = (field) => {
        switch (field.type) {
            case 'text-input':
                return (
                    <div className="form-group">
                        <label htmlFor={field.name}>{field.label}</label>
                        <input
                            type="text"
                            id={field.name}
                            value={formData[field.name]}
                            onChange={(e) => handleFieldChange(field.name, e.target.value)}
                            required={field.required}
                            placeholder={field.placeholder}
                            className="macos-input"
                        />
                        {subjectExistsError && field.name === 'name' && (
                            <p className="error-message">{subjectExistsError}</p>
                        )}
                    </div>
                );
            case 'file-type-selector':
                return (
                    <div className="file-type-selector">
                        <div className="options-grid">
                            {field.options.map(option => (
                                <button
                                    key={option.value}
                                    type="button"
                                    className={`macos-button ${formData.fileType === option.value ? 'active' : ''}`}
                                    onClick={() => handleFieldChange('fileType', option.value)}
                                >
                                    <span className="option-icon">{option.icon}</span>
                                    {option.label}
                                </button>
                            ))}
                        </div>
                    </div>
                );
            case 'file-upload':
                return formData.fileType === 'youtube' ? (
                    <div className="form-group">
                        <label>YouTube Link</label>
                        <input
                            type="text"
                            placeholder="Enter YouTube URL"
                            value={formData.fileName}
                            onChange={handleYouTubeLinkChange}
                            className="macos-input"
                            required
                        />
                    </div>
                ) : (
                    <div
                        className="drop-zone"
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                    >
                        <input
                            type="file"
                            onChange={handleFileChange}
                            style={{ display: 'none' }}
                            id="file-input"
                            accept={formData.fileType === 'pdf' ? '.pdf' : formData.fileType === 'doc' ? '.doc,.docx' : formData.fileType === 'audio' ? 'audio/*' : formData.fileType === 'video' ? 'video/*' : '*'}
                        />
                        <div className="drop-area">
                            <h2>Drag and drop file here</h2>
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                            <p className="hint">or click to browse</p>
                        </div>
                        <button
                            className="macos-button"
                            type="button"
                            onClick={() => document.getElementById('file-input').click()}
                        >
                            Browse Files
                        </button>
                        {files.length > 0 && (
                            <div className="selected-files-preview">
                                <div className="file-card">
                                    <div className="file-icon">
                                        {steps[1].fields[0].options.find(opt => opt.value === formData.fileType)?.icon || '📄'}
                                    </div>
                                    <div className="file-info">
                                        <p className="file-name">{files[0]?.name || 'No file selected'}</p>
                                        <p className="file-size">{files[0] && formatFileSize(files[0].size)}</p>
                                    </div>
                                    <button
                                        className="macos-button danger"
                                        type="button"
                                        onClick={removeFile}
                                    >
                                        <DeleteIcon />
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                );
            case 'study-tools':
                return (
                    <div className="form-group">

                        <div className="options-grid">
                            {field.options.map(option => (
                                <button
                                    key={option.value}
                                    type="button"
                                    className={`macos-button ${formData.studyTools.includes(option.value) ? 'active' : ''}`}
                                    onClick={() => handleFieldChange('studyTools', option.value)}
                                >
                                    {option.label}
                                </button>
                            ))}
                        </div>
                    </div>
                );
            case 'summary':
                return (
                    <div className="upload-summary">
                        {field.fields.map((item, index) => (
                            <div key={`summary-${index}`} className="summary-item">
                                <span className="summary-label">{item.label}:</span>
                                <span className="summary-value">{item.value || 'Not specified'}</span>
                            </div>
                        ))}
                    </div>
                );
            case 'progress':
                return field.show && (
                    <div className="upload-progress-container">
                        <div className="progress-header">
                            <span>Uploading file...</span>
                            <span>{Math.round(uploadProgress)}%</span>
                        </div>
                        <div className="progress-bar-container">
                            <div
                                className="progress-bar"
                                style={{ width: `${uploadProgress}%` }}
                            ></div>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    const renderButtons = () => {
        const isLastStep = currentStep === steps.length - 1;
        const isFirstStep = currentStep === 0;

        return (
            <div className="popup-footer">
                {!isFirstStep && (
                    <button
                        type="button"
                        className="macos-button"
                        onClick={prevStep}
                        disabled={isLoading}
                    >
                        <ArrowBackIcon /> Back
                    </button>
                )}
                {!isLastStep ? (
                    <button
                        type="button"
                        className="macos-button primary"
                        onClick={nextStep}
                        disabled={isLoading || subjectExistsError}
                    >
                        Next <ArrowForwardIcon />
                    </button>
                ) : (
                    <button
                        type="submit"
                        className="macos-button primary"
                        disabled={isLoading || (formData.fileType !== 'youtube' && files.length === 0)}
                    >
                        {isLoading ? (
                            <>
                                <span className="spinner"></span> Uploading...
                            </>
                        ) : (
                            <>Complete Upload <ArrowForwardIcon /></>
                        )}
                    </button>
                )}
            </div>
        );
    };

    return (
        <div className="add-subject-container">
            <button
                className="add-subject-button macos-button"
                type="button"
                onClick={() => setIsModalOpen(true)}
            >
                + Add New Subject
            </button>

            {isModalOpen && (
                <div className="modal-overlay-addsubject" onClick={() => !isLoading && setIsModalOpen(false)}>
                    <div className="modal-content macos-style" onClick={(e) => e.stopPropagation()}>
                        <div className="popup-header">
                            <h3>{steps[currentStep].title}</h3>
                            <button
                                className="close-button-popup"
                                type="button"
                                onClick={() => !isLoading && setIsModalOpen(false)}
                                disabled={isLoading}
                            >
                                ×
                            </button>
                        </div>

                        <form onSubmit={handleSubmit}>
                            <div className="popup-content">
                                <div className="step-content">
                                    {steps[currentStep].fields.map((field, index) => (
                                        <div key={`field-${index}`}>
                                            {renderField(field)}
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {error && <div className="error-message">{error}</div>}

                            {renderButtons()}
                        </form>
                    </div>
                </div>
            )}

            {successMessage && (
                <div className="modal-overlay-addsubject" onClick={() => setSuccessMessage(null)}>
                    <div className="modal-content macos-style success-popup" onClick={(e) => e.stopPropagation()}>
                        <div className="popup-header">
                            <h3>Success</h3>
                            <button
                                className="close-button-popup"
                                type="button"
                                onClick={() => setSuccessMessage(null)}
                            >
                                ×
                            </button>
                        </div>
                        <div className="popup-content">
                            <p className="success-message">{successMessage}</p>
                        </div>
                        <div className="popup-footer">

                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AddSubject;