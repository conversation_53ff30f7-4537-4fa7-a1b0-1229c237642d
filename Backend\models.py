from database import db
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, func, Boolean, Date, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta

class File(db.Model):
    __tablename__ = 'files'
    id = Column(Integer, primary_key=True)
    subject_id = Column(Integer, ForeignKey('subjects.id', ondelete='CASCADE'), nullable=False)
    filename = Column(Text, nullable=False)
    originalname = Column(Text, nullable=False)
    filetype = Column(Text, nullable=True)
    filepath = Column(Text, nullable=True)
    uploaded_at = Column(DateTime, default=func.now())

    # Relationships
    study_plans = relationship('StudyPlan', back_populates='file', foreign_keys='StudyPlan.curriculum_id')

    def __repr__(self):
        return f"<File(id={self.id}, filename={self.filename})>"

class User(db.Model):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True)
    full_name = Column(String(50), nullable=True)
    email = Column(String(100), unique=True, nullable=False)
    password = Column(String(255), nullable=False)
    preferences_completed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    curricula = relationship('Curriculum', back_populates='user')
    study_plans = relationship('StudyPlan', back_populates='user')

    def __repr__(self):
        return f"<User(id={self.id}, email={self.email})>"

class Curriculum(db.Model):
    __tablename__ = 'curricula'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    uploaded_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship('User', back_populates='curricula')

    def __repr__(self):
        return f"<Curriculum(id={self.id}, title={self.title})>"

class Subject(db.Model):
    __tablename__ = 'subjects'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)  # Changed from curriculum_id to match DB
    name = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=func.now())  # Added to match DB schema

    # Relationships
    user = relationship('User', backref='subjects')  # Changed from curriculum to user
    chapters = relationship('Chapter', back_populates='subject', cascade="all, delete-orphan")
    study_plans = relationship('StudyPlan', back_populates='subject')  # Add relationship to StudyPlan

    def __repr__(self):
        return f"<Subject(id={self.id}, name={self.name})>"

class Chapter(db.Model):
    __tablename__ = 'chapters'
    id = Column(Integer, primary_key=True)
    subject_id = Column(Integer, ForeignKey('subjects.id', ondelete='CASCADE'), nullable=False)
    chapter_number = Column(String(50), nullable=True)  # Changed from Integer to String to store "Chapter X" format
    title = Column(String(255), nullable=False)  # Title of the chapter
    content = Column(Text, nullable=True)  # Actual content of the chapter (for smaller content)
    content_file_path = Column(String(500), nullable=True)  # Path to JSON file for large content
    content_size = Column(Integer, default=0)  # Size of content in characters
    # Note: The database only has 'title', not 'name'
    # For backward compatibility, add a property for 'name' that returns the title

    # Relationships
    subject = relationship('Subject', back_populates='chapters')
    objectives = relationship('Objective', back_populates='chapter', cascade="all, delete-orphan")

    @property
    def name(self):
        """For backward compatibility - returns the title"""
        return self.title

    @name.setter
    def name(self, value):
        """For backward compatibility - sets the title"""
        self.title = value

    def get_content(self):
        """Get the full content of the chapter, whether stored in DB or file"""
        import json
        import os

        # If content is stored directly in the database
        if self.content and self.content.strip():
            return self.content

        # If content is stored in a file
        if self.content_file_path and os.path.exists(self.content_file_path):
            try:
                with open(self.content_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('content', '')
            except Exception as e:
                print(f"Error reading content file {self.content_file_path}: {str(e)}")
                return ''

        return ''

    def set_content(self, content_text, user_id=None):
        """Set the content of the chapter, storing in DB or file based on size"""
        import json
        import os
        from datetime import datetime

        if not content_text:
            self.content = ''
            self.content_size = 0
            return

        content_size = len(content_text)
        self.content_size = content_size

        # Threshold for storing in file vs database (50KB)
        FILE_STORAGE_THRESHOLD = 50000

        if content_size <= FILE_STORAGE_THRESHOLD:
            # Store directly in database
            self.content = content_text
            self.content_file_path = None
        else:
            # Store in JSON file
            self.content = None

            # Create content directory if it doesn't exist
            content_dir = os.path.join(os.path.dirname(__file__), 'chapter_content')
            if user_id:
                content_dir = os.path.join(content_dir, str(user_id))

            os.makedirs(content_dir, exist_ok=True)

            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"chapter_{self.id}_{timestamp}.json"
            file_path = os.path.join(content_dir, filename)

            # Save content to file
            content_data = {
                'chapter_id': self.id,
                'title': self.title,
                'content': content_text,
                'created_at': datetime.now().isoformat(),
                'size': content_size
            }

            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(content_data, f, ensure_ascii=False, indent=2)

                self.content_file_path = file_path
                print(f"Stored large content ({content_size} chars) in file: {file_path}")
            except Exception as e:
                print(f"Error saving content to file: {str(e)}")
                # Fallback to database storage
                self.content = content_text
                self.content_file_path = None

    def __repr__(self):
        return f"<Chapter(id={self.id}, title={self.title})>"

class Objective(db.Model):
    __tablename__ = 'objectives'
    id = Column(Integer, primary_key=True)
    chapter_id = Column(Integer, ForeignKey('chapters.id', ondelete='CASCADE'), nullable=False)
    objective = Column(Text, nullable=False)  # Changed from 'text' to 'objective' to match database schema
    estimated_time_minutes = Column(Integer, default=30)  # Estimated time to complete
    difficulty_level = Column(Integer, default=1)  # 1-5 scale
    display_date = Column(Date, nullable=True)  # Date when the objective will be displayed as a daily objective
    completed = Column(Boolean, default=False)  # Added to match database schema

    # Relationships
    chapter = relationship('Chapter', back_populates='objectives')
    schedule_items = relationship('ScheduleItem', back_populates='objective', cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Objective(id={self.id}, objective={self.objective[:50]})>"


class StudyPlan(db.Model):
    __tablename__ = 'study_plans'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    subject_id = Column(Integer, ForeignKey('subjects.id', ondelete='CASCADE'), nullable=False)  # Changed from curriculum_id
    curriculum_id = Column(Integer, ForeignKey('files.id', ondelete='CASCADE'), nullable=True)  # Keep for backward compatibility
    name = Column(String(255), nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    daily_study_time_minutes = Column(Integer, default=60)
    preferred_days = Column(String(50), nullable=True)  # Comma-separated list of days (0-6)
    unavailable_periods = Column(Text, nullable=True)  # JSON string of unavailable periods
    created_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship('User', back_populates='study_plans')
    subject = relationship('Subject', back_populates='study_plans')  # New relationship to Subject
    file = relationship('File', back_populates='study_plans', foreign_keys=[curriculum_id])  # Keep for backward compatibility
    schedule_items = relationship('ScheduleItem', back_populates='study_plan', cascade="all, delete-orphan")

    def __repr__(self):
        return f"<StudyPlan(id={self.id}, name={self.name})>"


class ScheduleItem(db.Model):
    __tablename__ = 'schedule_items'
    id = Column(Integer, primary_key=True)
    study_plan_id = Column(Integer, ForeignKey('study_plans.id', ondelete='CASCADE'), nullable=False)
    objective_id = Column(Integer, ForeignKey('objectives.id', ondelete='CASCADE'), nullable=False)
    scheduled_date = Column(Date, nullable=False)
    is_completed = Column(Boolean, default=False)
    completion_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())

    # Relationships
    study_plan = relationship('StudyPlan', back_populates='schedule_items')
    objective = relationship('Objective', back_populates='schedule_items')

    def __repr__(self):
        return f"<ScheduleItem(id={self.id}, date={self.scheduled_date}, completed={self.is_completed})>"


class Quiz(db.Model):
    __tablename__ = 'quizzes'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    subject_id = Column(Integer, ForeignKey('subjects.id', ondelete='CASCADE'), nullable=False)
    chapter_id = Column(Integer, ForeignKey('chapters.id', ondelete='SET NULL'), nullable=True)  # Optional chapter reference
    title = Column(String(255), nullable=False)
    description = Column(Text)
    difficulty = Column(String(20), default='medium')
    time_limit = Column(Integer, default=0)  # in minutes, 0 means no limit
    total_questions = Column(Integer, default=0)
    total_points = Column(Integer, default=0)
    question_types = Column(JSON)  # Array of question types
    ai_generated = Column(Boolean, default=False)
    ai_prompt = Column(Text)  # Store the original prompt used for AI generation
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship('User')
    subject = relationship('Subject')
    chapter = relationship('Chapter')  # Add chapter relationship
    questions = relationship('QuizQuestion', back_populates='quiz', cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Quiz(id={self.id}, title={self.title}, user_id={self.user_id})>"


class QuizQuestion(db.Model):
    __tablename__ = 'quiz_questions'
    id = Column(Integer, primary_key=True)
    quiz_id = Column(Integer, ForeignKey('quizzes.id', ondelete='CASCADE'), nullable=False)
    question_number = Column(Integer, nullable=False)
    question_text = Column(Text, nullable=False)
    question_type = Column(String(20), nullable=False)  # 'multiple_choice' or 'short_answer'
    options = Column(JSON)  # For multiple choice questions
    correct_answer = Column(Text, nullable=False)
    explanation = Column(Text)
    points = Column(Integer, default=1)
    difficulty_level = Column(String(20), default='medium')
    created_at = Column(DateTime, default=func.now())

    # Relationships
    quiz = relationship('Quiz', back_populates='questions')

    def __repr__(self):
        return f"<QuizQuestion(id={self.id}, quiz_id={self.quiz_id}, question_number={self.question_number})>"


class QuizResult(db.Model):
    __tablename__ = 'quiz_results'
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    quiz_id = Column(Integer, ForeignKey('quizzes.id', ondelete='CASCADE'), nullable=False)
    score = Column(Integer, nullable=False)  # Percentage score (0-100)
    time_taken = Column(Integer, nullable=False)  # Time in seconds
    answers = Column(JSON, nullable=False)  # Array of user answers
    completed_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship('User')
    quiz = relationship('Quiz')

    def __repr__(self):
        return f"<QuizResult(id={self.id}, user_id={self.user_id}, quiz_id={self.quiz_id}, score={self.score})>"