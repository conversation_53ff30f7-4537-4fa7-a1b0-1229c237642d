@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// <PERSON> Card - Professional macOS style
.eisenhower-card {
    width: 100%;
    height: 100%;
    padding: 1rem;
    cursor: pointer;
    @include transition(all);
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid $border-color;

    // Light theme
    background: linear-gradient(135deg, $window-background 0%, rgba($secondary-background, 0.8) 100%);

    // Dark theme
    &.theme-dark {
        background: linear-gradient(135deg, $dark-window-background 0%, rgba($dark-secondary-background, 0.8) 100%);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        border: 1px solid $dark-border-color;
    }

    // Card Header
    .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid $separator-color;

        &.theme-dark {
            border-bottom-color: $dark-separator-color;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .icon-container {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 8px;
                background: rgba($system-blue, 0.1);

                &.theme-dark {
                    background: rgba($system-blue, 0.2);
                }

                .matrix-icon {
                    color: $system-blue;

                    .theme-dark & {
                        color: color.adjust($system-blue, $lightness: 10%);
                    }
                }
            }

            .card-title {
                font-size: 1.1rem;
                font-weight: 600;
                margin: 0;
                letter-spacing: -0.2px;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }
        }

        .total-tasks {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.125rem;

            .total-count {
                font-size: 1.5rem;
                font-weight: 700;
                color: $system-blue;
                line-height: 1;

                .theme-dark & {
                    color: color.adjust($system-blue, $lightness: 10%);
                }
            }

            .total-label {
                font-size: 0.75rem;
                font-weight: 500;
                color: $tertiary-text;
                text-transform: uppercase;
                letter-spacing: 0.5px;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }
        }
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

        &.theme-dark {
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
        }

        .quadrant-preview {
            transform: scale(1.02);
        }

        .expand-indicator {
            opacity: 1;
            transform: translateY(0);
        }
    }

    &:focus {
        outline: 2px solid $system-blue;
        outline-offset: 2px;
    }

    // Matrix Preview
    .matrix-preview {
        flex: 1;
        display: flex;
        flex-direction: column;
        position: relative;

        .matrix-grid-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 0.5rem;
            flex: 1;
            min-height: 120px;

            .quadrant-preview {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding: 0.75rem;
                border-radius: 12px;
                @include transition(all);
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    opacity: 0.1;
                    border-radius: 12px;
                }

                .quadrant-header {
                    display: flex;
                    align-items: center;
                    gap: 0.375rem;
                    margin-bottom: 0.5rem;

                    .quadrant-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 20px;
                        height: 20px;
                        border-radius: 6px;
                        background: rgba(255, 255, 255, 0.2);

                        svg {
                            width: 12px;
                            height: 12px;
                        }
                    }

                    .quadrant-label {
                        font-size: 0.75rem;
                        font-weight: 600;
                        color: white;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                        letter-spacing: 0.25px;
                    }
                }

                .task-count {
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: white;
                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                    line-height: 1;
                    align-self: flex-end;
                }

                &.do-quadrant {
                    background: linear-gradient(135deg, $system-red 0%, color.adjust($system-red, $lightness: -10%) 100%);
                    border: 1px solid rgba($system-red, 0.3);

                    &::before {
                        background: $system-red;
                    }
                }

                &.schedule-quadrant {
                    background: linear-gradient(135deg, $system-green 0%, color.adjust($system-green, $lightness: -10%) 100%);
                    border: 1px solid rgba($system-green, 0.3);

                    &::before {
                        background: $system-green;
                    }
                }

                &.delegate-quadrant {
                    background: linear-gradient(135deg, $system-orange 0%, color.adjust($system-orange, $lightness: -10%) 100%);
                    border: 1px solid rgba($system-orange, 0.3);

                    &::before {
                        background: $system-orange;
                    }
                }

                &.delete-quadrant {
                    background: linear-gradient(135deg, $system-gray 0%, color.adjust($system-gray, $lightness: -10%) 100%);
                    border: 1px solid rgba($system-gray, 0.3);

                    &::before {
                        background: $system-gray;
                    }
                }
            }
        }

        .matrix-axes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;

            .axis-labels {
                position: relative;
                width: 100%;
                height: 100%;

                .axis-label {
                    position: absolute;
                    font-size: 0.625rem;
                    font-weight: 500;
                    color: $tertiary-text;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }

                    &.urgent {
                        top: -0.75rem;
                        left: 25%;
                        transform: translateX(-50%);
                    }

                    &.not-urgent {
                        top: -0.75rem;
                        right: 25%;
                        transform: translateX(50%);
                    }

                    &.important {
                        left: -2.5rem;
                        top: 25%;
                        transform: translateY(-50%) rotate(-90deg);
                        transform-origin: center;
                    }

                    &.not-important {
                        left: -2.5rem;
                        bottom: 25%;
                        transform: translateY(50%) rotate(-90deg);
                        transform-origin: center;
                    }
                }
            }
        }
    }

    // Expand Indicator
    .expand-indicator {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        opacity: 0.6;
        transform: translateY(2px);
        @include transition(all);

        svg {
            color: $tertiary-text;

            .theme-dark & {
                color: $dark-tertiary-text;
            }
        }
    }

    // Responsive Design
    @media (max-width: 768px) {
        padding: 0.75rem;

        .card-header {
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;

            .header-content {
                .icon-container {
                    width: 28px;
                    height: 28px;
                }

                .card-title {
                    font-size: 1rem;
                }
            }

            .total-tasks {
                .total-count {
                    font-size: 1.25rem;
                }

                .total-label {
                    font-size: 0.7rem;
                }
            }
        }

        .matrix-preview {
            .matrix-grid-preview {
                gap: 0.375rem;
                min-height: 100px;

                .quadrant-preview {
                    padding: 0.5rem;

                    .quadrant-header {
                        gap: 0.25rem;
                        margin-bottom: 0.375rem;

                        .quadrant-icon {
                            width: 16px;
                            height: 16px;

                            svg {
                                width: 10px;
                                height: 10px;
                            }
                        }

                        .quadrant-label {
                            font-size: 0.7rem;
                        }
                    }

                    .task-count {
                        font-size: 1.25rem;
                    }
                }
            }

            .matrix-axes {
                .axis-labels {
                    .axis-label {
                        font-size: 0.55rem;

                        &.important,
                        &.not-important {
                            left: -2rem;
                        }
                    }
                }
            }
        }

        .expand-indicator {
            top: 0.5rem;
            right: 0.5rem;

            svg {
                width: 14px;
                height: 14px;
            }
        }
    }
}

// Modal Overlay - macOS style
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    /* Increased z-index to ensure it's above everything */
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: rgba(0, 0, 0, 0.5);
    /* Increased opacity for better visibility */

    // Dark theme
    .theme-dark & {
        background-color: rgba(0, 0, 0, 0.7);
        /* Increased opacity for better visibility */
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }
}

// Modal Window - macOS style
.modal-window {
    width: 700px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    border-radius: 16px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    z-index: 999999;
    position: absolute;

    // Light theme
    background-color: $window-background;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid $border-color;

    // Dark theme
    .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        border: 1px solid $dark-border-color;
    }

    // Fullscreen mode
    &.fullscreen {
        width: 95vw;
        max-width: 95vw;
        height: 95vh;
        max-height: 95vh;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

        // Dark theme
        .theme-dark & {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            height: calc(95vh - 120px); // Adjust for header and footer
        }
    }

    // Eisenhower specific modal styling
    &.eisenhower-modal {
        .modal-content {
            padding: 0;
        }
    }

    // Custom scrollbar for light theme
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 20px;
    }

    // Custom scrollbar for dark theme
    .theme-dark & {
        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.2);
        }
    }

    @keyframes slideIn {
        from {
            transform: translateY(20px);
            opacity: 0;
        }

        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    // Modal Header - macOS style
    .modal-header {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        border-bottom: 1px solid $separator-color;
        background-color: rgba($window-background, 0.8);

        // Dark theme
        .theme-dark & {
            border-bottom: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        .traffic-lights {
            display: flex;
            gap: 8px;
            margin-right: 16px;

            .traffic-light {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: none;
                cursor: pointer;
                @include transition(all);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                &.red {
                    background: $system-red;
                }

                &.yellow {
                    background: $system-orange;
                }

                &.green {
                    background: $system-green;
                }

                &:hover {
                    transform: scale(1.15);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
                }

                &:active {
                    transform: scale(0.95);
                }

                &:disabled {
                    opacity: 0.4;
                    cursor: default;
                }
            }
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            flex: 1;
            text-align: center;
            margin: 0;
            letter-spacing: -0.2px;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        .view-controls {
            display: flex;
            gap: 8px;
            margin-left: 16px;

            .view-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                border-radius: 6px;
                border: 1px solid transparent;
                background: transparent;
                cursor: pointer;
                @include transition(all);

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }

                &:hover {
                    // Light theme
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                        color: color.adjust($system-blue, $lightness: 10%);
                    }
                }

                &.active {
                    // Light theme
                    background-color: rgba($system-blue, 0.15);
                    color: $system-blue;
                    border-color: rgba($system-blue, 0.3);

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-blue, 0.25);
                        color: color.adjust($system-blue, $lightness: 10%);
                        border-color: rgba($system-blue, 0.4);
                    }
                }
            }
        }
    }

    // Modal Content - macOS style
    .modal-content {
        padding: 24px;
        @include ios-autohide-scrollbar;

        // Loading container
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;

            .spinner {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                border: 3px solid transparent;
                border-top-color: $system-blue;
                animation: spin 1s linear infinite;
                margin-bottom: 16px;

                // Dark theme
                .theme-dark & {
                    border-top-color: color.adjust($system-blue, $lightness: 10%);
                }
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            p {
                font-size: 1rem;

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }
        }

        // Error container
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;

            svg {
                width: 48px;
                height: 48px;
                margin-bottom: 16px;

                // Light theme
                color: $system-red;

                // Dark theme
                .theme-dark & {
                    color: color.adjust($system-red, $lightness: 10%);
                }
            }

            .error-message {
                font-size: 1rem;
                text-align: center;

                // Light theme
                color: $system-red;

                // Dark theme
                .theme-dark & {
                    color: color.adjust($system-red, $lightness: 10%);
                }
            }
        }

        // Matrix container
        .matrix-container {
            position: relative;
            padding: 24px;

            // Matrix axes
            .matrix-axes {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                pointer-events: none;
                z-index: 1;

                .y-axis {
                    position: absolute;
                    left: 12px;
                    top: 0;
                    bottom: 0;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    .axis-label {
                        font-size: 0.8rem;
                        font-weight: 600;
                        padding: 8px;

                        // Light theme
                        color: $tertiary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-tertiary-text;
                        }

                        &.top {
                            margin-top: 40px;
                        }

                        &.bottom {
                            margin-bottom: 40px;
                        }
                    }
                }

                .x-axis {
                    position: absolute;
                    top: 12px;
                    left: 0;
                    right: 0;
                    display: flex;
                    justify-content: space-between;

                    .axis-label {
                        font-size: 0.8rem;
                        font-weight: 600;
                        padding: 8px;

                        // Light theme
                        color: $tertiary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-tertiary-text;
                        }

                        &.left {
                            margin-left: 40px;
                        }

                        &.right {
                            margin-right: 40px;
                        }
                    }
                }
            }

            .matrix-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
                gap: 20px;
                position: relative;
                z-index: 2;

                .matrix-quadrant {
                    padding: 20px;
                    border-radius: 12px;
                    min-height: 200px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                    display: flex;
                    flex-direction: column;
                    transition: all 0.2s ease;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

                        // Dark theme
                        .theme-dark & {
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                        }
                    }

                    // Dark theme
                    .theme-dark & {
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    }

                    &.do {
                        // Light theme
                        background: rgba($system-red, 0.1);
                        border: 1px solid rgba($system-red, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-red, 0.15);
                            border: 1px solid rgba($system-red, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-red;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-red, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-red, 0.2);
                            color: $system-red;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-red, 0.3);
                                color: color.adjust($system-red, $lightness: 10%);
                            }
                        }
                    }

                    &.schedule {
                        // Light theme
                        background: rgba($system-green, 0.1);
                        border: 1px solid rgba($system-green, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-green, 0.15);
                            border: 1px solid rgba($system-green, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-green;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-green, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-green, 0.2);
                            color: $system-green;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-green, 0.3);
                                color: color.adjust($system-green, $lightness: 10%);
                            }
                        }
                    }

                    &.delegate {
                        // Light theme
                        background: rgba($system-orange, 0.1);
                        border: 1px solid rgba($system-orange, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-orange, 0.15);
                            border: 1px solid rgba($system-orange, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-orange;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-orange, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-orange, 0.2);
                            color: $system-orange;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-orange, 0.3);
                                color: color.adjust($system-orange, $lightness: 10%);
                            }
                        }
                    }

                    &.delete {
                        // Light theme
                        background: rgba($system-gray, 0.1);
                        border: 1px solid rgba($system-gray, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-gray, 0.15);
                            border: 1px solid rgba($system-gray, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-gray;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-gray, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-gray, 0.2);
                            color: $system-gray;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-gray, 0.3);
                                color: color.adjust($system-gray, $lightness: 10%);
                            }
                        }
                    }

                    .quadrant-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 12px;
                        padding-bottom: 8px;
                        border-bottom: 1px solid $separator-color;

                        // Dark theme
                        .theme-dark & {
                            border-bottom: 1px solid $dark-separator-color;
                        }

                        h3 {
                            font-size: 1.1rem;
                            font-weight: 600;
                            margin: 0;

                            // Light theme
                            color: $primary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-primary-text;
                            }
                        }

                        .task-count {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            min-width: 28px;
                            height: 28px;
                            border-radius: 14px;
                            font-size: 0.9rem;
                            font-weight: 600;
                            padding: 0 8px;
                        }
                    }

                    &.schedule-title {
                        color: $system-green;

                        // Dark theme
                        .theme-dark & {
                            color: color.adjust($system-green, $lightness: 10%);
                        }
                    }

                    &.delegate-title {
                        color: $system-orange;

                        // Dark theme
                        .theme-dark & {
                            color: color.adjust($system-orange, $lightness: 10%);
                        }
                    }

                    &.delete-title {
                        color: $system-gray;

                        // Dark theme
                        .theme-dark & {
                            color: color.adjust($system-gray, $lightness: 10%);
                        }
                    }
                }

                .quadrant-description {
                    font-size: 0.9rem;
                    margin: 0 0 16px;
                    font-style: italic;
                    line-height: 1.4;

                    // Light theme
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }

                .task-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    overflow-y: auto;
                    flex: 1;
                    @include ios-autohide-scrollbar;

                    .task-item {
                        display: flex;
                        flex-direction: column;
                        font-size: 0.95rem;
                        margin-bottom: 10px;
                        padding: 10px;
                        border-radius: 8px;
                        transition: all 0.2s ease;

                        // Light theme
                        background-color: rgba($window-background, 0.7);
                        border: 1px solid $border-color;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($dark-window-background, 0.7);
                            border: 1px solid $dark-border-color;
                        }

                        &:hover {
                            transform: translateY(-2px);

                            // Light theme
                            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

                            // Dark theme
                            .theme-dark & {
                                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                            }
                        }

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .task-title {
                            font-weight: 500;
                            margin-bottom: 4px;

                            // Light theme
                            color: $primary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-primary-text;
                            }
                        }

                        .due-date {
                            display: block;
                            font-size: 0.8rem;

                            // Light theme
                            color: $tertiary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-tertiary-text;
                            }
                        }
                    }

                    .empty-task-list {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 80px;
                        font-style: italic;
                        font-size: 0.9rem;
                        border-radius: 8px;

                        // Light theme
                        background-color: rgba($window-background, 0.7);
                        color: $tertiary-text;
                        border: 1px dashed $border-color;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($dark-window-background, 0.7);
                            color: $dark-tertiary-text;
                            border: 1px dashed $dark-border-color;
                        }
                    }
                }
            }
        }
    }

    // Modal Footer - macOS style
    .modal-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-top: 1px solid $separator-color;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        background-color: rgba($window-background, 0.8);

        // Dark theme
        .theme-dark & {
            border-top: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        &::before {
            content: '';
            flex: 1;
        }

        .close-button {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            @include transition(all);
            margin-left: auto;

            // Light theme
            background-color: $system-blue;
            color: white;
            border: none;
            box-shadow: 0 1px 3px rgba($system-blue, 0.3);

            // Dark theme
            .theme-dark & {
                background-color: $system-blue;
                color: white;
                box-shadow: 0 1px 3px rgba($system-blue, 0.5);
            }

            &:hover {
                // Light theme
                background-color: darken($system-blue, 5%);
                transform: translateY(-1px);
                box-shadow: 0 2px 5px rgba($system-blue, 0.4);

                // Dark theme
                .theme-dark & {
                    background-color: darken($system-blue, 5%);
                    box-shadow: 0 2px 5px rgba($system-blue, 0.6);
                }
            }

            &:active {
                // Light theme
                background-color: darken($system-blue, 10%);
                transform: translateY(0);
                box-shadow: 0 1px 2px rgba($system-blue, 0.3);

                // Dark theme
                .theme-dark & {
                    background-color: darken($system-blue, 10%);
                    box-shadow: 0 1px 2px rgba($system-blue, 0.5);
                }
            }

            &:focus {
                outline: 2px solid $color-primary;
                outline-offset: 2px;
            }
        }
    }
}