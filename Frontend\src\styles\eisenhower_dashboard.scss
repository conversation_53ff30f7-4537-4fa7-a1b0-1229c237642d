@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// <PERSON> Card - Minimalistic Design
.eisenhower-card {
    width: 100%;
    height: 220px;
    padding: 1rem;
    cursor: pointer;
    @include transition(all);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    // Light theme
    background: $window-background;
    border: 1px solid rgba($border-color, 0.3);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    // Dark theme
    &.theme-dark {
        background: $dark-window-background;
        border: 1px solid rgba($dark-border-color, 0.3);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    }

    // Widget Header
    .widget-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;

        .widget-title {
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0;
            color: $secondary-text;
            letter-spacing: -0.1px;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .total-count {
            font-size: 1.25rem;
            font-weight: 700;
            color: $primary-text;
            background: rgba($system-blue, 0.08);
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            min-width: 2rem;
            text-align: center;

            .theme-dark & {
                color: $dark-primary-text;
                background: rgba($system-blue, 0.15);
            }
        }
    }

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

        &.theme-dark {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .quadrant {
            transform: scale(1.02);
        }
    }

    &:focus {
        outline: 2px solid $system-blue;
        outline-offset: 2px;
    }

    // Matrix Grid
    .matrix-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 0.75rem;
        flex: 1;
        height: 140px;

        .quadrant {
            border-radius: 10px;
            @include transition(all);
            position: relative;
            overflow: hidden;
            border: 1px solid transparent;

            .quadrant-content {
                padding: 0.75rem;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                position: relative;
                z-index: 2;

                .quadrant-info {
                    display: flex;
                    flex-direction: column;
                    gap: 0.125rem;

                    .quadrant-label {
                        font-size: 0.8rem;
                        font-weight: 600;
                        line-height: 1;
                    }

                    .quadrant-desc {
                        font-size: 0.65rem;
                        font-weight: 500;
                        opacity: 0.8;
                        line-height: 1;
                    }
                }

                .task-count {
                    font-size: 1.5rem;
                    font-weight: 700;
                    line-height: 1;
                    align-self: flex-end;
                }
            }

            // Attractive color scheme
            &.do-quadrant {
                background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
                border-color: rgba(#FF6B6B, 0.3);
                color: white;

                .theme-dark & {
                    background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
                }
            }

            &.schedule-quadrant {
                background: linear-gradient(135deg, #4ECDC4 0%, #26A69A 100%);
                border-color: rgba(#4ECDC4, 0.3);
                color: white;

                .theme-dark & {
                    background: linear-gradient(135deg, #319795 0%, #2C7A7B 100%);
                }
            }

            &.delegate-quadrant {
                background: linear-gradient(135deg, #FFD93D 0%, #FFC107 100%);
                border-color: rgba(#FFD93D, 0.3);
                color: #2D3748;

                .theme-dark & {
                    background: linear-gradient(135deg, #D69E2E 0%, #B7791F 100%);
                    color: white;
                }
            }

            &.delete-quadrant {
                background: linear-gradient(135deg, #A0AEC0 0%, #718096 100%);
                border-color: rgba(#A0AEC0, 0.3);
                color: white;

                .theme-dark & {
                    background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
                }
            }

            // Subtle overlay for depth
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
                z-index: 1;
                border-radius: 10px;
            }
        }
    }

    // Responsive Design
    @media (max-width: 768px) {
        height: 200px;
        padding: 0.75rem;

        .widget-header {
            margin-bottom: 0.75rem;

            .widget-title {
                font-size: 0.85rem;
            }

            .total-count {
                font-size: 1.1rem;
                padding: 0.2rem 0.4rem;
            }
        }

        .matrix-grid {
            gap: 0.5rem;
            height: 120px;

            .quadrant {
                border-radius: 8px;

                .quadrant-content {
                    padding: 0.5rem;

                    .quadrant-info {
                        .quadrant-label {
                            font-size: 0.75rem;
                        }

                        .quadrant-desc {
                            font-size: 0.6rem;
                        }
                    }

                    .task-count {
                        font-size: 1.25rem;
                    }
                }
            }
        }
    }
}

// Modal Overlay - macOS style
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    /* Increased z-index to ensure it's above everything */
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: rgba(0, 0, 0, 0.5);
    /* Increased opacity for better visibility */

    // Dark theme
    .theme-dark & {
        background-color: rgba(0, 0, 0, 0.7);
        /* Increased opacity for better visibility */
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }
}

// Modal Window - macOS style
.modal-window {
    width: 700px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    border-radius: 16px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    z-index: 999999;
    position: absolute;

    // Light theme
    background-color: $window-background;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid $border-color;

    // Dark theme
    .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        border: 1px solid $dark-border-color;
    }

    // Fullscreen mode
    &.fullscreen {
        width: 95vw;
        max-width: 95vw;
        height: 95vh;
        max-height: 95vh;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

        // Dark theme
        .theme-dark & {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            height: calc(95vh - 120px); // Adjust for header and footer
        }
    }

    // Eisenhower specific modal styling
    &.eisenhower-modal {
        .modal-content {
            padding: 0;
        }
    }

    // Custom scrollbar for light theme
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 20px;
    }

    // Custom scrollbar for dark theme
    .theme-dark & {
        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.2);
        }
    }

    @keyframes slideIn {
        from {
            transform: translateY(20px);
            opacity: 0;
        }

        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    // Modal Header - macOS style
    .modal-header {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        border-bottom: 1px solid $separator-color;
        background-color: rgba($window-background, 0.8);

        // Dark theme
        .theme-dark & {
            border-bottom: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        .traffic-lights {
            display: flex;
            gap: 8px;
            margin-right: 16px;

            .traffic-light {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: none;
                cursor: pointer;
                @include transition(all);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                &.red {
                    background: $system-red;
                }

                &.yellow {
                    background: $system-orange;
                }

                &.green {
                    background: $system-green;
                }

                &:hover {
                    transform: scale(1.15);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
                }

                &:active {
                    transform: scale(0.95);
                }

                &:disabled {
                    opacity: 0.4;
                    cursor: default;
                }
            }
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            flex: 1;
            text-align: center;
            margin: 0;
            letter-spacing: -0.2px;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        .view-controls {
            display: flex;
            gap: 8px;
            margin-left: 16px;

            .view-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                border-radius: 6px;
                border: 1px solid transparent;
                background: transparent;
                cursor: pointer;
                @include transition(all);

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }

                &:hover {
                    // Light theme
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                        color: color.adjust($system-blue, $lightness: 10%);
                    }
                }

                &.active {
                    // Light theme
                    background-color: rgba($system-blue, 0.15);
                    color: $system-blue;
                    border-color: rgba($system-blue, 0.3);

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-blue, 0.25);
                        color: color.adjust($system-blue, $lightness: 10%);
                        border-color: rgba($system-blue, 0.4);
                    }
                }
            }
        }
    }

    // Modal Content - macOS style
    .modal-content {
        padding: 24px;
        @include ios-autohide-scrollbar;

        // Loading container
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;

            .spinner {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                border: 3px solid transparent;
                border-top-color: $system-blue;
                animation: spin 1s linear infinite;
                margin-bottom: 16px;

                // Dark theme
                .theme-dark & {
                    border-top-color: color.adjust($system-blue, $lightness: 10%);
                }
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            p {
                font-size: 1rem;

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }
        }

        // Error container
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;

            svg {
                width: 48px;
                height: 48px;
                margin-bottom: 16px;

                // Light theme
                color: $system-red;

                // Dark theme
                .theme-dark & {
                    color: color.adjust($system-red, $lightness: 10%);
                }
            }

            .error-message {
                font-size: 1rem;
                text-align: center;

                // Light theme
                color: $system-red;

                // Dark theme
                .theme-dark & {
                    color: color.adjust($system-red, $lightness: 10%);
                }
            }
        }

        // Matrix container
        .matrix-container {
            position: relative;
            padding: 24px;

            // Matrix axes
            .matrix-axes {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                pointer-events: none;
                z-index: 1;

                .y-axis {
                    position: absolute;
                    left: 12px;
                    top: 0;
                    bottom: 0;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    .axis-label {
                        font-size: 0.8rem;
                        font-weight: 600;
                        padding: 8px;

                        // Light theme
                        color: $tertiary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-tertiary-text;
                        }

                        &.top {
                            margin-top: 40px;
                        }

                        &.bottom {
                            margin-bottom: 40px;
                        }
                    }
                }

                .x-axis {
                    position: absolute;
                    top: 12px;
                    left: 0;
                    right: 0;
                    display: flex;
                    justify-content: space-between;

                    .axis-label {
                        font-size: 0.8rem;
                        font-weight: 600;
                        padding: 8px;

                        // Light theme
                        color: $tertiary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-tertiary-text;
                        }

                        &.left {
                            margin-left: 40px;
                        }

                        &.right {
                            margin-right: 40px;
                        }
                    }
                }
            }

            .matrix-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
                gap: 20px;
                position: relative;
                z-index: 2;

                .matrix-quadrant {
                    padding: 20px;
                    border-radius: 12px;
                    min-height: 200px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                    display: flex;
                    flex-direction: column;
                    transition: all 0.2s ease;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

                        // Dark theme
                        .theme-dark & {
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                        }
                    }

                    // Dark theme
                    .theme-dark & {
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    }

                    &.do {
                        // Light theme
                        background: rgba($system-red, 0.1);
                        border: 1px solid rgba($system-red, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-red, 0.15);
                            border: 1px solid rgba($system-red, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-red;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-red, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-red, 0.2);
                            color: $system-red;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-red, 0.3);
                                color: color.adjust($system-red, $lightness: 10%);
                            }
                        }
                    }

                    &.schedule {
                        // Light theme
                        background: rgba($system-green, 0.1);
                        border: 1px solid rgba($system-green, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-green, 0.15);
                            border: 1px solid rgba($system-green, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-green;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-green, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-green, 0.2);
                            color: $system-green;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-green, 0.3);
                                color: color.adjust($system-green, $lightness: 10%);
                            }
                        }
                    }

                    &.delegate {
                        // Light theme
                        background: rgba($system-orange, 0.1);
                        border: 1px solid rgba($system-orange, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-orange, 0.15);
                            border: 1px solid rgba($system-orange, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-orange;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-orange, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-orange, 0.2);
                            color: $system-orange;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-orange, 0.3);
                                color: color.adjust($system-orange, $lightness: 10%);
                            }
                        }
                    }

                    &.delete {
                        // Light theme
                        background: rgba($system-gray, 0.1);
                        border: 1px solid rgba($system-gray, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background: rgba($system-gray, 0.15);
                            border: 1px solid rgba($system-gray, 0.4);
                        }

                        .quadrant-header h3 {
                            color: $system-gray;

                            // Dark theme
                            .theme-dark & {
                                color: color.adjust($system-gray, $lightness: 10%);
                            }
                        }

                        .task-count {
                            background-color: rgba($system-gray, 0.2);
                            color: $system-gray;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-gray, 0.3);
                                color: color.adjust($system-gray, $lightness: 10%);
                            }
                        }
                    }

                    .quadrant-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 12px;
                        padding-bottom: 8px;
                        border-bottom: 1px solid $separator-color;

                        // Dark theme
                        .theme-dark & {
                            border-bottom: 1px solid $dark-separator-color;
                        }

                        h3 {
                            font-size: 1.1rem;
                            font-weight: 600;
                            margin: 0;

                            // Light theme
                            color: $primary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-primary-text;
                            }
                        }

                        .task-count {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            min-width: 28px;
                            height: 28px;
                            border-radius: 14px;
                            font-size: 0.9rem;
                            font-weight: 600;
                            padding: 0 8px;
                        }
                    }

                    &.schedule-title {
                        color: $system-green;

                        // Dark theme
                        .theme-dark & {
                            color: color.adjust($system-green, $lightness: 10%);
                        }
                    }

                    &.delegate-title {
                        color: $system-orange;

                        // Dark theme
                        .theme-dark & {
                            color: color.adjust($system-orange, $lightness: 10%);
                        }
                    }

                    &.delete-title {
                        color: $system-gray;

                        // Dark theme
                        .theme-dark & {
                            color: color.adjust($system-gray, $lightness: 10%);
                        }
                    }
                }

                .quadrant-description {
                    font-size: 0.9rem;
                    margin: 0 0 16px;
                    font-style: italic;
                    line-height: 1.4;

                    // Light theme
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }

                .task-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                    overflow-y: auto;
                    flex: 1;
                    @include ios-autohide-scrollbar;

                    .task-item {
                        display: flex;
                        flex-direction: column;
                        font-size: 0.95rem;
                        margin-bottom: 10px;
                        padding: 10px;
                        border-radius: 8px;
                        transition: all 0.2s ease;

                        // Light theme
                        background-color: rgba($window-background, 0.7);
                        border: 1px solid $border-color;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($dark-window-background, 0.7);
                            border: 1px solid $dark-border-color;
                        }

                        &:hover {
                            transform: translateY(-2px);

                            // Light theme
                            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

                            // Dark theme
                            .theme-dark & {
                                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                            }
                        }

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .task-title {
                            font-weight: 500;
                            margin-bottom: 4px;

                            // Light theme
                            color: $primary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-primary-text;
                            }
                        }

                        .due-date {
                            display: block;
                            font-size: 0.8rem;

                            // Light theme
                            color: $tertiary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-tertiary-text;
                            }
                        }
                    }

                    .empty-task-list {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 80px;
                        font-style: italic;
                        font-size: 0.9rem;
                        border-radius: 8px;

                        // Light theme
                        background-color: rgba($window-background, 0.7);
                        color: $tertiary-text;
                        border: 1px dashed $border-color;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($dark-window-background, 0.7);
                            color: $dark-tertiary-text;
                            border: 1px dashed $dark-border-color;
                        }
                    }
                }
            }
        }
    }

    // Modal Footer - macOS style
    .modal-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-top: 1px solid $separator-color;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        background-color: rgba($window-background, 0.8);

        // Dark theme
        .theme-dark & {
            border-top: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        &::before {
            content: '';
            flex: 1;
        }

        .close-button {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            @include transition(all);
            margin-left: auto;

            // Light theme
            background-color: $system-blue;
            color: white;
            border: none;
            box-shadow: 0 1px 3px rgba($system-blue, 0.3);

            // Dark theme
            .theme-dark & {
                background-color: $system-blue;
                color: white;
                box-shadow: 0 1px 3px rgba($system-blue, 0.5);
            }

            &:hover {
                // Light theme
                background-color: darken($system-blue, 5%);
                transform: translateY(-1px);
                box-shadow: 0 2px 5px rgba($system-blue, 0.4);

                // Dark theme
                .theme-dark & {
                    background-color: darken($system-blue, 5%);
                    box-shadow: 0 2px 5px rgba($system-blue, 0.6);
                }
            }

            &:active {
                // Light theme
                background-color: darken($system-blue, 10%);
                transform: translateY(0);
                box-shadow: 0 1px 2px rgba($system-blue, 0.3);

                // Dark theme
                .theme-dark & {
                    background-color: darken($system-blue, 10%);
                    box-shadow: 0 1px 2px rgba($system-blue, 0.5);
                }
            }

            &:focus {
                outline: 2px solid $color-primary;
                outline-offset: 2px;
            }
        }
    }
}