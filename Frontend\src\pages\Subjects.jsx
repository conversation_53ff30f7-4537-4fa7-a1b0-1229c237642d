import { useState, useEffect, useRef } from 'react';
import AddSubject from '../components/AddSubject.jsx';
import '../styles/Subject.scss';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import { tools } from '../utils/toolsData.jsx';

// Add Tool Modal Component
const AddToolModal = ({ onClose, onAddTool, chapterId }) => {
    const [availableTools, setAvailableTools] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const isDarkMode = document.body.classList.contains('theme-dark');

    useEffect(() => {
        const fetchTools = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('token');
                if (!token) {
                    setError('Please log in to view tools');
                    setLoading(false);
                    return;
                }

                const response = await fetch('http://localhost:5001/api/study-tools', {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch tools: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.success) {
                    // Map server tools to client tools with images
                    const toolsWithImages = data.tools.map(serverTool => {
                        const matchingTool = tools.find(t => t.name === serverTool.name);
                        return {
                            ...serverTool,
                            image: matchingTool ? matchingTool.image : null
                        };
                    });
                    setAvailableTools(toolsWithImages);
                } else {
                    setError(data.message || 'Failed to fetch tools');
                }
            } catch (err) {
                console.error('Error fetching available tools:', err);
                setError('Failed to fetch tools. Please try again.');
            } finally {
                setLoading(false);
            }
        };

        fetchTools();
    }, []);

    const handleToolSelect = (tool) => {
        onAddTool(tool);
    };

    return (
        <div className="tool-popup-overlay">
            <div className="tool-popup-content">
                <div className="tool-popup-header">
                    <h3>Add Study Tool</h3>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>
                <div className="tool-popup-body">
                    {loading ? (
                        <div className="tools-loading">
                            <div className="loading-spinner"></div>
                            <p>Loading available tools...</p>
                        </div>
                    ) : error ? (
                        <div className="tools-error">{error}</div>
                    ) : (
                        <div className="available-tools-grid">
                            {availableTools.map(tool => (
                                <div
                                    key={tool.id}
                                    className="available-tool-card"
                                    onClick={() => handleToolSelect(tool)}
                                >
                                    {tool.image ? (
                                        <img src={tool.image} alt={tool.name} className="tool-image" />
                                    ) : (
                                        <div className="tool-icon-placeholder">🔧</div>
                                    )}
                                    <h4>{tool.name}</h4>
                                    <p>{tool.description}</p>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// Import FlashCards component
import FlashCards from '../components/tools/list/FlashCards';

// Tool Popup Component
const ToolPopup = ({ tool, onClose, chapterId, subjectId: propSubjectId, chapterTitle }) => {
    const isDarkMode = document.body.classList.contains('theme-dark');
    const [toolComponent, setToolComponent] = useState(null);
    const [subjectId, setSubjectId] = useState(propSubjectId || null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [chapterData, setChapterData] = useState(null);
    const [toolContent, setToolContent] = useState(null);

    // Get the chapter data including subject ID if not provided
    useEffect(() => {
        const fetchChapterData = async () => {
            try {
                setLoading(true);
                setError(null);

                // If we already have the subject ID and chapter title from props, we can skip this fetch
                if (propSubjectId && chapterTitle) {
                    setSubjectId(propSubjectId);
                    setChapterData({
                        id: chapterId,
                        subject_id: propSubjectId,
                        title: chapterTitle
                    });
                    return;
                }

                const token = localStorage.getItem('token');
                if (!token) {
                    setError('Please log in to view chapter data');
                    setLoading(false);
                    return;
                }

                console.log(`Fetching chapter data for chapter ID: ${chapterId}`);
                // Log token information (first 10 chars only for security)
                const tokenPreview = token.substring(0, 10) + '...';
                console.log(`Using token: ${tokenPreview}`);

                const response = await fetch(`http://localhost:5001/api/chapters/${chapterId}`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });

                // Handle specific HTTP status codes
                if (response.status === 403) {
                    console.error(`Permission denied for chapter ID: ${chapterId}`);

                    // Try to get more detailed error information
                    try {
                        const errorData = await response.json();
                        console.error('Error details:', errorData);
                        throw new Error(errorData.message || errorData.error || 'You do not have permission to access this chapter. This may happen if the chapter belongs to another user or if your session has expired.');
                    } catch (jsonError) {
                        // If we can't parse the JSON, use the default error message
                        throw new Error('You do not have permission to access this chapter. This may happen if the chapter belongs to another user or if your session has expired.');
                    }
                } else if (response.status === 404) {
                    throw new Error('Chapter not found. It may have been deleted.');
                } else if (response.status === 401) {
                    // Handle expired or invalid token
                    console.error('Authentication failed with 401 status');
                    localStorage.removeItem('token'); // Clear the invalid token
                    throw new Error('Your session has expired. Please log in again.');
                } else if (!response.ok) {
                    throw new Error(`Failed to fetch chapter data: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Chapter data response:', data);

                if (data.success && data.chapter) {
                    setChapterData(data.chapter);
                    setSubjectId(data.chapter.subject_id);

                    // Log the tools that came with the chapter data
                    if (data.chapter.tools && data.chapter.tools.length > 0) {
                        console.log(`Received ${data.chapter.tools.length} tools for chapter ID: ${chapterId}`);
                    } else {
                        console.log(`No tools found for chapter ID: ${chapterId}`);
                    }
                } else {
                    setError(data.message || 'Failed to fetch chapter data');
                }
            } catch (err) {
                console.error('Error fetching chapter details:', err);
                setError(err.message || 'Failed to fetch chapter data. Please try again.');
            } finally {
                setLoading(false);
            }
        };

        if (chapterId) {
            fetchChapterData();
        }
    }, [chapterId, propSubjectId, chapterTitle]);

    // Fetch tool content
    useEffect(() => {
        const fetchToolContent = async () => {
            if (loading || error || !subjectId || !chapterId || !tool || !tool.tool_id) return;

            try {
                console.log(`Fetching content for tool ID: ${tool.tool_id}, chapter ID: ${chapterId}, subject ID: ${subjectId}`);

                const token = localStorage.getItem('token');
                if (!token) {
                    setError('Please log in to view tool content');
                    return;
                }

                // For now, we'll focus on flashcards, but this can be expanded for other tool types
                if (tool.name === 'Flash Cards') {
                    // We don't need to fetch content separately as the FlashCards component will do that
                    setToolContent(null);
                } else {
                    // For other tools, we might need to fetch their content
                    // This can be expanded later for other tool types
                    console.log(`No specific content fetch needed for ${tool.name}`);
                }
            } catch (err) {
                console.error(`Error fetching content for tool ${tool.name}:`, err);
                setError(`Failed to load content for ${tool.name}: ${err.message}`);
            }
        };

        fetchToolContent();
    }, [tool, chapterId, subjectId, loading, error]);

    // Render the appropriate tool component
    useEffect(() => {
        if (loading || error || !subjectId) return;

        try {
            console.log(`Rendering tool component for ${tool.name} (ID: ${tool.tool_id})`);
            console.log('- chapterId:', chapterId);
            console.log('- subjectId:', subjectId);
            console.log('- chapterTitle:', chapterData?.title);

            // Special handling for Flash Cards tool
            // Check for various flashcard naming conventions
            if (tool.name && typeof tool.name === 'string' &&
                (tool.name === 'Flash Cards' ||
                    tool.name === 'Flashcards' ||
                    tool.name.toLowerCase().includes('flash') ||
                    tool.name.toLowerCase().includes('card'))) {

                console.log('Rendering FlashCards component');
                console.log('- chapterId:', chapterId);
                console.log('- subjectId:', subjectId);
                console.log('- chapterTitle:', chapterData?.title);
                console.log('- toolId:', tool.tool_id);

                try {
                    setToolComponent(
                        <FlashCards
                            key={`flashcards-${chapterId}-${tool.tool_id}`}
                            chapterId={parseInt(chapterId, 10)}
                            subjectId={parseInt(subjectId, 10)}
                            chapterTitle={chapterData?.title || ''}
                            isDarkMode={isDarkMode}
                            settings={tool.settings || {}}
                            toolId={tool.tool_id}
                        />
                    );
                    return;
                } catch (flashcardError) {
                    console.error('Error creating FlashCards component:', flashcardError);
                    setError(`Error loading FlashCards component: ${flashcardError.message}. Please try again.`);
                    return;
                }
            }

            // Validate tool data
            if (!tool) {
                console.error('Tool data is undefined or null');
                throw new Error('Tool data is missing. Please try again or contact support.');
            }

            console.log('Attempting to match tool:', tool);

            // Find the matching tool from the tools array to get its component
            let matchingTool = null;

            // First, try to match by name (case-insensitive) if both names exist
            if (tool.name && typeof tool.name === 'string') {
                const toolNameLower = tool.name.toLowerCase();
                matchingTool = tools.find(t =>
                    t.name && typeof t.name === 'string' && t.name.toLowerCase() === toolNameLower
                );

                console.log(`Tried matching by name "${tool.name}": ${matchingTool ? 'Found match' : 'No match'}`);
            } else {
                console.log('Tool name is missing or not a string:', tool.name);
            }

            // If no match by name, try to match by ID
            if (!matchingTool && tool.tool_id) {
                // Try direct ID match
                matchingTool = tools.find(t => t.id === tool.tool_id);
                console.log(`Tried matching by direct ID "${tool.tool_id}": ${matchingTool ? 'Found match' : 'No match'}`);

                // Try matching numeric part of ID (e.g., "T13" matches "13")
                if (!matchingTool) {
                    matchingTool = tools.find(t => {
                        if (!t.id) return false;
                        const numericId = t.id.replace(/\D/g, ''); // Remove non-digits
                        const match = numericId === tool.tool_id.toString();
                        if (match) console.log(`Found match by numeric ID: ${t.id} matches ${tool.tool_id}`);
                        return match;
                    });
                }
            } else if (!tool.tool_id) {
                console.log('Tool ID is missing:', tool.tool_id);
            }

            // Special case for Flash Cards/Flashcards naming inconsistency
            if (!matchingTool && tool.name && typeof tool.name === 'string' &&
                (tool.name === 'Flash Cards' || tool.name === 'Flashcards' ||
                    tool.name.toLowerCase().includes('flash') || tool.name.toLowerCase().includes('card'))) {
                matchingTool = tools.find(t =>
                    t.name && (
                        t.name === 'Flash Cards' ||
                        t.name === 'Flashcards' ||
                        (typeof t.name === 'string' && (
                            t.name.toLowerCase().includes('flash') ||
                            t.name.toLowerCase().includes('card')
                        ))
                    )
                );
                console.log(`Tried matching as flashcard tool: ${matchingTool ? 'Found match' : 'No match'}`);
            }

            // Last resort: just pick the first tool that has a component
            if (!matchingTool) {
                console.log('No match found by name or ID, trying to find any tool with a component');
                matchingTool = tools.find(t => t.component);
                if (matchingTool) {
                    console.log(`Using fallback tool: ${matchingTool.name}`);
                }
            }

            console.log('Tool from API:', tool);
            console.log('Matching tool from toolsData:', matchingTool);

            if (matchingTool && matchingTool.component) {
                try {
                    // Create an instance of the tool component
                    const Component = matchingTool.component;

                    // Render the tool component
                    setToolComponent(
                        <Component
                            key={`${tool.id || tool.tool_id}-component`}
                            tool={matchingTool}
                            chapterId={parseInt(chapterId, 10)}
                            subjectId={parseInt(subjectId, 10)}
                            isDarkMode={isDarkMode}
                            settings={tool.settings || {}}
                            toolData={tool} // Pass the original tool data from API
                            toolContent={toolContent} // Pass any fetched content
                            onClose={onClose} // Pass the onClose handler
                        />
                    );
                } catch (componentError) {
                    console.error('Error creating component instance:', componentError);
                    setError(`Error loading tool component: ${componentError.message}. Please try again or contact support.`);
                }
            } else {
                console.error(`Tool component not found for ${tool.name || 'Unknown Tool'} (ID: ${tool.tool_id || tool.id || 'Unknown ID'})`);

                // Provide a more detailed error message
                let errorMessage = 'Tool component not found.';

                if (tool.name) {
                    errorMessage += ` The tool "${tool.name}" `;
                } else {
                    errorMessage += ' This tool ';
                }

                errorMessage += 'could not be loaded. This may be because:';
                errorMessage += '\n1. The tool is not yet implemented';
                errorMessage += '\n2. There is a mismatch between the tool name in the database and the frontend';
                errorMessage += '\n3. The tool requires additional configuration';

                errorMessage += '\n\nPlease try again or contact support.';

                setError(errorMessage);
            }
        } catch (err) {
            console.error('Error setting up tool component:', err);
            setError(`Failed to set up tool component: ${err.message}. Please try again.`);
        }
    }, [tool, chapterId, subjectId, chapterData, loading, error, isDarkMode, toolContent]);

    return (
        <div className="tool-popup-overlay">
            <div className="tool-popup-content">
                <div className="tool-popup-header">
                    <h3>{tool.name}</h3>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>
                <div className="tool-popup-body">
                    {loading ? (
                        <div className="tools-loading">
                            <div className="loading-spinner"></div>
                            <p>Loading tool content...</p>
                        </div>
                    ) : error ? (
                        <div className="tool-error">
                            <div className="tool-icon">⚠️</div>
                            <h4>Error Loading Tool</h4>
                            <p>{error.split('\n').map((line, i) => (
                                <React.Fragment key={i}>
                                    {line}
                                    <br />
                                </React.Fragment>
                            ))}</p>

                            {error.includes('session has expired') && (
                                <div className="error-help">
                                    <button
                                        className="login-button"
                                        onClick={() => {
                                            localStorage.removeItem('token');
                                            window.location.href = '/login';
                                        }}
                                    >
                                        Log In Again
                                    </button>
                                </div>
                            )}

                            <div className="tool-debug-info">
                                <p>Debug Information:</p>
                                <ul>
                                    <li>Tool ID: {tool?.tool_id || 'Unknown'}</li>
                                    <li>Tool Name: {tool?.name || 'Unknown'}</li>
                                    <li>Chapter ID: {chapterId || 'Unknown'}</li>
                                    <li>Subject ID: {subjectId || 'Unknown'}</li>
                                </ul>
                                <button
                                    className="retry-button"
                                    onClick={() => window.location.reload()}
                                >
                                    Refresh Page
                                </button>
                            </div>
                        </div>
                    ) : toolComponent ? (
                        toolComponent
                    ) : (
                        <div className="tool-placeholder">
                            <div className="tool-icon">{getToolIcon(tool.name)}</div>
                            <h4>Tool Content for Chapter {chapterId}</h4>
                            <p>This is where the content for {tool.name} would be displayed.</p>
                            <p className="tool-note">This tool may not be fully implemented yet or may require additional configuration.</p>
                            <div className="tool-debug-info">
                                <p>Debug Information:</p>
                                <ul>
                                    <li>Tool ID: {tool?.tool_id || tool?.id || 'Unknown'}</li>
                                    <li>Tool Name: {tool?.name || 'Unknown'}</li>
                                    <li>Chapter ID: {chapterId || 'Unknown'}</li>
                                    <li>Subject ID: {subjectId || 'Unknown'}</li>
                                    <li>Chapter Title: {chapterData?.title || 'Unknown'}</li>
                                </ul>
                                <button
                                    className="retry-button"
                                    onClick={() => window.location.reload()}
                                >
                                    Refresh Page
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// Study Tool Card Component
const StudyToolCard = ({ name, icon, onClick, description, toolId }) => {
    // Determine if dark mode is active
    const isDarkMode = document.body.classList.contains('theme-dark');

    return (
        <div className="study-tool-card" onClick={onClick} title={description || name}>
            <div className="tool-content">
                <span className="tool-icon">{icon}</span>
                <p>{name}</p>
                {description && <small className="tool-description">{description}</small>}
                {toolId && <small className="tool-id">ID: {toolId}</small>}
            </div>
        </div>
    );
};

// Add Tool Card Component
const AddToolCard = ({ onClick }) => {
    return (
        <div className="study-tool-card add-tool-card" onClick={onClick}>
            <div className="tool-content">
                <span className="tool-icon"><AddIcon /></span>
                <p>Add Tool</p>
            </div>
        </div>
    );
};

// Helper function to get appropriate icon for each tool
const getToolIcon = (toolName) => {
    if (!toolName) return '🔧';

    const name = toolName.toLowerCase();

    if (name.includes('flash') || name.includes('card')) return '🎴';
    if (name.includes('pomodoro') || name.includes('timer')) return '⏱️';
    if (name.includes('eisenhower')) return '🧠';
    if (name.includes('kanban')) return '📋';
    if (name.includes('cornell') || name.includes('notes')) return '📝';
    if (name.includes('doodle') || name.includes('draw')) return '🎨';
    if (name.includes('mindmap')) return '🌐';
    if (name.includes('retrospective')) return '🔄';
    if (name.includes('mastery')) return '🏆';
    if (name.includes('quiz')) return '❓';
    if (name.includes('calendar')) return '📅';
    if (name.includes('podcast')) return '🎧';

    return '🔧'; // Default icon
};

// Subject Section Component
const SubjectSection = ({ subject, chapters, onDeleteSubject }) => {
    const [selectedChapter, setSelectedChapter] = useState(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [chapterTools, setChapterTools] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedTool, setSelectedTool] = useState(null);
    const [showAddToolModal, setShowAddToolModal] = useState(false);
    const [addingTool, setAddingTool] = useState(false);
    const [addToolError, setAddToolError] = useState(null);

    const handleChapterClick = async (chapterId) => {
        setSelectedChapter(chapterId);
        await fetchChapterTools(chapterId);
    };

    const fetchChapterTools = async (chapterId) => {
        try {
            setLoading(true);
            setError(null);
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Please log in to view tools');
                setLoading(false);
                return;
            }

            console.log(`Fetching tools for chapter ID: ${chapterId}, subject ID: ${subject.id}`);

            // Log token information (first 10 chars only for security)
            const tokenPreview = token.substring(0, 10) + '...';
            console.log(`Using token: ${tokenPreview}`);

            const response = await fetch(`http://localhost:5001/api/chapters/${chapterId}/tools`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            // Handle specific HTTP status codes
            if (response.status === 403) {
                console.error(`Permission denied for chapter tools, chapter ID: ${chapterId}`);
                throw new Error('You do not have permission to access tools for this chapter. This may happen if the chapter belongs to another user or if your session has expired.');
            } else if (response.status === 404) {
                throw new Error('Chapter not found. It may have been deleted.');
            } else if (response.status === 401) {
                // Handle expired or invalid token
                localStorage.removeItem('token'); // Clear the invalid token
                throw new Error('Your session has expired. Please log in again.');
            } else if (!response.ok) {
                throw new Error(`Failed to fetch tools: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Chapter tools response:', data);

            if (data.success) {
                const tools = data.tools || [];
                console.log(`Received ${tools.length} tools for chapter ID: ${chapterId}`);

                // Log each tool for debugging
                tools.forEach((tool, index) => {
                    console.log(`Tool ${index + 1}:`, {
                        id: tool.id,
                        tool_id: tool.tool_id,
                        name: tool.name,
                        description: tool.description
                    });
                });

                setChapterTools(tools);
            } else {
                setError(data.message || 'Failed to fetch tools');
            }
        } catch (err) {
            console.error('Error fetching chapter tools:', err);
            setError(err.message || 'Failed to fetch tools. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleToolClick = (tool) => {
        console.log(`Tool clicked: ${tool.name} (ID: ${tool.tool_id})`);
        console.log('Tool details:', tool);
        console.log('Current chapter ID:', selectedChapter);
        console.log('Current subject ID:', subject.id);

        // Get the chapter title
        const chapterTitle = chapters.find(chapter => chapter.id === selectedChapter)?.title || '';
        console.log('Chapter title:', chapterTitle);

        setSelectedTool(tool);
    };

    const handleAddToolClick = () => {
        setShowAddToolModal(true);
    };

    const closeAddToolModal = () => {
        setShowAddToolModal(false);
    };

    const handleAddTool = async (tool) => {
        try {
            setAddingTool(true);
            setAddToolError(null);

            const token = localStorage.getItem('token');
            if (!token) {
                setAddToolError('Please log in to add tools');
                setAddingTool(false);
                return;
            }

            const response = await fetch(`http://localhost:5001/api/chapters/${selectedChapter}/tools`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    tool_id: tool.id,
                    settings: {}
                }),
            });

            // Handle specific HTTP status codes
            if (response.status === 403) {
                console.error(`Permission denied when adding tool to chapter ID: ${selectedChapter}`);
                throw new Error('You do not have permission to add tools to this chapter. This may happen if the chapter belongs to another user or if your session has expired.');
            } else if (response.status === 404) {
                throw new Error('Chapter not found. It may have been deleted.');
            } else if (response.status === 401) {
                // Handle expired or invalid token
                localStorage.removeItem('token'); // Clear the invalid token
                throw new Error('Your session has expired. Please log in again.');
            } else if (!response.ok) {
                throw new Error(`Failed to add tool: ${response.statusText}`);
            }

            const data = await response.json();
            if (data.success) {
                // Refresh the tools list
                await fetchChapterTools(selectedChapter);
                closeAddToolModal();
            } else {
                setAddToolError(data.message || 'Failed to add tool');
            }
        } catch (err) {
            console.error('Error adding tool:', err);
            setAddToolError(err.message || 'Failed to add tool. Please try again.');
        } finally {
            setAddingTool(false);
        }
    };

    const closeToolPopup = () => {
        setSelectedTool(null);
    };

    const handleDeleteClick = () => {
        setShowDeleteConfirm(true);
    };

    const confirmDelete = () => {
        onDeleteSubject(subject.id);
        setShowDeleteConfirm(false);
    };

    const cancelDelete = () => {
        setShowDeleteConfirm(false);
    };

    return (
        <div className="subject-section">
            <div className="subject-header">
                <div className="subject-title-actions">
                    <h2>{subject.name}</h2>
                    <button
                        className="delete-subject-btn"
                        onClick={handleDeleteClick}
                        title="Delete subject"
                        aria-label="Delete subject"
                    >
                        <DeleteIcon fontSize="small" />
                    </button>
                </div>
                <div className="chapter-tabs">
                    {chapters.length > 0 ? (
                        chapters.map((chapter) => (
                            <button
                                key={chapter.id}
                                className={`chapter-tab ${selectedChapter === chapter.id ? 'active' : ''}`}
                                onClick={() => handleChapterClick(chapter.id)}
                            >
                                {chapter.chapter_number || 'Chapter'}
                            </button>
                        ))
                    ) : (
                        <p className="no-chapters">No chapters available for this subject.</p>
                    )}
                </div>
            </div>
            {selectedChapter && (
                <>
                    <div className="selected-chapter-title">
                        <h3>
                            {chapters.find((chapter) => chapter.id === selectedChapter)?.title || 'Chapter Title'}
                        </h3>
                    </div>
                    <div className="study-tools-grid">
                        {loading ? (
                            <div className="tools-loading">
                                <div className="loading-spinner"></div>
                                <p>Loading tools...</p>
                            </div>
                        ) : error ? (
                            <p className="tools-error">{error}</p>
                        ) : chapterTools.length > 0 ? (
                            <>
                                {chapterTools.map((tool) => (
                                    <StudyToolCard
                                        key={tool.id}
                                        name={tool.name}
                                        icon={getToolIcon(tool.name)}
                                        //description={tool.description}
                                        //toolId={tool.tool_id}
                                        onClick={() => handleToolClick(tool)}
                                    />
                                ))}
                                <AddToolCard onClick={handleAddToolClick} />
                            </>
                        ) : (
                            <>
                                <p className="no-tools-message">No study tools available for this chapter.</p>
                                <AddToolCard onClick={handleAddToolClick} />
                            </>
                        )}
                    </div>
                </>
            )}

            {selectedTool && (
                <ToolPopup
                    tool={selectedTool}
                    onClose={closeToolPopup}
                    chapterId={selectedChapter}
                    subjectId={subject.id}
                    chapterTitle={chapters.find(chapter => chapter.id === selectedChapter)?.title || ''}
                />
            )}

            {showAddToolModal && (
                <AddToolModal
                    onClose={closeAddToolModal}
                    onAddTool={handleAddTool}
                    chapterId={selectedChapter}
                />
            )}

            {addingTool && (
                <div className="loading-overlay">
                    <div className="loading-spinner"></div>
                    <p>Adding tool...</p>
                </div>
            )}

            {addToolError && (
                <div className="error-notification">
                    <div className="error-content">
                        <p>{addToolError}</p>
                        {addToolError.includes('session has expired') && (
                            <button
                                className="login-button"
                                onClick={() => window.location.href = '/login'}
                            >
                                Log In Again
                            </button>
                        )}
                    </div>
                    <button className="close-error" onClick={() => setAddToolError(null)}>×</button>
                </div>
            )}

            {showDeleteConfirm && (
                <div className="delete-confirm-modal">
                    <div className="delete-confirm-content">
                        <div className="delete-icon-container">
                            <DeleteIcon className="delete-icon" style={{ fontSize: 40, color: 'var(--system-red)' }} />
                        </div>
                        <h3>Are you sure you want to delete this subject?</h3>
                        <p>The subject "<strong>{subject.name}</strong>" will be permanently deleted.</p>
                        <p className="warning">This action cannot be undone and will delete all associated chapters and files.</p>
                        <div className="delete-confirm-actions">
                            <button className="cancel-btn" onClick={cancelDelete}>Cancel</button>
                            <button className="delete-btn" onClick={confirmDelete}>Delete</button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

// Main Subjects Component
const Subjects = () => {
    const [subjects, setSubjects] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [deleteStatus, setDeleteStatus] = useState({ loading: false, error: null, success: false });
    const navRef = useRef(null);
    const [isDarkMode, setIsDarkMode] = useState(false);
    const [tokenRefreshNeeded, setTokenRefreshNeeded] = useState(false);

    // Check for dark mode on component mount and when theme changes
    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    // Fetch subjects from the database
    const fetchSubjects = async () => {
        try {
            setLoading(true);
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Please log in to view subjects');
                setLoading(false);
                return;
            }
            const response = await fetch('http://localhost:5001/api/subjects', {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    setError('Authentication failed. Please log in again.');
                    localStorage.removeItem('token');
                } else if (response.headers.get('content-type')?.includes('text/html')) {
                    setError('Unexpected server response. Please try again.');
                } else {
                    setError(`Failed to fetch subjects: ${response.statusText}`);
                }
                setLoading(false);
                return;
            }
            const data = await response.json();
            if (data.success) {
                // Fetch chapters directly from the subject endpoint
                const subjectsWithChapters = await Promise.all(
                    data.subjects.map(async (subject) => {
                        console.log(`Fetching chapters for subject ${subject.id} (${subject.name})`);

                        try {
                            // Use the dedicated endpoint for fetching chapters by subject ID
                            const chaptersResponse = await fetch(`http://localhost:5001/api/subjects/${subject.id}/chapters`, {
                                headers: {
                                    Authorization: `Bearer ${token}`,
                                },
                            });

                            if (!chaptersResponse.ok) {
                                console.error(`Failed to fetch chapters for subject ${subject.id}: ${chaptersResponse.statusText}`);
                                console.error(`Status code: ${chaptersResponse.status}`);

                                // Try to get more detailed error information
                                try {
                                    const errorData = await chaptersResponse.json();
                                    console.error('Error details:', errorData);
                                } catch (jsonError) {
                                    console.error('Could not parse error response as JSON');
                                }

                                return { ...subject, chapters: [] };
                            }

                            const chaptersData = await chaptersResponse.json();
                            console.log(`Received chapters data for subject ${subject.id}:`, chaptersData);

                            if (chaptersData.success) {
                                // Map the chapters to the expected format
                                const formattedChapters = chaptersData.chapters.map(chapter => ({
                                    id: chapter.id,
                                    title: chapter.title,
                                    chapter_number: chapter.chapter_number,
                                    displayTitle: chapter.chapter_number ? `Chapter ${chapter.chapter_number}: ${chapter.title}` : chapter.title,
                                    objectives: chapter.objectives || [],
                                    // We don't have tools information here, so we'll fetch that separately when needed
                                }));

                                console.log(`Formatted ${formattedChapters.length} chapters for subject ${subject.id}`);
                                return {
                                    ...subject,
                                    chapters: formattedChapters,
                                };
                            } else {
                                console.error(`Failed to fetch chapters for subject ${subject.id}: ${chaptersData.message}`);
                                return { ...subject, chapters: [] };
                            }
                        } catch (error) {
                            console.error(`Error fetching chapters for subject ${subject.id}:`, error);
                            return { ...subject, chapters: [] };
                        }
                    })
                );
                setSubjects(subjectsWithChapters);
            } else {
                setError(data.message);
            }
        } catch (err) {
            setError('Failed to fetch subjects');
            console.error('Error fetching subjects:', err);
        } finally {
            setLoading(false);
        }
    };

    // Initial fetch
    useEffect(() => {
        fetchSubjects();
    }, []);

    // Handle subject deletion
    const handleDeleteSubject = async (subjectId) => {
        try {
            setDeleteStatus({ loading: true, error: null, success: false });
            const token = localStorage.getItem('token');
            if (!token) {
                setDeleteStatus({ loading: false, error: 'Please log in to delete subjects', success: false });
                return;
            }

            const response = await fetch(`http://localhost:5001/api/subjects/${subjectId}`, {
                method: 'DELETE',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to delete subject');
            }

            const data = await response.json();
            if (data.success) {
                setDeleteStatus({ loading: false, error: null, success: true });
                // Remove the deleted subject from state
                setSubjects(subjects.filter(subject => subject.id !== subjectId));
            } else {
                throw new Error(data.message || 'Failed to delete subject');
            }
        } catch (err) {
            console.error('Error deleting subject:', err);
            setDeleteStatus({ loading: false, error: err.message, success: false });
        }
    };

    // Scroll navigation left or right
    const scrollNav = (direction) => {
        if (navRef.current) {
            const scrollAmount = direction === 'left' ? -200 : 200;
            navRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
        }
    };

    // Handle token refresh or logout
    const handleAuthIssue = () => {
        // Clear the token and redirect to login
        localStorage.removeItem('token');
        window.location.href = '/login';
    };

    if (loading) {
        return (
            <div className={`study-page ${isDarkMode ? 'theme-dark' : ''}`}>
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Loading subjects...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`study-page ${isDarkMode ? 'theme-dark' : ''}`}>
                <div className="error-container">
                    <div className="error-icon">⚠️</div>
                    <p className="error-message">{error}</p>
                    <div className="error-actions">
                        <button
                            className="retry-button"
                            onClick={() => window.location.reload()}
                        >
                            Retry
                        </button>
                        {error.includes('permission') || error.includes('session') || error.includes('token') ? (
                            <button
                                className="login-button"
                                onClick={handleAuthIssue}
                            >
                                Log In Again
                            </button>
                        ) : null}
                    </div>
                </div>
            </div>
        );
    }

    // Status notification component
    const StatusNotification = ({ message, type, onClose }) => (
        <div className={`status-notification ${type}`}>
            <p>{message}</p>
            <button onClick={onClose} aria-label="Close notification">
                <CloseIcon fontSize="small" />
            </button>
        </div>
    );

    return (
        <div className={`study-page ${isDarkMode ? 'theme-dark' : ''}`}>
            {deleteStatus.success && (
                <StatusNotification
                    message="Subject deleted successfully"
                    type="success"
                    onClose={() => setDeleteStatus({ ...deleteStatus, success: false })}
                />
            )}

            {deleteStatus.error && (
                <StatusNotification
                    message={deleteStatus.error}
                    type="error"
                    onClose={() => setDeleteStatus({ ...deleteStatus, error: null })}
                />
            )}

            <AddSubject onSubjectAdded={fetchSubjects} />
            <nav className="subject-nav" ref={navRef}>
                <div className="left-button" onClick={() => scrollNav('left')}>
                    <PlayArrowIcon />
                </div>
                <div className="middle-content">
                    {subjects.length > 0 ? (
                        subjects.map((subject) => (
                            <button key={subject.id} className="subject-btn">
                                {subject.name}
                            </button>
                        ))
                    ) : (
                        <p className="no-subjects">No subjects available. Add a subject to get started!</p>
                    )}
                </div>
                <div className="right-button" onClick={() => scrollNav('right')}>
                    <PlayArrowIcon />
                </div>
            </nav>
            <div className="subjects-container">
                {deleteStatus.loading && (
                    <div className="delete-loading-overlay">
                        <div className="loading-spinner"></div>
                        <p>Deleting subject...</p>
                    </div>
                )}

                {subjects.length > 0 ? (
                    subjects.map((subject) => (
                        <SubjectSection
                            key={subject.id}
                            subject={subject}
                            chapters={subject.chapters}
                            onDeleteSubject={handleDeleteSubject}
                        />
                    ))
                ) : (
                    <div className="empty-state">
                        <div className="empty-icon">📚</div>
                        <p className="no-subjects">No subjects available.</p>
                        <p className="empty-hint">Click the "Add Subject" button to create your first subject.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Subjects;