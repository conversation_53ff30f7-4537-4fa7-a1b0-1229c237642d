// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;
$system-gold: #fbb034;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

.eisenhower-matrix {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border: 0.0625rem solid rgba(255, 255, 255, 0.3);
  border-radius: 1.25rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.0625rem;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
  }

  &.theme-dark {
    background: linear-gradient(135deg, rgba(28, 28, 30, 0.95) 0%, rgba(44, 44, 46, 0.95) 100%);
    border: 0.0625rem solid rgba(255, 255, 255, 0.1);
    color: $dark-primary-text;

    &::before {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    }
  }

  &.is-dragging {
    cursor: grabbing !important;

    .task-item {
      cursor: grabbing !important;
    }
  }

  .error-message {
    background: rgba($system-red, 0.1);
    border: 0.0625rem solid rgba($system-red, 0.3);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: $system-red;
    font-size: 0.875rem;

    .close-error {
      background: none;
      border: none;
      color: $system-red;
      font-size: 1.25rem;
      cursor: pointer;
      padding: 0;
      width: 1.5rem;
      height: 1.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba($system-red, 0.1);
      }
    }
  }

  // Fixed Header
  .matrix-header-fixed {
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
    backdrop-filter: blur(40px) saturate(180%);
    border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.3);
    border-radius: 1.25rem 1.25rem 0 0;
    flex-wrap: wrap;
    gap: 1.5rem;

    .theme-dark & {
      background: linear-gradient(135deg, rgba(28, 28, 30, 0.98) 0%, rgba(44, 44, 46, 0.98) 100%);
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .header-left {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      h2 {
        margin: 0;
        font-size: 2.25rem;
        font-weight: 700;
        background: linear-gradient(135deg, $primary-text 0%, rgba($primary-text, 0.8) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -0.02em;
        line-height: 1.2;

        .theme-dark & {
          background: linear-gradient(135deg, $dark-primary-text 0%, rgba($dark-primary-text, 0.9) 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .subject-name {
        font-size: 0.9375rem;
        color: $secondary-text;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        background: rgba($system-blue, 0.1);
        border: 0.0625rem solid rgba($system-blue, 0.2);
        border-radius: 1.5rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &::before {
          content: '📚';
          font-size: 0.875rem;
        }

        .theme-dark & {
          color: $dark-secondary-text;
          background: rgba($system-blue, 0.15);
          border-color: rgba($system-blue, 0.3);
        }
      }
    }

    .header-controls {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
      background: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(20px) saturate(180%);
      border: 0.0625rem solid rgba(255, 255, 255, 0.3);
      border-radius: 1rem;
      padding: 0.75rem;

      .theme-dark & {
        background: rgba(44, 44, 46, 0.6);
        border-color: rgba(255, 255, 255, 0.1);
      }

      // Close Button
      .close-btn {
        background: linear-gradient(135deg, $system-red 0%, lighten($system-red, 5%) 100%);
        color: white;
        border: none;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        font-size: 1.25rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        margin-left: 0.5rem;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, lighten($system-red, 5%) 0%, lighten($system-red, 10%) 100%);
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgba($system-red, 0.3);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: scale(0.95);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }

      .search-box {
        position: relative;
        min-width: 14rem;

        .search-input {
          width: 100%;
          padding: 0.625rem 1rem 0.625rem 2.5rem;
          border: 0.0625rem solid rgba($border-color, 0.3);
          border-radius: 0.75rem;
          font-size: 0.875rem;
          background: rgba(255, 255, 255, 0.8);
          color: $primary-text;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
          font-weight: 500;

          &:focus {
            outline: none;
            border-color: $system-blue;
          }

          &::placeholder {
            color: $placeholder-text;
            font-weight: 400;
          }

          .theme-dark & {
            background: rgba(44, 44, 46, 0.8);
            border-color: rgba($dark-border-color, 0.5);
            color: $dark-primary-text;

            &::placeholder {
              color: $dark-placeholder-text;
            }

            &:focus {
              border-color: $system-blue;
            }
          }
        }

        .search-icon {
          position: absolute;
          left: 0.875rem;
          top: 50%;
          transform: translateY(-50%);
          opacity: 0.7;
          font-size: 1rem;
          transition: all 0.3s ease;
        }

        &:focus-within .search-icon {
          opacity: 1;
          color: $system-blue;
        }
      }

      .filter-select,
      .quadrant-select,
      .priority-select {
        padding: 0.625rem 1rem;
        border: 0.0625rem solid rgba($border-color, 0.3);
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.8);
        color: $primary-text;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
        min-width: 8rem;

        &:focus {
          outline: none;
          border-color: $system-blue;
        }

        &:hover {
          border-color: rgba($system-blue, 0.4);
        }

        .theme-dark & {
          background: rgba(44, 44, 46, 0.8);
          border-color: rgba($dark-border-color, 0.5);
          color: $dark-primary-text;

          &:focus {
            border-color: $system-blue;
          }
        }
      }

      .add-task-btn {
        background: linear-gradient(135deg, $system-blue 0%, lighten($system-blue, 5%) 100%);
        color: white;
        border: none;
        padding: 0.625rem 1.25rem;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, lighten($system-blue, 5%) 0%, lighten($system-blue, 10%) 100%);
        }

        &:active {
          transform: translateY(-0.0625rem);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }

  // Scrollable Content Container
  .matrix-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    position: relative;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba($system-blue, 0.3);
      border-radius: 4px;
      transition: background 0.3s ease;

      &:hover {
        background: rgba($system-blue, 0.5);
      }
    }

    .theme-dark & {
      &::-webkit-scrollbar-thumb {
        background: rgba($system-blue, 0.4);

        &:hover {
          background: rgba($system-blue, 0.6);
        }
      }
    }

    // Firefox scrollbar
    scrollbar-width: thin;
    scrollbar-color: rgba($system-blue, 0.3) transparent;

    .theme-dark & {
      scrollbar-color: rgba($system-blue, 0.4) transparent;
    }
  }

  .add-task-form {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px) saturate(180%);
    border: 0.0625rem solid rgba(255, 255, 255, 0.4);
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.1875rem;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
      z-index: 1;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at top left, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      pointer-events: none;
      z-index: 1;
    }

    .theme-dark & {
      background: linear-gradient(135deg, rgba(44, 44, 46, 0.95) 0%, rgba(58, 58, 60, 0.95) 100%);
      border-color: rgba(255, 255, 255, 0.15);

      &::before {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      }
    }

    .form-row {
      display: flex;
      gap: 0.75rem;
      margin-bottom: 1rem;
      flex-wrap: wrap;

      .task-input {
        flex: 2;
        min-width: 12rem;
        padding: 0.5rem 0.75rem;
        border: 0.0625rem solid $border-color;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        background: $window-background;
        color: $primary-text;

        &:focus {
          outline: none;
          border-color: $system-blue;
        }

        &::placeholder {
          color: $placeholder-text;
        }

        .theme-dark & {
          background: $dark-window-background;
          border-color: $dark-border-color;
          color: $dark-primary-text;

          &::placeholder {
            color: $dark-placeholder-text;
          }

          &:focus {
            border-color: $system-blue;
          }
        }
      }

      .date-input {
        flex: 1;
        min-width: 8rem;
        padding: 0.5rem 0.75rem;
        border: 0.0625rem solid $border-color;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        background: $window-background;
        color: $primary-text;

        &:focus {
          outline: none;
          border-color: $system-blue;
        }

        .theme-dark & {
          background: $dark-window-background;
          border-color: $dark-border-color;
          color: $dark-primary-text;

          &:focus {
            border-color: $system-blue;
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;

      button {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &.add-btn,
        &.save-btn {
          background: $system-green;
          color: white;

          &:hover:not(:disabled) {
            background: darken($system-green, 10%);
          }
        }

        &.cancel-btn {
          background: $system-light-gray;
          color: $primary-text;

          &:hover {
            background: darken($system-light-gray, 10%);
          }

          .theme-dark & {
            background: $dark-border-color;
            color: $dark-primary-text;

            &:hover {
              background: lighten($dark-border-color, 10%);
            }
          }
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  .matrix-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 1.5rem;
    min-height: 36rem;
    position: relative;

    .quadrant {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
      backdrop-filter: blur(20px) saturate(180%);
      border: 0.0625rem solid rgba(255, 255, 255, 0.4);
      border-radius: 1.25rem;
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 0.1875rem;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        z-index: 1;
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at top left, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
        z-index: 1;
      }

      &.drag-over {
        border-style: dashed;
        border-width: 0.125rem;
        background: linear-gradient(135deg, rgba($system-blue, 0.1) 0%, rgba($system-blue, 0.05) 100%);
        transform: scale(1.02);
      }

      .theme-dark & {
        background: linear-gradient(135deg, rgba(44, 44, 46, 0.9) 0%, rgba(58, 58, 60, 0.9) 100%);
        border-color: rgba(255, 255, 255, 0.15);

        &::before {
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }

        &.drag-over {
          background: linear-gradient(135deg, rgba($system-blue, 0.15) 0%, rgba($system-blue, 0.1) 100%);
        }
      }

      .quadrant-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 2;

        &::after {
          content: '';
          position: absolute;
          bottom: -0.0625rem;
          left: 0;
          right: 0;
          height: 0.0625rem;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        }

        h3 {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 700;
          letter-spacing: -0.01em;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          position: relative;

          &::before {
            content: '';
            width: 0.375rem;
            height: 0.375rem;
            border-radius: 50%;
            background: currentColor;
            opacity: 0.8;
          }
        }

        .task-count {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
          backdrop-filter: blur(10px);
          border: 0.0625rem solid rgba(255, 255, 255, 0.4);
          padding: 0.25rem 0.625rem;
          border-radius: 1rem;
          font-size: 0.75rem;
          font-weight: 700;
          min-width: 1.5rem;
          text-align: center;
          color: inherit;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          .theme-dark & {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
            border-color: rgba(255, 255, 255, 0.2);
          }
        }

        .theme-dark & {
          border-bottom-color: rgba(255, 255, 255, 0.1);

          &::after {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          }
        }
      }

      .tasks-container {
        flex: 1;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba($system-gray, 0.3) transparent;

        &::-webkit-scrollbar {
          width: 0.25rem;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba($system-gray, 0.3);
          border-radius: 0.125rem;

          &:hover {
            background: rgba($system-gray, 0.5);
          }
        }

        .empty-state {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 8rem;
          color: $tertiary-text;
          font-size: 0.875rem;
          text-align: center;
          border: 0.125rem dashed $border-color;
          border-radius: 0.5rem;
          margin: 0.5rem 0;

          .theme-dark & {
            color: $dark-tertiary-text;
            border-color: $dark-border-color;
          }
        }
      }
    }
  }

  .task-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    backdrop-filter: blur(15px) saturate(180%);
    border: 0.0625rem solid rgba(255, 255, 255, 0.3);
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s ease;
    }

    &.completed {
      opacity: 0.7;
      background: linear-gradient(135deg, rgba(142, 142, 147, 0.1) 0%, rgba(142, 142, 147, 0.05) 100%);

      .task-text {
        text-decoration: line-through;
        color: $tertiary-text;
      }

      &::after {
        content: '✓';
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        width: 1.5rem;
        height: 1.5rem;
        background: linear-gradient(135deg, $system-green 0%, lighten($system-green, 10%) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: white;
        font-weight: 700;
      }
    }

    .theme-dark & {
      background: linear-gradient(135deg, rgba(44, 44, 46, 0.95) 0%, rgba(58, 58, 60, 0.85) 100%);
      border-color: rgba(255, 255, 255, 0.15);

      &.completed {
        background: linear-gradient(135deg, rgba(142, 142, 147, 0.15) 0%, rgba(142, 142, 147, 0.1) 100%);

        .task-text {
          color: $dark-tertiary-text;
        }
      }
    }

    .task-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;
      min-width: 0;
      position: relative;
      z-index: 2;

      .task-checkbox {
        width: 1.25rem;
        height: 1.25rem;
        cursor: pointer;
        accent-color: $system-blue;
        border-radius: 0.25rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .task-text {
        flex: 1;
        font-size: 0.9375rem;
        font-weight: 500;
        color: $primary-text;
        word-wrap: break-word;
        line-height: 1.4;
        letter-spacing: -0.01em;

        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .due-date {
        font-size: 0.8125rem;
        font-weight: 600;
        color: $secondary-text;
        white-space: nowrap;
        background: linear-gradient(135deg, rgba($system-orange, 0.15) 0%, rgba($system-orange, 0.1) 100%);
        border: 0.0625rem solid rgba($system-orange, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 0.5rem;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        .theme-dark & {
          color: $dark-secondary-text;
          background: linear-gradient(135deg, rgba($system-orange, 0.2) 0%, rgba($system-orange, 0.15) 100%);
          border-color: rgba($system-orange, 0.3);
        }
      }

      .priority-dot {
        width: 0.75rem;
        height: 0.75rem;
        border-radius: 50%;
        flex-shrink: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .task-actions {
      display: flex;
      gap: 0.375rem;
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateX(0.5rem);

      button {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        backdrop-filter: blur(10px);
        border: 0.0625rem solid rgba(255, 255, 255, 0.3);
        cursor: pointer;
        font-size: 0.875rem;
        padding: 0.375rem;
        border-radius: 0.5rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;

        &.edit-btn {
          &:hover {
            background: linear-gradient(135deg, rgba($system-blue, 0.2) 0%, rgba($system-blue, 0.1) 100%);
            border-color: rgba($system-blue, 0.3);
            color: $system-blue;
          }
        }

        &.delete-btn {
          &:hover {
            background: linear-gradient(135deg, rgba($system-red, 0.2) 0%, rgba($system-red, 0.1) 100%);
            border-color: rgba($system-red, 0.3);
            color: $system-red;
          }
        }

        .theme-dark & {
          background: linear-gradient(135deg, rgba(44, 44, 46, 0.9) 0%, rgba(58, 58, 60, 0.7) 100%);
          border-color: rgba(255, 255, 255, 0.15);
          color: $dark-primary-text;
        }
      }
    }

    &:hover .task-actions {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .drag-overlay {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    backdrop-filter: blur(15px) saturate(180%);
    border: 0.0625rem solid rgba($system-blue, 0.5);
    border-radius: 1rem;
    padding: 1rem;
    cursor: grabbing;

    .theme-dark & {
      background: linear-gradient(135deg, rgba(44, 44, 46, 0.95) 0%, rgba(58, 58, 60, 0.85) 100%);
      border-color: rgba($system-blue, 0.7);
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.75rem;
    z-index: 10;

    .theme-dark & {
      background: rgba(0, 0, 0, 0.8);
    }

    .loading-spinner {
      width: 2rem;
      height: 2rem;
      border: 0.1875rem solid rgba($system-blue, 0.3);
      border-top: 0.1875rem solid $system-blue;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  @media (max-width: 768px) {
    .eisenhower-matrix {
      padding: 1rem;

      .matrix-header-fixed {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;

        .header-controls {
          justify-content: stretch;

          .search-box {
            min-width: auto;
            flex: 1;
          }
        }
      }

      .matrix-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 0.75rem;
        min-height: auto;
      }

      .add-task-form .form-row {
        flex-direction: column;

        .task-input,
        .date-input {
          min-width: auto;
        }
      }

      .task-item {
        .task-actions {
          opacity: 1;
        }
      }
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes checkboxPulse {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.2);
    }

    100% {
      transform: scale(1);
    }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(1rem);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromLeft {
    0% {
      opacity: 0;
      transform: translateX(-2rem);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    0% {
      opacity: 0;
      transform: translateX(2rem);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .eisenhower-matrix {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);

    .matrix-header {
      animation: slideInFromLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .header-controls {
      animation: slideInFromRight 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
    }

    .add-task-form {
      animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
    }

    .quadrant {
      animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);

      &:nth-child(1) {
        animation-delay: 0.1s;
      }

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.3s;
      }

      &:nth-child(4) {
        animation-delay: 0.4s;
      }
    }

    .task-item {
      animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}