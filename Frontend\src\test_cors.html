<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #007aff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>CORS Test for Blueprint AI Backend</h1>
    
    <div>
        <h2>Test Endpoints</h2>
        <button onclick="testEndpoint('/api/validate_study_plan')">Test Validate Study Plan</button>
        <button onclick="testEndpoint('/api/create_study_plan')">Test Create Study Plan</button>
        <button onclick="testEndpoint('/api/process_document_content')">Test Process Document</button>
    </div>
    
    <div>
        <h2>Results</h2>
        <pre id="results"></pre>
    </div>
    
    <script>
        const API_URL = 'http://127.0.0.1:5000';
        const resultsElement = document.getElementById('results');
        
        async function testEndpoint(endpoint) {
            resultsElement.innerHTML = `Testing ${endpoint}...\n`;
            
            try {
                // First test OPTIONS request
                resultsElement.innerHTML += `Sending OPTIONS request to ${endpoint}...\n`;
                
                const optionsResponse = await fetch(`${API_URL}${endpoint}`, {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token'
                    }
                });
                
                const optionsHeaders = {};
                for (const [key, value] of optionsResponse.headers.entries()) {
                    optionsHeaders[key] = value;
                }
                
                resultsElement.innerHTML += `OPTIONS response status: ${optionsResponse.status}\n`;
                resultsElement.innerHTML += `OPTIONS response headers: ${JSON.stringify(optionsHeaders, null, 2)}\n\n`;
                
                if (optionsResponse.status === 200) {
                    resultsElement.innerHTML += `<span class="success">✓ OPTIONS request successful</span>\n\n`;
                } else {
                    resultsElement.innerHTML += `<span class="error">✗ OPTIONS request failed</span>\n\n`;
                }
                
                // Now test POST request with minimal data
                resultsElement.innerHTML += `Sending POST request to ${endpoint}...\n`;
                
                const postResponse = await fetch(`${API_URL}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token'
                    },
                    body: JSON.stringify({
                        subject_id: 1,
                        name: 'Test Study Plan',
                        start_date: '2023-01-01',
                        end_date: '2023-02-01',
                        daily_study_time_minutes: 60
                    })
                });
                
                const postHeaders = {};
                for (const [key, value] of postResponse.headers.entries()) {
                    postHeaders[key] = value;
                }
                
                resultsElement.innerHTML += `POST response status: ${postResponse.status}\n`;
                resultsElement.innerHTML += `POST response headers: ${JSON.stringify(postHeaders, null, 2)}\n\n`;
                
                if (postResponse.status !== 0) {
                    resultsElement.innerHTML += `<span class="success">✓ POST request received a response (status ${postResponse.status})</span>\n\n`;
                } else {
                    resultsElement.innerHTML += `<span class="error">✗ POST request failed with CORS error</span>\n\n`;
                }
                
                // Overall result
                if (optionsResponse.status === 200 && postResponse.status !== 0) {
                    resultsElement.innerHTML += `<span class="success">✓ CORS is properly configured for ${endpoint}</span>\n`;
                } else {
                    resultsElement.innerHTML += `<span class="error">✗ CORS is not properly configured for ${endpoint}</span>\n`;
                }
                
            } catch (error) {
                resultsElement.innerHTML += `<span class="error">Error: ${error.message}</span>\n`;
            }
        }
    </script>
</body>
</html>
