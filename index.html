<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Discover The Blueprint Book and Blueprint AI to learn smarter. Buy the book on Amazon KDP and try our AI-powered learning tool.">
  <meta name="keywords" content="learn smarter, study tips, AI learning tool, Blueprint Book, Blueprint AI">
  <meta name="author" content="Your Name">
  <title>The Blueprint: Learn Smarter with Book & AI</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body class="font-['Inter'] bg-gray-50">
  <!-- Navigation -->
  <nav class="bg-white shadow-md sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <a href="#" class="text-2xl font-bold text-blue-600">The Blueprint</a>
        </div>
        <div class="hidden sm:flex sm:items-center sm:space-x-8">
          <a href="#home" class="text-gray-700 hover:text-blue-600">Home</a>
          <a href="#book" class="text-gray-700 hover:text-blue-600">The Book</a>
          <a href="#ai" class="text-gray-700 hover:text-blue-600">The AI</a>
          <a href="#about" class="text-gray-700 hover:text-blue-600">About</a>
          <a href="#blog" class="text-gray-700 hover:text-blue-600">Blog</a>
          <a href="#contact" class="text-gray-700 hover:text-blue-600">Contact</a>
        </div>
        <div class="flex items-center sm:hidden">
          <button id="menu-toggle" class="text-gray-700 hover:text-blue-600 focus:outline-none">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden sm:hidden bg-white shadow-md">
      <div class="px-2 pt-2 pb-3 space-y-1">
        <a href="#home" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Home</a>
        <a href="#book" class="block px-3 py-2 text-gray-700 hover:text-blue-600">The Book</a>
        <a href="#ai" class="block px-3 py-2 text-gray-700 hover:text-blue-600">The AI</a>
        <a href="#about" class="block px-3 py-2 text-gray-700 hover:text-blue-600">About</a>
        <a href="#blog" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Blog</a>
        <a href="#contact" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Contact</a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="bg-gradient-to-r from-blue-600 to-blue-400 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h1 class="text-4xl sm:text-5xl font-bold mb-6">Learn Smarter with The Blueprint</h1>
      <p class="text-lg sm:text-xl mb-8">Master your studies with our bestselling book and AI-powered learning assistant.</p>
      <div class="flex justify-center space-x-4">
        <a href="https://www.amazon.com/your-book-link" target="_blank" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100">Buy The Book</a>
        <a href="#ai" class="bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-800">Try Blueprint AI</a>
      </div>
    </div>
  </section>

  <!-- Book Section -->
  <section id="book" class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col lg:flex-row items-center">
        <div class="lg:w-1/2">
          <h2 class="text-3xl font-bold text-gray-800 mb-4">The Blueprint Book</h2>
          <p class="text-gray-600 mb-6">Discover proven strategies to learn smarter, not harder. This book provides practical tools and systems to boost your study efficiency, backed by research and real-world applications.</p>
          <ul class="list-disc list-inside text-gray-600 mb-6">
            <li>Step-by-step learning frameworks</li>
            <li>Techniques to improve memory and retention</li>
            <li>Time management strategies for students</li>
          </ul>
          <a href="https://www.amazon.com/your-book-link" target="_blank" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700">Get the Book on Amazon</a>
        </div>
        <div class="lg:w-1/2 mt-8 lg:mt-0">
          <img src="https://via.placeholder.com/400x500?text=Book+Cover" alt="The Blueprint Book Cover" class="rounded-lg shadow-md">
        </div>
      </div>
    </div>
  </section>

  <!-- AI Section -->
  <section id="ai" class="bg-gray-100 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col lg:flex-row-reverse items-center">
        <div class="lg:w-1/2">
          <h2 class="text-3xl font-bold text-gray-800 mb-4">Blueprint AI</h2>
          <p class="text-gray-600 mb-6">Our AI-powered learning assistant takes the principles from The Blueprint Book and brings them to life. Personalized study plans, real-time feedback, and smart tools to optimize your learning.</p>
          <ul class="list-disc list-inside text-gray-600 mb-6">
            <li>AI-driven study schedules</li>
            <li>Interactive quizzes and progress tracking</li>
            <li>Integration with your learning goals</li>
          </ul>
          <a href="/signup" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700">Try Blueprint AI Free</a>
        </div>
        <div class="lg:w-1/2 mt-8 lg:mt-0">
          <img src="https://via.placeholder.com/400x500?text=AI+Dashboard" alt="Blueprint AI Dashboard" class="rounded-lg shadow-md">
        </div>
      </div>
    </div>
  </section>

  <!-- Synergy Section -->
  <section id="synergy" class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl font-bold text-gray-800 mb-4">The Ultimate Learning System</h2>
      <p class="text-gray-600 mb-8 max-w-3xl mx-auto">The Blueprint Book provides the strategies, while Blueprint AI brings them to life with personalized, real-time support. Together, they create a seamless system to help you learn smarter and achieve your goals.</p>
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-8">
        <div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Read & Learn</h3>
          <p class="text-gray-600">Master the principles of smart learning with the book’s proven frameworks.</p>
        </div>
        <div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Apply with AI</h3>
          <p class="text-gray-600">Use Blueprint AI to implement strategies with tailored study plans and feedback.</p>
        </div>
        <div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Succeed</h3>
          <p class="text-gray-600">Combine both for a comprehensive approach to academic and personal growth.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section id="testimonials" class="bg-gray-100 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-gray-800 text-center mb-8">What Our Users Say</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <p class="text-gray-600 mb-4">"The Blueprint Book changed how I study. The AI makes it even easier to stay on track!"</p>
          <p class="font-semibold text-gray-800">Sarah M., Student</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
          <p class="text-gray-600 mb-4">"Blueprint AI’s personalized plans helped me ace my exams. Highly recommend both!"</p>
          <p class="font-semibold text-gray-800">James T., College Freshman</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
          <p class="text-gray-600 mb-4">"Combining the book and AI gave me a clear path to improve my learning."</p>
          <p class="font-semibold text-gray-800">Emily R., Lifelong Learner</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Blog/Resources Teaser -->
  <section id="blog" class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl font-bold text-gray-800 mb-4">Learn Smarter Today</h2>
      <p class="text-gray-600 mb-8 max-w-3xl mx-auto">Check out our blog for free tips, study hacks, and insights to boost your learning, inspired by The Blueprint system.</p>
      <a href="/blog" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700">Visit the Blog</a>
    </div>
  </section>

  <!-- Footer -->
  <footer id="contact" class="bg-gray-800 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-8">
        <div>
          <h3 class="text-lg font-semibold mb-4">The Blueprint</h3>
          <p class="text-gray-400">Empowering students to learn smarter with our book and AI tools.</p>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
          <ul class="space-y-2">
            <li><a href="#book" class="text-gray-400 hover:text-white">The Book</a></li>
            <li><a href="#ai" class="text-gray-400 hover:text-white">The AI</a></li>
            <li><a href="#blog" class="text-gray-400 hover:text-white">Blog</a></li>
            <li><a href="#contact" class="text-gray-400 hover:text-white">Contact</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-semibold mb-4">Stay Connected</h3>
          <p class="text-gray-400 mb-4">Subscribe to our newsletter for updates and learning tips.</p>
          <div class="flex">
            <input type="email" placeholder="Your email" class="px-4 py-2 rounded-l-lg text-gray-800 focus:outline-none">
            <button class="bg-blue-600 px-4 py-2 rounded-r-lg hover:bg-blue-700">Subscribe</button>
          </div>
        </div>
      </div>
      <div class="mt-8 text-center text-gray-400">
        <p>&copy; 2025 The Blueprint. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- JavaScript for Mobile Menu Toggle -->
  <script>
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');

    menuToggle.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });
  </script>
</body>
</html>