import { useState, useEffect, useCallback } from 'react';
import {
    DndContext,
    closestCorners,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragOverlay,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
    useSortable,
} from '@dnd-kit/sortable';
import {
    useDroppable,
} from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { createPortal } from 'react-dom';
import { useAuth } from '../../../../authContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import axios from 'axios';

// Material Design Icons
import DeleteIcon from '@mui/icons-material/Delete';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import WarningIcon from '@mui/icons-material/Warning';
import '../../../styles/kanban.scss';

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

// Custom modifier to align dragged element with cursor
const adjustTranslate = ({ transform, activatorEvent }) => {
    if (!activatorEvent) return transform;

    const { offsetX, offsetY } = activatorEvent;

    return {
        ...transform,
        x: transform.x - offsetX,
        y: transform.y - offsetY,
    };
};

// SortableTask component for individual tasks
const SortableTask = ({ task, darkMode, onEdit, onDelete, formatDate, parseTags }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: task.id });

    const style = {
        transform: isDragging ? 'none' : CSS.Transform.toString(transform),
        transition: isDragging ? 'none' : transition,
        opacity: isDragging ? 0.5 : 1,
        cursor: isDragging ? 'grabbing' : 'grab',
        pointerEvents: isDragging ? 'none' : 'auto',
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            {...attributes}
            {...listeners}
            data-task-id={task.id}
            className={`kanban-task ${darkMode ? 'theme-dark' : ''} ${isDragging ? 'is-dragging' : ''} priority-${task.priority}`}
        >
            <div className="task-header">
                <h4 className="task-title">{task.title}</h4>
                <div className="task-actions">
                    <button
                        className="action-btn edit-btn"
                        onClick={(e) => {
                            e.stopPropagation();
                            onEdit(task);
                        }}
                        title="Edit task"
                    >
                        ✏️
                    </button>
                    <button
                        className="action-btn delete-btn"
                        onClick={(e) => {
                            e.stopPropagation();
                            onDelete(task.id);
                        }}
                        title="Delete task"
                    >
                        <DeleteIcon />
                    </button>
                </div>
            </div>

            {task.description && (
                <div className="task-description">
                    {task.description}
                </div>
            )}

            <div className="task-footer">
                <div className="task-meta">
                    <div className={`priority-indicator ${task.priority}`}></div>
                    {task.due_date && (
                        <div className={`due-date ${formatDate(task.due_date)?.className || ''}`}>
                            📅 {formatDate(task.due_date)?.text}
                        </div>
                    )}
                </div>

                {task.tags && (
                    <div className="task-tags">
                        {parseTags(task.tags).map((tag, tagIndex) => (
                            <span key={tagIndex} className="tag">
                                {tag}
                            </span>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

// Droppable Column component
const DroppableColumn = ({ column, tasks, darkMode, onEdit, onDelete, formatDate, parseTags, isOver }) => {
    const { setNodeRef } = useDroppable({
        id: column.id,
    });

    return (
        <div
            ref={setNodeRef}
            className={`kanban-column ${darkMode ? 'theme-dark' : ''} ${isOver ? 'is-dragging-over' : ''}`}
        >
            <div className="column-header">
                <h3 className="column-title">
                    <span className={`status-indicator ${column.status}`}></span>
                    {column.title}
                </h3>
                <div className="task-count-badge">
                    {tasks.length}
                </div>
            </div>

            <div className="tasks-container">
                <SortableContext
                    items={tasks.map(task => task.id)}
                    strategy={verticalListSortingStrategy}
                >
                    {tasks.length === 0 ? (
                        <div className={`empty-drop-zone ${isOver ? 'is-dragging-over' : ''}`}>
                            <div className="empty-state">
                                <div className="empty-icon">📝</div>
                                <div className="empty-title">No tasks</div>
                                <div className="empty-description">
                                    Drag tasks here or create a new one
                                </div>
                            </div>
                        </div>
                    ) : (
                        tasks.map((task) => (
                            <SortableTask
                                key={task.id}
                                task={task}
                                darkMode={darkMode}
                                onEdit={onEdit}
                                onDelete={onDelete}
                                formatDate={formatDate}
                                parseTags={parseTags}
                            />
                        ))
                    )}
                </SortableContext>
            </div>
        </div>
    );
};

const KanbanBoard = ({
    subject,
    chapter,
    subjectId,
    chapterId,
    toolData,
    settings,
    onClose
}) => {
    const { user, token } = useAuth();
    const { darkMode } = useTheme();

    const [tasks, setTasks] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Handle both old and new prop formats
    const actualSubject = (typeof subject === 'object' && subject?.id)
        ? subject
        : {
            id: subjectId,
            name: typeof subject === 'string' ? subject : (toolData?.subject_name || `Subject ${subjectId}`)
        };

    const actualChapter = (typeof chapter === 'object' && chapter?.id)
        ? chapter
        : {
            id: chapterId,
            title: typeof chapter === 'string' ? chapter : (toolData?.title || `Chapter ${chapterId}`)
        };

    // Additional validation
    if (!actualSubject?.id) {
        console.error('No valid subject ID found:', { subject, subjectId, actualSubject });
    }
    if (!user || !token) {
        console.error('Missing authentication:', { user: !!user, token: !!token });
    }

    console.log('KanbanBoard props:', {
        subject,
        chapter,
        subjectId,
        chapterId,
        actualSubject,
        actualChapter,
        user,
        token: !!token
    });

    const [searchTerm, setSearchTerm] = useState('');
    const [showTaskForm, setShowTaskForm] = useState(false);
    const [editingTask, setEditingTask] = useState(null);
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        priority: 'medium',
        due_date: '',
        tags: ''
    });

    // Delete confirmation modal state
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [taskToDelete, setTaskToDelete] = useState(null);
    const [deleteSuccess, setDeleteSuccess] = useState(false);

    // Column configuration
    const columns = [
        { id: 'To Do', title: 'To Do', status: 'todo' },
        { id: 'In Progress', title: 'In Progress', status: 'in-progress' },
        { id: 'Done', title: 'Done', status: 'done' }
    ];

    // @dnd-kit sensors with improved cursor positioning
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 5,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    // Drag and drop state
    const [activeTask, setActiveTask] = useState(null);
    const [dragOverColumn, setDragOverColumn] = useState(null);

    // Fetch tasks from API
    const fetchTasks = useCallback(async () => {
        if (!user || !token) return;

        try {
            setLoading(true);
            setError(null);

            let url = `${API_URL}/api/kanban`;
            const params = new URLSearchParams();

            if (actualSubject?.id) {
                params.append('subjectId', actualSubject.id);
            }
            if (actualChapter?.id) {
                params.append('chapterId', actualChapter.id);
            }

            if (params.toString()) {
                url += `?${params.toString()}`;
            }

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.data.success) {
                setTasks(response.data.tasks || []);
            } else {
                setError('Failed to fetch tasks');
            }
        } catch (err) {
            console.error('Error fetching kanban tasks:', err);
            setError(err.response?.data?.message || err.message || 'Failed to fetch tasks');
        } finally {
            setLoading(false);
        }
    }, [user, token, actualSubject?.id, actualChapter?.id]);

    useEffect(() => {
        fetchTasks();
    }, [fetchTasks]);

    // Create new task
    const createTask = async () => {
        if (!formData.title.trim() || !actualSubject?.id) {
            console.log('Validation failed:', { title: formData.title, subjectId: actualSubject?.id });
            setError('Title and subject are required');
            return;
        }

        console.log('Creating task with data:', {
            subject_id: actualSubject.id,
            chapter_id: actualChapter?.id,
            title: formData.title,
            description: formData.description,
            priority: formData.priority,
            due_date: formData.due_date || null,
            tags: formData.tags
        });

        try {
            const response = await axios.post(`${API_URL}/api/kanban`, {
                subject_id: actualSubject.id,
                chapter_id: actualChapter?.id,
                title: formData.title,
                description: formData.description,
                priority: formData.priority,
                due_date: formData.due_date || null,
                tags: formData.tags
            }, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Create task response:', response.data);

            if (response.data.success) {
                console.log('Task created successfully, refreshing tasks...');
                await fetchTasks();
                resetForm();
                setShowTaskForm(false);
                setError(null);
            } else {
                console.error('Task creation failed:', response.data);
                setError(response.data.message || 'Failed to create task');
            }
        } catch (err) {
            console.error('Error creating task:', err);
            console.error('Error response:', err.response?.data);
            console.error('Error status:', err.response?.status);
            setError(err.response?.data?.message || err.message || 'Failed to create task');
        }
    };

    // Update existing task
    const updateTask = async () => {
        if (!formData.title.trim() || !editingTask) return;

        try {
            const response = await axios.put(`${API_URL}/api/kanban/${editingTask.id}`, {
                title: formData.title,
                description: formData.description,
                priority: formData.priority,
                due_date: formData.due_date || null,
                tags: formData.tags
            }, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.data.success) {
                await fetchTasks();
                resetForm();
                setShowTaskForm(false);
                setEditingTask(null);
            }
        } catch (err) {
            console.error('Error updating task:', err);
            setError(err.response?.data?.message || 'Failed to update task');
        }
    };

    // Show delete confirmation modal
    const showDeleteConfirmation = (taskId) => {
        const task = tasks.find(t => t.id === taskId);
        setTaskToDelete(task);
        setShowDeleteModal(true);
        setDeleteSuccess(false);
    };

    // Confirm delete task
    const confirmDeleteTask = async () => {
        if (!taskToDelete) return;

        try {
            const response = await axios.delete(`${API_URL}/api/kanban/${taskToDelete.id}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.data.success) {
                setDeleteSuccess(true);
                await fetchTasks();

                // Auto-close modal after showing success for 2 seconds
                setTimeout(() => {
                    setShowDeleteModal(false);
                    setTaskToDelete(null);
                    setDeleteSuccess(false);
                }, 2000);
            }
        } catch (err) {
            console.error('Error deleting task:', err);
            setError(err.response?.data?.message || 'Failed to delete task');
            setShowDeleteModal(false);
            setTaskToDelete(null);
            setDeleteSuccess(false);
        }
    };

    // Cancel delete
    const cancelDelete = () => {
        setShowDeleteModal(false);
        setTaskToDelete(null);
        setDeleteSuccess(false);
    };

    // Handle drag start
    const handleDragStart = (event) => {
        const { active } = event;
        const task = tasks.find(t => t.id === active.id);
        setActiveTask(task);

        console.log('Drag start - Active task:', task);

        document.body.classList.add('is-dragging');

        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    };

    // Handle drag over
    const handleDragOver = (event) => {
        const { over } = event;
        if (over) {
            setDragOverColumn(over.id);
        }
    };

    // Handle drag end
    const handleDragEnd = async (event) => {
        const { active, over } = event;

        document.body.classList.remove('is-dragging');
        setActiveTask(null);
        setDragOverColumn(null);

        if (!over) return;

        const activeTask = tasks.find(t => t.id === active.id);
        if (!activeTask) return;

        let newStatus = activeTask.status;

        const columnIds = ['To Do', 'In Progress', 'Done'];
        if (columnIds.includes(over.id)) {
            newStatus = over.id;
        } else {
            const overTask = tasks.find(t => t.id === over.id);
            if (overTask) {
                newStatus = overTask.status;
            }
        }

        if (newStatus === activeTask.status) {
            return;
        }

        const updatedTasks = tasks.map(t =>
            t.id === activeTask.id
                ? { ...t, status: newStatus }
                : t
        );
        setTasks(updatedTasks);

        if (navigator.vibrate) {
            navigator.vibrate([30, 10, 30]);
        }

        try {
            const response = await axios.put(`${API_URL}/api/kanban/${activeTask.id}`, {
                status: newStatus
            }, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.data.success) {
                throw new Error(response.data.message || 'Failed to update task position');
            }
        } catch (err) {
            console.error('Error updating task position:', err);
            setTasks(tasks);
            setError(err.response?.data?.message || 'Failed to update task position');

            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100, 50, 100]);
            }
        }
    };

    // Utility functions
    const resetForm = () => {
        setFormData({
            title: '',
            description: '',
            priority: 'medium',
            due_date: '',
            tags: ''
        });
    };

    const openTaskForm = (task = null) => {
        if (task) {
            setEditingTask(task);
            setFormData({
                title: task.title || '',
                description: task.description || '',
                priority: task.priority || 'medium',
                due_date: task.due_date || '',
                tags: task.tags || ''
            });
        } else {
            setEditingTask(null);
            resetForm();
        }
        setShowTaskForm(true);
    };

    const closeTaskForm = () => {
        setShowTaskForm(false);
        setEditingTask(null);
        resetForm();
    };

    useEffect(() => {
        const handleEscapeKey = (e) => {
            if (e.key === 'Escape' && showTaskForm) {
                closeTaskForm();
            }
        };

        if (showTaskForm) {
            document.addEventListener('keydown', handleEscapeKey);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscapeKey);
            document.body.style.overflow = 'unset';
        };
    }, [showTaskForm]);

    const handleFormSubmit = (e) => {
        e.preventDefault();
        console.log('Form submitted:', { editingTask: !!editingTask, formData });
        if (editingTask) {
            console.log('Updating existing task...');
            updateTask();
        } else {
            console.log('Creating new task...');
            createTask();
        }
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const filteredTasks = tasks.filter(task => {
        if (!searchTerm) return true;
        return task.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            task.tags?.toLowerCase().includes(searchTerm.toLowerCase());
    });

    const getTasksByStatus = (status) => {
        return filteredTasks.filter(task => task.status === status);
    };

    const formatDate = (dateString) => {
        if (!dateString) return null;
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        if (date.toDateString() === today.toDateString()) {
            return { text: 'Today', className: 'due-soon' };
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return { text: 'Tomorrow', className: 'due-soon' };
        } else if (date < today) {
            return { text: date.toLocaleDateString(), className: 'overdue' };
        } else {
            return { text: date.toLocaleDateString(), className: '' };
        }
    };

    const parseTags = (tagsString) => {
        if (!tagsString) return [];
        return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    };

    const totalTasks = tasks.length;

    if (loading) {
        return (
            <div className={`advanced-kanban-board ${darkMode ? 'theme-dark' : ''}`}>
                <div className="loading-state">
                    <div className="loading-spinner"></div>
                    <span style={{ marginLeft: '1rem' }}>Loading tasks...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`advanced-kanban-board ${darkMode ? 'theme-dark' : ''}`}>
                <div className="empty-state">
                    <div className="empty-icon">⚠️</div>
                    <div className="empty-title">Error Loading Tasks</div>
                    <div className="empty-description">{error}</div>
                    <button
                        className="btn-primary"
                        onClick={fetchTasks}
                        style={{ marginTop: '1rem' }}
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={`advanced-kanban-board ${darkMode ? 'theme-dark' : ''}`}>
            <div className="kanban-header">
                <div className="header-left">
                    <h1 className="board-title">
                        {actualSubject?.name || 'Kanban Board'}
                        {actualChapter && ` - ${actualChapter.title}`}
                    </h1>
                    <div className="task-count">
                        {totalTasks} {totalTasks === 1 ? 'task' : 'tasks'}
                    </div>
                </div>

                <div className="header-actions">
                    <div className="search-container">
                        {/*<input
                            type="text"
                            className="search-input"
                            placeholder="Search tasks..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <span className="search-icon">🔍</span>*/}
                    </div>

                    <button
                        className="add-task-btn"
                        onClick={() => openTaskForm()}
                    >
                        ✨ Add Task
                    </button>

                    {onClose && (
                        <button
                            className="close-btn"
                            onClick={onClose}
                            title="Close Kanban Board"
                        >
                            ✕
                        </button>
                    )}
                </div>
            </div>

            <DndContext
                sensors={sensors}
                collisionDetection={closestCorners}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDragEnd={handleDragEnd}
                modifiers={[adjustTranslate, restrictToWindowEdges]}
            >
                <div className="kanban-board-container">
                    {columns.map(column => {
                        const columnTasks = getTasksByStatus(column.id);

                        return (
                            <DroppableColumn
                                key={column.id}
                                column={column}
                                tasks={columnTasks}
                                darkMode={darkMode}
                                onEdit={openTaskForm}
                                onDelete={showDeleteConfirmation}
                                formatDate={formatDate}
                                parseTags={parseTags}
                                isOver={dragOverColumn === column.id}
                            />
                        );
                    })}
                </div>

                <DragOverlay
                    dropAnimation={{
                        duration: 200,
                        easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
                    }}
                    style={{
                        cursor: 'grabbing',
                        transformOrigin: '0 0',
                    }}
                >
                    {activeTask ? (
                        <div
                            className={`kanban-task ${darkMode ? 'theme-dark' : ''} is-dragging priority-${activeTask.priority}`}
                            style={{
                                boxShadow: '0 15px 30px rgba(0, 0, 0, 0.3)',
                                opacity: 0.95,
                                cursor: 'grabbing',
                            }}
                        >
                            <div className="task-header">
                                <h4 className="task-title">{activeTask.title}</h4>
                            </div>
                            {activeTask.description && (
                                <div className="task-description">
                                    {activeTask.description}
                                </div>
                            )}
                            <div className="task-meta">
                                <div className="task-priority">
                                    <span className={`priority-indicator ${activeTask.priority}`}>
                                        {activeTask.priority === 'high' ? '🔴' :
                                            activeTask.priority === 'medium' ? '🟡' : '🟢'}
                                    </span>
                                </div>
                                {activeTask.due_date && (
                                    <div className="task-due-date">
                                        📅 {formatDate(activeTask.due_date)?.text}
                                    </div>
                                )}
                                {activeTask.tags && (
                                    <div className="task-tags">
                                        {parseTags(activeTask.tags).map((tag, index) => (
                                            <span key={index} className="task-tag">
                                                {tag}
                                            </span>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    ) : null}
                </DragOverlay>
            </DndContext>

            {showTaskForm && createPortal(
                <div
                    className="kanban-task-modal-overlay"
                    onClick={(e) => e.target === e.currentTarget && closeTaskForm()}
                    style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        width: '100vw',
                        height: '100vh',
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        zIndex: 999999,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        animation: 'fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1)'
                    }}
                >
                    <form
                        className="kanban-task-form"
                        onSubmit={handleFormSubmit}
                        onClick={(e) => e.stopPropagation()}
                        style={{
                            backgroundColor: darkMode ? '#1C1C1E' : 'white',
                            borderRadius: '1rem',
                            padding: '2rem',
                            width: '90%',
                            maxWidth: '32rem',
                            position: 'relative',
                            zIndex: 1000000,
                            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
                            animation: 'slideUp 0.3s cubic-bezier(0.25, 0.1, 0.25, 1)',
                            border: darkMode ? '1px solid #38383A' : '1px solid #C6C6C8'
                        }}
                    >
                        <div className="form-header" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1.5rem' }}>
                            <h2 style={{ fontSize: '1.25rem', fontWeight: '700', margin: 0, color: darkMode ? '#FFFFFF' : '#000000' }}>
                                {editingTask ? 'Edit Task' : 'Create New Task'}
                            </h2>
                            <button
                                type="button"
                                onClick={closeTaskForm}
                                style={{
                                    background: 'none',
                                    border: 'none',
                                    fontSize: '1.5rem',
                                    cursor: 'pointer',
                                    color: darkMode ? '#FFFFFF' : '#000000'
                                }}
                            >
                                ✕
                            </button>
                        </div>

                        <div className="form-group" style={{ marginBottom: '1rem' }}>
                            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', marginBottom: '0.5rem', color: darkMode ? '#EBEBF5' : '#3C3C43' }}>
                                Title *
                            </label>
                            <input
                                type="text"
                                placeholder="Enter task title..."
                                value={formData.title}
                                onChange={(e) => handleInputChange('title', e.target.value)}
                                required
                                style={{
                                    width: '100%',
                                    padding: '0.75rem',
                                    border: `1px solid ${darkMode ? '#38383A' : '#C6C6C8'}`,
                                    borderRadius: '0.5rem',
                                    background: darkMode ? '#2C2C2E' : '#FFFFFF',
                                    color: darkMode ? '#FFFFFF' : '#000000',
                                    fontSize: '0.875rem',
                                    boxSizing: 'border-box'
                                }}
                            />
                        </div>

                        <div className="form-group" style={{ marginBottom: '1rem' }}>
                            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', marginBottom: '0.5rem', color: darkMode ? '#EBEBF5' : '#3C3C43' }}>
                                Description
                            </label>
                            <textarea
                                placeholder="Enter task description..."
                                value={formData.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                rows={4}
                                style={{
                                    width: '100%',
                                    padding: '0.75rem',
                                    border: `1px solid ${darkMode ? '#38383A' : '#C6C6C8'}`,
                                    borderRadius: '0.5rem',
                                    background: darkMode ? '#2C2C2E' : '#FFFFFF',
                                    color: darkMode ? '#FFFFFF' : '#000000',
                                    fontSize: '0.875rem',
                                    boxSizing: 'border-box',
                                    resize: 'vertical'
                                }}
                            />
                        </div>

                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
                            <div className="form-group">
                                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', marginBottom: '0.5rem', color: darkMode ? '#EBEBF5' : '#3C3C43' }}>
                                    Priority
                                </label>
                                <select
                                    value={formData.priority}
                                    onChange={(e) => handleInputChange('priority', e.target.value)}
                                    style={{
                                        width: '100%',
                                        padding: '0.75rem',
                                        border: `1px solid ${darkMode ? '#38383A' : '#C6C6C8'}`,
                                        borderRadius: '0.5rem',
                                        background: darkMode ? '#2C2C2E' : '#FFFFFF',
                                        color: darkMode ? '#FFFFFF' : '#000000',
                                        fontSize: '0.875rem',
                                        boxSizing: 'border-box'
                                    }}
                                >
                                    <option value="low">🟢 Low</option>
                                    <option value="medium">🟡 Medium</option>
                                    <option value="high">🔴 High</option>
                                </select>
                            </div>

                            <div className="form-group">
                                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', marginBottom: '0.5rem', color: darkMode ? '#EBEBF5' : '#3C3C43' }}>
                                    Due Date
                                </label>
                                <input
                                    type="date"
                                    value={formData.due_date}
                                    onChange={(e) => handleInputChange('due_date', e.target.value)}
                                    style={{
                                        width: '100%',
                                        padding: '0.75rem',
                                        border: `1px solid ${darkMode ? '#38383A' : '#C6C6C8'}`,
                                        borderRadius: '0.5rem',
                                        background: darkMode ? '#2C2C2E' : '#FFFFFF',
                                        color: darkMode ? '#FFFFFF' : '#000000',
                                        fontSize: '0.875rem',
                                        boxSizing: 'border-box'
                                    }}
                                />
                            </div>
                        </div>

                        <div className="form-group" style={{ marginBottom: '1.5rem' }}>
                            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '600', marginBottom: '0.5rem', color: darkMode ? '#EBEBF5' : '#3C3C43' }}>
                                Tags (comma-separated)
                            </label>
                            <input
                                type="text"
                                placeholder="e.g., urgent, frontend, bug"
                                value={formData.tags}
                                onChange={(e) => handleInputChange('tags', e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '0.75rem',
                                    border: `1px solid ${darkMode ? '#38383A' : '#C6C6C8'}`,
                                    borderRadius: '0.5rem',
                                    background: darkMode ? '#2C2C2E' : '#FFFFFF',
                                    color: darkMode ? '#FFFFFF' : '#000000',
                                    fontSize: '0.875rem',
                                    boxSizing: 'border-box'
                                }}
                            />
                        </div>

                        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                            <button
                                type="button"
                                onClick={closeTaskForm}
                                style={{
                                    padding: '0.75rem 1.5rem',
                                    border: `1px solid ${darkMode ? '#38383A' : '#C6C6C8'}`,
                                    borderRadius: '0.5rem',
                                    background: 'transparent',
                                    color: darkMode ? '#FFFFFF' : '#000000',
                                    fontSize: '0.875rem',
                                    cursor: 'pointer'
                                }}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                style={{
                                    padding: '0.75rem 1.5rem',
                                    border: 'none',
                                    borderRadius: '0.5rem',
                                    background: '#007AFF',
                                    color: 'white',
                                    fontSize: '0.875rem',
                                    cursor: 'pointer'
                                }}
                            >
                                {editingTask ? 'Update Task' : 'Create Task'}
                            </button>
                        </div>
                    </form>
                </div>,
                document.body
            )}

            {/* Delete Confirmation Modal */}
            {showDeleteModal && createPortal(
                <div
                    className="kanban-delete-modal-overlay"
                    onClick={(e) => e.target === e.currentTarget && cancelDelete()}
                    style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        width: '100vw',
                        height: '100vh',
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        zIndex: 999999,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        animation: 'fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1)'
                    }}
                >
                    <div
                        className="kanban-delete-modal"
                        onClick={(e) => e.stopPropagation()}
                        style={{
                            backgroundColor: darkMode ? '#1C1C1E' : 'white',
                            borderRadius: '1rem',
                            padding: '2rem',
                            width: '90%',
                            maxWidth: '28rem',
                            position: 'relative',
                            zIndex: 1000000,
                            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
                            animation: 'slideUp 0.3s cubic-bezier(0.25, 0.1, 0.25, 1)',
                            border: darkMode ? '1px solid #38383A' : '1px solid #C6C6C8',
                            textAlign: 'center'
                        }}
                    >
                        {deleteSuccess ? (
                            // Success state
                            <div className="delete-success-content">
                                <div style={{
                                    width: '4rem',
                                    height: '4rem',
                                    background: '#34C759',
                                    borderRadius: '50%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    margin: '0 auto 1.5rem',
                                    color: 'white'
                                }}>
                                    <CheckCircleIcon style={{ fontSize: '2rem' }} />
                                </div>
                                <h3 style={{
                                    fontSize: '1.25rem',
                                    fontWeight: '600',
                                    margin: '0 0 0.5rem 0',
                                    color: darkMode ? '#FFFFFF' : '#000000'
                                }}>
                                    Task Deleted Successfully
                                </h3>
                                <p style={{
                                    fontSize: '0.875rem',
                                    color: darkMode ? '#EBEBF5' : '#3C3C43',
                                    margin: '0',
                                    lineHeight: '1.4'
                                }}>
                                    The task "{taskToDelete?.title}" has been permanently deleted.
                                </p>
                            </div>
                        ) : (
                            // Confirmation state
                            <div className="delete-confirm-content">
                                <div style={{
                                    width: '4rem',
                                    height: '4rem',
                                    background: '#FF9500',
                                    borderRadius: '50%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    margin: '0 auto 1.5rem',
                                    color: 'white'
                                }}>
                                    <WarningIcon style={{ fontSize: '2rem' }} />
                                </div>
                                <h3 style={{
                                    fontSize: '1.25rem',
                                    fontWeight: '600',
                                    margin: '0 0 0.5rem 0',
                                    color: darkMode ? '#FFFFFF' : '#000000'
                                }}>
                                    Delete Task
                                </h3>
                                <p style={{
                                    fontSize: '0.875rem',
                                    color: darkMode ? '#EBEBF5' : '#3C3C43',
                                    margin: '0 0 1.5rem 0',
                                    lineHeight: '1.4'
                                }}>
                                    Are you sure you want to delete "{taskToDelete?.title}"? This action cannot be undone.
                                </p>
                                <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
                                    <button
                                        onClick={cancelDelete}
                                        style={{
                                            padding: '0.75rem 1.5rem',
                                            border: `1px solid ${darkMode ? '#38383A' : '#C6C6C8'}`,
                                            borderRadius: '0.5rem',
                                            background: 'transparent',
                                            color: darkMode ? '#FFFFFF' : '#000000',
                                            fontSize: '0.875rem',
                                            cursor: 'pointer',
                                            fontWeight: '500'
                                        }}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={confirmDeleteTask}
                                        style={{
                                            padding: '0.75rem 1.5rem',
                                            border: 'none',
                                            borderRadius: '0.5rem',
                                            background: '#FF3B30',
                                            color: 'white',
                                            fontSize: '0.875rem',
                                            cursor: 'pointer',
                                            fontWeight: '500',
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '0.5rem'
                                        }}
                                    >
                                        <DeleteIcon style={{ fontSize: '1rem' }} />
                                        Delete
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>,
                document.body
            )}
        </div>
    );
};

export default KanbanBoard;