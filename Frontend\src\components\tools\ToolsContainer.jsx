import React, { useState, useEffect } from 'react';
import ToolsGrid from './ToolsGrid';
import '../../styles/tools.scss';

const ToolsContainer = () => {
    const [isDarkMode, setIsDarkMode] = useState(false);

    // Check for dark mode on component mount and when theme changes
    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    return (
        <div className={`tools-container ${isDarkMode ? 'theme-dark' : ''}`}>
            {/*
            <div className="tools-header">
                <h1>Study Tools</h1>
                <p>Click on a tool to use it with your subjects and chapters</p>
            </div>*/}
            <ToolsGrid />
        </div>
    );
};

export default ToolsContainer;
