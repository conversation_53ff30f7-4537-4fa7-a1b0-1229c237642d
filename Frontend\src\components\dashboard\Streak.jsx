import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../../authContext.jsx';
import { useObjectives } from '../../contexts/ObjectivesContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';
import streak from '../../assets/Streak.png';
import '../../styles/streak_dashboard.scss';

// Get API URL from environment or use default
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

const Streak = () => {
    // Get dark mode state from ThemeContext
    const { darkMode } = useTheme();

    // Helper function to check if streak was updated today
    const wasStreakUpdatedToday = () => {
        const today = new Date().toISOString().split('T')[0];
        const lastUpdated = localStorage.getItem('streakLastUpdated');
        return lastUpdated === today;
    };

    // Helper function to mark streak as updated today
    const markStreakUpdatedToday = () => {
        const today = new Date().toISOString().split('T')[0];
        localStorage.setItem('streakLastUpdated', today);
    };

    // State hooks
    const [showModal, setShowModal] = useState(false);
    const [streakData, setStreakData] = useState([]);
    const [streakInfo, setStreakInfo] = useState({
        currentStreak: 0,
        longestStreak: 0,
        totalStreakDays: 0
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [streakUpdatedToday, setStreakUpdatedToday] = useState(wasStreakUpdatedToday());
    const [showWeeklyCalendar, setShowWeeklyCalendar] = useState(false);

    // Ref hooks
    const modalRef = useRef(null);

    // Context hooks
    const { token } = useAuth();
    const { progressPercentage } = useObjectives();

    // Fetch streak data from API
    const fetchStreakData = React.useCallback(async () => {
        try {
            setLoading(true);
            const response = await fetch(`${API_URL}/api/streak`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch streak data');
            }

            const data = await response.json();

            if (data.success) {
                // Convert streak history to the format we need
                const formattedData = data.streak.streak_history.map(entry => ({
                    date: entry.date,
                    completion: entry.completion,
                    isStreak: entry.is_streak_day
                }));

                // Log the streak data for debugging
                console.log('Received streak data:', {
                    currentStreak: data.streak.current_streak,
                    longestStreak: data.streak.longest_streak,
                    streakHistory: data.streak.streak_history,
                    totalStreakDays: formattedData.filter(day => day.isStreak).length
                });

                setStreakData(formattedData);
                setStreakInfo({
                    currentStreak: data.streak.current_streak,
                    longestStreak: data.streak.longest_streak,
                    totalStreakDays: formattedData.filter(day => day.isStreak).length
                });
            } else {
                setError(data.message || 'Failed to fetch streak data');
            }
        } catch (error) {
            console.error('Error fetching streak data:', error);
            setError(error.message);
        } finally {
            setLoading(false);
        }
    }, [token]);

    // Update streak when objectives progress changes
    // Using useCallback to memoize the function
    const updateStreak = React.useCallback(async () => {
        if (progressPercentage === undefined) return;

        // Check if we've already updated the streak today
        const todayFormatted = new Date().toISOString().split('T')[0];
        const todayRecord = streakData.find(record => record.date === todayFormatted);

        // If we already have a streak for today and it's already counted as a streak day
        if (todayRecord && todayRecord.isStreak) {
            console.log('Streak already recorded for today');

            // Even if streak is already counted, we should still update the completion percentage
            // if it has changed since the last update
            if (todayRecord.completion !== progressPercentage) {
                console.log(`Updating completion percentage from ${todayRecord.completion}% to ${progressPercentage}%`);
                try {
                    const response = await fetch(`${API_URL}/api/streak/update`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            update_completion_only: true
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            console.log('Updated completion percentage without affecting streak');
                            fetchStreakData();
                        }
                    }
                } catch (error) {
                    console.error('Error updating completion percentage:', error);
                }
            } else {
                console.log('Completion percentage unchanged, no update needed');
            }

            setStreakUpdatedToday(true);
            markStreakUpdatedToday();
            return;
        }

        // If we've already made an API call to update the streak today, but we need to update the completion percentage
        if (streakUpdatedToday) {
            console.log('Already attempted to update streak today');

            // If there's a record for today, update the completion percentage if it has changed
            if (todayRecord && todayRecord.completion !== progressPercentage) {
                console.log(`Updating completion percentage from ${todayRecord.completion}% to ${progressPercentage}%`);
                try {
                    const response = await fetch(`${API_URL}/api/streak/update`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            update_completion_only: true
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            console.log('Updated completion percentage without affecting streak');
                            fetchStreakData();
                        }
                    }
                } catch (error) {
                    console.error('Error updating completion percentage:', error);
                }
            } else {
                console.log('Completion percentage unchanged or no record for today, no update needed');
            }

            return;
        }

        // If progress is below 60%, we don't want to count it as a streak yet
        if (progressPercentage < 60) {
            console.log('Progress below 60%, not counting as streak yet');

            // If there's a record for today but it's not a streak day yet, update the completion percentage
            if (todayRecord && !todayRecord.isStreak) {
                try {
                    const response = await fetch(`${API_URL}/api/streak/update`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            update_completion_only: true
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            console.log('Updated completion percentage without counting streak');
                            fetchStreakData();
                        }
                    }
                } catch (error) {
                    console.error('Error updating completion percentage:', error);
                }
            }
            return;
        }

        // If we reach here, progress is 60% or higher for the first time today
        console.log(`Updating streak - progress is ${progressPercentage}%, which is >= 60% threshold`);

        try {
            // Mark that we've attempted to update the streak today to prevent multiple API calls
            setStreakUpdatedToday(true);
            markStreakUpdatedToday();

            const response = await fetch(`${API_URL}/api/streak/update`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    count_streak: true  // Explicitly tell the backend to count this as a streak day
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update streak');
            }

            const data = await response.json();

            if (data.success && data.streak_updated) {
                console.log('Streak updated successfully for today');

                // Refresh streak data
                fetchStreakData();
            }
        } catch (error) {
            console.error('Error updating streak:', error);
        }
    }, [progressPercentage, streakData, streakUpdatedToday, token, fetchStreakData]);

    // Extract values from streakInfo
    const { currentStreak, longestStreak, totalStreakDays } = streakInfo;

    // Initial fetch when component mounts
    useEffect(() => {
        if (token) {
            fetchStreakData();
        }
    }, [token, fetchStreakData]);

    // Check if we've already updated the streak today
    useEffect(() => {
        if (streakData.length > 0) {
            const todayFormatted = new Date().toISOString().split('T')[0];
            const todayRecord = streakData.find(record => record.date === todayFormatted);

            // If we have a record for today and it's already a streak day, mark as updated
            if (todayRecord && todayRecord.isStreak) {
                setStreakUpdatedToday(true);
                markStreakUpdatedToday();
                console.log('Found existing streak record for today, marked as updated');
            } else if (wasStreakUpdatedToday()) {
                // If localStorage says we've already tried to update today, respect that
                setStreakUpdatedToday(true);
                console.log('Found localStorage record indicating streak was already processed today');
            }
        }
    }, [streakData]);

    // Update streak when progress changes
    useEffect(() => {
        if (token && progressPercentage !== undefined) {
            updateStreak();
        }
    }, [token, progressPercentage, updateStreak]);

    // Reset streak updated flag at midnight
    useEffect(() => {
        // Function to calculate milliseconds until midnight
        const getMsUntilMidnight = () => {
            const now = new Date();
            const midnight = new Date(now);
            midnight.setHours(24, 0, 0, 0);
            return midnight - now;
        };

        // Function to reset the streak updated flag
        const resetStreakUpdatedFlag = () => {
            setStreakUpdatedToday(false);

            // Clear the localStorage flag
            localStorage.removeItem('streakLastUpdated');

            // Set up the next day's timer
            const msUntilMidnight = getMsUntilMidnight();
            timeoutRef.current = setTimeout(resetStreakUpdatedFlag, msUntilMidnight);
        };

        // Set up the initial timer
        const msUntilMidnight = getMsUntilMidnight();
        const timeoutRef = { current: null };
        timeoutRef.current = setTimeout(resetStreakUpdatedFlag, msUntilMidnight);

        // Clean up the timer when component unmounts
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        if (!showModal) return;

        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowModal(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowModal(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [showModal]);

    // Get current week's days
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday
    const weekDays = Array.from({ length: 7 }, (_, i) => {
        const date = new Date(startOfWeek);
        date.setDate(startOfWeek.getDate() + i);
        const dateString = date.toISOString().split('T')[0];
        const data = streakData.find((d) => d.date === dateString);
        return {
            date: dateString,
            day: date.getDate(),
            completion: data ? data.completion : 0,
            isStreak: data ? data.isStreak : false,
        };
    });

    // Group streak data by week for history
    const groupByWeek = () => {
        if (!streakData.length) return [];

        const weeks = [];
        let currentWeek = [];

        // Sort data by date (newest first)
        const sortedData = [...streakData].sort((a, b) => new Date(b.date) - new Date(a.date));

        let weekStart = new Date(sortedData[0].date);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());

        sortedData.forEach((day) => {
            const dayDate = new Date(day.date);
            if (dayDate >= weekStart && dayDate < new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)) {
                currentWeek.push(day);
            } else {
                if (currentWeek.length) weeks.push(currentWeek);
                currentWeek = [day];
                weekStart = new Date(day.date);
                weekStart.setDate(weekStart.getDate() - weekStart.getDay());
            }
        });
        if (currentWeek.length) weeks.push(currentWeek);
        return weeks;
    };

    const weeklyHistory = groupByWeek();

    // Calculate stats
    const averageCompletion =
        streakData.length > 0
            ? Math.round(
                streakData.reduce((sum, day) => sum + day.completion, 0) / streakData.length
            )
            : 0;
    const streakConsistency =
        streakData.length > 0 ? Math.round((totalStreakDays / streakData.length) * 100) : 0;

    // Render loading state
    if (loading) {
        return (
            <div className="streak-card">
                <h2 className="card-title">Streak</h2>
                <div className="streak-count">
                    <span className="streak-icon" aria-hidden="true">
                        <img src={streak} alt="Streak icon" />
                    </span>
                    <h2>Loading streak data...</h2>
                </div>
            </div>
        );
    }

    // Render error state
    if (error) {
        return (
            <div className="streak-card">
                <h2 className="card-title">Streak</h2>
                <div className="streak-count">
                    <span className="streak-icon" aria-hidden="true">
                        <img src={streak} alt="Streak icon" />
                    </span>
                    <h2>Error loading streak data</h2>
                </div>
            </div>
        );
    }

    // Render main content
    return (
        <>
            <div
                className={`streak-card ${!showWeeklyCalendar ? 'expanded-icon-mode' : ''}`}
                onClick={() => setShowModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
                aria-label="Open Streak Tracker"
            >
                <h2 className="card-title">Streak</h2>
                <div className="streak-count">
                    <span className="streak-icon" aria-hidden="true">
                        <img src={streak} alt="Streak icon" className={!showWeeklyCalendar ? "large-icon" : ""} />
                    </span>
                    <h2>{currentStreak} Days Streak</h2>
                </div>

                {showWeeklyCalendar && (
                    <div className="weekly-calender">
                        <div className="week-label">This Week</div>
                        <div className="week-calendar">
                            <div className="day-labels">
                                {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((label, i) => (
                                    <span key={i}>{label}</span>
                                ))}
                            </div>
                            <div className="day-numbers">
                                {weekDays.map((day, i) => (
                                    <span
                                        key={i}
                                        className={`day ${day.isStreak ? 'streak-day' : ''}`}
                                        aria-label={`${day.date}: ${day.completion}% completed`}
                                    >
                                        {day.day}
                                        {day.isStreak && <span className="streak-mark" aria-hidden="true">✅</span>}
                                    </span>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                <button
                    className="calendar-toggle-button"
                    onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering the card's onClick
                        setShowWeeklyCalendar(!showWeeklyCalendar);
                    }}
                    aria-label={showWeeklyCalendar ? "Hide weekly calendar" : "Show weekly calendar"}
                >
                    {showWeeklyCalendar ? "Hide Calendar" : "Show Calendar"}
                </button>
            </div>
            {showModal && (
                <div className={`modal-overlay ${darkMode ? 'theme-dark' : ''}`}>
                    <div className={`modal-window ${darkMode ? 'theme-dark' : ''}`} ref={modalRef} role="dialog" aria-labelledby="modal-title">
                        <div className="modal-header">
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowModal(false)}
                                    aria-label="Close"
                                ></button>
                                <button className="traffic-light yellow" disabled aria-label="Minimize"></button>
                                <button className="traffic-light green" disabled aria-label="Maximize"></button>
                            </div>
                            <h2 id="modal-title" className="modal-title">
                                Streak Tracker
                            </h2>
                        </div>
                        <div className="modal-content">
                            <div className="summary-section">
                                <h3>Streak Summary</h3>
                                <p>Current Streak: {currentStreak} days</p>
                                <p>Longest Streak: {longestStreak} days</p>
                                <p>Total Streak Days: {totalStreakDays}</p>
                                <p>Average Daily Completion: {averageCompletion}%</p>
                                <p>Streak Consistency: {streakConsistency}%</p>
                            </div>
                            <div className="calendar-section">
                                <h3>This Week</h3>
                                <div className="week-calendar">
                                    <div className="day-labels">
                                        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((label, i) => (
                                            <span key={i}>{label}</span>
                                        ))}
                                    </div>
                                    <div className="day-numbers">
                                        {weekDays.map((day, i) => (
                                            <div
                                                key={i}
                                                className={`day ${day.isStreak ? 'streak-day' : ''}`}
                                                aria-label={`${day.date}: ${day.completion}% completed`}
                                            >
                                                <span className="day-number">{day.day}</span>
                                                {day.completion > 0 && (
                                                    <div
                                                        className="progress-ring"
                                                        role="progressbar"
                                                        aria-valuenow={day.completion}
                                                        aria-valuemin="0"
                                                        aria-valuemax="100"
                                                    >
                                                        <svg width="40" height="40">
                                                            <circle
                                                                className="progress-ring-bg"
                                                                cx="20"
                                                                cy="20"
                                                                r="15"
                                                                strokeWidth="5"
                                                            />
                                                            <circle
                                                                className="progress-ring-fill"
                                                                cx="20"
                                                                cy="20"
                                                                r="15"
                                                                strokeWidth="5"
                                                                strokeDasharray="94.25"
                                                                strokeDashoffset={
                                                                    94.25 * (1 - day.completion / 100)
                                                                }
                                                            />
                                                        </svg>
                                                    </div>
                                                )}
                                                <span className="tooltip">
                                                    {day.date}: {day.completion}% completed
                                                    {day.isStreak ? ' (Streak day)' : ''}
                                                </span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <div className="history-section">
                                <h3>Streak History</h3>
                                {weeklyHistory.length === 0 ? (
                                    <p>No streak history available yet. Complete your daily objectives to build a streak!</p>
                                ) : (
                                    weeklyHistory.map((week, index) => {
                                        if (!week || week.length === 0) return null;

                                        const weekStart = new Date(week[0].date);
                                        const weekEnd = new Date(week[week.length - 1].date);
                                        const streakDays = week.filter((day) => day.isStreak).length;

                                        return (
                                            <div key={index} className="week-history">
                                                <h4>
                                                    Week of {weekStart.toLocaleDateString()} -{' '}
                                                    {weekEnd.toLocaleDateString()}
                                                </h4>
                                                <p>Streak Days: {streakDays}/{week.length}</p>
                                                <div className="week-calendar small">
                                                    <div className="day-numbers">
                                                        {week.map((day, i) => (
                                                            <span
                                                                key={i}
                                                                className={`day ${day.isStreak ? 'streak-day' : ''}`}
                                                                aria-label={`${day.date}: ${day.completion}% completed`}
                                                            >
                                                                {new Date(day.date).getDate()}
                                                                {day.isStreak && (
                                                                    <span className="streak-mark" aria-hidden="true">
                                                                        ✅
                                                                    </span>
                                                                )}
                                                            </span>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })
                                )}
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button className="close-button" onClick={() => setShowModal(false)}>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Streak;