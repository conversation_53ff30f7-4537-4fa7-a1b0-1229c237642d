import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import '../styles/Settings.scss';

// Material UI Icons
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import MicIcon from '@mui/icons-material/Mic';
import PsychologyIcon from '@mui/icons-material/Psychology';
import SchoolIcon from '@mui/icons-material/School';
import TuneIcon from '@mui/icons-material/Tune';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import BugReportIcon from '@mui/icons-material/BugReport';
import ScreenshotMonitorIcon from '@mui/icons-material/ScreenshotMonitor';
import MemoryIcon from '@mui/icons-material/Memory';
import LinkIcon from '@mui/icons-material/Link';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import SpeedIcon from '@mui/icons-material/Speed';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import AccessibilityNewIcon from '@mui/icons-material/AccessibilityNew';

const Settings = () => {
    const navigate = useNavigate();
    const { darkMode } = useTheme();
    const [activeCategory, setActiveCategory] = useState('personality');
    const [activeSection, setActiveSection] = useState('voice');
    const [isDarkMode, setIsDarkMode] = useState(darkMode);
    const [reducedMotion, setReducedMotion] = useState(false);

    // Voice settings
    const [voiceWarmth, setVoiceWarmth] = useState(50);
    const [voiceClarity, setVoiceClarity] = useState(70);
    const [voiceSpeed, setVoiceSpeed] = useState(50);

    // Teaching style
    const [teachingStyle, setTeachingStyle] = useState('balanced');

    // Knowledge depth
    const [knowledgeDepth, setKnowledgeDepth] = useState(3);

    // Advanced settings
    const [neuralProcessing, setNeuralProcessing] = useState(true);
    const [contextMemory, setContextMemory] = useState('medium');
    const [crossSubjectLinking, setCrossSubjectLinking] = useState(true);

    // Contact settings
    const [responseTime, setResponseTime] = useState('normal');
    const [issueCategory, setIssueCategory] = useState('suggestion');

    // Detect theme changes
    useEffect(() => {
        setIsDarkMode(darkMode);
    }, [darkMode]);

    // Categories for the first column
    const categories = [
        { id: 'personality', icon: <PsychologyIcon />, label: 'AI Personality' },
        { id: 'advanced', icon: <TuneIcon />, label: 'Advanced Controls' },
        { id: 'contact', icon: <SupportAgentIcon />, label: 'Contact & Support' },
        { id: 'accessibility', icon: <AccessibilityNewIcon />, label: 'Accessibility' }
    ];

    // Sections for each category (second column)
    const sections = {
        personality: [
            { id: 'voice', icon: <MicIcon />, label: 'Voice Modulation' },
            { id: 'teaching', icon: <SchoolIcon />, label: 'Teaching Style' },
            { id: 'knowledge', icon: <AutoAwesomeIcon />, label: 'Knowledge Depth' }
        ],
        advanced: [
            { id: 'neural', icon: <MemoryIcon />, label: 'Neural Processing' },
            { id: 'context', icon: <AccessTimeIcon />, label: 'Context Memory' },
            { id: 'linking', icon: <LinkIcon />, label: 'Cross-Subject Linking' }
        ],
        contact: [
            { id: 'priority', icon: <SpeedIcon />, label: 'Response Priority' },
            { id: 'issue', icon: <BugReportIcon />, label: 'Issue Reporting' },
            { id: 'screenshot', icon: <ScreenshotMonitorIcon />, label: 'Screenshot Tool' }
        ],
        accessibility: [
            { id: 'theme', icon: isDarkMode ? <LightModeIcon /> : <DarkModeIcon />, label: 'Theme Settings' },
            { id: 'motion', icon: <AutoAwesomeIcon />, label: 'Motion Settings' }
        ]
    };

    // Handle back button click
    const handleBack = () => {
        navigate(-1);
    };

    return (
        <div className={`settings-container ${isDarkMode ? 'theme-dark' : ''}`}>
            <div className="settings-header">
                <button className="back-button" onClick={handleBack}>
                    <ArrowBackIcon />
                </button>
                <h1>Settings</h1>
            </div>

            <div className="settings-content">
                {/* First column - Categories */}
                <div className="settings-categories">
                    {categories.map(category => (
                        <div
                            key={category.id}
                            className={`category-item ${activeCategory === category.id ? 'active' : ''}`}
                            onClick={() => {
                                setActiveCategory(category.id);
                                setActiveSection(sections[category.id][0].id);
                            }}
                        >
                            <div className="category-icon">{category.icon}</div>
                            <div className="category-label">{category.label}</div>
                        </div>
                    ))}
                </div>

                {/* Second column - Sections */}
                <div className="settings-sections">
                    {sections[activeCategory].map(section => (
                        <div
                            key={section.id}
                            className={`section-item ${activeSection === section.id ? 'active' : ''}`}
                            onClick={() => setActiveSection(section.id)}
                        >
                            <div className="section-icon">{section.icon}</div>
                            <div className="section-label">{section.label}</div>
                        </div>
                    ))}
                </div>

                {/* Third column - Detail Panel */}
                <div className="settings-details">
                    <SettingsDetailPanel
                        category={activeCategory}
                        section={activeSection}
                        settings={{
                            voiceWarmth, setVoiceWarmth,
                            voiceClarity, setVoiceClarity,
                            voiceSpeed, setVoiceSpeed,
                            teachingStyle, setTeachingStyle,
                            knowledgeDepth, setKnowledgeDepth,
                            neuralProcessing, setNeuralProcessing,
                            contextMemory, setContextMemory,
                            crossSubjectLinking, setCrossSubjectLinking,
                            responseTime, setResponseTime,
                            issueCategory, setIssueCategory,
                            isDarkMode, reducedMotion, setReducedMotion
                        }}
                    />
                </div>
            </div>

            {/* Persistent action bar */}
            <div className="settings-action-bar">
                <div className="action-item">
                    <HelpOutlineIcon />
                    <span>Help Center</span>
                </div>
                <div className="action-item">
                    <BugReportIcon />
                    <span>Report Issue</span>
                </div>
                <div className="action-item">
                    <SupportAgentIcon />
                    <span>Contact Support</span>
                </div>
            </div>
        </div>
    );
};

// Import panel components
import VoiceModulationPanel from '../components/settings/VoiceModulationPanel';
import TeachingStylePanel from '../components/settings/TeachingStylePanel';
import KnowledgeDepthPanel from '../components/settings/KnowledgeDepthPanel';
import NeuralProcessingPanel from '../components/settings/NeuralProcessingPanel';
import ContextMemoryPanel from '../components/settings/ContextMemoryPanel';
import CrossSubjectPanel from '../components/settings/CrossSubjectPanel';
import ResponsePriorityPanel from '../components/settings/ResponsePriorityPanel';
import IssueReportingPanel from '../components/settings/IssueReportingPanel';
import ScreenshotToolPanel from '../components/settings/ScreenshotToolPanel';
import ThemeSettingsPanel from '../components/settings/ThemeSettingsPanel';
import MotionSettingsPanel from '../components/settings/MotionSettingsPanel';

// Detail Panel Component
const SettingsDetailPanel = ({ category, section, settings }) => {
    // Render different content based on category and section
    switch (`${category}-${section}`) {
        case 'personality-voice':
            return <VoiceModulationPanel settings={settings} />;
        case 'personality-teaching':
            return <TeachingStylePanel settings={settings} />;
        case 'personality-knowledge':
            return <KnowledgeDepthPanel settings={settings} />;
        case 'advanced-neural':
            return <NeuralProcessingPanel settings={settings} />;
        case 'advanced-context':
            return <ContextMemoryPanel settings={settings} />;
        case 'advanced-linking':
            return <CrossSubjectPanel settings={settings} />;
        case 'contact-priority':
            return <ResponsePriorityPanel settings={settings} />;
        case 'contact-issue':
            return <IssueReportingPanel settings={settings} />;
        case 'contact-screenshot':
            return <ScreenshotToolPanel settings={settings} />;
        case 'accessibility-theme':
            return <ThemeSettingsPanel settings={settings} />;
        case 'accessibility-motion':
            return <MotionSettingsPanel settings={settings} />;
        default:
            return <div className="detail-panel">Select a setting to configure</div>;
    }
};

export default Settings;
