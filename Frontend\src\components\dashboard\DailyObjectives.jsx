import React from 'react';
import '../../styles/Dashboard.scss';
import { useObjectives } from '../../contexts/ObjectivesContext.jsx';

const DailyObjectives = () => {
    const {
        subjects,
        objectives,
        loading,
        error,
        toggleObjective
    } = useObjectives();

    if (loading) {
        return <div className="daily-objectives">Loading...</div>;
    }

    if (error) {
        return <div className="daily-objectives error">Error: {error}</div>;
    }

    if (subjects.length === 0) {
        return (
            <div className="daily-objectives">
                <h2>Daily Objectives</h2>
                <p className="no-subjects">You don't have any subjects. Please add a subject to start setting objectives.</p>
            </div>
        );
    }

    const pending = objectives.filter(obj => !obj.completed);
    const done = objectives.filter(obj => obj.completed);
    const total = objectives.length;
    const remaining = pending.length;

    // Get subjects that have objectives for today
    const subjectsWithObjectives = subjects.filter(subject =>
        objectives.some(obj => obj.subject_name === subject.name)
    );

    // If no subjects have objectives for today
    if (subjectsWithObjectives.length === 0) {
        return (
            <div className="daily-objectives">
                <div className="objectives-header">
                    <h2>Daily Objectives</h2>
                    <div className="objectives-number">
                        <span>0</span>
                    </div>
                </div>
                <div className="objectives-content">
                    <p className="no-objectives">No objectives scheduled for today.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="daily-objectives">
            <div className="objectives-header">
                <h2>Daily Objectives</h2>
                <div className="objectives-number">
                    <span>{remaining}</span>
                </div>
            </div>
            <div className="objectives-content">
                {subjectsWithObjectives.map(subject => {
                    // Get objectives for this subject
                    const subjectObjectives = objectives.filter(obj => obj.subject_name === subject.name);
                    const subjectPending = pending.filter(obj => obj.subject_name === subject.name);
                    const subjectDone = done.filter(obj => obj.subject_name === subject.name);

                    // Only render if there are objectives for this subject
                    if (subjectObjectives.length === 0) return null;

                    return (
                        <div key={subject.id} className="subject-section">
                            <h3>{subject.name}</h3>
                            <>
                                {subjectPending.map(obj => (
                                    <div key={obj.id} className="objective">
                                        <span> <span>{obj.chapter_number}</span>{obj.objective}</span>
                                        <div
                                            className={`toggle ${obj.completed ? 'done' : ''}`}
                                            onClick={() => toggleObjective(obj.id, obj.completed)}
                                        />
                                    </div>
                                ))}
                                {subjectDone.length > 0 && (
                                    <h4>Done</h4>
                                )}
                                {subjectDone.map(obj => (
                                    <div key={obj.id} className="objective">
                                        <span> <span>{obj.chapter_number}</span>{obj.objective}</span>
                                        <div
                                            className={`toggle ${obj.completed ? 'done' : ''}`}
                                            onClick={() => toggleObjective(obj.id, obj.completed)}
                                        />
                                    </div>
                                ))}
                            </>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default DailyObjectives;