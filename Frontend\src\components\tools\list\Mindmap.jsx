import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../../contexts/ThemeContext';

// Material Design Icons
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import CenterFocusStrongIcon from '@mui/icons-material/CenterFocusStrong';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import PsychologyIcon from '@mui/icons-material/Psychology';
import TaskIcon from '@mui/icons-material/Task';
import NoteIcon from '@mui/icons-material/Note';
import QuestionMarkIcon from '@mui/icons-material/QuestionMark';
import ImageIcon from '@mui/icons-material/Image';
import GroupWorkIcon from '@mui/icons-material/GroupWork';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';

// Node Types
const NODE_TYPES = {
    CONCEPT: 'concept',
    TASK: 'task',
    NOTE: 'note',
    QUESTION: 'question',
    MEDIA: 'media',
    GROUP: 'group'
};

// Connection Types
const CONNECTION_TYPES = {
    CORRECT: 'correct',
    INCORRECT: 'incorrect',
    NEUTRAL: 'neutral'
};

const Mindmap = ({ tool, subject }) => {
    const { darkMode } = useTheme();

    // Core state
    const [nodes, setNodes] = useState([
        {
            id: 1,
            text: subject,
            x: 400,
            y: 300,
            type: NODE_TYPES.CONCEPT,
            connections: [],
            color: '#007AFF',
            size: 'large',
            data: {
                description: '',
                tags: [],
                priority: 'medium',
                completed: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        }
    ]);
    const [nextId, setNextId] = useState(2);
    const [selectedNodes, setSelectedNodes] = useState([]);
    const [connections, setConnections] = useState([]);

    // UI state
    const [dragging, setDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [zoom, setZoom] = useState(1);
    const [pan, setPan] = useState({ x: 0, y: 0 });
    const [showNodeForm, setShowNodeForm] = useState(false);
    const [editingNode, setEditingNode] = useState(null);
    const [showConnectionForm, setShowConnectionForm] = useState(false);
    const [connectionStart, setConnectionStart] = useState(null);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [showTemplates, setShowTemplates] = useState(false);
    const [showAIPanel, setShowAIPanel] = useState(false);

    // Refs
    const svgRef = useRef(null);
    const containerRef = useRef(null);
    const nodeFormRef = useRef(null);

    // Add selectedNode state
    const [selectedNode, setSelectedNode] = useState(null);

    // Node type configurations
    const nodeTypeConfig = {
        [NODE_TYPES.CONCEPT]: {
            icon: PsychologyIcon,
            color: '#007AFF',
            size: { width: 120, height: 80 },
            shape: 'rounded-rect'
        },
        [NODE_TYPES.TASK]: {
            icon: TaskIcon,
            color: '#28CD41',
            size: { width: 100, height: 60 },
            shape: 'rect'
        },
        [NODE_TYPES.NOTE]: {
            icon: NoteIcon,
            color: '#FF9500',
            size: { width: 110, height: 70 },
            shape: 'rounded-rect'
        },
        [NODE_TYPES.QUESTION]: {
            icon: QuestionMarkIcon,
            color: '#FFCC00',
            size: { width: 90, height: 60 },
            shape: 'circle'
        },
        [NODE_TYPES.MEDIA]: {
            icon: ImageIcon,
            color: '#FF3B30',
            size: { width: 100, height: 100 },
            shape: 'rect'
        },
        [NODE_TYPES.GROUP]: {
            icon: GroupWorkIcon,
            color: '#8E8E93',
            size: { width: 150, height: 100 },
            shape: 'rounded-rect'
        }
    };

    // Load mindmap from localStorage
    useEffect(() => {
        const savedMindmap = localStorage.getItem(`${subject}-advanced-mindmap`);
        if (savedMindmap) {
            try {
                const { nodes: savedNodes, nextId: savedNextId, connections: savedConnections } = JSON.parse(savedMindmap);
                setNodes(savedNodes || [{
                    id: 1,
                    text: subject,
                    x: 400,
                    y: 300,
                    type: NODE_TYPES.CONCEPT,
                    connections: [],
                    color: '#007AFF',
                    size: 'large',
                    data: {
                        description: '',
                        tags: [],
                        priority: 'medium',
                        completed: false,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                }]);
                setNextId(savedNextId || 2);
                setConnections(savedConnections || []);
            } catch (error) {
                console.error('Error loading mindmap:', error);
            }
        }
    }, [subject]);

    // Save mindmap to localStorage
    useEffect(() => {
        localStorage.setItem(`${subject}-advanced-mindmap`, JSON.stringify({
            nodes,
            nextId,
            connections,
            version: '2.0',
            lastModified: new Date().toISOString()
        }));
    }, [nodes, nextId, connections, subject]);

    // Utility functions
    const getNodeIcon = (type) => {
        const config = nodeTypeConfig[type];
        return config ? config.icon : PsychologyIcon;
    };

    const getNodeColor = (type) => {
        const config = nodeTypeConfig[type];
        return config ? config.color : '#007AFF';
    };

    const getNodeSize = (type) => {
        const config = nodeTypeConfig[type];
        return config ? config.size : { width: 120, height: 80 };
    };

    // Advanced node creation
    const createNode = useCallback((type = NODE_TYPES.CONCEPT, parentNode = null) => {
        const parentX = parentNode ? parentNode.x : 400;
        const parentY = parentNode ? parentNode.y : 300;
        const offset = 150;
        const angle = Math.random() * 2 * Math.PI;

        const newNode = {
            id: nextId,
            text: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
            x: parentX + Math.cos(angle) * offset,
            y: parentY + Math.sin(angle) * offset,
            type,
            connections: [],
            color: getNodeColor(type),
            size: 'medium',
            data: {
                description: '',
                tags: [],
                priority: 'medium',
                completed: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                ...(type === NODE_TYPES.TASK && { dueDate: '', status: 'pending' }),
                ...(type === NODE_TYPES.NOTE && { content: '', category: 'general' }),
                ...(type === NODE_TYPES.QUESTION && { answer: '', difficulty: 'medium' }),
                ...(type === NODE_TYPES.MEDIA && { url: '', mediaType: 'image' })
            }
        };

        setNodes(prev => [...prev, newNode]);

        // Create connection if parent exists
        if (parentNode) {
            const newConnection = {
                id: `conn-${nextId}-${Date.now()}`,
                from: parentNode.id,
                to: newNode.id,
                type: CONNECTION_TYPES.NEUTRAL,
                label: '',
                strength: 1
            };
            setConnections(prev => [...prev, newConnection]);
        }

        setNextId(nextId + 1);
        setSelectedNodes([newNode.id]);
        return newNode;
    }, [nextId, setConnections, setSelectedNodes]);

    const deleteNode = useCallback((nodeId) => {
        if (nodeId === 1) return; // Prevent deleting root node

        setNodes(prev => prev.filter(node => node.id !== nodeId));
        setConnections(prev => prev.filter(conn => conn.from !== nodeId && conn.to !== nodeId));
        setSelectedNodes(prev => prev.filter(id => id !== nodeId));

        if (selectedNode?.id === nodeId) {
            setSelectedNode(null);
        }
    }, [selectedNode, setConnections, setSelectedNodes]);

    // Node update functions
    const updateNodeText = useCallback((id, text) => {
        setNodes(prev => prev.map(node =>
            node.id === id ? {
                ...node,
                text,
                data: { ...node.data, updatedAt: new Date().toISOString() }
            } : node
        ));
    }, []);

    const updateNodeColor = useCallback((id, color) => {
        setNodes(prev => prev.map(node =>
            node.id === id ? {
                ...node,
                color,
                data: { ...node.data, updatedAt: new Date().toISOString() }
            } : node
        ));
    }, []);

    const updateNodeData = useCallback((id, newData) => {
        setNodes(prev => prev.map(node =>
            node.id === id ? {
                ...node,
                data: {
                    ...node.data,
                    ...newData,
                    updatedAt: new Date().toISOString()
                }
            } : node
        ));
    }, []);

    // Drag and drop functionality
    const startDrag = useCallback((node, e) => {
        e.preventDefault();
        setSelectedNode(node);
        setDragging(true);

        const svgRect = svgRef.current.getBoundingClientRect();
        setDragStart({
            x: e.clientX - svgRect.left - node.x * zoom,
            y: e.clientY - svgRect.top - node.y * zoom
        });
    }, [zoom]);

    const onDrag = useCallback((e) => {
        if (!dragging || !selectedNode) return;

        const svgRect = svgRef.current.getBoundingClientRect();
        const newX = (e.clientX - svgRect.left - dragStart.x) / zoom;
        const newY = (e.clientY - svgRect.top - dragStart.y) / zoom;

        setNodes(prev => prev.map(node =>
            node.id === selectedNode.id
                ? {
                    ...node,
                    x: newX,
                    y: newY,
                    data: { ...node.data, updatedAt: new Date().toISOString() }
                }
                : node
        ));
    }, [dragging, selectedNode, dragStart, zoom]);

    const endDrag = useCallback(() => {
        setDragging(false);
    }, []);

    // Zoom and pan functions
    const handleZoom = useCallback((delta) => {
        setZoom(prev => Math.max(0.1, Math.min(3, prev + delta)));
    }, []);

    const centerView = useCallback(() => {
        setZoom(1);
        setPan({ x: 0, y: 0 });
    }, []);

    // Connection functions
    const createConnection = useCallback((fromId, toId, type = CONNECTION_TYPES.NEUTRAL) => {
        const newConnection = {
            id: `conn-${fromId}-${toId}-${Date.now()}`,
            from: fromId,
            to: toId,
            type,
            label: '',
            strength: 1,
            createdAt: new Date().toISOString()
        };
        setConnections(prev => [...prev, newConnection]);
    }, []);

    const updateConnection = useCallback((connectionId, updates) => {
        setConnections(prev => prev.map(conn =>
            conn.id === connectionId ? { ...conn, ...updates } : conn
        ));
    }, []);

    const deleteConnection = useCallback((connectionId) => {
        setConnections(prev => prev.filter(conn => conn.id !== connectionId));
    }, []);

    // Advanced mindmap functions
    const resetMindmap = useCallback(() => {
        if (window.confirm('Are you sure you want to reset the mindmap? This action cannot be undone.')) {
            setNodes([{
                id: 1,
                text: subject,
                x: 400,
                y: 300,
                type: NODE_TYPES.CONCEPT,
                connections: [],
                color: '#007AFF',
                size: 'large',
                data: {
                    description: '',
                    tags: [],
                    priority: 'medium',
                    completed: false,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            }]);
            setNextId(2);
            setConnections([]);
            setSelectedNode(null);
            setSelectedNodes([]);
        }
    }, [subject]);

    const exportMindmap = useCallback(() => {
        const exportData = {
            version: '2.0',
            subject,
            nodes,
            connections,
            nextId,
            metadata: {
                createdAt: new Date().toISOString(),
                nodeCount: nodes.length,
                connectionCount: connections.length
            }
        };

        const data = JSON.stringify(exportData, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${subject}-advanced-mindmap-${new Date().toISOString().slice(0, 10)}.json`;
        link.click();
        URL.revokeObjectURL(url);
    }, [subject, nodes, connections, nextId]);

    const importMindmap = useCallback((e) => {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const importedData = JSON.parse(event.target.result);

                // Validate imported data
                if (importedData.nodes && Array.isArray(importedData.nodes)) {
                    setNodes(importedData.nodes);
                    setNextId(importedData.nextId || importedData.nodes.length + 1);
                    setConnections(importedData.connections || []);
                    setSelectedNode(null);
                    setSelectedNodes([]);
                } else {
                    throw new Error('Invalid mindmap format');
                }
            } catch (error) {
                alert('Error importing mindmap: Invalid file format or corrupted data');
                console.error('Import error:', error);
            }
        };
        reader.readAsText(file);

        // Reset file input
        e.target.value = '';
    }, []);

    // Add missing state variables for compatibility
    const [editMode, setEditMode] = useState(false);
    const [nodeColor, setNodeColor] = useState('#007AFF');
    const [connectionColor, setConnectionColor] = useState('#007AFF');

    // Legacy functions for compatibility
    const addNode = () => {
        if (!selectedNode) return;
        createNode(NODE_TYPES.CONCEPT, selectedNode);
    };

    const deleteSelectedNode = () => {
        if (!selectedNode || selectedNode.id === 1) return;
        deleteNode(selectedNode.id);
    };

    return (
        <div className={`advanced-mindmap-app ${darkMode ? 'theme-dark' : ''}`}>
            {/* Advanced Header with Glass Morphism */}
            <header className="mindmap-header">
                <div className="header-left">
                    <div className="mindmap-title">
                        <AccountTreeIcon className="title-icon" />
                        <h1>{subject} Mind Map</h1>
                        <span className="version-badge">v2.0</span>
                    </div>
                </div>

                <div className="header-center">
                    <div className="view-controls">
                        <button
                            className="control-btn"
                            onClick={() => handleZoom(-0.1)}
                            title="Zoom Out"
                        >
                            <ZoomOutIcon />
                        </button>
                        <span className="zoom-indicator">{Math.round(zoom * 100)}%</span>
                        <button
                            className="control-btn"
                            onClick={() => handleZoom(0.1)}
                            title="Zoom In"
                        >
                            <ZoomInIcon />
                        </button>
                        <button
                            className="control-btn"
                            onClick={centerView}
                            title="Center View"
                        >
                            <CenterFocusStrongIcon />
                        </button>
                    </div>
                </div>

                <div className="header-right">
                    <div className="action-controls">
                        <button
                            className="control-btn ai-btn"
                            onClick={() => setShowAIPanel(!showAIPanel)}
                            title="AI Assistant"
                        >
                            <SmartToyIcon />
                        </button>
                        <button
                            className="control-btn"
                            onClick={() => setIsFullscreen(!isFullscreen)}
                            title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
                        >
                            {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                        </button>
                        <button
                            className="control-btn export-btn"
                            onClick={exportMindmap}
                            title="Export Mindmap"
                        >
                            <FileDownloadIcon />
                        </button>
                        <label className="control-btn import-btn" title="Import Mindmap">
                            <FileUploadIcon />
                            <input
                                type="file"
                                accept=".json"
                                onChange={importMindmap}
                                style={{ display: 'none' }}
                            />
                        </label>
                        <button
                            className="control-btn danger-btn"
                            onClick={resetMindmap}
                            title="Reset Mindmap"
                        >
                            <RestartAltIcon />
                        </button>
                    </div>
                </div>
            </header>

            {/* Main Content Area */}
            <div className="mindmap-workspace">
                {/* Left Sidebar - Node Creation Tools */}
                <div className="left-sidebar">
                    <div className="sidebar-section">
                        <h3>Create Nodes</h3>
                        <div className="node-type-grid">
                            {Object.values(NODE_TYPES).map(type => {
                                const IconComponent = getNodeIcon(type);
                                return (
                                    <button
                                        key={type}
                                        className="node-type-btn"
                                        onClick={() => createNode(type, selectedNode)}
                                        title={`Create ${type} node`}
                                        style={{ '--node-color': getNodeColor(type) }}
                                    >
                                        <IconComponent />
                                        <span>{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                    </button>
                                );
                            })}
                        </div>
                    </div>

                    <div className="sidebar-section">
                        <h3>Quick Actions</h3>
                        <div className="quick-actions">
                            <button
                                className="action-btn primary"
                                onClick={addNode}
                                disabled={!selectedNode}
                                title="Add child node to selected"
                            >
                                <AddIcon />
                                Add Child
                            </button>
                            <button
                                className="action-btn danger"
                                onClick={deleteSelectedNode}
                                disabled={!selectedNode || selectedNode?.id === 1}
                                title="Delete selected node"
                            >
                                <DeleteIcon />
                                Delete
                            </button>
                        </div>
                    </div>

                    {selectedNode && (
                        <div className="sidebar-section">
                            <h3>Node Properties</h3>
                            <div className="node-properties">
                                <div className="property-group">
                                    <label>Type</label>
                                    <div className="node-type-display">
                                        {React.createElement(getNodeIcon(selectedNode.type))}
                                        <span>{selectedNode.type}</span>
                                    </div>
                                </div>
                                <div className="property-group">
                                    <label>Color</label>
                                    <input
                                        type="color"
                                        value={selectedNode.color}
                                        onChange={(e) => updateNodeColor(selectedNode.id, e.target.value)}
                                        className="color-picker"
                                    />
                                </div>
                                <div className="property-group">
                                    <label>Priority</label>
                                    <select
                                        value={selectedNode.data?.priority || 'medium'}
                                        onChange={(e) => updateNodeData(selectedNode.id, { priority: e.target.value })}
                                        className="priority-select"
                                    >
                                        <option value="low">Low</option>
                                        <option value="medium">Medium</option>
                                        <option value="high">High</option>
                                    </select>
                                </div>
                                <div className="property-group">
                                    <label>Description</label>
                                    <textarea
                                        value={selectedNode.data?.description || ''}
                                        onChange={(e) => updateNodeData(selectedNode.id, { description: e.target.value })}
                                        placeholder="Add description..."
                                        className="description-input"
                                        rows={3}
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Main Canvas Area */}
                <div
                    className="canvas-container"
                    ref={containerRef}
                    onMouseMove={onDrag}
                    onMouseUp={endDrag}
                    onMouseLeave={endDrag}
                >
                    <svg
                        ref={svgRef}
                        className="mindmap-canvas"
                        width="100%"
                        height="100%"
                        style={{
                            transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`,
                            transformOrigin: 'center center'
                        }}
                    >
                        {/* Render Connections */}
                        <defs>
                            <marker
                                id="arrowhead-correct"
                                markerWidth="10"
                                markerHeight="7"
                                refX="9"
                                refY="3.5"
                                orient="auto"
                            >
                                <polygon
                                    points="0 0, 10 3.5, 0 7"
                                    fill="#007AFF"
                                />
                            </marker>
                            <marker
                                id="arrowhead-incorrect"
                                markerWidth="10"
                                markerHeight="7"
                                refX="9"
                                refY="3.5"
                                orient="auto"
                            >
                                <polygon
                                    points="0 0, 10 3.5, 0 7"
                                    fill="#FF3B30"
                                />
                            </marker>
                            <marker
                                id="arrowhead-neutral"
                                markerWidth="10"
                                markerHeight="7"
                                refX="9"
                                refY="3.5"
                                orient="auto"
                            >
                                <polygon
                                    points="0 0, 10 3.5, 0 7"
                                    fill="#8E8E93"
                                />
                            </marker>
                        </defs>

                        {/* Render connections */}
                        {connections.map(connection => {
                            const fromNode = nodes.find(n => n.id === connection.from);
                            const toNode = nodes.find(n => n.id === connection.to);
                            if (!fromNode || !toNode) return null;

                            const connectionColor = connection.type === CONNECTION_TYPES.CORRECT ? '#007AFF' :
                                connection.type === CONNECTION_TYPES.INCORRECT ? '#FF3B30' : '#8E8E93';
                            const markerEnd = `url(#arrowhead-${connection.type})`;

                            return (
                                <motion.line
                                    key={connection.id}
                                    x1={fromNode.x}
                                    y1={fromNode.y}
                                    x2={toNode.x}
                                    y2={toNode.y}
                                    stroke={connectionColor}
                                    strokeWidth="3"
                                    strokeDasharray={connection.type === CONNECTION_TYPES.INCORRECT ? "8,4" : "none"}
                                    markerEnd={markerEnd}
                                    className="connection-line"
                                    initial={{ pathLength: 0, opacity: 0 }}
                                    animate={{ pathLength: 1, opacity: 1 }}
                                    transition={{ duration: 0.5, ease: "easeInOut" }}
                                    onClick={() => {
                                        // Connection editing logic here
                                        console.log('Connection clicked:', connection);
                                    }}
                                />
                            );
                        })}

                        {/* Render nodes */}
                        {nodes.map(node => {
                            const nodeSize = getNodeSize(node.type);
                            const isSelected = selectedNode?.id === node.id;
                            const IconComponent = getNodeIcon(node.type);

                            return (
                                <motion.g
                                    key={node.id}
                                    className="mindmap-node"
                                    onMouseDown={(e) => startDrag(node, e)}
                                    initial={{ opacity: 0, scale: 0.5 }}
                                    animate={{
                                        opacity: 1,
                                        scale: isSelected ? 1.1 : 1,
                                        filter: isSelected ? 'drop-shadow(0 0 12px rgba(0,122,255,0.5))' : 'none'
                                    }}
                                    transition={{
                                        duration: 0.3,
                                        type: "spring",
                                        stiffness: 300,
                                        damping: 20
                                    }}
                                    style={{ cursor: dragging ? 'grabbing' : 'grab' }}
                                >
                                    {/* Node Background */}
                                    <rect
                                        x={node.x - nodeSize.width / 2}
                                        y={node.y - nodeSize.height / 2}
                                        width={nodeSize.width}
                                        height={nodeSize.height}
                                        rx={nodeTypeConfig[node.type]?.shape === 'circle' ? nodeSize.width / 2 : 12}
                                        ry={nodeTypeConfig[node.type]?.shape === 'circle' ? nodeSize.height / 2 : 12}
                                        fill={node.color}
                                        stroke={isSelected ? '#007AFF' : 'rgba(255,255,255,0.3)'}
                                        strokeWidth={isSelected ? 3 : 1}
                                        className="node-background"
                                        style={{
                                            filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))',
                                            opacity: 0.9
                                        }}
                                    />

                                    {/* Node Icon */}
                                    <foreignObject
                                        x={node.x - 12}
                                        y={node.y - nodeSize.height / 2 + 8}
                                        width="24"
                                        height="24"
                                    >
                                        <div className="node-icon">
                                            <IconComponent style={{
                                                color: 'white',
                                                fontSize: '18px',
                                                filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))'
                                            }} />
                                        </div>
                                    </foreignObject>

                                    {/* Node Text */}
                                    <foreignObject
                                        x={node.x - nodeSize.width / 2 + 8}
                                        y={node.y - 8}
                                        width={nodeSize.width - 16}
                                        height="40"
                                    >
                                        <div className="node-content">
                                            <input
                                                type="text"
                                                value={node.text}
                                                onChange={(e) => updateNodeText(node.id, e.target.value)}
                                                onClick={(e) => e.stopPropagation()}
                                                className="node-text-input"
                                                style={{
                                                    fontSize: node.id === 1 ? '14px' : '12px',
                                                    fontWeight: node.id === 1 ? '600' : '500',
                                                    color: 'white',
                                                    textShadow: '0 1px 2px rgba(0,0,0,0.5)'
                                                }}
                                            />
                                        </div>
                                    </foreignObject>

                                    {/* Priority Indicator */}
                                    {node.data?.priority === 'high' && (
                                        <circle
                                            cx={node.x + nodeSize.width / 2 - 8}
                                            cy={node.y - nodeSize.height / 2 + 8}
                                            r="4"
                                            fill="#FF3B30"
                                            stroke="white"
                                            strokeWidth="1"
                                        />
                                    )}

                                    {/* Completion Indicator */}
                                    {node.data?.completed && (
                                        <circle
                                            cx={node.x + nodeSize.width / 2 - 8}
                                            cy={node.y + nodeSize.height / 2 - 8}
                                            r="6"
                                            fill="#28CD41"
                                            stroke="white"
                                            strokeWidth="2"
                                        />
                                    )}
                                </motion.g>
                            );
                        })}
                    </svg>
                </div>

                {/* Right Sidebar - AI Panel */}
                <AnimatePresence>
                    {showAIPanel && (
                        <motion.div
                            className="right-sidebar"
                            initial={{ x: 300, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            exit={{ x: 300, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                        >
                            <div className="sidebar-section">
                                <h3>AI Assistant</h3>
                                <div className="ai-suggestions">
                                    <p>AI-powered suggestions will appear here based on your mindmap structure and content.</p>
                                    <button className="ai-action-btn">
                                        <SmartToyIcon />
                                        Analyze Structure
                                    </button>
                                    <button className="ai-action-btn">
                                        <SmartToyIcon />
                                        Suggest Connections
                                    </button>
                                    <button className="ai-action-btn">
                                        <SmartToyIcon />
                                        Generate Ideas
                                    </button>
                                </div>
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </div>
    );
};

export default Mindmap;