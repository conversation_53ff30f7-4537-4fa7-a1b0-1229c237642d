@use "variables" as *;
@use "sass:color";
@use "_scrollbar" as *;

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Left Sidebar - macOS style
.left-sidebar {
  position: relative;
  width: $sidebar-width !important;
  @include transition(width);
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 0 0.3rem;
  border-right: 1px solid $border-color;
  background-color: $window-background;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    border-right: 1px solid $dark-border-color;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }

  &.collapsed {
    width: $sidebar-collapsed-width !important;
    padding: 0;

    .logo-text,
    .nav-text {
      display: none;
    }

    .nav-item {
      justify-content: center;
    }
  }

  .toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    width: 2rem;
    aspect-ratio: 1/1;
    @include transition(all);
    outline: none;
    position: absolute;
    top: 1.5rem;
    right: 0;
    z-index: 10;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    // Light theme
    color: $tertiary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-tertiary-text;
    }

    &:hover {
      // Light theme
      background-color: rgba($system-blue, 0.1);
      color: $system-blue;
      transform: scale(1.05);

      // Dark theme
      .theme-dark & {
        background-color: rgba($system-blue, 0.2);
        color: $system-blue;
      }
    }

    &:active {
      // Light theme
      background-color: rgba($system-blue, 0.2);
      transform: scale(0.95);

      // Dark theme
      .theme-dark & {
        background-color: rgba($system-blue, 0.3);
      }
    }

    * {
      margin: auto;
      padding: 0;
    }

    .material-icons {
      padding: 0;
      margin: 0;
      border-radius: 50%;
      width: 2rem;
      aspect-ratio: 1/1;
      display: flex;
      align-items: center;
      justify-content: center;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }
  }

  .sidebar-top {
    padding: .5rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid $separator-color;
    margin-bottom: 5px;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-separator-color;
    }

    .logo {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      gap: .5rem;
      font-weight: 600;
      width: 100%;
      font-size: 1.2rem;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      @include transition(all);

      img {
        width: 50px;
      }

      &:hover {
        transform: scale(1.02);
      }

      * {
        // Light theme
        color: $system-gold !important;

        // Dark theme
        .theme-dark & {
          color: $system-blue;
        }
      }

      span {
        font-size: 1.5rem;
        font-weight: 700;

        // Light theme
        color: $system-blue;

        // Dark theme
        .theme-dark & {
          color: $system-blue;
        }
      }

      .material-icons {
        font-size: 2rem;
        filter: drop-shadow(0 0 2px rgba($system-blue, 0.3));

        // Light theme
        color: $system-blue;

        // Dark theme
        .theme-dark & {
          color: $system-blue;
          filter: drop-shadow(0 0 3px rgba($system-blue, 0.5));
        }
      }
    }
  }

  .sidebar-middle {
    flex: 1;
    padding: 15px 0;
    margin-top: 0.5rem;
    overflow-y: auto;

    // Light theme
    border-bottom: 1px solid $separator-color;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-separator-color;
    }

    // Apply scrollbar styling
    @include ios-scrollbar;

    // Streak component styling
    .sidebar-streak {
      display: flex;
      align-items: center;
      padding: 0.8rem 1rem;
      margin: 0 0.5rem 1rem 0.5rem;
      border-radius: 10px;
      background-color: $secondary-background;
      border: 1px solid $border-color;
      transition: all 0.2s ease;
      text-decoration: none;
      cursor: pointer;

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        border-color: $dark-border-color;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
      }

      &.active {
        background-color: rgba($system-blue, 0.1);
        border-color: rgba($system-blue, 0.5);

        // Dark theme
        .theme-dark & {
          background-color: rgba($system-blue, 0.15);
          border-color: rgba($system-blue, 0.3);
        }

        .streak-count {
          color: darken($system-blue, 5%);

          // Dark theme
          .theme-dark & {
            color: lighten($system-blue, 10%);
          }
        }
      }

      &.collapsed {
        justify-content: center;
        padding: 0.8rem 0;

        .streak-icon img {
          width: 30px;
          height: 30px;
        }
      }

      .streak-icon {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 35px;
          height: 35px;
          transition: all 0.2s ease;
        }
      }

      .streak-info {
        margin-left: 0.8rem;

        .streak-count {
          font-size: 1.1rem;
          font-weight: 600;
          color: $system-blue;

          // Dark theme
          .theme-dark & {
            color: lighten($system-blue, 5%);
          }
        }

        .streak-text {
          font-size: 0.8rem;
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    ul {
      padding: 0 8px;

      li {
        margin-bottom: 5px;
        border-radius: 8px;
        overflow: hidden;
        @include transition(background-color);

        &:hover {
          // Light theme
          background-color: rgba($system-blue, 0.05);

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-blue, 0.1);
          }

          a {
            // Light theme
            color: $system-blue;

            // Dark theme
            .theme-dark & {
              color: $system-blue;
            }
          }
        }
      }

      a {
        background: none;
        display: flex;
        gap: 0.8rem;
        padding: 10px 12px;
        align-items: center;
        justify-content: flex-start;
        text-decoration: none;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        transition: all 0.2s ease;
        border-radius: 8px;
        font-size: 0.95rem;
        font-weight: 500;

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }

        .nav-icon {
          padding: 0;
          margin: 0;
          height: fit-content;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.2rem;

          * {
            margin: 0;
          }
        }

        * {
          margin: 0;
        }
      }
    }
  }

  .sidebar-bottom {
    position: relative;
    margin-top: auto;
    padding: 1rem .5rem 1rem .5rem;

    .profile-section {
      display: flex;
      align-items: center;
      gap: .8rem;
      cursor: pointer;
      padding: 0.7rem;
      border-radius: 10px;
      @include transition(all);
      margin: 0;

      // Light theme
      background-color: $secondary-background;
      color: $primary-text;
      border: 1px solid $border-color;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        color: $dark-primary-text;
        border: 1px solid $dark-border-color;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      &:hover {
        // Light theme
        background-color: rgba($system-blue, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);

        // Dark theme
        .theme-dark & {
          background-color: rgba($system-blue, 0.1);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }
      }

      &:active {
        transform: translateY(0);
      }

      &.collapsed {
        justify-content: center;
        padding: 0.7rem 0;
      }

      .profile-pic {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        // Light theme
        background-color: rgba($system-blue, 0.1);
        border: 1px solid rgba($system-blue, 0.3);

        // Dark theme
        .theme-dark & {
          background-color: rgba($system-blue, 0.2);
          border: 1px solid rgba($system-blue, 0.4);
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .profile-info {
        .profile-name {
          font-weight: 600;
          font-size: 0.9rem;
          text-wrap: no-wrap !important;

          // Light theme
          color: $primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        .profile-email {
          font-size: 0.8rem;
          opacity: 0.8;

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .profile-menu {
      position: absolute;
      bottom: 80px;
      left: .5rem;
      right: .5rem;
      border-radius: 12px;
      padding: 0.5rem 0;
      z-index: 100;
      overflow: hidden;

      // Light theme
      background-color: $window-background;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      border: 1px solid $border-color;
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
        border: 1px solid $dark-border-color;
        color: $dark-primary-text;
      }

      &.collapsed {
        left: 4.5rem;
        right: auto;
        min-width: 180px;
      }

      .profile-menu-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        cursor: pointer;
        @include transition(background-color);

        &:hover {
          // Light theme
          background-color: rgba($system-blue, 0.05);

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-blue, 0.1);
          }
        }

        &.active {
          // Light theme
          background-color: rgba($system-blue, 0.1);
          color: $system-blue;

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-blue, 0.15);
            color: $system-blue;
          }

          .profile-menu-icon,
          .profile-menu-text {
            // Light theme
            color: $system-blue;

            // Dark theme
            .theme-dark & {
              color: $system-blue;
            }
          }
        }

        .profile-menu-icon {
          margin-right: 1rem;
          display: flex;
          align-items: center;
          font-size: 1.1rem;

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }

        .profile-menu-text {
          font-size: 0.9rem;
          font-weight: 500;

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }
  }

  .nav-menu {
    list-style: none;
  }
}

// Navigation item - macOS style
.nav-item {
  padding: 0;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: left;
  gap: 10px;
  cursor: pointer;
  position: relative;
  @include transition(all);
  border-radius: 10px;
  overflow: hidden;
  background: none;
  border: none;

  .nav-link {
    display: flex;
    padding: .7rem !important;
    align-items: center;
    gap: 15px;
    text-decoration: none;
    width: 100%;
    @include transition;
    border-radius: 8px;

    .nav-text {
      width: 100%;
      text-align: left;
      margin-left: 5px;
      white-space: nowrap;
      font-weight: 500;

      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    .nav-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 24px;

      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    &.active {
      // Light theme
      background-color: rgba($system-blue, 0.1);
      border-left: 3px solid $system-blue;

      // Dark theme
      .theme-dark & {
        background-color: rgba($system-blue, 0.15);
        border-left: 3px solid $system-blue;
      }

      .nav-icon,
      .nav-text {
        // Light theme
        color: $system-blue;
        font-weight: 600;

        // Dark theme
        .theme-dark & {
          color: $system-blue;
        }
      }
    }

    &:hover:not(.active) {
      // Light theme
      background-color: rgba($system-blue, 0.05);

      // Dark theme
      .theme-dark & {
        background-color: rgba($system-blue, 0.1);
      }

      .nav-icon,
      .nav-text {
        // Light theme
        color: $system-blue;

        // Dark theme
        .theme-dark & {
          color: $system-blue;
        }
      }
    }
  }

  .material-icons {
    font-size: 1.5rem;
  }
}

// Tooltip for collapsed sidebar
.hovered-text {
  position: absolute;
  left: $sidebar-collapsed-width + 10px;
  top: 50%;
  transform: translateY(-50%);
  padding: 8px 16px;
  border-radius: 10px;
  z-index: 1000;
  white-space: nowrap;
  font-size: 0.9rem;
  font-weight: 500;
  animation: fadeIn 0.2s ease-out;

  // Light theme
  background-color: $window-background;
  color: $primary-text;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid $border-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    color: $dark-primary-text;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
    border: 1px solid $dark-border-color;
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -6px;
    transform: translateY(-50%) rotate(45deg);
    width: 12px;
    height: 12px;

    // Light theme
    background-color: $window-background;
    border-left: 1px solid $border-color;
    border-bottom: 1px solid $border-color;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      border-left: 1px solid $dark-border-color;
      border-bottom: 1px solid $dark-border-color;
    }
  }
}






// Main Header - macOS style
.main-header {
  height: $header-height;
  padding: 0.8rem 1.2rem;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 0;
  width: 100%;
  border: none !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;

  // Light theme
  background-color: rgba($window-background, 0.8);
  color: $primary-text;
  border-bottom: 1px solid $separator-color;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  // Dark theme
  .theme-dark & {
    background-color: rgba($dark-window-background, 0.8);
    color: $dark-primary-text;
    border-bottom: 1px solid $dark-separator-color;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  }

  .upload {
    position: relative;
    display: flex;
    justify-content: center;
    padding: .6rem 1.2rem;
    border-radius: 8px;
    gap: .6rem;
    align-items: center;
    @include transition(all);
    font-weight: 500;
    font-size: 0.95rem;
    letter-spacing: 0.2px;
    //position: absolute;
    //z-index: 1000;

    // Light theme
    background-color: $system-blue;
    color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

    // Dark theme
    .theme-dark & {
      background-color: $system-blue;
      color: white;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    &:hover {
      transform: translateY(-2px);

      // Light theme
      background-color: color.adjust($system-blue, $lightness: 5%);
      box-shadow: 0 4px 8px rgba(0, 122, 255, 0.25);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($system-blue, $lightness: 5%);
        box-shadow: 0 4px 8px rgba(0, 122, 255, 0.4);
      }
    }

    &:active {
      transform: translateY(0);

      // Light theme
      background-color: color.adjust($system-blue, $lightness: -5%);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($system-blue, $lightness: -5%);
      }
    }

    .material-icons {
      font-size: 1.2rem;
    }
  }

  .header-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 30px;
    padding-left: 0.5rem;

    .search-container {
      position: relative;
      display: flex;
      align-items: center;
      margin: 0;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover .search-input {
        // Light theme
        background-color: rgba($secondary-background, 0.8);

        // Dark theme
        .theme-dark & {
          background-color: rgba($dark-secondary-background, 0.8);
        }
      }

      input {
        margin: auto;
      }

      .search-icon {
        position: absolute;
        left: 12px;
        font-size: 1.1rem;
        z-index: 1;
        pointer-events: none;

        // Light theme
        color: $tertiary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }

      .search-input {
        padding: 8px 12px 8px 36px;
        border-radius: 10px;
        outline: none;
        width: 280px;
        @include transition(all);
        font-size: 0.9rem;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        // Light theme
        border: 1px solid $border-color;
        background-color: $secondary-background;
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          border: 1px solid $dark-border-color;
          background-color: $dark-secondary-background;
          color: $dark-primary-text;
        }

        &::placeholder {
          // Light theme
          color: $placeholder-text;

          // Dark theme
          .theme-dark & {
            color: $dark-placeholder-text;
          }
        }

        &:focus {
          // Light theme
          background-color: $window-background;
          border-color: $system-blue;
          box-shadow: 0 0 0 3px rgba($system-blue, 0.15);

          // Dark theme
          .theme-dark & {
            background-color: color.adjust($dark-window-background, $lightness: 3%);
            border-color: $system-blue;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.25);
          }

          &+.search-icon {
            // Light theme
            color: $system-blue;

            // Dark theme
            .theme-dark & {
              color: $system-blue;
            }
          }
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;


    .notification-wrapper {
      position: relative;

      .notification-button {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 1.1rem;
        outline: none;
        padding: 0.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        position: relative;
        z-index: 10;
        @include transition(all);

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }

        &:hover {
          // Light theme
          background-color: rgba($secondary-background, 0.8);
          color: $system-blue;
          transform: scale(1.05);

          // Dark theme
          .theme-dark & {
            background-color: rgba($dark-secondary-background, 0.8);
            color: $system-blue;
          }
        }

        &:active {
          transform: scale(0.95);
        }

        .notification-badge {
          position: absolute;
          top: 6px;
          right: 6px;
          background-color: $system-red;
          color: white;
          border-radius: 50%;
          width: 14px;
          height: 14px;
          font-size: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
      }

      .notification-popup {
        position: absolute;
        top: 50px;
        right: 0;
        width: 350px;
        border-radius: 12px;
        z-index: 100;
        transform-origin: top right;
        animation: fadeIn 0.2s ease-out;
        overflow: hidden;

        // Light theme
        background-color: $window-background;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        border: 1px solid $border-color;

        // Dark theme
        .theme-dark & {
          background-color: $dark-window-background;
          box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
          border: 1px solid $dark-border-color;
        }

        .notification-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 14px 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

          // Light theme
          border-bottom: 1px solid $separator-color;
          background-color: rgba($window-background, 0.8);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);

          // Dark theme
          .theme-dark & {
            border-bottom: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
          }

          h3 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }
          }

          .close-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 6px;
            border-radius: 50%;
            @include transition(all);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;

            // Light theme
            color: $secondary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-secondary-text;
            }

            &:hover {
              // Light theme
              background-color: rgba($secondary-background, 0.8);
              color: $system-blue;
              transform: scale(1.05);

              // Dark theme
              .theme-dark & {
                background-color: rgba($dark-secondary-background, 0.8);
                color: $system-blue;
              }
            }

            &:active {
              transform: scale(0.95);
            }
          }
        }

        .notification-list {
          max-height: 400px;
          overflow-y: auto;
          //@include scrollbar.custom-scrollbar;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

          .notification-item {
            display: flex;
            padding: 14px 16px;
            @include transition(all);
            position: relative;

            // Light theme
            border-bottom: 1px solid $separator-color;

            // Dark theme
            .theme-dark & {
              border-bottom: 1px solid $dark-separator-color;
            }

            &.unread {
              // Light theme
              background-color: rgba($secondary-background, 0.7);

              // Dark theme
              .theme-dark & {
                background-color: rgba($dark-secondary-background, 0.7);
              }

              .unread-dot {
                display: block;
              }
            }

            &:hover {
              // Light theme
              background-color: rgba($secondary-background, 0.9);

              // Dark theme
              .theme-dark & {
                background-color: rgba($dark-secondary-background, 0.9);
              }
            }

            .unread-dot {
              margin-right: 12px;
              display: none;
              width: 8px;
              height: 8px;
              border-radius: 50%;
              align-self: center;

              // Light theme
              background-color: $system-blue;

              // Dark theme
              .theme-dark & {
                background-color: $system-blue;
              }
            }

            .notification-content {
              flex: 1;
              display: flex;
              flex-direction: column;

              .notification-text {
                margin: 0 0 6px 0;
                font-size: 0.9rem;
                text-align: left;
                line-height: 1.4;

                // Light theme
                color: $primary-text;

                // Dark theme
                .theme-dark & {
                  color: $dark-primary-text;
                }
              }

              .notification-time {
                font-size: 0.75rem;
                text-align: left;
                width: 100%;

                // Light theme
                color: $tertiary-text;

                // Dark theme
                .theme-dark & {
                  color: $dark-tertiary-text;
                }
              }
            }
          }

          .empty-notifications {
            padding: 30px 20px;
            text-align: center;
            font-size: 0.9rem;
            font-style: italic;

            // Light theme
            color: $tertiary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-tertiary-text;
            }
          }
        }

        .notification-footer {
          padding: 14px 16px;
          text-align: center;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

          // Light theme
          border-top: 1px solid $separator-color;
          background-color: rgba($window-background, 0.8);
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);

          // Dark theme
          .theme-dark & {
            border-top: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
          }

          .view-all-btn {
            background: none;
            border: 1px solid $system-blue;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 6px;
            @include transition(all);
            display: inline-block;

            // Light theme
            color: $system-blue;

            // Dark theme
            .theme-dark & {
              color: $system-blue;
            }

            &:hover {
              // Light theme
              background-color: rgba($system-blue, 0.1);
              transform: translateY(-1px);

              // Dark theme
              .theme-dark & {
                background-color: rgba($system-blue, 0.15);
              }
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }

    .theme-toggle {
      background: transparent;
      border: none;
      cursor: pointer;
      font-size: 1.1rem;
      outline: none;
      padding: 0.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      @include transition(all);

      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }

      &:hover {
        // Light theme
        background-color: rgba($secondary-background, 0.8);
        color: $system-blue;
        transform: scale(1.05);

        // Dark theme
        .theme-dark & {
          background-color: rgba($dark-secondary-background, 0.8);
          color: $system-blue;
        }
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .notification-button {
      @extend .theme-toggle;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 6px;
        right: 6px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: $system-red;
        box-shadow: 0 0 0 2px $window-background;

        // Dark theme
        .theme-dark & {
          box-shadow: 0 0 0 2px $dark-window-background;
        }
      }
    }

    .upload {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      background-color: $system-blue;
      color: white;
      border: none;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      @include transition(all);

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &:hover {
        background-color: lighten($system-blue, 5%);
        transform: translateY(-1px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
        background-color: darken($system-blue, 5%);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      // Dark theme
      .theme-dark & {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

        &:hover {
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
        }
      }
    }

    .user-info {
      position: relative;

      .user-avatar-container {
        position: relative;
        cursor: pointer;

        .user-avatar {
          width: 38px;
          height: 38px;
          border-radius: 50%;
          background-color: $system-blue;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 0.95rem;
          @include transition(all);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 2px solid $window-background;

          // Dark theme
          .theme-dark & {
            border: 2px solid $dark-window-background;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          }

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

            // Dark theme
            .theme-dark & {
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            }
          }

          &:active {
            transform: scale(0.95);
          }
        }

        .avatar-tooltip {
          visibility: hidden;
          position: absolute;
          bottom: -40px;
          left: 50%;
          transform: translateX(-50%);
          padding: 8px 14px;
          border-radius: 8px;
          font-size: 0.8rem;
          font-weight: 500;
          white-space: nowrap;
          opacity: 0;
          @include transition(all);
          z-index: 10;

          // Light theme
          background-color: $window-background;
          color: $primary-text;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          border: 1px solid $border-color;

          // Dark theme
          .theme-dark & {
            background-color: $dark-window-background;
            color: $dark-primary-text;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
            border: 1px solid $dark-border-color;
          }

          &::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 12px;
            transform-origin: center;
            transform: translateX(-50%) rotate(45deg);

            // Light theme
            background-color: $window-background;
            border-left: 1px solid $border-color;
            border-top: 1px solid $border-color;

            // Dark theme
            .theme-dark & {
              background-color: $dark-window-background;
              border-left: 1px solid $dark-border-color;
              border-top: 1px solid $dark-border-color;
            }
          }
        }

        &:hover .avatar-tooltip {
          visibility: visible;
          opacity: 1;
          transform: translateX(-50%) translateY(5px);
        }
      }
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Content Area - macOS style
.content-area {
  flex: 1;
  padding: .75rem;
  position: relative;

  // Light theme
  background-color: $light-background;

  // Dark theme
  .theme-dark & {
    background-color: color.adjust($dark-background, $lightness: -2%);
  }

  .ai-chat-toggle {
    position: fixed;
    right: 20px;
    bottom: 100px;
    width: fit-content;
    height: 50px;
    border-radius: 50px;
    border: none;
    cursor: pointer;
    @include flex-center;
    z-index: 5;
    padding: 0 1.2rem;
    @include transition;

    // Light theme
    background-color: $color-primary;
    color: white;
    box-shadow: 0 2px 10px rgba(0, 122, 255, 0.2);

    // Dark theme
    .theme-dark & {
      background-color: $color-primary;
      color: white;
      box-shadow: 0 2px 10px rgba(0, 122, 255, 0.4);
    }

    &:hover {
      transform: translateY(-2px);

      // Light theme
      box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);

      // Dark theme
      .theme-dark & {
        box-shadow: 0 4px 15px rgba(0, 122, 255, 0.5);
      }
    }

    &:active {
      transform: translateY(0);
    }

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: .5rem;
    }
  }

  .content {
    margin: 0 auto;
    padding: 1rem;
    border-radius: 12px;

    // Light theme
    background-color: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

    // Dark theme
    .theme-dark & {
      background-color: $dark-background;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }
  }
}




























// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

.right-sidebar {
  position: relative;
  background: $window-background;
  border-left: 1px solid $border-color;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 350px;
  height: 100dvh;
  z-index: 0;
  transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out;

  // Dark mode
  .theme-dark & {
    background: $dark-window-background;
    border-left: 1px solid $dark-border-color;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }

  .ai-orb {
    position: absolute;
    top: 20%;
    right: 20%;
    width: 150px;
    height: 150px;
    z-index: 0;
    opacity: 0.7;

    .orb-core {
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: radial-gradient(circle at center,
          rgba($system-blue, 0.8) 0%,
          rgba($system-blue, 0.3) 70%,
          transparent 90%);
      filter: blur(10px);
      animation: pulse 4s infinite alternate;
    }

    .orb-ring {
      position: absolute;
      border-radius: 50%;
      border: 1px solid rgba($system-blue, 0.5);
      animation: rotate linear infinite;

      &.orb-ring-1 {
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        animation-duration: 30s;
      }

      &.orb-ring-2 {
        width: 70%;
        height: 70%;
        top: 15%;
        left: 15%;
        animation-duration: 25s;
        animation-direction: reverse;
      }

      &.orb-ring-3 {
        width: 40%;
        height: 40%;
        top: 30%;
        left: 30%;
        animation-duration: 20s;
        border-width: 2px;
      }
    }
  }

  .sidebar-header {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid $separator-color;

    // Dark mode
    .theme-dark & {
      border-bottom: 1px solid $dark-separator-color;
    }

    h3 {
      color: $primary-text;
      font-weight: 600;
      margin: 0;

      // Dark mode
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .toggle-btn {
      background: transparent;
      border: none;
      color: $system-blue;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: rgba($system-blue, 0.1);
        transform: rotate(90deg);
      }

      // Dark mode
      .theme-dark & {
        color: $system-blue;

        &:hover {
          background-color: rgba($system-blue, 0.2);
        }
      }
    }
  }

  .chat-area {
    position: relative;
    z-index: 1;
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 15px;
    background-color: $secondary-background;
    overflow: hidden; // Prevent overflow from child elements

    // Dark mode
    .theme-dark & {
      background-color: $dark-secondary-background;
    }

    .chat-bottom-container {
      display: flex;
      flex-direction: column-reverse;
      gap: 10px;
      margin-top: auto;
      width: 100%;

      .chat-input {
        display: flex;
        gap: 10px;
        align-items: center;
        width: 100%;
        position: relative;

        /* Ensure proper positioning */
        //z-index: 5;
        /* Keep above other elements */

        input {
          flex: 1;
          padding: 12px 15px;
          border-radius: 20px;
          border: 1px solid $border-color;
          background: $window-background;
          color: $primary-text;
          outline: none;
          transition: all 0.3s;
          font-size: 14px;
          margin: 0;

          &:focus {
            border-color: $system-blue;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.2);
          }

          &::placeholder {
            color: $placeholder-text;
          }

          // Dark mode
          .theme-dark & {
            border: 1px solid $dark-border-color;
            background: $dark-window-background;
            color: $dark-primary-text;

            &::placeholder {
              color: $dark-placeholder-text;
            }

            &:focus {
              border-color: $system-blue;
              box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
            }
          }
        }

        .send-btn {
          background: $system-blue;
          border: none;
          border-radius: 50%;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          &:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
          }

          &:active:not(:disabled) {
            transform: scale(0.98);
          }

          &:disabled {
            background: $system-gray;
            opacity: 0.5;
            cursor: not-allowed;
          }

          // Dark mode
          .theme-dark & {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

            &:hover:not(:disabled) {
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
            }
          }
        }
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
      padding-right: 5px;
      min-height: 0;
      /* Critical for scrolling to work in a flex container */
      height: calc(100vh - 250px);
      /* Fixed height to ensure scrolling works */

      // Scrollbar styling is now handled by _scrollbar.scss

      .energy-sphere-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20px auto;
        height: 220px;
        width: 220px;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
      }

      .message {
        padding: 12px 16px;
        margin-bottom: 12px;
        border-radius: 18px;
        max-width: 80%;
        line-height: 1.4;
        position: relative;
        animation: fadeIn 0.3s ease-out;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        // Dark mode
        .theme-dark & {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .message-content {
          p {
            margin: 0;
            color: $primary-text;
            text-align: left;

            // Dark mode
            .theme-dark & {
              color: $dark-primary-text;
            }
          }

          .audio-player {
            margin-top: 8px;

            audio {
              width: 100%;
              height: 30px;
              border-radius: 15px;
              outline: none;
            }
          }
        }

        &.ai {
          background: rgba($system-green, 0.1);
          border: 1px solid rgba($system-green, 0.5);
          align-self: flex-start;
          border-bottom-left-radius: 4px;

          // Dark mode
          .theme-dark & {
            background: rgba($system-green, 0.15);
            border: 1px solid rgba($system-green, 0.25);
          }

          &::before {
            content: '';
            position: absolute;
            bottom: -8px;
            left: -8px;
            width: 16px;
            height: 16px;
            background: rgba($system-green, 0.1);
            border-left: 1px solid rgba($system-green, 0.2);
            border-bottom: 1px solid rgba($system-green, 0.2);
            border-radius: 0 0 0 16px;
            clip-path: polygon(0 0, 100% 100%, 0 100%);

            // Dark mode
            .theme-dark & {
              background: rgba($system-green, 0.15);
              border-left: 1px solid rgba($system-green, 0.25);
              border-bottom: 1px solid rgba($system-green, 0.25);
            }
          }

          &.loading {
            background: rgba($system-blue, 0.05);

            // Dark mode
            .theme-dark & {
              background: rgba($system-blue, 0.1);
            }

            .typing-indicator {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 6px 0;

              span {
                display: inline-block;
                width: 8px;
                height: 8px;
                margin: 0 2px;
                background-color: $system-blue;
                border-radius: 50%;
                animation: typingBounce 1.4s infinite ease-in-out;
                opacity: 0.6;

                &:nth-child(1) {
                  animation-delay: 0s;
                }

                &:nth-child(2) {
                  animation-delay: 0.2s;
                }

                &:nth-child(3) {
                  animation-delay: 0.4s;
                }
              }
            }
          }
        }

        &.user {
          background: rgba($system-blue, 0.1);
          border: 1px solid rgba($system-blue, 0.5);
          align-self: flex-end;
          border-bottom-right-radius: 4px;

          // Dark mode
          .theme-dark & {
            background: rgba($system-blue, 0.15);
            border: 1px solid rgba($system-blue, 0.25);
          }

          .message-content p {
            color: $primary-text;
            text-align: left;

            // Dark mode
            .theme-dark & {
              color: $dark-primary-text;
            }
          }

          &::before {
            content: '';
            position: absolute;
            bottom: -8px;
            right: -8px;
            width: 16px;
            height: 16px;
            background: rgba($system-blue, 0.1);
            border-right: 1px solid rgba($system-blue, 0.2);
            border-bottom: 1px solid rgba($system-blue, 0.2);
            border-radius: 0 0 16px 0;
            clip-path: polygon(100% 0, 100% 100%, 0 100%);

            // Dark mode
            .theme-dark & {
              background: rgba($system-blue, 0.15);
              border-right: 1px solid rgba($system-blue, 0.25);
              border-bottom: 1px solid rgba($system-blue, 0.25);
            }
          }
        }
      }
    }

    .chat-controls {
      display: flex;
      gap: 10px;
      align-items: center;
      justify-content: flex-start;
      padding: 12px;
      background: $window-background;
      border-radius: 12px;
      border: 1px solid $border-color;
      margin-bottom: 10px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      width: 100%;
      flex-wrap: wrap;
      /* Allow wrapping on smaller screens */

      // Dark mode
      .theme-dark & {
        background: $dark-window-background;
        border: 1px solid $dark-border-color;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      input {
        flex: 1;
        padding: 12px 15px;
        border-radius: 20px;
        border: 1px solid $border-color;
        background: $secondary-background;
        color: $primary-text;
        outline: none;
        transition: all 0.3s;
        font-size: 14px;

        &:focus {
          border-color: $system-blue;
          box-shadow: 0 0 0 3px rgba($system-blue, 0.2);
        }

        &::placeholder {
          color: $placeholder-text;
        }

        // Dark mode
        .theme-dark & {
          border: 1px solid $dark-border-color;
          background: $dark-secondary-background;
          color: $dark-primary-text;

          &::placeholder {
            color: $dark-placeholder-text;
          }

          &:focus {
            border-color: $system-blue;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
          }
        }
      }



      .voice-btn {
        background: $system-blue;
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &.listening {
          background: $system-red;
          animation: pulse 1.5s infinite;
        }

        &:hover:not(:disabled) {
          transform: scale(1.05);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        }

        &:active:not(:disabled) {
          transform: scale(0.98);
        }

        &:disabled {
          background: $system-gray;
          opacity: 0.5;
          cursor: not-allowed;
        }

        // Dark mode
        .theme-dark & {
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

          &:hover:not(:disabled) {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
          }
        }
      }

      .speech-controls {
        display: flex;
        gap: 8px;
        margin: 0 8px;

        .speech-control-btn {
          background: $system-blue;
          border: none;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          &.paused {
            background: $system-green;
          }

          &.stop {
            background: $system-red;
          }

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: scale(0.98);
          }

          // Dark mode
          .theme-dark & {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

            &:hover {
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
            }
          }
        }
      }

      .voice-controls {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 5px;
        margin-left: auto;
        /* Push to the right */

        .tts-btn {
          background: $system-blue;
          border: none;
          border-radius: 50%;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          &.enabled {
            background: $system-green;
          }

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: scale(0.98);
          }

          // Dark mode
          .theme-dark & {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

            &:hover {
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
            }
          }
        }

        .voice-selector {
          display: flex;
          flex-direction: column;
          /* Changed to column for better layout */
          align-items: flex-start;
          gap: 8px;
          margin-top: 4px;
          background: $window-background;
          padding: 12px;
          border-radius: 8px;
          border: 1px solid $border-color;
          position: absolute;
          top: 45px;
          right: 0;
          z-index: 100;
          /* Higher z-index to ensure it's above other elements */
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          min-width: 120px;

          // Dark mode
          .theme-dark & {
            background: $dark-secondary-background;
            border: 1px solid $dark-border-color;
          }

          .voice-selector-label {
            color: $secondary-text;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            width: 100%;
            text-align: center;

            // Dark mode
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .voice-toggle {
            width: 100%;
            height: 32px;
            border-radius: 6px;
            background: rgba($system-blue, 0.1);
            border: 1px solid rgba($system-blue, 0.2);
            color: $secondary-text;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            margin: 2px 0;

            // Dark mode
            .theme-dark & {
              background: rgba($system-blue, 0.3);
              color: $dark-secondary-text;
            }

            &.selected {
              background: $system-green;
              color: white;
              transform: scale(1.05);
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

              // Dark mode
              .theme-dark & {
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
              }
            }

            &:hover {
              background: rgba($system-blue, 0.3);

              // Dark mode
              .theme-dark & {
                background: rgba($system-blue, 0.4);
              }
            }
          }
        }
      }

      .send-btn {
        background: $system-blue;
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        }

        &:active {
          transform: scale(0.98);
        }

        // Dark mode
        .theme-dark & {
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

          &:hover {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
          }
        }
      }
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba($system-red, 0.4);
      }

      70% {
        box-shadow: 0 0 0 10px rgba($system-red, 0);
      }

      100% {
        box-shadow: 0 0 0 0 rgba($system-red, 0);
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  &.collapsed {
    width: 0 !important;
    min-width: 0 !important;
    padding: 0 !important;
    border-left: none !important;
    margin-left: 0 !important;
    overflow: hidden !important;
    transition: width 0.3s ease-in-out;

    .ai-orb {
      display: none;
    }

    .sidebar-header,
    .chat-area {
      display: none;
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.7;
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba($system-blue, 0.4);
    }

    50% {
      opacity: 1;
      transform: scale(1.05);
      box-shadow: 0 0 0 5px rgba($system-blue, 0.1);
    }

    100% {
      opacity: 0.7;
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba($system-blue, 0);
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @keyframes typingBounce {

    0%,
    80%,
    100% {
      transform: scale(0.6);
      opacity: 0.6;
    }

    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
}