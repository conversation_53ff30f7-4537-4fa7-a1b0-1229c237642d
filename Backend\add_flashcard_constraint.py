"""
<PERSON><PERSON>t to add a unique constraint to the flashcards table to prevent duplicate flashcards.
This will:
1. Add a unique constraint on user_id, subject_id, chapter_id, and question
2. Remove duplicate flashcards, keeping the most recently created one

Run this script from the Backend directory:
    python add_flashcard_constraint.py
"""

import psycopg2
import psycopg2.extras
import os
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '5432')
DB_NAME = os.environ.get('DB_NAME', 'blueprint')
DB_USER = os.environ.get('DB_USER', 'postgres')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Lukind@1956')

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        logging.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logging.error(f"Error connecting to database: {str(e)}")
        raise

def remove_duplicate_flashcards(conn):
    """Remove duplicate flashcards, keeping the most recently created one"""
    try:
        cursor = conn.cursor()
        
        # Create a temporary table to identify duplicates
        cursor.execute("""
            CREATE TEMP TABLE duplicate_flashcards AS
            SELECT 
                id,
                user_id,
                subject_id,
                chapter_id,
                question,
                ROW_NUMBER() OVER (
                    PARTITION BY user_id, subject_id, chapter_id, question
                    ORDER BY created_at DESC
                ) as row_num
            FROM flashcards;
        """)
        
        # Count duplicates
        cursor.execute("""
            SELECT COUNT(*) FROM duplicate_flashcards WHERE row_num > 1;
        """)
        duplicate_count = cursor.fetchone()[0]
        logging.info(f"Found {duplicate_count} duplicate flashcards")
        
        if duplicate_count > 0:
            # Delete duplicates, keeping the most recent one
            cursor.execute("""
                DELETE FROM flashcards
                WHERE id IN (
                    SELECT id FROM duplicate_flashcards WHERE row_num > 1
                );
            """)
            conn.commit()
            logging.info(f"Removed {duplicate_count} duplicate flashcards")
        
        # Drop the temporary table
        cursor.execute("DROP TABLE duplicate_flashcards;")
        conn.commit()
        
        return duplicate_count
    except Exception as e:
        conn.rollback()
        logging.error(f"Error removing duplicate flashcards: {str(e)}")
        raise

def add_unique_constraint(conn):
    """Add a unique constraint to prevent duplicate flashcards"""
    try:
        cursor = conn.cursor()
        
        # Check if the constraint already exists
        cursor.execute("""
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'flashcards'
            AND constraint_name = 'unique_flashcard_question';
        """)
        
        if cursor.fetchone() is None:
            # Add the unique constraint
            cursor.execute("""
                ALTER TABLE flashcards
                ADD CONSTRAINT unique_flashcard_question
                UNIQUE (user_id, subject_id, chapter_id, question);
            """)
            conn.commit()
            logging.info("Added unique constraint to flashcards table")
            return True
        else:
            logging.info("Unique constraint already exists")
            return False
    except Exception as e:
        conn.rollback()
        logging.error(f"Error adding unique constraint: {str(e)}")
        raise

def main():
    """Main function to run the script"""
    try:
        conn = connect_to_db()
        
        # Remove duplicate flashcards
        duplicate_count = remove_duplicate_flashcards(conn)
        
        # Add unique constraint
        constraint_added = add_unique_constraint(conn)
        
        # Close connection
        conn.close()
        
        logging.info("Script completed successfully")
        print(f"Removed {duplicate_count} duplicate flashcards")
        if constraint_added:
            print("Added unique constraint to flashcards table")
        else:
            print("Unique constraint already exists")
            
    except Exception as e:
        logging.error(f"Script failed: {str(e)}")
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
