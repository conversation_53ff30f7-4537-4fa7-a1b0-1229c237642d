  // macOS-inspired color palette
  $system-blue: #007AFF;
  $system-green: #28CD41;
  $system-red: #FF3B30;
  $system-orange: #FF9500;
  $system-yellow: #FFCC00;
  $system-gray: #8E8E93;
  $system-light-gray: #E5E5EA;
  $system-dark-gray: #636366;

  // Background colors
  $window-background: #FFFFFF;
  $secondary-background: #F2F2F7;

  // Text colors
  $primary-text: #000000;
  $secondary-text: #3C3C43;
  $tertiary-text: #8E8E93;
  $placeholder-text: #C7C7CC;

  // Border colors
  $border-color: #C6C6C8;
  $separator-color: #D1D1D6;

  // Dark mode colors
  $dark-window-background: #1E1E1E;
  $dark-secondary-background: #2C2C2C;
  $dark-primary-text: #FFFFFF;
  $dark-secondary-text: #EBEBF5;
  $dark-tertiary-text: #EBEBF599; // with opacity
  $dark-placeholder-text: #EBEBF54D; // with opacity
  $dark-border-color: #38383A;
  $dark-separator-color: #38383A;

  // Calendar Card
  .calendar-card {
      background-color: $window-background;
      border-radius: 12px;
      border: 1px solid $border-color;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      z-index: 1;
      padding: .5rem;
      width: 100%;
      margin: 0;
      height: 100%;
      transition: all 0.2s ease;

      &:focus {
          outline: 2px solid $system-blue;
          outline-offset: 2px;
      }

      .card-title {
          font-size: 18px;
          font-weight: 600;
          color: $primary-text;
          margin-bottom: 16px;
          text-align: center;
      }

      &.theme-dark {
          background-color: $dark-window-background;
          border-color: $dark-border-color;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

          .card-title {
              color: $dark-primary-text;
          }
      }

      .calendar-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: .5rem;
          padding: 0 1rem;
          border-radius: 8px;
          background: $secondary-background;
          border: none;
          color: $primary-text;

          .month-year {
              font-size: 16px;
              font-weight: 500;
              color: $primary-text;
          }

          .nav-button {
              background: none;
              border: none;
              font-size: 14px;
              cursor: pointer;
              color: $system-blue;
              transition: all 0.2s ease;
              border-radius: 50%;
              width: 28px;
              height: 28px;
              display: flex;
              align-items: center;
              justify-content: center;

              &:hover {
                  background-color: rgba($system-blue, 0.1);
              }

              &:focus {
                  outline: 2px solid $system-blue;
                  outline-offset: 2px;
              }
          }
      }

      &.theme-dark .calendar-header {
          background: $dark-secondary-background;
          color: $dark-primary-text;

          .month-year {
              color: $dark-primary-text;
          }

          .nav-button {
              color: $system-blue;

              &:hover {
                  background-color: rgba($system-blue, 0.2);
              }
          }
      }

      .calendar-grid {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          gap: 6px;
          padding: 0;
          margin: 0;
          text-align: center;

          &.small {
              gap: 4px;

              .day-header {
                  font-size: 11px;
                  padding: 0;
                  margin: 0;
              }

              .day {
                  width: 24px;
                  height: 24px;
                  font-size: 11px;

                  .day-number {
                      font-size: 11px;
                  }

                  .event-indicator {
                      width: 4px;
                      height: 4px;
                      bottom: -2px;
                  }

                  .streak-mark {
                      font-size: 8px;
                      top: -2px;
                      right: -2px;
                  }
              }
          }

          .day-header {
              font-size: 12px;
              color: $tertiary-text;
              text-align: center;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0;
              font-weight: 500;
          }

          .day {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 25px;
              height: 25px;
              padding: 0;
              font-size: 13px;
              color: $primary-text;
              cursor: pointer;
              transition: all 0.2s ease;
              margin: auto;
              border-radius: 50%;

              &.empty {
                  background: none;
                  cursor: default;
                  color: $placeholder-text;
              }

              &.has-events {
                  font-weight: 600;
              }

              &.streak-day {
                  background: $system-green;
                  color: white;
              }

              &.selected {
                  background: rgba($system-blue, 0.2);
                  font-weight: 600;
              }

              &:hover:not(.empty) {
                  background: rgba($system-blue, 0.1);
              }

              &:focus {
                  outline: 2px solid $system-blue;
                  outline-offset: 2px;
              }

              .day-number {
                  z-index: 1;
                  margin: auto;
              }

              .event-indicator {
                  position: absolute;
                  bottom: 2px;
                  width: 5px;
                  height: 5px;
                  background: $system-blue;
                  border-radius: 50%;
              }

              .streak-mark {
                  position: absolute;
                  top: -2px;
                  right: -2px;
                  font-size: 10px;
                  color: $system-green;
              }

              .tooltip {
                  position: absolute;
                  top: -36px;
                  left: 50%;
                  transform: translateX(-50%);
                  background: $secondary-background;
                  color: $primary-text;
                  font-size: 12px;
                  padding: 6px 10px;
                  border-radius: 8px;
                  opacity: 0;
                  pointer-events: none;
                  transition: opacity 0.2s ease;
                  z-index: 10;
                  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                  border: 1px solid $border-color;
              }

              &:hover .tooltip {
                  opacity: 1;
              }
          }
      }

      &.theme-dark .calendar-grid {
          .day-header {
              color: $dark-tertiary-text;
          }

          .day {
              color: $dark-primary-text;

              &.empty {
                  color: $dark-placeholder-text;
              }

              &:hover:not(.empty) {
                  background: rgba($system-blue, 0.2);
              }

              .tooltip {
                  background: $dark-secondary-background;
                  color: $dark-primary-text;
                  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
                  border-color: $dark-border-color;

                  .streak-note {
                      color: $system-green;
                  }
              }
          }
      }
  }

  // Modal Overlay
  .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.25);
      backdrop-filter: blur(5px);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      animation: fadeIn 0.2s ease;

      @keyframes fadeIn {
          from {
              opacity: 0;
          }

          to {
              opacity: 1;
          }
      }

      &.theme-dark {
          background: rgba(0, 0, 0, 0.4);
      }
  }

  // Modal Window
  .modal-window {
      background: $window-background;
      backdrop-filter: blur(20px);
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      width: 550px;
      max-width: 90vw;
      max-height: 80vh;
      overflow-y: auto;
      transform: translateY(0);
      animation: slideIn 0.2s ease;
      border: 1px solid $border-color;

      @keyframes slideIn {
          from {
              transform: translateY(20px);
              opacity: 0;
          }

          to {
              transform: translateY(0);
              opacity: 1;
          }
      }

      &.theme-dark {
          background: $dark-window-background;
          border-color: $dark-border-color;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      }

      .modal-header {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid $separator-color;

          .traffic-lights {
              display: flex;
              gap: 8px;

              .traffic-light {
                  width: 12px;
                  height: 12px;
                  border-radius: 50%;
                  border: none;
                  cursor: pointer;
                  transition: all 0.2s ease;

                  &.red {
                      background: $system-red;
                  }

                  &.yellow {
                      background: $system-yellow;
                  }

                  &.green {
                      background: $system-green;
                  }

                  &:hover {
                      filter: brightness(1.1);
                  }

                  &:disabled {
                      opacity: 0.4;
                      cursor: default;
                  }
              }
          }

          .modal-title {
              font-size: 16px;
              font-weight: 600;
              color: $primary-text;
              flex: 1;
              text-align: center;
              margin: 0;
          }
      }

      &.theme-dark .modal-header {
          border-bottom-color: $dark-separator-color;

          .modal-title {
              color: $dark-primary-text;
          }
      }

      .modal-content {
          padding: 20px;

          .summary-section {
              margin-bottom: 24px;

              p {
                  font-size: 14px;
                  color: $secondary-text;
                  margin: 0 0 8px 0;
                  line-height: 1.5;

                  .streak-note {
                      color: $system-green;
                      margin-left: 8px;
                      font-weight: 500;
                  }
              }
          }
      }

      &.theme-dark .modal-content {
          .summary-section p {
              color: $dark-secondary-text;
          }
      }

      .event-form {
          margin-bottom: 24px;

          h3 {
              font-size: 15px;
              font-weight: 600;
              margin: 0 0 16px;
              color: $primary-text;
          }

          .form-group {
              margin-bottom: 16px;

              label {
                  font-size: 13px;
                  color: $secondary-text;
                  display: block;
                  margin-bottom: 6px;
                  font-weight: 500;
              }

              input,
              textarea {
                  width: 100%;
                  padding: 10px 12px;
                  border: 1px solid $border-color;
                  border-radius: 8px;
                  font-size: 14px;
                  box-sizing: border-box;
                  background-color: $window-background;
                  color: $primary-text;
                  transition: all 0.2s ease;

                  &:focus {
                      outline: none;
                      border-color: $system-blue;
                      box-shadow: 0 0 0 3px rgba($system-blue, 0.2);
                  }

                  &::placeholder {
                      color: $placeholder-text;
                  }
              }

              textarea {
                  height: 100px;
                  resize: vertical;
              }
          }

          .form-buttons {
              display: flex;
              gap: 10px;
              justify-content: flex-end;
              margin-top: 20px;

              .submit-button,
              .cancel-button {
                  background: $system-blue;
                  color: white;
                  border: none;
                  border-radius: 8px;
                  padding: 8px 16px;
                  font-size: 14px;
                  font-weight: 500;
                  cursor: pointer;
                  transition: all 0.2s ease;

                  &:hover {
                      filter: brightness(1.1);
                  }

                  &:focus {
                      outline: none;
                      box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
                  }

                  &:disabled {
                      opacity: 0.5;
                      cursor: not-allowed;
                  }
              }

              .cancel-button {
                  background: transparent;
                  color: $system-blue;
                  border: 1px solid $system-blue;

                  &:hover {
                      background: rgba($system-blue, 0.1);
                  }
              }
          }
      }

      &.theme-dark .event-form {
          h3 {
              color: $dark-primary-text;
          }

          .form-group {
              label {
                  color: $dark-secondary-text;
              }

              input,
              textarea {
                  background-color: $dark-secondary-background;
                  color: $dark-primary-text;
                  border-color: $dark-border-color;

                  &:focus {
                      border-color: $system-blue;
                      background-color: rgba($dark-secondary-background, 0.8);
                  }

                  &::placeholder {
                      color: $dark-placeholder-text;
                  }
              }
          }

          .form-buttons {
              .cancel-button {
                  color: $system-blue;

                  &:hover {
                      background: rgba($system-blue, 0.2);
                  }
              }
          }
      }

      .events-list {
          margin-bottom: 24px;

          h3 {
              font-size: 15px;
              font-weight: 600;
              margin: 0 0 16px;
              color: $primary-text;
          }

          p {
              font-size: 14px;
              color: $secondary-text;
              margin: 0 0 8px 0;
              line-height: 1.5;
          }

          .event-items {
              list-style: none;
              padding: 0;
              margin: 0;

              .event-item {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 12px;
                  border-radius: 8px;
                  background: $secondary-background;
                  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                  margin-bottom: 10px;
                  transition: all 0.2s ease;
                  border: 1px solid transparent;

                  &:hover {
                      border-color: $border-color;
                      transform: translateY(-1px);
                      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
                  }

                  .event-details {
                      flex: 1;

                      strong {
                          font-size: 14px;
                          color: $primary-text;
                          font-weight: 600;
                      }

                      span {
                          font-size: 12px;
                          color: $tertiary-text;
                          margin-left: 8px;
                      }

                      p {
                          font-size: 13px;
                          color: $secondary-text;
                          margin: 4px 0 0;
                          line-height: 1.4;
                      }
                  }

                  .event-actions {
                      display: flex;
                      gap: 10px;

                      .edit-button,
                      .delete-button {
                          background: $system-blue;
                          color: white;
                          border: none;
                          border-radius: 8px;
                          padding: 6px 10px;
                          font-size: 12px;
                          font-weight: 500;
                          cursor: pointer;
                          transition: all 0.2s ease;
                          display: flex;
                          align-items: center;
                          justify-content: center;

                          &:hover {
                              filter: brightness(1.1);
                          }

                          &:focus {
                              outline: none;
                              box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
                          }
                      }

                      .delete-button {
                          background: $system-red;

                          &:hover {
                              filter: brightness(1.1);
                          }

                          &:focus {
                              box-shadow: 0 0 0 3px rgba($system-red, 0.3);
                          }
                      }
                  }
              }
          }
      }

      &.theme-dark .events-list {
          h3 {
              color: $dark-primary-text;
          }

          p {
              color: $dark-secondary-text;
          }

          .event-items .event-item {
              background: $dark-secondary-background;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

              &:hover {
                  border-color: $dark-border-color;
                  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
              }

              .event-details {
                  strong {
                      color: $dark-primary-text;
                  }

                  span {
                      color: $dark-tertiary-text;
                  }

                  p {
                      color: $dark-secondary-text;
                  }
              }
          }
      }

      .mini-calendar {
          h3 {
              font-size: 16px;
              font-weight: 600;
              margin: 0 0 12px;
          }
      }

      &.theme-dark .mini-calendar h3 {
          color: $dark-primary-text;
      }
  }

  .modal-footer {
      padding: 16px;
      border-top: 1px solid $separator-color;
      text-align: right;
      display: flex;
      justify-content: flex-end;

      .close-button {
          background: $system-blue;
          color: white;
          border: none;
          border-radius: 8px;
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
              filter: brightness(1.1);
          }

          &:focus {
              outline: none;
              box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
          }
      }
  }

  .theme-dark .modal-footer {
      border-top-color: $dark-separator-color;
  }


  // Event Modal - macOS style
  .event-modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(8px);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      animation: fadeIn 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);

      &.theme-dark {
          background: rgba(0, 0, 0, 0.4);
      }
  }

  .event-modal {
      background: rgba($window-background, 0.95);
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.05);
      width: 550px;
      max-width: 90vw;
      max-height: 80vh;
      overflow-y: auto;
      backdrop-filter: blur(20px);
      animation: modalSlideIn 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
      transform-origin: center;

      &.theme-dark {
          background: rgba($dark-window-background, 0.95);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.1);
      }

      @keyframes modalSlideIn {
          from {
              opacity: 0;
              transform: scale(0.95);
          }

          to {
              opacity: 1;
              transform: scale(1);
          }
      }

      .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          border-bottom: 1px solid $separator-color;
          position: relative;

          // macOS-style traffic lights
          .traffic-lights {
              position: absolute;
              left: 16px;
              top: 16px;
              display: flex;
              gap: 8px;

              .traffic-light {
                  width: 12px;
                  height: 12px;
                  border-radius: 50%;
                  border: none;
                  cursor: pointer;
                  transition: all 0.2s ease;

                  &.red {
                      background: $system-red;

                      &:hover {
                          background: darken($system-red, 5%);
                      }
                  }

                  &.yellow {
                      background: $system-yellow;

                      &:hover {
                          background: darken($system-yellow, 5%);
                      }
                  }

                  &.green {
                      background: $system-green;

                      &:hover {
                          background: darken($system-green, 5%);
                      }
                  }
              }
          }

          h3 {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: $primary-text;
              text-align: center;
              width: 100%;
          }

          .close-button {
              background: none;
              border: none;
              font-size: 16px;
              cursor: pointer;
              color: $tertiary-text;
              width: 28px;
              height: 28px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 50%;
              transition: all 0.2s ease;
              z-index: 1;

              &:hover {
                  background: rgba($system-blue, 0.1);
                  color: $system-blue;
              }
          }
      }

      &.theme-dark .modal-header {
          border-bottom-color: $dark-border-color;

          h3 {
              color: $dark-primary-text;
          }

          .close-button {
              color: $dark-tertiary-text;

              &:hover {
                  background: rgba($system-blue, 0.2);
                  color: $system-blue;
              }
          }
      }

      .modal-content {
          padding: 24px;

          .event-form {
              margin-bottom: 28px;

              h4 {
                  margin: 0 0 20px;
                  font-size: 16px;
                  font-weight: 600;
                  color: $primary-text;
                  position: relative;

                  &:after {
                      content: '';
                      position: absolute;
                      left: 0;
                      bottom: -8px;
                      width: 40px;
                      height: 2px;
                      background: $system-blue;
                      border-radius: 2px;
                  }
              }

              .form-group {
                  margin-bottom: 20px;
                  position: relative;

                  label {
                      display: block;
                      margin-bottom: 8px;
                      font-size: 13px;
                      font-weight: 500;
                      color: $secondary-text;
                      letter-spacing: 0.2px;
                  }

                  input,
                  textarea,
                  select {
                      width: 100%;
                      padding: 12px 14px;
                      border: 1px solid $border-color;
                      border-radius: 8px;
                      font-size: 14px;
                      background-color: $secondary-background;
                      color: $primary-text;
                      transition: all 0.2s ease;
                      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
                      -webkit-appearance: none;

                      &:focus {
                          outline: none;
                          border-color: $system-blue;
                          box-shadow: 0 0 0 3px rgba($system-blue, 0.15), inset 0 1px 2px rgba(0, 0, 0, 0.05);
                          background-color: $window-background;
                      }

                      &::placeholder {
                          color: $placeholder-text;
                      }
                  }

                  select {
                      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%238E8E93' d='M6 8.825l-4.175-4.15.875-.875L6 7.1 9.3 3.8l.875.875z'/%3E%3C/svg%3E");
                      background-repeat: no-repeat;
                      background-position: right 14px center;
                      padding-right: 32px;
                  }

                  textarea {
                      height: 120px;
                      resize: vertical;
                      line-height: 1.5;
                  }

                  // Date input styling
                  input[type="date"] {
                      padding-right: 10px;
                  }

                  // Time input styling
                  input[type="time"] {
                      padding-right: 10px;
                  }
              }

              .form-actions {
                  display: flex;
                  gap: 12px;
                  justify-content: flex-end;
                  margin-top: 28px;

                  button {
                      padding: 10px 18px;
                      border: none;
                      border-radius: 8px;
                      font-size: 14px;
                      font-weight: 500;
                      cursor: pointer;
                      transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
                      letter-spacing: 0.3px;

                      &.save-button {
                          background: $system-blue;
                          color: white;
                          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                          &:hover {
                              transform: translateY(-1px);
                              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                              background: lighten($system-blue, 5%);
                          }

                          &:active {
                              transform: translateY(0);
                              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                              background: darken($system-blue, 5%);
                          }

                          &:disabled {
                              opacity: 0.5;
                              cursor: not-allowed;
                              transform: none;
                              box-shadow: none;
                          }

                          &:focus {
                              outline: none;
                              box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
                          }
                      }

                      &.cancel-button {
                          background: $secondary-background;
                          color: $secondary-text;
                          border: 1px solid $border-color;
                          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

                          &:hover {
                              transform: translateY(-1px);
                              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
                              background: lighten($secondary-background, 2%);
                              color: $primary-text;
                          }

                          &:active {
                              transform: translateY(0);
                              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                              background: darken($secondary-background, 2%);
                          }

                          &:focus {
                              outline: none;
                              box-shadow: 0 0 0 3px rgba($system-blue, 0.15);
                          }
                      }
                  }
              }
          }

          &.theme-dark .event-form {
              h4 {
                  color: $dark-primary-text;
              }

              .form-group {
                  label {
                      color: $dark-secondary-text;
                  }

                  input,
                  textarea,
                  select {
                      background-color: $dark-secondary-background;
                      color: $dark-primary-text;
                      border-color: $dark-border-color;
                      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);

                      &:focus {
                          border-color: $system-blue;
                          background-color: rgba($dark-secondary-background, 0.8);
                          box-shadow: 0 0 0 3px rgba($system-blue, 0.25), inset 0 1px 2px rgba(0, 0, 0, 0.2);
                      }

                      &::placeholder {
                          color: $dark-placeholder-text;
                      }
                  }

                  select {
                      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23EBEBF599' d='M6 8.825l-4.175-4.15.875-.875L6 7.1 9.3 3.8l.875.875z'/%3E%3C/svg%3E");
                  }
              }

              .form-actions {
                  button.cancel-button {
                      background: $dark-secondary-background;
                      color: $dark-secondary-text;
                      border-color: $dark-border-color;

                      &:hover {
                          background: lighten($dark-secondary-background, 5%);
                          color: $dark-primary-text;
                      }

                      &:active {
                          background: darken($dark-secondary-background, 5%);
                      }
                  }
              }
          }

          .events-list {
              h4 {
                  margin: 0 0 20px;
                  font-size: 16px;
                  font-weight: 600;
                  color: $primary-text;
                  position: relative;

                  &:after {
                      content: '';
                      position: absolute;
                      left: 0;
                      bottom: -8px;
                      width: 40px;
                      height: 2px;
                      background: $system-blue;
                      border-radius: 2px;
                  }
              }

              .empty-state {
                  text-align: center;
                  padding: 30px 0;

                  svg {
                      width: 60px;
                      height: 60px;
                      margin-bottom: 16px;
                      color: $tertiary-text;
                  }

                  p {
                      font-size: 14px;
                      color: $secondary-text;
                      margin: 0;
                  }
              }

              .event-item {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  padding: 16px;
                  background: $secondary-background;
                  border-radius: 10px;
                  margin-bottom: 12px;
                  transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
                  border: 1px solid transparent;
                  position: relative;
                  overflow: hidden;

                  &:before {
                      content: '';
                      position: absolute;
                      left: 0;
                      top: 0;
                      bottom: 0;
                      width: 4px;
                      background: $system-blue;
                      opacity: 0;
                      transition: opacity 0.2s ease;
                  }

                  &:hover {
                      border-color: $border-color;
                      transform: translateY(-2px);
                      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);

                      &:before {
                          opacity: 1;
                      }
                  }

                  &.priority-high:before {
                      background: $system-red;
                      opacity: 1;
                  }

                  &.priority-medium:before {
                      background: $system-orange;
                      opacity: 1;
                  }

                  &.priority-low:before {
                      background: $system-green;
                      opacity: 1;
                  }

                  .event-details {
                      flex: 1;
                      padding-right: 16px;

                      h5 {
                          margin: 0 0 8px;
                          font-size: 15px;
                          font-weight: 600;
                          color: $primary-text;
                          display: flex;
                          align-items: center;

                          .event-icon {
                              margin-right: 8px;
                              color: $system-blue;
                              font-size: 16px;
                          }
                      }

                      .event-meta {
                          display: flex;
                          align-items: center;
                          margin-bottom: 8px;

                          .event-date {
                              display: flex;
                              align-items: center;
                              font-size: 13px;
                              color: $tertiary-text;
                              margin-right: 16px;

                              svg {
                                  margin-right: 4px;
                                  font-size: 14px;
                              }
                          }

                          .event-time {
                              display: flex;
                              align-items: center;
                              font-size: 13px;
                              color: $tertiary-text;

                              svg {
                                  margin-right: 4px;
                                  font-size: 14px;
                              }
                          }
                      }

                      p {
                          margin: 8px 0 0;
                          font-size: 14px;
                          color: $secondary-text;
                          line-height: 1.5;
                      }

                      .countdown {
                          display: inline-block;
                          margin-top: 10px;
                          font-weight: 500;
                          color: $system-blue;
                          font-size: 13px;
                          background: rgba($system-blue, 0.1);
                          padding: 4px 8px;
                          border-radius: 4px;
                      }
                  }

                  .event-actions {
                      display: flex;
                      gap: 8px;
                      opacity: 0.7;
                      transition: opacity 0.2s ease;

                      &:hover {
                          opacity: 1;
                      }

                      button {
                          width: 32px;
                          height: 32px;
                          padding: 0;
                          border: none;
                          border-radius: 6px;
                          font-size: 14px;
                          cursor: pointer;
                          transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
                          display: flex;
                          align-items: center;
                          justify-content: center;
                          background: transparent;
                          color: $tertiary-text;

                          &:hover {
                              background: rgba($system-blue, 0.1);
                              color: $system-blue;
                              transform: translateY(-1px);
                          }

                          &:active {
                              transform: translateY(0);
                          }

                          &:focus {
                              outline: none;
                              box-shadow: 0 0 0 2px rgba($system-blue, 0.3);
                          }

                          &.edit-button {
                              &:hover {
                                  color: $system-blue;
                              }
                          }

                          &.delete-button {
                              &:hover {
                                  background: rgba($system-red, 0.1);
                                  color: $system-red;
                              }

                              &:focus {
                                  box-shadow: 0 0 0 2px rgba($system-red, 0.3);
                              }
                          }
                      }
                  }
              }
          }

          &.theme-dark .events-list {
              h4 {
                  color: $dark-primary-text;
              }

              .empty-state {
                  svg {
                      color: $dark-tertiary-text;
                  }

                  p {
                      color: $dark-secondary-text;
                  }
              }

              .event-item {
                  background: $dark-secondary-background;
                  border-color: transparent;

                  &:hover {
                      border-color: $dark-border-color;
                      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                  }

                  .event-details {
                      h5 {
                          color: $dark-primary-text;
                      }

                      .event-meta {

                          .event-date,
                          .event-time {
                              color: $dark-tertiary-text;
                          }
                      }

                      p {
                          color: $dark-secondary-text;
                      }

                      .countdown {
                          background: rgba($system-blue, 0.2);
                      }
                  }

                  .event-actions {
                      button {
                          color: $dark-tertiary-text;

                          &:hover {
                              background: rgba($system-blue, 0.2);
                          }

                          &.delete-button:hover {
                              background: rgba($system-red, 0.2);
                          }
                      }
                  }
              }
          }
      }
  }