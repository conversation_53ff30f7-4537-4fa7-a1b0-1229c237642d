#!/bin/bash

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🎯 Starting Full Development Environment${NC}"
echo -e "${CYAN}=====================================${NC}"

echo -e "${YELLOW}🚀 Installing dependencies if needed...${NC}"
npm install

echo ""
echo -e "${GREEN}🎉 Starting all services...${NC}"
echo -e "${CYAN}📱 Frontend: http://localhost:5173${NC}"
echo -e "${YELLOW}🐍 Python API: http://localhost:5000${NC}"
echo -e "${GREEN}🟢 Node.js API: http://localhost:5001${NC}"
echo ""
echo -e "${BLUE}Press Ctrl+C to stop all services${NC}"
echo ""

npm run dev
