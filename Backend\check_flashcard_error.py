"""
Script to check for errors when saving flashcards.
"""

import psycopg2
import psycopg2.extras
import os
import json
import traceback
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '5432')
DB_NAME = os.environ.get('DB_NAME', 'blueprint')
DB_USER = os.environ.get('DB_USER', 'postgres')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Lukind@1956')

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        logging.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logging.error(f"Error connecting to database: {str(e)}")
        raise

def check_chapter_exists(conn, chapter_id):
    """Check if the chapter exists"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cursor.execute(
            "SELECT id, title, chapter_number, subject_id FROM chapters WHERE id = %s",
            [chapter_id]
        )
        chapter = cursor.fetchone()
        if chapter:
            logging.info(f"Chapter found: ID={chapter['id']}, Title={chapter['title']}, Subject ID={chapter['subject_id']}")
        else:
            logging.error(f"Chapter ID {chapter_id} not found")
        return chapter
    except Exception as e:
        logging.error(f"Error checking chapter: {str(e)}")
        return None
    finally:
        cursor.close()

def check_unique_constraint(conn):
    """Check the unique constraint on the flashcards table"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cursor.execute("""
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'flashcards' AND constraint_type = 'UNIQUE'
        """)
        
        constraints = cursor.fetchall()
        if constraints:
            for constraint in constraints:
                logging.info(f"Found unique constraint: {constraint['constraint_name']}")
                
                # Get the columns in the constraint
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.constraint_column_usage
                    WHERE constraint_name = %s
                """, [constraint['constraint_name']])
                
                columns = cursor.fetchall()
                column_names = [col['column_name'] for col in columns]
                logging.info(f"Constraint columns: {', '.join(column_names)}")
                
                # Check if this is the constraint we added
                if 'unique_flashcard_question' in constraint['constraint_name']:
                    logging.info("Found our unique constraint on flashcards")
                    return True
        else:
            logging.warning("No unique constraints found on flashcards table")
        
        return False
    except Exception as e:
        logging.error(f"Error checking constraints: {str(e)}")
        return False
    finally:
        cursor.close()

def test_insert_flashcard(conn, chapter_id):
    """Test inserting a flashcard"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # First, get the chapter info
        chapter = check_chapter_exists(conn, chapter_id)
        if not chapter:
            return False
        
        # Get a user ID that has access to this subject
        cursor.execute(
            "SELECT user_id FROM subjects WHERE id = %s",
            [chapter['subject_id']]
        )
        user_result = cursor.fetchone()
        if not user_result:
            logging.error(f"No user found with access to subject ID {chapter['subject_id']}")
            return False
        
        user_id = user_result['user_id']
        logging.info(f"Using user ID {user_id} for test")
        
        # Try to insert a test flashcard
        try:
            cursor.execute("""
                INSERT INTO flashcards (
                    user_id, chapter_id, subject_id, question, answer,
                    type, box_level, color, difficult, tags, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                RETURNING id
            """, [
                user_id, 
                chapter_id, 
                chapter['subject_id'],
                f"Test question {chapter_id}_{user_id}",
                "Test answer",
                "basic",
                1,
                "#ffffff",
                False,
                "test",
            ])
            
            flashcard_id = cursor.fetchone()['id']
            logging.info(f"Successfully inserted test flashcard with ID {flashcard_id}")
            
            # Now try to insert a duplicate to see if the constraint works
            try:
                cursor.execute("""
                    INSERT INTO flashcards (
                        user_id, chapter_id, subject_id, question, answer,
                        type, box_level, color, difficult, tags, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    RETURNING id
                """, [
                    user_id, 
                    chapter_id, 
                    chapter['subject_id'],
                    f"Test question {chapter_id}_{user_id}",
                    "Test answer",
                    "basic",
                    1,
                    "#ffffff",
                    False,
                    "test",
                ])
                
                logging.error("Duplicate insert succeeded - constraint not working!")
                return False
            except psycopg2.errors.UniqueViolation:
                logging.info("Duplicate insert failed as expected - constraint is working")
                conn.rollback()
                return True
            
        except Exception as e:
            logging.error(f"Error inserting test flashcard: {str(e)}")
            logging.error(traceback.format_exc())
            return False
            
    except Exception as e:
        logging.error(f"Error in test_insert_flashcard: {str(e)}")
        return False
    finally:
        conn.rollback()  # Roll back any changes
        cursor.close()

def main():
    """Main function"""
    try:
        conn = connect_to_db()
        
        # Check if the unique constraint exists
        constraint_exists = check_unique_constraint(conn)
        logging.info(f"Unique constraint exists: {constraint_exists}")
        
        # Test with the specific chapter ID from the error
        chapter_id = 106
        chapter = check_chapter_exists(conn, chapter_id)
        
        if chapter:
            # Test inserting a flashcard
            test_result = test_insert_flashcard(conn, chapter_id)
            logging.info(f"Test insert result: {'Success' if test_result else 'Failed'}")
        
        conn.close()
    except Exception as e:
        logging.error(f"Script failed: {str(e)}")

if __name__ == "__main__":
    main()
