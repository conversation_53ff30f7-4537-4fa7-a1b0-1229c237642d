@use "sass:color";

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

$card-border-radius: 12px;
$transition-speed: 0.3s;

// Base Styles - macOS style
.tools-container {
  //padding: 2rem;
  //min-height: 100vh;
  height: calc(100dvh - 7rem);

  // Light theme
  background-color: $window-background;

  // Dark theme
  &.theme-dark {
    background-color: $dark-window-background;
  }
}

// Tools Header - macOS style
.tools-header {
  text-align: center;
  margin-bottom: 2rem;

  h1 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  p {
    font-size: 1.1rem;

    // Light theme
    color: $secondary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-secondary-text;
    }
  }
}

// Subject and Chapter Selection
.tools-selection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  .selection-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      font-size: 0.9rem;
      font-weight: 500;

      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    select {
      padding: 0.75rem 1rem;
      border-radius: 8px;
      font-size: 1rem;
      appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 1rem center;
      background-size: 1em;

      // Light theme
      background-color: $secondary-background;
      border: 1px solid $border-color;
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        border: 1px solid $dark-border-color;
        color: $dark-primary-text;
      }

      &:focus {
        outline: none;

        // Light theme
        border-color: $system-blue;
        box-shadow: 0 0 0 3px rgba($system-blue, 0.1);

        // Dark theme
        .theme-dark & {
          border-color: $system-blue;
          box-shadow: 0 0 0 3px rgba($system-blue, 0.2);
        }
      }
    }
  }
}

// Tools Grid - macOS style
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  max-width: auto;
  //margin: 1rem;
  //height: calc(100dvh - 10rem);
  height: auto;
  overflow: auto;
  padding-right: 0.5rem;

  // Custom scrollbar for light theme
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba($system-gray, 0.3);
    border-radius: 20px;
  }

  // Custom scrollbar for dark theme
  .theme-dark & {
    &::-webkit-scrollbar-thumb {
      background-color: rgba($dark-tertiary-text, 0.3);
    }
  }

  @media (max-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 3rem;
  grid-column: 1 / -1;

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;

    // Light theme
    color: $system-gray;

    // Dark theme
    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  p {
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;

    // Light theme
    color: $tertiary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }
}

// Tool Card - macOS style
.tool-card {
  border-radius: $card-border-radius;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  @include transition;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  // Light theme
  background-color: $window-background;
  color: $primary-text;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid $border-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-secondary-background;
    color: $dark-primary-text;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border: 1px solid $dark-border-color;
  }

  &:hover {
    transform: translateY(-4px);

    // Light theme
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    border-color: $system-blue;

    // Dark theme
    .theme-dark & {
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
      border-color: $system-blue;
    }

    .tool-icon {
      transform: scale(1.05);
    }

    h3 {
      // Light theme
      color: $system-blue;

      // Dark theme
      .theme-dark & {
        color: $system-blue;
      }
    }

    .remove-button {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(-2px);
  }

  img {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin-bottom: 1rem;
    @include transition;
  }

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0.5rem 0;
    @include transition;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  p {
    font-size: 0.9rem;
    margin-top: 0.5rem;

    // Light theme
    color: $tertiary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }

  // Remove button
  .remove-button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 24px;
    height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    line-height: 1;
    cursor: pointer;
    opacity: 0;
    @include transition;
    z-index: 2;

    // Light theme
    background-color: $system-red;
    color: white;

    // Dark theme
    .theme-dark & {
      background-color: $system-red;
    }

    &:hover {
      transform: scale(1.1);

      // Light theme
      background-color: darken($system-red, 5%);

      // Dark theme
      .theme-dark & {
        background-color: lighten($system-red, 5%);
      }
    }
  }

  // Add mode styling
  &.add-mode {
    // Light theme
    border: 1px dashed $border-color;
    background-color: rgba($system-blue, 0.03);

    // Dark theme
    .theme-dark & {
      border: 1px dashed $dark-border-color;
      background-color: rgba($system-blue, 0.05);
    }

    &:hover {
      // Light theme
      background-color: rgba($system-blue, 0.08);
      border-color: $system-blue;

      // Dark theme
      .theme-dark & {
        background-color: rgba($system-blue, 0.1);
        border-color: $system-blue;
      }
    }

    .add-indicator {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      opacity: 0;
      @include transition;

      // Light theme
      background-color: rgba($window-background, 0.7);
      color: $system-blue;

      // Dark theme
      .theme-dark & {
        background-color: rgba($dark-secondary-background, 0.7);
        color: $system-blue;
      }
    }

    &:hover .add-indicator {
      opacity: 1;
    }
  }
}

// Modal Styles - macOS style
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  @include transition;
  backdrop-filter: blur(5px);

  // Light theme
  background-color: rgba(0, 0, 0, 0.4);

  // Dark theme
  .theme-dark & {
    background-color: rgba(0, 0, 0, 0.6);
  }

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Modal Content - macOS style
.modal-content {
  //padding: 2rem;
  border-radius: 12px;
  width: 90%;
  //max-width: 800px;
  max-height: 95%;
  overflow-y: auto;
  position: relative;
  transform: translateY(20px);
  @include transition;

  // Light theme
  background-color: $window-background;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid $border-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid $dark-border-color;
  }

  .modal-overlay.active & {
    transform: translateY(0);
  }

  // Custom scrollbar for light theme
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba($system-gray, 0.3);
    border-radius: 20px;
  }

  // Custom scrollbar for dark theme
  .theme-dark & {
    &::-webkit-scrollbar-thumb {
      background-color: rgba($dark-tertiary-text, 0.3);
    }
  }
}

// Close Button - macOS style
.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  @include transition;

  // Light theme
  color: $secondary-text;

  // Dark theme
  .theme-dark & {
    color: $dark-secondary-text;
  }

  &:hover {
    // Light theme
    background-color: $secondary-background;
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      background-color: $dark-secondary-background;
      color: $dark-primary-text;
    }
  }
}

// Tool Subject Input - macOS style
.tool-subject-input {
  text-align: center;

  h2 {
    margin-bottom: 1rem;
    font-weight: 600;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  p {
    margin-bottom: 1.5rem;

    // Light theme
    color: $secondary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-secondary-text;
    }
  }

  form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
  }

  label {
    text-align: left;
    font-weight: 600;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  input {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.95rem;
    @include transition;

    // Light theme
    border: 1px solid $border-color;
    background-color: $secondary-background;
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      border: 1px solid $dark-border-color;
      background-color: $dark-secondary-background;
      color: $dark-primary-text;
    }

    &:focus {
      outline: none;

      // Light theme
      border-color: $system-blue;
      box-shadow: 0 0 0 4px rgba($system-blue, 0.1);

      // Dark theme
      .theme-dark & {
        border-color: $system-blue;
        box-shadow: 0 0 0 4px rgba($system-blue, 0.2);
      }
    }

    &::placeholder {
      // Light theme
      color: $placeholder-text;
      opacity: 0.7;

      // Dark theme
      .theme-dark & {
        color: $dark-placeholder-text;
        opacity: 0.7;
      }
    }
  }

  button {
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 500;
    @include transition;
    font-size: 0.95rem;
    margin-top: 1rem;

    // Light theme
    background-color: $system-blue;
    color: white;
    border: none;

    // Dark theme
    .theme-dark & {
      background-color: $system-blue;
      color: white;
    }

    &:hover {
      transform: translateY(-2px);

      // Light theme
      background-color: color.adjust($system-blue, $lightness: -5%);
      box-shadow: 0 4px 8px rgba($system-blue, 0.2);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($system-blue, $lightness: -5%);
        box-shadow: 0 4px 8px rgba($system-blue, 0.4);
      }
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      // Light theme
      background-color: color.adjust($system-blue, $lightness: 20%, $saturation: -30%);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
      opacity: 0.7;

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($system-blue, $lightness: 20%, $saturation: -30%);
        opacity: 0.7;
      }
    }
  }
}

// Add Tool Button
.add-tool-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 100;
  @include transition;

  // Light theme
  background-color: $system-blue;
  color: white;
  box-shadow: 0 4px 10px rgba($system-blue, 0.3);

  // Dark theme
  .theme-dark & {
    background-color: $system-blue;
    box-shadow: 0 4px 10px rgba($system-blue, 0.5);
  }

  &:hover {
    transform: translateY(-3px) scale(1.05);

    // Light theme
    box-shadow: 0 6px 15px rgba($system-blue, 0.4);

    // Dark theme
    .theme-dark & {
      box-shadow: 0 6px 15px rgba($system-blue, 0.6);
    }
  }

  &:active {
    transform: translateY(-1px) scale(1);
  }
}

// Animations - macOS style
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Staggered animation for tool cards
.tool-card {
  animation: fadeIn 0.5s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;

  @for $i from 1 through 15 {
    &:nth-child(#{$i}) {
      animation-delay: $i * 0.05s;
    }
  }
}

// Individual Tool Overrides (maintaining their unique colors in content)
.pomodoro-timer {
  .timer-display.work {
    color: $system-red;
  }

  .timer-display.break {
    color: $system-green;
  }
}



// Tool Settings Modal
.tool-settings-modal {
  .settings-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid $separator-color;

    .theme-dark & {
      border-bottom-color: $dark-separator-color;
    }

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      padding: 1rem 0 0 0;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    p {
      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  .settings-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      label {
        font-weight: 500;
        font-size: 0.9rem;

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }

      input,
      select,
      textarea {
        padding: 0.75rem;
        border-radius: 8px;
        font-size: 1rem;

        // Light theme
        background-color: $secondary-background;
        border: 1px solid $border-color;
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          background-color: $dark-secondary-background;
          border: 1px solid $dark-border-color;
          color: $dark-primary-text;
        }

        &:focus {
          outline: none;

          // Light theme
          border-color: $system-blue;
          box-shadow: 0 0 0 3px rgba($system-blue, 0.1);

          // Dark theme
          .theme-dark & {
            border-color: $system-blue;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.2);
          }
        }
      }

      textarea {
        min-height: 100px;
        resize: vertical;
      }
    }

    .actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 1rem;

      button {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        @include transition;

        &.cancel {
          // Light theme
          background-color: $secondary-background;
          color: $secondary-text;
          border: 1px solid $border-color;

          // Dark theme
          .theme-dark & {
            background-color: $dark-secondary-background;
            color: $dark-secondary-text;
            border: 1px solid $dark-border-color;
          }

          &:hover {
            // Light theme
            background-color: darken($secondary-background, 5%);

            // Dark theme
            .theme-dark & {
              background-color: lighten($dark-secondary-background, 5%);
            }
          }
        }

        &.save {
          // Light theme
          background-color: $system-blue;
          color: white;
          border: none;

          // Dark theme
          .theme-dark & {
            background-color: $system-blue;
          }

          &:hover:not(:disabled) {
            // Light theme
            background-color: darken($system-blue, 5%);

            // Dark theme
            .theme-dark & {
              background-color: lighten($system-blue, 5%);
            }
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }

    .error-message {
      margin-top: 1rem;
      padding: 0.75rem;
      border-radius: 8px;
      font-size: 0.9rem;
      text-align: center;

      // Light theme
      background-color: rgba($system-red, 0.1);
      color: $system-red;
      border: 1px solid rgba($system-red, 0.2);

      // Dark theme
      .theme-dark & {
        background-color: rgba($system-red, 0.2);
        color: lighten($system-red, 10%);
        border: 1px solid rgba($system-red, 0.3);
      }
    }
  }
}

// Responsive Adjustments - macOS style
@media (max-width: 768px) {
  .tools-selection {
    flex-direction: column;
  }

  .tools-grid {
    gap: 1rem;
    margin: 0.5rem;
    height: calc(100dvh - 20rem);
  }

  .tool-card {
    padding: 1rem;

    img {
      width: 80px;
      height: 80px;
    }
  }

  .modal-content {
    padding: 1.5rem;
    width: 95%;
  }

  .tool-subject-input {
    form {
      max-width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .tools-container {
    padding: 1rem;
  }

  .tools-header {
    h1 {
      font-size: 1.8rem;
    }

    p {
      font-size: 0.95rem;
    }
  }

  .tool-card {
    h3 {
      font-size: 1.1rem;
    }

    img {
      width: 70px;
      height: 70px;
    }
  }

  .modal-content {
    padding: 1.25rem;
  }

  .add-tool-button {
    bottom: 1rem;
    right: 1rem;
  }
}

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

$work-color: #ff6b6b;
$break-color: #51cf66;
$dark-bg: #2c3e50;
$light-text: #ecf0f1;
$dark-text: #2c3e50;

.pomodoro-timer {
  font-family: 'Roboto', sans-serif;
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background: white;
  color: $dark-text;
  text-align: center;
  transition: all 0.3s ease;

  &.work {
    border-top: 6px solid $work-color;
  }

  &.break {
    border-top: 6px solid $break-color;
  }

  .timer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      margin: 0;
      font-size: 1.8rem;
      color: $dark-bg;
    }

    .settings-btn {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 50%;
      transition: all 0.2s;
      color: $dark-bg;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  .timer-display-container {
    display: flex;
    justify-content: space-between;
    margin: 2rem 0;
    gap: 1rem;

    .time-display,
    .break-display {
      flex: 1;
      padding: 1.5rem;
      border-radius: 10px;
      background: rgba(0, 0, 0, 0.03);
    }

    .time-display {
      border-left: 5px solid $work-color;
    }

    .break-display {
      border-left: 5px solid $break-color;
    }

    .time-label,
    .break-label {
      font-size: 1rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
    }

    .time-value,
    .break-value {
      font-size: 2rem;
      font-weight: 300;
      font-feature-settings: "tnum";
      font-variant-numeric: tabular-nums;
    }
  }

  .current-timer {
    margin: 2rem 0;
    padding: 1.5rem;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.05);

    .session-indicator {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 1rem;

      &.work {
        color: darken($work-color, 15%);
      }

      &.break {
        color: darken($break-color, 15%);
      }
    }

    .time-remaining {
      font-size: 3.5rem;
      font-weight: 300;
      font-feature-settings: "tnum";
      font-variant-numeric: tabular-nums;
    }
  }

  .timer-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;

    .control-btn {
      padding: 0.8rem 1.5rem;
      border: none;
      border-radius: 50px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      &.start {
        background: $work-color;
        color: white;

        &:hover {
          background: darken($work-color, 10%);
          transform: translateY(-2px);
        }
      }

      &.pause {
        background: #f1c40f;
        color: $dark-text;

        &:hover {
          background: darken(#f1c40f, 10%);
          transform: translateY(-2px);
        }
      }

      &.reset {
        background: #e0e0e0;
        color: $dark-text;

        &:hover {
          background: darken(#e0e0e0, 10%);
          transform: translateY(-2px);
        }
      }
    }
  }

  .session-info {
    margin: 1.5rem 0;
    padding: 0.8rem;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.05);

    .cycles {
      font-weight: 500;
      color: $dark-bg;
    }
  }

  .subject-notes {
    margin-top: 2rem;
    text-align: left;

    h3 {
      margin-bottom: 0.8rem;
      color: $dark-bg;
    }

    textarea {
      width: 100%;
      min-height: 100px;
      padding: 1rem;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-family: inherit;
      resize: vertical;
      transition: border 0.3s;

      &:focus {
        outline: none;
        border-color: $work-color;
        box-shadow: 0 0 0 2px rgba($work-color, 0.2);
      }
    }
  }

  .settings-panel {
    text-align: left;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 10px;

    h3 {
      margin-top: 0;
      color: $dark-bg;
    }

    .setting-group {
      margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
      }

      input {
        width: 100%;
        padding: 0.8rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;

        &:focus {
          outline: none;
          border-color: $work-color;
        }
      }
    }

    .save-btn {
      width: 100%;
      padding: 0.8rem;
      background: $work-color;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: darken($work-color, 10%);
      }
    }
  }
}

@media (max-width: 500px) {
  .pomodoro-timer {
    padding: 1.5rem;
    border-radius: 0;

    .timer-display-container {
      flex-direction: column;

      .time-display,
      .break-display {
        width: 100%;
      }
    }

    .current-timer .time-remaining {
      font-size: 2.5rem;
    }
  }
}













@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

















































/* Doodle App Styling */
.doodle-app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.doodle-header {
  background-color: #4a6fa5;
  color: white;
  padding: 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.doodle-header h2 {
  margin: 0;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.tool-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.tool-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.tool-button.active {
  background-color: rgba(255, 255, 255, 0.4);
}

.tool-button i {
  font-size: 14px;
}

.doodle-container {
  display: grid;
  grid-template-columns: 250px 1fr 250px;
  height: 600px;
}

.tool-panel {
  background-color: #ffffff;
  border-right: 1px solid #e0e0e0;
  padding: 20px;
  overflow-y: auto;
}

.tool-section {
  margin-bottom: 25px;
}

.tool-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #4a6fa5;
  font-size: 1.1em;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 5px;
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9em;
  color: #555;
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-picker-container input[type="color"] {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.color-picker-container span {
  font-family: monospace;
  font-size: 0.9em;
}

input[type="range"] {
  width: 100%;
  cursor: pointer;
}

select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.action-button {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #4a6fa5;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #3a5a8f;
}

.action-button i {
  font-size: 14px;
}

.canvas-wrapper {
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.doodle-canvas {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: crosshair;
  touch-action: none;
  width: 100%;
  height: 100%;
}

.notes-panel {
  background-color: #ffffff;
  border-left: 1px solid #e0e0e0;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.notes-panel h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #4a6fa5;
  font-size: 1.1em;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 5px;
}

.notes-panel textarea {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: none;
  font-family: inherit;
  line-height: 1.5;
}

.gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.gallery-content {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 80%;
  max-width: 1000px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.close-gallery {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  color: #777;
}

.doodle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.doodle-thumbnail {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.2s;
}

.doodle-thumbnail:hover {
  transform: translateY(-5px);
}

.doodle-thumbnail img {
  width: 100%;
  height: 150px;
  object-fit: contain;
  background-color: #f5f5f5;
  cursor: pointer;
}

.doodle-meta {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9f9f9;
}

.doodle-meta span {
  font-size: 0.8em;
  color: #666;
}

.load-button {
  background-color: #4a6fa5;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8em;
}

.no-doodles {
  text-align: center;
  color: #777;
  grid-column: 1 / -1;
  padding: 40px 0;
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .doodle-container {
    grid-template-columns: 200px 1fr;
  }

  .notes-panel {
    grid-column: 1 / -1;
    border-left: none;
    border-top: 1px solid #e0e0e0;
  }
}

@media (max-width: 600px) {
  .doodle-container {
    grid-template-columns: 1fr;
    height: auto;
  }

  .tool-panel {
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }

  .canvas-wrapper {
    height: 400px;
  }
}






















/* macOS-style Objectives Tracker */
.macos-objectives {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.header {
  padding: 20px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #e0e0e0;
}

.header h2 {
  margin: 0 0 15px 0;
  font-weight: 600;
  color: #1a1a1a;
}

.date-tabs {
  display: flex;
  margin-bottom: 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 4px;
}

.tab {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.tab.active {
  background-color: white;
  color: #007AFF;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.date-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.date-picker {
  border: 1px solid #d6d6d6;
  border-radius: 6px;
  padding: 6px 10px;
  font-family: inherit;
  font-size: 14px;
}

.content {
  padding: 0 20px 20px;
}

.to-do-section,
.done-section {
  margin-bottom: 20px;
}

.to-do-section h3,
.done-section h3 {
  font-size: 15px;
  font-weight: 600;
  color: #666;
  margin: 20px 0 10px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.objectives-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.objective-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s ease;
}

.objective-item:last-child {
  border-bottom: none;
}

.objective-content {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.toggle-button {
  width: 22px;
  height: 22px;
  border-radius: 11px;
  border: 1px solid #ff3b30;
  background-color: white;
  margin-right: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.2s ease;
}

.toggle-button.completed {
  border-color: #34c759;
  background-color: #34c759;
}

.toggle-circle {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: white;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.toggle-button.completed .toggle-circle {
  opacity: 1;
}

.objective-text {
  flex-grow: 1;
  font-size: 15px;
  color: #1a1a1a;
}

.objective-item.completed .objective-text {
  color: #888;
  text-decoration: line-through;
}

.remove-button {
  background: none;
  border: none;
  color: #888;
  font-size: 18px;
  cursor: pointer;
  padding: 0 5px;
  margin-left: 10px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.objective-item:hover .remove-button {
  opacity: 1;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.add-section {
  margin-top: 25px;
}

.progress-bar {
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  margin-bottom: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #34c759;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 13px;
  color: #888;
  margin: 0 0 15px 0;
  text-align: center;
}

.input-group {
  display: flex;
  gap: 10px;
}

.input-group input {
  flex-grow: 1;
  padding: 10px 15px;
  border: 1px solid #d6d6d6;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
}

.input-group input:focus {
  outline: none;
  border-color: #007AFF;
}

.add-button {
  padding: 0 20px;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-button:disabled {
  background-color: #d6d6d6;
  cursor: not-allowed;
}

.add-button:not(:disabled):hover {
  background-color: #0062cc;
}

/* Animation for new items */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.objective-item {
  animation: fadeIn 0.3s ease forwards;
}
























/* Modern Calendar Styling */
.calendar-app {
  font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  max-width: 1000px;
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.calendar-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.view-controls {
  display: flex;
  background-color: #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.view-button {
  padding: 6px 12px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.view-button.active {
  background-color: #ffffff;
  color: #2c3e50;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.month-navigation {
  display: flex;
  align-items: center;
  gap: 10px;
}

.month-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: #2c3e50;
  min-width: 150px;
  text-align: center;
}

.nav-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #6c757d;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav-button:hover {
  background-color: #e9ecef;
}

.today-button {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.today-button:hover {
  background-color: #e9ecef;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 0 20px 20px;
}

.day-header {
  text-align: center;
  padding: 10px 0;
  font-size: 0.85rem;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
}

.day-cell {
  aspect-ratio: 1;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid transparent;
}

.day-cell:hover {
  background-color: #f8f9fa;
}

.day-cell.today {
  background-color: #e3f2fd;
  border-color: #bbdefb;
}

.day-cell.selected {
  background-color: #e3f2fd;
  border: 1px solid #90caf9;
  box-shadow: 0 0 0 2px rgba(66, 165, 245, 0.2);
}

.day-cell.other-month {
  color: #adb5bd;
  opacity: 0.6;
}

.day-cell.same-week:not(.selected) {
  background-color: #f8f9fa;
}

.day-number {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 4px;
}

.today .day-number {
  font-weight: bold;
  color: #1976d2;
}

.event-indicator {
  margin-top: auto;
  display: flex;
  gap: 4px;
  justify-content: center;
}

.pending-count,
.completed-count {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.pending-count {
  background-color: #ffcdd2;
  color: #c62828;
}

.completed-count {
  background-color: #c8e6c9;
  color: #2e7d32;
}

.day-view {
  padding: 20px;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.day-header h3 {
  margin: 0;
  font-size: 1.3rem;
  color: #2c3e50;
}

.day-tabs {
  display: flex;
  background-color: #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.tab-button {
  padding: 8px 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.tab-button.active {
  background-color: #ffffff;
  color: #2c3e50;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.day-content {
  margin-top: 20px;
}

.events-section,
.objectives-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.events-list {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.event-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.event-item:last-child {
  border-bottom: none;
}

.event-item.completed {
  opacity: 0.7;
}

.event-item.completed .event-title {
  text-decoration: line-through;
  color: #6c757d;
}

.event-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex-grow: 1;
}

.toggle-button {
  min-width: 24px;
  height: 24px;
  border-radius: 12px;
  border: 2px solid #6c757d;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  flex-shrink: 0;
  transition: all 0.2s;
}

.toggle-button.completed {
  border-color: #4caf50;
  background-color: #4caf50;
  color: white;
}

.event-details {
  flex-grow: 1;
}

.event-details h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  color: #2c3e50;
}

.event-details p {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.event-time {
  font-size: 0.8rem;
  color: #adb5bd;
}

.delete-button {
  background: none;
  border: none;
  color: #adb5bd;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 5px;
  transition: color 0.2s;
}

.delete-button:hover {
  color: #dc3545;
}

.add-event-form {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.add-event-form h4 {
  margin: 0 0 15px 0;
  font-size: 1rem;
  color: #2c3e50;
}

.form-group {
  margin-bottom: 15px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.9rem;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #90caf9;
  box-shadow: 0 0 0 2px rgba(66, 165, 245, 0.2);
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.form-row {
  display: flex;
  gap: 10px;
}

.date-input {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-family: inherit;
}

.add-button {
  padding: 10px 20px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-button:disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
}

.add-button:not(:disabled):hover {
  background-color: #1565c0;
}

.objective-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.objective-item:last-child {
  border-bottom: none;
}

.objective-item.completed {
  opacity: 0.7;
}

.objective-item.completed .objective-title {
  text-decoration: line-through;
  color: #6c757d;
}

.objective-toggle {
  min-width: 22px;
  height: 22px;
  border-radius: 11px;
  border: 2px solid #dc3545;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  flex-shrink: 0;
  transition: all 0.2s;
}

.objective-toggle.completed {
  border-color: #4caf50;
  background-color: #4caf50;
}

.toggle-circle {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  background-color: white;
  opacity: 0;
  transition: opacity 0.2s;
}

.objective-toggle.completed .toggle-circle {
  opacity: 1;
}

.objective-content {
  flex-grow: 1;
}

.objective-title {
  font-weight: 500;
  color: #2c3e50;
}

.objective-description {
  margin: 4px 0 0 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.objective-delete {
  background: none;
  border: none;
  color: #adb5bd;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 5px;
  transition: color 0.2s;
}

.objective-delete:hover {
  color: #dc3545;
}

.empty-state {
  text-align: center;
  padding: 30px;
  color: #adb5bd;
}

.upcoming-events {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.upcoming-events h3 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  color: #2c3e50;
}

.upcoming-events ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.upcoming-events li {
  padding: 8px 0;
  cursor: pointer;
  display: flex;
  gap: 10px;
  transition: background-color 0.2s;
  border-radius: 4px;
  padding: 8px;
}

.upcoming-events li:hover {
  background-color: #f8f9fa;
}

.event-date {
  font-weight: 500;
  color: #6c757d;
  min-width: 50px;
}

.event-title {
  color: #2c3e50;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.event-item,
.objective-item {
  animation: fadeIn 0.3s ease forwards;
}


















/* Modern Flashcard App Styling */
:root {
  --primary-color: #4361ee;
  --secondary-color: #3f37c9;
  --accent-color: #4895ef;
  --danger-color: #f72585;
  --success-color: #4cc9f0;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --border-radius: 12px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
}

.flashcards-app {
  font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  //max-width: 1000px;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  color: var(--dark-color);
  overflow-y: auto;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.app-header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--primary-color);
}

.app-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.icon-button {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.icon-button:hover {
  background-color: rgba(67, 97, 238, 0.1);
}

.mode-select {
  padding: 8px 12px;
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
  font-family: inherit;
  background-color: white;
  cursor: pointer;
  transition: var(--transition);
}

.mode-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.flashcard-container {
  position: relative;
  perspective: 1000px;
  margin-bottom: 30px;
  min-height: 300px;
}

.card {
  width: 100%;
  height: 300px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s ease, box-shadow 0.3s ease;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  cursor: pointer;
  overflow: hidden;
}

.card.flipped {
  transform: rotateY(180deg);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  flex-direction: column;
  padding: 25px;
  box-sizing: border-box;
}

.card-front {
  background-color: white;
}

.card-back {
  background-color: white;
  transform: rotateY(180deg);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-content h3 {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  color: var(--primary-color);
  font-weight: 500;
}

.card-content textarea {
  flex: 1;
  width: 100%;
  border: none;
  background: none;
  resize: none;
  font-family: inherit;
  font-size: 1.1rem;
  line-height: 1.5;
  padding: 0;
  color: inherit;
  outline: none;
  pointer-events: none;
}

.card-footer {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hint {
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
}

.show-answer-button {
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
  border: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: var(--transition);
}

.show-answer-button:hover {
  background-color: rgba(67, 97, 238, 0.2);
}

.answer-preview {
  position: absolute;
  bottom: 20px;
  right: 20px;
  left: 20px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-height: 40%;
  overflow-y: auto;
  font-size: 0.9rem;
  line-height: 1.5;
  border-left: 4px solid var(--primary-color);
}

.no-cards {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  border-radius: var(--border-radius);
  background-color: white;
  box-shadow: var(--box-shadow);
}

.empty-state {
  text-align: center;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #adb5bd;
}

.empty-state p {
  margin: 5px 0;
}

.card-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}

.nav-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nav-button:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.nav-button:not(:disabled):hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

.card-counter {
  font-weight: 500;
  min-width: 80px;
  text-align: center;
}

.card-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
}

.color-picker {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-picker label {
  font-size: 0.9rem;
  color: #495057;
}

.color-picker input {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  padding: 0;
}

.difficult-button {
  background: none;
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
}

.difficult-button.active {
  background-color: var(--danger-color);
  color: white;
}

.difficult-button:hover {
  background-color: rgba(247, 37, 133, 0.1);
}

.difficult-button.active:hover {
  background-color: rgba(247, 37, 133, 0.8);
}

.card-editor {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--box-shadow);
  margin-bottom: 30px;
}

.card-editor h3 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.form-group {
  margin-bottom: 15px;
}

.question-input,
.answer-input {
  width: 100%;
  padding: 15px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-family: inherit;
  font-size: 1rem;
  resize: none;
  transition: var(--transition);
}

.question-input:focus,
.answer-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.add-button,
.save-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.add-button:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.add-button:not(:disabled):hover,
.save-button:hover {
  background-color: var(--secondary-color);
}

.cancel-button {
  background: none;
  border: 1px solid #6c757d;
  color: #6c757d;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.cancel-button:hover {
  background-color: #f8f9fa;
}

.cards-list-container {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--box-shadow);
}

.cards-list-container h3 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  color: var(--primary-color);
}

.cards-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.card-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: var(--transition);
}

.card-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.card-item.active {
  border: 2px solid var(--primary-color);
}

.card-item.difficult {
  border-left: 4px solid var(--danger-color);
}

.card-preview {
  height: 120px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
}

.card-preview-front {
  font-size: 0.9rem;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-bottom: 10px;
}

.edit-button,
.delete-button {
  background: none;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.edit-button {
  color: var(--primary-color);
}

.edit-button:hover {
  background-color: rgba(67, 97, 238, 0.1);
}

.delete-button {
  color: var(--danger-color);
}

.delete-button:hover {
  background-color: rgba(247, 37, 133, 0.1);
}

.card-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #6c757d;
}

.card-number {
  font-weight: 500;
}

.difficult-tag {
  color: var(--danger-color);
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .cards-list {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 480px) {
  .card-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .cards-list {
    grid-template-columns: 1fr;
  }
}












// ============================================================================
// ADVANCED MINDMAP STYLES - macOS Inspired v2.0 - Ultra Premium Edition
// ============================================================================


// Advanced Mindmap App with Ultra Premium Styling
.advanced-mindmap-app {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(135deg, rgba($system-blue, 0.02) 0%, rgba($system-green, 0.01) 50%, rgba($system-orange, 0.02) 100%),
    linear-gradient(45deg, $window-background 0%, color-mix(in srgb, $window-background 95%, $system-blue) 100%);
  color: $primary-text;
  overflow: hidden;
  position: relative;
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);

  &.theme-dark {
    background:
      linear-gradient(135deg, rgba($system-blue, 0.05) 0%, rgba($system-green, 0.03) 50%, rgba($system-orange, 0.04) 100%),
      linear-gradient(45deg, $dark-window-background 0%, color-mix(in srgb, $dark-window-background 95%, $system-blue) 100%);
    color: $dark-primary-text;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba($system-blue, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($system-green, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba($system-orange, 0.01) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }

  * {
    box-sizing: border-box;
    position: relative;
    z-index: 1;
  }
}

// Header with Ultra Premium Glass Morphism
.mindmap-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 2rem;
  background:
    linear-gradient(135deg, rgba($window-background, 0.95) 0%, rgba($window-background, 0.85) 100%);
  backdrop-filter: blur(40px) saturate(180%);
  -webkit-backdrop-filter: blur(40px) saturate(180%);
  border-bottom: 1px solid rgba($border-color, 0.2);
  box-shadow:
    0 1px 0 0 rgba(255, 255, 255, 0.8),
    0 1px 3px 0 rgba(0, 0, 0, 0.05),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.9);
  z-index: 100;
  position: relative;

  .theme-dark & {
    background:
      linear-gradient(135deg, rgba($dark-window-background, 0.95) 0%, rgba($dark-window-background, 0.85) 100%);
    border-bottom-color: rgba($dark-border-color, 0.2);
    box-shadow:
      0 1px 0 0 rgba(255, 255, 255, 0.1),
      0 1px 3px 0 rgba(0, 0, 0, 0.2),
      inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba($system-blue, 0.02) 25%,
        rgba($system-green, 0.01) 50%,
        rgba($system-orange, 0.02) 75%,
        transparent 100%);
    pointer-events: none;
    z-index: -1;
  }
}

.header-left {
  flex: 1;

  .mindmap-title {
    display: flex;
    align-items: center;
    gap: 1rem;

    .title-icon {
      color: $system-blue;
      font-size: 1.75rem;
      filter: drop-shadow(0 2px 4px rgba($system-blue, 0.3));
      animation: iconPulse 3s ease-in-out infinite;
    }

    h1 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      background: linear-gradient(135deg, $primary-text 0%, color-mix(in srgb, $primary-text 80%, $system-blue) 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: -0.02em;

      .theme-dark & {
        background: linear-gradient(135deg, $dark-primary-text 0%, color-mix(in srgb, $dark-primary-text 80%, $system-blue) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .version-badge {
      background: linear-gradient(135deg, $system-blue 0%, $system-green 50%, $system-orange 100%);
      color: white;
      padding: 0.375rem 0.75rem;
      border-radius: 1rem;
      font-size: 0.75rem;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      box-shadow:
        0 2px 8px rgba($system-blue, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shimmer 2s infinite;
      }
    }
  }
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;

  .view-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background:
      linear-gradient(135deg, rgba($secondary-background, 0.8) 0%, rgba($secondary-background, 0.6) 100%);
    padding: 0.75rem 1.25rem;
    border-radius: 1.25rem;
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba($border-color, 0.2);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);

    .theme-dark & {
      background:
        linear-gradient(135deg, rgba($dark-secondary-background, 0.8) 0%, rgba($dark-secondary-background, 0.6) 100%);
      border-color: rgba($dark-border-color, 0.2);
      box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .zoom-indicator {
      font-size: 0.875rem;
      font-weight: 600;
      color: $secondary-text;
      min-width: 3.5rem;
      text-align: center;
      background: linear-gradient(135deg, $system-blue, $system-green);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;

  .action-controls {
    display: flex;
    gap: 0.5rem;
  }
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border: none;
  border-radius: 0.75rem;
  background:
    linear-gradient(135deg, rgba($secondary-background, 0.9) 0%, rgba($secondary-background, 0.7) 100%);
  color: $secondary-text;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba($border-color, 0.2);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);

  .theme-dark & {
    background:
      linear-gradient(135deg, rgba($dark-secondary-background, 0.9) 0%, rgba($dark-secondary-background, 0.7) 100%);
    color: $dark-secondary-text;
    border-color: rgba($dark-border-color, 0.2);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba($system-blue, 0.15);

    &::before {
      opacity: 1;
    }

    &::after {
      width: 100%;
      height: 100%;
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.02);
  }

  &.ai-btn {
    &:hover {
      background: linear-gradient(135deg, rgba($system-green, 0.15) 0%, rgba($system-green, 0.05) 100%);
      color: $system-green;
      box-shadow: 0 8px 25px rgba($system-green, 0.2);
      border-color: rgba($system-green, 0.3);
    }
  }

  &.export-btn {
    &:hover {
      background: linear-gradient(135deg, rgba($system-orange, 0.15) 0%, rgba($system-orange, 0.05) 100%);
      color: $system-orange;
      box-shadow: 0 8px 25px rgba($system-orange, 0.2);
      border-color: rgba($system-orange, 0.3);
    }
  }

  &.import-btn {
    &:hover {
      background: linear-gradient(135deg, rgba($system-yellow, 0.15) 0%, rgba($system-yellow, 0.05) 100%);
      color: $system-yellow;
      box-shadow: 0 8px 25px rgba($system-yellow, 0.2);
      border-color: rgba($system-yellow, 0.3);
    }
  }

  &.danger-btn {
    &:hover {
      background: linear-gradient(135deg, rgba($system-red, 0.15) 0%, rgba($system-red, 0.05) 100%);
      color: $system-red;
      box-shadow: 0 8px 25px rgba($system-red, 0.2);
      border-color: rgba($system-red, 0.3);
    }
  }
}

// Workspace Layout
.mindmap-workspace {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

// Left Sidebar with Ultra Premium Styling
.left-sidebar {
  width: 320px;
  background:
    linear-gradient(135deg, rgba($window-background, 0.95) 0%, rgba($window-background, 0.85) 100%);
  backdrop-filter: blur(40px) saturate(180%);
  -webkit-backdrop-filter: blur(40px) saturate(180%);
  border-right: 1px solid rgba($border-color, 0.2);
  padding: 1.5rem;
  overflow-y: auto;
  z-index: 50;
  box-shadow:
    inset -1px 0 0 rgba(255, 255, 255, 0.8),
    2px 0 20px rgba(0, 0, 0, 0.05);

  .theme-dark & {
    background:
      linear-gradient(135deg, rgba($dark-window-background, 0.95) 0%, rgba($dark-window-background, 0.85) 100%);
    border-right-color: rgba($dark-border-color, 0.2);
    box-shadow:
      inset -1px 0 0 rgba(255, 255, 255, 0.1),
      2px 0 20px rgba(0, 0, 0, 0.2);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 10% 20%, rgba($system-blue, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 90% 80%, rgba($system-green, 0.01) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  .sidebar-section {
    margin-bottom: 2rem;
    padding: 1.25rem;
    background:
      linear-gradient(135deg, rgba($secondary-background, 0.4) 0%, rgba($secondary-background, 0.2) 100%);
    border-radius: 1rem;
    border: 1px solid rgba($border-color, 0.15);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);

    .theme-dark & {
      background:
        linear-gradient(135deg, rgba($dark-secondary-background, 0.4) 0%, rgba($dark-secondary-background, 0.2) 100%);
      border-color: rgba($dark-border-color, 0.15);
      box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    }

    h3 {
      margin: 0 0 1.25rem 0;
      font-size: 0.875rem;
      font-weight: 700;
      background: linear-gradient(135deg, $secondary-text 0%, color-mix(in srgb, $secondary-text 70%, $system-blue) 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      position: relative;

      .theme-dark & {
        background: linear-gradient(135deg, $dark-secondary-text 0%, color-mix(in srgb, $dark-secondary-text 70%, $system-blue) 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -0.5rem;
        left: 0;
        width: 2rem;
        height: 2px;
        background: linear-gradient(90deg, $system-blue, $system-green);
        border-radius: 1px;
      }
    }
  }
}

// Node Type Grid
.node-type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.node-type-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem 1rem;
  border: 2px solid transparent;
  border-radius: 1rem;
  background:
    linear-gradient(135deg, var(--node-color) 0%, color-mix(in srgb, var(--node-color) 85%, black) 100%);
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
      radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.4s ease;
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 12px 35px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.4);

    &::before {
      opacity: 1;
    }

    &::after {
      transform: scale(1);
    }

    span {
      transform: translateY(-1px);
    }
  }

  &:active {
    transform: translateY(-2px) scale(1.01);
  }

  .MuiSvgIcon-root {
    font-size: 1.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transition: all 0.3s ease;
  }

  span {
    font-size: 0.8rem;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
  }
}

// Quick Actions
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);

  &.primary {
    background: linear-gradient(135deg, $system-blue, color-mix(in srgb, $system-blue 80%, black));
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba($system-blue, 0.3);
    }
  }

  &.danger {
    background: linear-gradient(135deg, $system-red, color-mix(in srgb, $system-red 80%, black));
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba($system-red, 0.3);
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Node Properties Panel
.node-properties {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.property-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-size: 0.75rem;
    font-weight: 600;
    color: $secondary-text;
    text-transform: uppercase;
    letter-spacing: 0.05em;

    .theme-dark & {
      color: $dark-secondary-text;
    }
  }
}

.node-type-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba($secondary-background, 0.5);
  border-radius: 0.5rem;

  .theme-dark & {
    background: rgba($dark-secondary-background, 0.5);
  }

  span {
    font-size: 0.875rem;
    font-weight: 500;
    color: $primary-text;
    text-transform: capitalize;

    .theme-dark & {
      color: $dark-primary-text;
    }
  }
}

.color-picker {
  width: 100%;
  height: 2.5rem;
  border: 2px solid rgba($border-color, 0.3);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;

  .theme-dark & {
    border-color: rgba($dark-border-color, 0.3);
  }

  &:hover {
    border-color: $system-blue;
    box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
  }
}

.priority-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid rgba($border-color, 0.3);
  border-radius: 0.5rem;
  background: $window-background;
  color: $primary-text;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;

  .theme-dark & {
    background: $dark-window-background;
    color: $dark-primary-text;
    border-color: rgba($dark-border-color, 0.3);
  }

  &:focus {
    outline: none;
    border-color: $system-blue;
    box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
  }
}

.description-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid rgba($border-color, 0.3);
  border-radius: 0.5rem;
  background: $window-background;
  color: $primary-text;
  font-size: 0.875rem;
  font-family: inherit;
  resize: vertical;
  transition: all 0.2s ease;

  .theme-dark & {
    background: $dark-window-background;
    color: $dark-primary-text;
    border-color: rgba($dark-border-color, 0.3);
  }

  &:focus {
    outline: none;
    border-color: $system-blue;
    box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
  }

  &::placeholder {
    color: $placeholder-text;

    .theme-dark & {
      color: $dark-placeholder-text;
    }
  }
}

// Canvas Container with Ultra Premium Background
.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background:
    radial-gradient(circle at 25px 25px, rgba($system-blue, 0.08) 1.5px, transparent 1.5px),
    radial-gradient(circle at 75px 75px, rgba($system-green, 0.04) 1px, transparent 1px),
    radial-gradient(circle at 125px 125px, rgba($system-orange, 0.02) 0.5px, transparent 0.5px),
    linear-gradient(135deg,
      rgba($window-background, 0.98) 0%,
      rgba($secondary-background, 0.95) 50%,
      rgba($window-background, 0.98) 100%);
  background-size: 50px 50px, 100px 100px, 150px 150px, 100% 100%;
  background-attachment: local, local, local, fixed;

  .theme-dark & {
    background:
      radial-gradient(circle at 25px 25px, rgba($system-blue, 0.12) 1.5px, transparent 1.5px),
      radial-gradient(circle at 75px 75px, rgba($system-green, 0.06) 1px, transparent 1px),
      radial-gradient(circle at 125px 125px, rgba($system-orange, 0.03) 0.5px, transparent 0.5px),
      linear-gradient(135deg,
        rgba($dark-window-background, 0.98) 0%,
        rgba($dark-secondary-background, 0.95) 50%,
        rgba($dark-window-background, 0.98) 100%);
    background-size: 50px 50px, 100px 100px, 150px 150px, 100% 100%;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 30%, rgba($system-blue, 0.03) 0%, transparent 60%),
      radial-gradient(circle at 80% 70%, rgba($system-green, 0.02) 0%, transparent 60%),
      radial-gradient(circle at 50% 50%, rgba($system-orange, 0.01) 0%, transparent 80%);
    pointer-events: none;
    z-index: 0;
  }
}

.mindmap-canvas {
  width: 100%;
  height: 100%;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

// Connection Lines
.connection-line {
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    stroke-width: 4;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}

// Ultra Premium Mindmap Nodes
.mindmap-node {
  cursor: grab;
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.08));

  &:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 8px 25px rgba(0, 0, 0, 0.15));

    .node-background {
      filter:
        drop-shadow(0 8px 20px rgba(0, 0, 0, 0.2)) drop-shadow(0 0 0 2px rgba(255, 255, 255, 0.3)) !important;
      transform: scale(1.02);
    }

    .node-text-input {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border-radius: 6px;
      padding: 4px 8px;
    }
  }

  &:active {
    cursor: grabbing;
    transform: scale(1.02);
    filter: drop-shadow(0 6px 15px rgba(0, 0, 0, 0.12));
  }
}

.node-background {
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  filter:
    drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1)) drop-shadow(inset 0 1px 0 rgba(255, 255, 255, 0.3));
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.node-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.node-text-input {
  width: 100%;
  background: transparent;
  border: none;
  text-align: center;
  font-family: inherit;
  outline: none;
  padding: 0;
  transition: all 0.2s ease;

  &:focus {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 2px 4px;
  }
}

// Right Sidebar - AI Panel
.right-sidebar {
  width: 300px;
  background: rgba($window-background, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-left: 1px solid rgba($border-color, 0.3);
  padding: 1rem;
  overflow-y: auto;
  z-index: 50;

  .theme-dark & {
    background: rgba($dark-window-background, 0.9);
    border-left-color: rgba($dark-border-color, 0.3);
  }
}

.ai-suggestions {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  p {
    margin: 0;
    font-size: 0.875rem;
    color: $secondary-text;
    line-height: 1.5;

    .theme-dark & {
      color: $dark-secondary-text;
    }
  }
}

.ai-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 2px solid rgba($system-green, 0.3);
  border-radius: 0.5rem;
  background: rgba($system-green, 0.05);
  color: $system-green;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  font-weight: 500;

  .theme-dark & {
    background: rgba($system-green, 0.1);
  }

  &:hover {
    background: rgba($system-green, 0.1);
    border-color: rgba($system-green, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba($system-green, 0.2);

    .theme-dark & {
      background: rgba($system-green, 0.15);
    }
  }

  &:active {
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .left-sidebar {
    width: 240px;
  }

  .right-sidebar {
    width: 260px;
  }
}

@media (max-width: 900px) {
  .mindmap-workspace {
    flex-direction: column;
  }

  .left-sidebar,
  .right-sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
    border: none;
    border-bottom: 1px solid rgba($border-color, 0.3);

    .theme-dark & {
      border-bottom-color: rgba($dark-border-color, 0.3);
    }
  }

  .canvas-container {
    flex: 1;
    min-height: 400px;
  }

  .header-center {
    display: none;
  }

  .mindmap-header {
    padding: 0.75rem 1rem;
  }

  .node-type-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .node-type-btn {
    padding: 0.75rem 0.5rem;

    span {
      font-size: 0.7rem;
    }
  }
}

@media (max-width: 600px) {
  .mindmap-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .header-left,
  .header-right {
    flex: none;
  }

  .action-controls {
    justify-content: center;
  }

  .control-btn {
    width: 2rem;
    height: 2rem;
  }

  .node-type-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Ultra Premium Animations and Effects
@keyframes nodeAppear {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-10deg);
    filter: blur(4px);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(2deg);
    filter: blur(1px);
  }

  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
    filter: blur(0px);
  }
}

@keyframes connectionDraw {
  0% {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    opacity: 0;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0;
    opacity: 1;
  }
}

@keyframes iconPulse {

  0%,
  100% {
    transform: scale(1);
    filter: drop-shadow(0 2px 4px rgba($system-blue, 0.3));
  }

  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 8px rgba($system-blue, 0.5));
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

@keyframes gradientShift {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

@keyframes floatingGlow {

  0%,
  100% {
    box-shadow: 0 4px 16px rgba($system-blue, 0.1);
  }

  50% {
    box-shadow: 0 8px 32px rgba($system-blue, 0.2);
  }
}

.mindmap-node {
  animation: nodeAppear 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.connection-line {
  animation: connectionDraw 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.node-type-btn {
  animation: floatingGlow 3s ease-in-out infinite;
}

.version-badge {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

// Scrollbar Styling
.left-sidebar,
.right-sidebar {
  scrollbar-width: thin;
  scrollbar-color: rgba($system-blue, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba($system-blue, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba($system-blue, 0.5);
    }
  }
}






















// Variables
$went-well-color: #4cc9f0;
$to-improve-color: #f8961e;
$action-items-color: #f72585;
$light-color: #f8f9fa;
$dark-color: #212529;
$gray-200: #e9ecef;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-800: #343a40;

$border-radius: 8px;
$box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
$transition: all 0.3s ease;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin button-style($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  padding: 8px 16px;
  border-radius: $border-radius;
  font-weight: 500;
  cursor: pointer;
  transition: $transition;
  @include flex-center;
  gap: 8px;

  &:hover:not(:disabled) {
    background-color: darken($bg-color, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Base Styles
.retrospective-app {
  font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  color: $dark-color;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  * {
    box-sizing: border-box;
  }
}

// App Header
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid $gray-200;

  h1 {
    margin: 0;
    font-size: 1.6rem;
    font-weight: 600;
    color: $dark-color;
  }
}

.app-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-button {
  background: none;
  border: none;
  color: $gray-600;
  font-size: 1rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  @include flex-center;
  transition: $transition;

  &:hover {
    background-color: $gray-200;
    color: $dark-color;
  }
}

.error-message {
  background-color: lighten($action-items-color, 40%);
  color: darken($action-items-color, 10%);
  padding: 12px 20px;
  border-radius: $border-radius;
  margin-bottom: 20px;
  @include flex-center;
  gap: 10px;
  border-left: 4px solid $action-items-color;

  i {
    font-size: 1.2rem;
  }
}

// Retro Grid
.retro-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  flex: 1;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.category {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.went-well {
    border-top: 4px solid $went-well-color;
  }

  &.to-improve {
    border-top: 4px solid $to-improve-color;
  }

  &.action-items {
    border-top: 4px solid $action-items-color;
  }
}

.category-header {
  padding: 15px;
  border-bottom: 1px solid $gray-200;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 1.1rem;
  }
}

.count-badge {
  background-color: $gray-200;
  color: $gray-800;
  border-radius: 20px;
  padding: 2px 8px;
  font-size: 0.8rem;
  font-weight: 500;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow-y: auto;
  max-height: 400px;

  li {
    padding: 12px 15px;
    border-bottom: 1px solid $gray-200;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.item-text {
  flex: 1;
  margin-right: 10px;
  word-break: break-word;
}

.item-actions {
  display: flex;
  gap: 5px;
}

.edit-button,
.delete-button,
.move-button {
  background: none;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  @include flex-center;
  cursor: pointer;
  transition: $transition;
}

.edit-button {
  color: $gray-600;

  &:hover {
    background-color: rgba($gray-600, 0.1);
  }
}

.delete-button {
  color: lighten($action-items-color, 10%);

  &:hover {
    background-color: rgba($action-items-color, 0.1);
  }
}

.move-button {
  color: $gray-600;

  &:hover {
    background-color: rgba($gray-600, 0.1);
  }
}

.edit-form {
  display: flex;
  width: 100%;
  gap: 5px;

  input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid $gray-200;
    border-radius: $border-radius;
    font-family: inherit;
  }

  button {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    @include flex-center;
    cursor: pointer;
    border: none;
  }

  .save-button {
    background-color: $went-well-color;
    color: white;
  }

  .cancel-button {
    background-color: $gray-200;
    color: $gray-800;
  }
}

// Add Item Form
.add-item {
  display: flex;
  gap: 10px;
  padding: 15px;
  background-color: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;

  @media (max-width: 576px) {
    flex-direction: column;
  }
}

.category-select {
  padding: 8px 12px;
  border: 1px solid $gray-200;
  border-radius: $border-radius;
  font-family: inherit;
  min-width: 150px;

  @media (max-width: 576px) {
    width: 100%;
  }
}

.item-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid $gray-200;
  border-radius: $border-radius;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: $went-well-color;
  }
}

.add-button {
  @include button-style($went-well-color);

  @media (max-width: 576px) {
    width: 100%;
  }
}





























// Variables
$primary-color: #4361ee;
$secondary-color: #3f37c9;
$accent-color: #4895ef;
$danger-color: #f72585;
$success-color: #4cc9f0;
$light-color: #f8f9fa;
$dark-color: #212529;
$gray-200: #e9ecef;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-800: #343a40;

$border-radius: 8px;
$box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
$transition: all 0.3s ease;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin button-style($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  padding: 8px 16px;
  border-radius: $border-radius;
  font-weight: 500;
  cursor: pointer;
  transition: $transition;
  @include flex-center;
  gap: 8px;

  &:hover:not(:disabled) {
    background-color: darken($bg-color, 10%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Base Styles
.podcast-app {
  font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  color: $dark-color;
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  * {
    box-sizing: border-box;
  }
}

// App Header
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid $gray-200;

  h1 {
    margin: 0;
    font-size: 1.6rem;
    font-weight: 600;
    color: $primary-color;
  }
}

.app-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-button {
  background: none;
  border: none;
  color: $gray-600;
  font-size: 1rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  @include flex-center;
  transition: $transition;

  &:hover {
    background-color: $gray-200;
    color: $dark-color;
  }
}

.error-message {
  background-color: lighten($danger-color, 40%);
  color: darken($danger-color, 10%);
  padding: 12px 20px;
  border-radius: $border-radius;
  margin-bottom: 20px;
  @include flex-center;
  gap: 10px;
  border-left: 4px solid $danger-color;

  i {
    font-size: 1.2rem;
  }
}

.loading-state {
  @include flex-center;
  gap: 10px;
  padding: 40px;
  color: $gray-600;

  i {
    font-size: 1.2rem;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// Main Container
.podcast-container {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 20px;
  flex: 1;
  margin-bottom: 20px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// Episodes Panel
.episodes-panel {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-bar {
  padding: 15px;
  border-bottom: 1px solid $gray-200;
  position: relative;

  input {
    width: 100%;
    padding: 8px 35px 8px 15px;
    border: 1px solid $gray-200;
    border-radius: $border-radius;
    font-family: inherit;

    &:focus {
      outline: none;
      border-color: $primary-color;
    }
  }

  i {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    color: $gray-500;
  }
}

.episodes-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;

  h3 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: $gray-800;
  }
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

li {
  padding: 12px 15px;
  margin-bottom: 10px;
  border-radius: $border-radius;
  background-color: $light-color;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: $transition;
  cursor: pointer;

  &:hover {
    background-color: darken($light-color, 2%);
  }

  &.active {
    background-color: rgba($primary-color, 0.1);
    border-left: 3px solid $primary-color;
  }

  &.listened {
    opacity: 0.8;
  }
}

.episode-info {
  flex: 1;
  margin-right: 10px;

  h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    font-weight: 500;
  }
}

.episode-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.8rem;
  color: $gray-600;

  .date {
    font-size: 0.75rem;
  }
}

.tags {
  display: flex;
  gap: 5px;
}

.tag {
  background-color: rgba($primary-color, 0.1);
  color: $primary-color;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  white-space: nowrap;
}

.more-tags {
  font-size: 0.7rem;
  color: $gray-500;
}

.episode-actions {
  display: flex;
  gap: 5px;
}

.play-button,
.delete-button {
  background: none;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  @include flex-center;
  cursor: pointer;
  transition: $transition;
}

.play-button {
  color: $primary-color;

  &:hover {
    background-color: rgba($primary-color, 0.1);
  }
}

.delete-button {
  color: lighten($danger-color, 10%);

  &:hover {
    background-color: rgba($danger-color, 0.1);
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: $gray-600;

  i {
    font-size: 2rem;
    margin-bottom: 15px;
    color: $gray-500;
  }

  p {
    margin: 0 0 15px 0;
  }
}

.clear-search {
  background: none;
  border: none;
  color: $primary-color;
  text-decoration: underline;
  cursor: pointer;
  font-size: 0.9rem;
}

// Player Panel
.player-panel {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  display: flex;
  flex-direction: column;
}

.player-card {
  padding: 20px;
  border-bottom: 1px solid $gray-200;
}

.player-header {
  margin-bottom: 20px;

  h3 {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
  }
}

.player-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: $gray-600;

  .date {
    font-size: 0.8rem;
  }

  .tags {
    flex-wrap: wrap;
  }
}

audio {
  display: none;
}

.player-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.play-pause-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: $primary-color;
  color: white;
  border: none;
  @include flex-center;
  cursor: pointer;
  transition: $transition;

  &:hover {
    background-color: darken($primary-color, 10%);
  }

  i {
    font-size: 1rem;
  }
}

.time-display {
  display: flex;
  gap: 5px;
  font-size: 0.9rem;
  color: $gray-600;
}

.progress-bar {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: $gray-200;
  border-radius: 3px;
  outline: none;
  cursor: pointer;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: $primary-color;
    cursor: pointer;
  }
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 10px;

  i {
    color: $gray-600;
    font-size: 0.9rem;
  }

  input[type="range"] {
    flex: 1;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: $gray-200;
    border-radius: 2px;
    outline: none;
    cursor: pointer;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: $primary-color;
      cursor: pointer;
    }
  }
}

.episode-notes {
  padding: 20px;
  flex: 1;

  h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: $gray-800;
  }

  p {
    margin: 0;
    line-height: 1.6;
    color: $gray-800;
  }

  .no-notes {
    color: $gray-500;
    font-style: italic;
  }
}

.no-episode-selected {
  @include flex-center;
  flex-direction: column;
  padding: 40px;
  color: $gray-500;

  i {
    font-size: 2rem;
    margin-bottom: 15px;
  }

  p {
    margin: 0;
  }
}

// Add Episode Panel
.add-episode-panel {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 20px;
  margin-top: 20px;

  h3 {
    margin: 0 0 20px 0;
    font-size: 1.2rem;
    color: $primary-color;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-group {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
    font-weight: 500;
    color: $gray-800;
  }

  input,
  textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid $gray-200;
    border-radius: $border-radius;
    font-family: inherit;
    transition: $transition;

    &:focus {
      outline: none;
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
    }
  }

  textarea {
    min-height: 100px;
    resize: vertical;
  }

  &.tags-group {
    grid-column: span 2;

    @media (max-width: 768px) {
      grid-column: span 1;
    }
  }
}

.tags-input {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;

  input {
    flex: 1;
    padding: 8px 12px;
  }
}

.add-tag-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: $primary-color;
  color: white;
  border: none;
  @include flex-center;
  cursor: pointer;
  transition: $transition;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    background-color: darken($primary-color, 10%);
  }
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;

  .tag {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
  }
}

.remove-tag {
  background: none;
  border: none;
  color: inherit;
  padding: 0;
  cursor: pointer;
  @include flex-center;
  width: 16px;
  height: 16px;

  i {
    font-size: 0.6rem;
  }
}

.add-button {
  @include button-style($primary-color);
  margin-top: 10px;
}

















// Variables
$primary-color: #6366f1;
$primary-hover: #4f46e5;
$text-color: #374151;
$light-gray: #f3f4f6;
$medium-gray: #e5e7eb;
$dark-gray: #6b7280;
$danger-color: #ef4444;
$warning-color: #f59e0b;
$success-color: #10b981;

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Base Styles
.mnemonic-app {
  max-width: 56rem;
  margin: 0 auto;
  padding: 1.5rem;
  animation: fadeIn 0.3s ease-out;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;

    .icon {
      color: $primary-color;
      font-size: 1.75rem;
      margin-right: 0.75rem;
    }

    h1 {
      font-size: 1.5rem;
      font-weight: 700;
      color: $text-color;

      span {
        color: $primary-color;
      }
    }
  }
}

// Search and Filter
.mnemonic-controls {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  margin-bottom: 1.5rem;

  &__search {
    position: relative;
    margin-bottom: 1rem;

    .search-icon {
      position: absolute;
      top: 50%;
      left: 0.75rem;
      transform: translateY(-50%);
      color: $dark-gray;
    }

    input {
      width: 100%;
      padding-left: 2.5rem;
      padding-right: 1rem;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      border: 1px solid $medium-gray;
      border-radius: 0.5rem;
      transition: all 0.2s;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
    }
  }

  &__filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;

    .filter-tabs {
      display: flex;
      background: $light-gray;
      border-radius: 0.5rem;
      padding: 0.25rem;

      button {
        padding: 0.25rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
        transition: all 0.2s;

        &.active {
          background: white;
          color: $primary-color;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
      }
    }

    select {
      border: 1px solid $medium-gray;
      border-radius: 0.5rem;
      padding: 0.25rem 0.75rem;
      font-size: 0.875rem;
      transition: all 0.2s;

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
    }
  }
}

// Mnemonic List
.mnemonic-list {
  margin-bottom: 2rem;

  &__empty {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    text-align: center;
    animation: slideUp 0.3s ease-out;

    .icon {
      margin: 0 auto 0.75rem;
      font-size: 2.25rem;
      color: $dark-gray;
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 500;
      color: $text-color;
      margin-bottom: 0.25rem;
    }

    p {
      color: $dark-gray;
    }
  }
}

// Mnemonic Card
.mnemonic-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 1rem;
  animation: slideUp 0.3s ease-out;

  &__header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .content {
      flex: 1;
      min-width: 0;

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: $text-color;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .meta {
        display: flex;
        align-items: center;
        font-size: 0.875rem;
        color: $dark-gray;
        margin-top: 0.25rem;

        .icon {
          margin-right: 0.25rem;
        }

        .edited {
          margin-left: 0.5rem;
          font-size: 0.75rem;
        }
      }
    }

    .actions {
      display: flex;
      gap: 0.5rem;
      margin-left: 0.5rem;

      button {
        padding: 0.25rem;
        border-radius: 50%;
        color: $dark-gray;
        transition: all 0.2s;

        &:hover {
          background: $light-gray;
        }

        &.favorite {
          color: $warning-color;
        }

        &.edit:hover {
          color: $primary-color;
        }

        &.delete:hover {
          color: $danger-color;
        }
      }
    }
  }

  &__tags {
    padding: 0 1rem 0.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;

    .tag {
      display: inline-flex;
      align-items: center;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      font-weight: 500;
      background: rgba($primary-color, 0.1);
      color: $primary-color;

      button {
        margin-left: 0.25rem;
        color: inherit;
        opacity: 0.7;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  &__content {
    padding: 0 1rem 1rem;

    .mnemonic-text {
      font-size: 0.9375rem;
      line-height: 1.5;
      color: $text-color;

      strong {
        font-weight: 600;
      }
    }
  }

  &__explanation {
    background: $light-gray;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.75rem;
    overflow: hidden;

    h4 {
      font-size: 0.875rem;
      font-weight: 500;
      color: $text-color;
      margin-bottom: 0.25rem;
    }

    p {
      font-size: 0.875rem;
      line-height: 1.5;
      color: $text-color;
    }
  }

  &__toggle {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: $primary-color;
    margin-top: 0.5rem;
    transition: color 0.2s;

    &:hover {
      color: $primary-hover;
    }
  }
}

// Form Styles
.mnemonic-form {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  animation: slideUp 0.3s ease-out;

  &__header {
    padding: 1rem;
    border-bottom: 1px solid $medium-gray;

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: $text-color;
      margin: 0;
    }
  }

  &__body {
    padding: 1rem;

    .form-group {
      margin-bottom: 1rem;

      label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: $text-color;
        margin-bottom: 0.25rem;
      }

      input,
      textarea {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid $medium-gray;
        border-radius: 0.375rem;
        font-size: 0.9375rem;
        transition: all 0.2s;

        &:focus {
          outline: none;
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        }
      }

      textarea {
        min-height: 5rem;
        resize: vertical;
      }

      .hint {
        font-size: 0.75rem;
        color: $dark-gray;
        margin-top: 0.25rem;
      }
    }

    .tag-input {
      display: flex;

      input {
        flex: 1;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      button {
        padding: 0 0.75rem;
        background: $primary-color;
        color: white;
        border: none;
        border-top-right-radius: 0.375rem;
        border-bottom-right-radius: 0.375rem;
        transition: background 0.2s;

        &:hover {
          background: $primary-hover;
        }
      }
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 0.5rem;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
      padding-top: 0.5rem;

      button {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;

        &.cancel {
          border: 1px solid $medium-gray;
          color: $text-color;
          background: white;

          &:hover {
            background: $light-gray;
          }
        }

        &.submit {
          background: $primary-color;
          color: white;
          border: none;

          &:hover {
            background: $primary-hover;
          }

          &:disabled {
            background: lighten($primary-color, 25%);
            cursor: not-allowed;
          }
        }
      }
    }
  }
}

// Markdown Content Styles
.prose {
  max-width: 100%;

  p {
    margin-top: 0;
    margin-bottom: 0.5rem;
  }

  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }

  ul,
  ol {
    padding-left: 1.25rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  a {
    color: $primary-color;
    text-decoration: underline;

    &:hover {
      color: $primary-hover;
    }
  }
}

// Loading State
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rem;

  &__circle {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba($primary-color, 0.2);
    border-top-color: $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}