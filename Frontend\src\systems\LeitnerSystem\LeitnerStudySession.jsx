import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

// Material Icons
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import FlipIcon from '@mui/icons-material/Flip';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import TimerIcon from '@mui/icons-material/Timer';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import Looks4Icon from '@mui/icons-material/Looks4';
import Looks5Icon from '@mui/icons-material/Looks5';

const LeitnerStudySession = ({ cards, boxNumber, onBack, onCardReview }) => {
  const { darkMode } = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [answered, setAnswered] = useState(false);
  const [sessionStats, setSessionStats] = useState({
    total: cards.length,
    correct: 0,
    incorrect: 0,
    remaining: cards.length,
    startTime: new Date(),
  });
  const [shuffledCards, setShuffledCards] = useState([]);

  // Get the appropriate box icon
  const getBoxIcon = (boxNum) => {
    switch (boxNum) {
      case 1: return <LooksOneIcon />;
      case 2: return <LooksTwoIcon />;
      case 3: return <Looks3Icon />;
      default: return <LooksOneIcon />;
    }
  };

  // Shuffle cards on component mount or when cards prop changes
  useEffect(() => {
    // Ensure cards is an array and has items
    if (cards && Array.isArray(cards) && cards.length > 0) {
      try {
        // Create a shuffled copy of the cards
        const shuffled = [...cards].sort(() => Math.random() - 0.5);
        setShuffledCards(shuffled);

        // Reset session stats
        setSessionStats({
          total: cards.length,
          correct: 0,
          incorrect: 0,
          remaining: cards.length,
          startTime: new Date(),
        });

        // Reset current index and flip state
        setCurrentIndex(0);
        setIsFlipped(false);
        setAnswered(false);
      } catch (error) {
        console.error("Error processing cards:", error);
        // Set empty shuffled cards as fallback
        setShuffledCards([]);
      }
    } else {
      // If cards is empty or not an array, set shuffledCards to empty array
      setShuffledCards([]);
    }
  }, [cards]);

  // Handle card flip
  const flipCard = () => {
    setIsFlipped(prev => !prev);
  };

  // Handle correct answer
  const handleCorrect = () => {
    if (answered) return;

    // Safety check to ensure the current card exists
    if (!shuffledCards || !shuffledCards[currentIndex]) {
      console.error("Cannot handle correct answer: current card is undefined");
      return;
    }

    setAnswered(true);
    onCardReview(shuffledCards[currentIndex].id, true);

    setSessionStats(prev => ({
      ...prev,
      correct: prev.correct + 1,
      remaining: prev.remaining - 1,
    }));

    // Move to next card after a short delay
    setTimeout(() => {
      nextCard();
    }, 1000);
  };

  // Handle incorrect answer
  const handleIncorrect = () => {
    if (answered) return;

    // Safety check to ensure the current card exists
    if (!shuffledCards || !shuffledCards[currentIndex]) {
      console.error("Cannot handle incorrect answer: current card is undefined");
      return;
    }

    setAnswered(true);
    onCardReview(shuffledCards[currentIndex].id, false);

    setSessionStats(prev => ({
      ...prev,
      incorrect: prev.incorrect + 1,
      remaining: prev.remaining - 1,
    }));

    // Move to next card after a short delay
    setTimeout(() => {
      nextCard();
    }, 1000);
  };

  // Move to next card
  const nextCard = () => {
    // Safety check to ensure shuffledCards exists and has items
    if (!shuffledCards || shuffledCards.length === 0) {
      console.error("Cannot move to next card: no cards available");
      onBack();
      return;
    }

    if (currentIndex < shuffledCards.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setIsFlipped(false);
      setAnswered(false);
    } else {
      // End of session
      // You could show a summary or return to the boxes view
      onBack();
    }
  };

  // Calculate session duration
  const getSessionDuration = () => {
    const now = new Date();
    const diffMs = now - sessionStats.startTime;
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    return `${diffMins}m ${diffSecs}s`;
  };

  // If no cards, show empty state
  if (!shuffledCards || shuffledCards.length === 0) {
    return (
      <div className={`leitner-study-session ${darkMode ? 'theme-dark' : ''}`}>
        <div className="empty-session">
          <h2>No cards to study</h2>
          <p>This box is empty. Add some cards or select a different box.</p>
          <button onClick={onBack}>Back to Boxes</button>
        </div>
      </div>
    );
  }

  // Get current card
  const currentCard = shuffledCards[currentIndex];

  // Additional safety check - if currentCard is undefined, show empty state
  if (!currentCard) {
    return (
      <div className={`leitner-study-session ${darkMode ? 'theme-dark' : ''}`}>
        <div className="empty-session">
          <h2>No card found</h2>
          <p>There was an issue loading the current card. Please try again.</p>
          <button onClick={onBack}>Back to Boxes</button>
        </div>
      </div>
    );
  }

  return (
    <div className={`leitner-study-session ${darkMode ? 'theme-dark' : ''}`}>
      <div className="session-header">
        <div className="session-info">
          <div className="box-indicator">
            {getBoxIcon(boxNumber)}
            <span>Box {boxNumber}</span>
          </div>
          <div className="session-progress">
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{
                  width: `${((sessionStats.correct + sessionStats.incorrect) / sessionStats.total) * 100}%`,
                  backgroundColor: sessionStats.correct > sessionStats.incorrect ? '#28CD41' : '#FF3B30'
                }}
              ></div>
            </div>
            <div className="progress-text">
              {currentIndex + 1} / {shuffledCards.length}
            </div>
          </div>
        </div>

        <div className="session-stats">
          <div className="stat">
            <CheckCircleIcon className="stat-icon correct" />
            <span>{sessionStats.correct}</span>
          </div>
          <div className="stat">
            <CancelIcon className="stat-icon incorrect" />
            <span>{sessionStats.incorrect}</span>
          </div>
          <div className="stat">
            <TimerIcon className="stat-icon" />
            <span>{getSessionDuration()}</span>
          </div>
        </div>
      </div>

      <div className="card-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentCard.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div
              className={`study-card ${isFlipped ? 'flipped' : ''}`}
              onClick={() => !answered && flipCard()}
            >
              <div className="card-face card-front">
                <div className="card-content">
                  <h3>
                    <HelpOutlineIcon className="card-icon" />
                    Question
                  </h3>
                  <div className="card-text">
                    {currentCard.question}
                  </div>

                  {!isFlipped && !answered && (
                    <div className="card-hint">
                      <FlipIcon className="flip-icon" /> Tap to flip
                    </div>
                  )}
                </div>
              </div>

              <div className="card-face card-back">
                <div className="card-content">
                  <h3>
                    <CheckCircleIcon className="card-icon" />
                    Answer
                  </h3>
                  <div className="card-text">
                    {currentCard.answer}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      <div className="answer-controls">
        <button
          className={`answer-button incorrect ${answered && !isFlipped ? 'disabled' : ''}`}
          onClick={handleIncorrect}
          disabled={answered || !isFlipped}
        >
          <CancelIcon />
          <span>Incorrect</span>
        </button>

        <button
          className={`answer-button correct ${answered && !isFlipped ? 'disabled' : ''}`}
          onClick={handleCorrect}
          disabled={answered || !isFlipped}
        >
          <CheckCircleIcon />
          <span>Correct</span>
        </button>
      </div>

      <div className="navigation-controls">
        <button
          className="nav-button"
          onClick={() => setCurrentIndex(prev => Math.max(0, prev - 1))}
          disabled={currentIndex === 0}
        >
          <NavigateBeforeIcon />
        </button>

        <div className="card-counter">
          {currentIndex + 1} / {shuffledCards.length}
        </div>

        <button
          className="nav-button"
          onClick={nextCard}
          disabled={currentIndex === shuffledCards.length - 1}
        >
          <NavigateNextIcon />
        </button>
      </div>
    </div>
  );
};

export default LeitnerStudySession;
