import { useState, useEffect } from 'react';
import { format, isToday, isYesterday, isTomorrow, parseISO } from 'date-fns';


const DailyObjectives = ({ tool, subject }) => {
    const [objectives, setObjectives] = useState([]);
    const [newObjective, setNewObjective] = useState('');
    const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
    const [activeTab, setActiveTab] = useState('today');

    useEffect(() => {
        // Load objectives from localStorage
        const loadObjectives = () => {
            const saved = JSON.parse(localStorage.getItem('dailyObjectives') || '[]');
            const filtered = saved.filter(obj => obj.date === date);
            setObjectives(filtered);
        };

        loadObjectives();

        // Set up date based on active tab
        const today = new Date();
        if (activeTab === 'today') {
            setDate(format(today, 'yyyy-MM-dd'));
        } else if (activeTab === 'yesterday') {
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            setDate(format(yesterday, 'yyyy-MM-dd'));
        } else if (activeTab === 'tomorrow') {
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            setDate(format(tomorrow, 'yyyy-MM-dd'));
        }
    }, [date, activeTab]);

    const addObjective = () => {
        if (newObjective.trim()) {
            const updatedObjectives = [
                ...objectives,
                {
                    id: Date.now(),
                    text: newObjective,
                    date,
                    completed: false,
                    createdAt: new Date().toISOString()
                }
            ];

            updateObjectives(updatedObjectives);
            setNewObjective('');
        }
    };

    const toggleComplete = (id) => {
        const updatedObjectives = objectives.map(obj =>
            obj.id === id ? { ...obj, completed: !obj.completed } : obj
        );
        updateObjectives(updatedObjectives);
    };

    const removeObjective = (id) => {
        const updatedObjectives = objectives.filter(obj => obj.id !== id);
        updateObjectives(updatedObjectives);
    };

    const updateObjectives = (updatedObjectives) => {
        setObjectives(updatedObjectives);
        const allObjectives = JSON.parse(localStorage.getItem('dailyObjectives') || '[]');
        const otherDays = allObjectives.filter(obj => obj.date !== date);
        localStorage.setItem('dailyObjectives', JSON.stringify([...otherDays, ...updatedObjectives]));
    };

    const getDateLabel = () => {
        const parsedDate = parseISO(date);
        if (isToday(parsedDate)) return 'Today';
        if (isYesterday(parsedDate)) return 'Yesterday';
        if (isTomorrow(parsedDate)) return 'Tomorrow';
        return format(parsedDate, 'MMMM d, yyyy');
    };

    const pendingObjectives = objectives.filter(obj => !obj.completed);
    const completedObjectives = objectives.filter(obj => obj.completed);

    return (
        <div className="macos-objectives">
            <div className="header">
                <h2>{subject} Objectives</h2>
                <div className="date-tabs">
                    <button
                        className={`tab ${activeTab === 'yesterday' ? 'active' : ''}`}
                        onClick={() => setActiveTab('yesterday')}
                    >
                        Yesterday
                    </button>
                    <button
                        className={`tab ${activeTab === 'today' ? 'active' : ''}`}
                        onClick={() => setActiveTab('today')}
                    >
                        Today
                    </button>
                    <button
                        className={`tab ${activeTab === 'tomorrow' ? 'active' : ''}`}
                        onClick={() => setActiveTab('tomorrow')}
                    >
                        Tomorrow
                    </button>
                </div>
                <div className="date-display">
                    {getDateLabel()}
                    <input
                        type="date"
                        value={date}
                        onChange={(e) => setDate(e.target.value)}
                        className="date-picker"
                    />
                </div>
            </div>

            <div className="content">
                <div className="to-do-section">
                    <h3>To Do ({pendingObjectives.length})</h3>
                    {pendingObjectives.length > 0 ? (
                        <ul className="objectives-list">
                            {pendingObjectives.map(obj => (
                                <ObjectiveItem
                                    key={obj.id}
                                    obj={obj}
                                    onToggle={toggleComplete}
                                    onRemove={removeObjective}
                                    type="pending"
                                />
                            ))}
                        </ul>
                    ) : (
                        <div className="empty-state">
                            <p>No pending objectives</p>
                        </div>
                    )}
                </div>

                <div className="done-section">
                    <h3>Done ({completedObjectives.length})</h3>
                    {completedObjectives.length > 0 ? (
                        <ul className="objectives-list">
                            {completedObjectives.map(obj => (
                                <ObjectiveItem
                                    key={obj.id}
                                    obj={obj}
                                    onToggle={toggleComplete}
                                    onRemove={removeObjective}
                                    type="completed"
                                />
                            ))}
                        </ul>
                    ) : (
                        <div className="empty-state">
                            <p>No completed objectives yet</p>
                        </div>
                    )}
                </div>

                <div className="add-section">
                    <div className="progress-bar">
                        <div
                            className="progress-fill"
                            style={{
                                width: `${objectives.length > 0
                                    ? (completedObjectives.length / objectives.length * 100)
                                    : 0}%`
                            }}
                        ></div>
                    </div>
                    <p className="progress-text">
                        {objectives.length > 0
                            ? `${completedObjectives.length} of ${objectives.length} completed`
                            : 'Add your first objective'}
                    </p>
                    <div className="input-group">
                        <input
                            type="text"
                            value={newObjective}
                            onChange={(e) => setNewObjective(e.target.value)}
                            placeholder="New objective..."
                            onKeyPress={(e) => e.key === 'Enter' && addObjective()}
                        />
                        <button
                            className="add-button"
                            onClick={addObjective}
                            disabled={!newObjective.trim()}
                        >
                            Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

const ObjectiveItem = ({ obj, onToggle, onRemove, type }) => {
    return (
        <li className={`objective-item ${type}`}>
            <div className="objective-content">
                <button
                    className={`toggle-button ${obj.completed ? 'completed' : ''}`}
                    onClick={() => onToggle(obj.id)}
                >
                    <div className="toggle-circle"></div>
                </button>
                <span className="objective-text">{obj.text}</span>
            </div>
            <button
                className="remove-button"
                onClick={() => onRemove(obj.id)}
            >
                ×
            </button>
        </li>
    );
};

export default DailyObjectives;