import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import SettingsBrightnessIcon from '@mui/icons-material/SettingsBrightness';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import '../../styles/SettingsPanels.scss';

const ThemeSettingsPanel = ({ settings }) => {
  const { darkMode, toggleTheme } = useTheme();
  const { isDarkMode } = settings;
  
  const themeOptions = [
    { id: 'light', label: 'Light', icon: <LightModeIcon />, description: 'Clean, bright interface for daytime use' },
    { id: 'dark', label: 'Dark', icon: <DarkModeIcon />, description: 'Reduced eye strain in low-light environments' },
    { id: 'system', label: 'System', icon: <SettingsBrightnessIcon />, description: 'Follows your device theme settings' },
    { id: 'auto', label: 'Auto', icon: <AccessTimeIcon />, description: 'Adjusts based on time of day' }
  ];
  
  // Current time for the preview
  const currentHour = new Date().getHours();
  const isDaytime = currentHour >= 6 && currentHour < 18;
  
  return (
    <div className={`detail-panel theme-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Theme Settings</h2>
        <p className="panel-description">
          Customize the appearance of Blueprint AI to suit your preferences.
        </p>
      </div>
      
      <div className="settings-group theme-options">
        {themeOptions.map(option => (
          <div 
            key={option.id}
            className={`theme-option ${
              (option.id === 'light' && !darkMode) || 
              (option.id === 'dark' && darkMode) ? 
              'active' : ''
            }`}
            onClick={() => {
              if ((option.id === 'light' && darkMode) || 
                  (option.id === 'dark' && !darkMode)) {
                toggleTheme();
              }
            }}
          >
            <div className="theme-icon">{option.icon}</div>
            <div className="theme-content">
              <h3>{option.label}</h3>
              <p>{option.description}</p>
            </div>
            <div className="theme-selector"></div>
          </div>
        ))}
      </div>
      
      <div className="theme-preview">
        <div className="preview-header">
          <h3>Preview</h3>
        </div>
        
        <div className="preview-container">
          <div className={`preview-frame ${isDarkMode ? 'dark' : 'light'}`}>
            <div className="preview-sidebar"></div>
            <div className="preview-content">
              <div className="preview-header-bar"></div>
              <div className="preview-main">
                <div className="preview-card"></div>
                <div className="preview-card"></div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="preview-controls">
          <button 
            className={`preview-toggle ${!isDarkMode ? 'active' : ''}`}
            onClick={() => toggleTheme()}
          >
            <LightModeIcon />
            <span>Light</span>
          </button>
          
          <button 
            className={`preview-toggle ${isDarkMode ? 'active' : ''}`}
            onClick={() => toggleTheme()}
          >
            <DarkModeIcon />
            <span>Dark</span>
          </button>
        </div>
      </div>
      
      <div className="info-box">
        <p>
          Theme settings affect the appearance of the entire application. 
          The System option will automatically match your device's theme settings.
        </p>
      </div>
    </div>
  );
};

export default ThemeSettingsPanel;
