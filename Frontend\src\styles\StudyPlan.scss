@use "variables" as *;
@use "sass:color";

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.2, 0.8, 0.2, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }
}

// Study Plan Creator
.study-plan-creator-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);

  // Dark theme
  .theme-dark & {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.study-plan-creator-container {
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  // Light theme
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  // Dark theme
  .theme-dark & {
    background-color: $dark-background;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .popup-header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;

    h2 {
      margin: 0;
      font-size: 1.5rem;

      // Light theme
      color: $color-primary;

      // Dark theme
      .theme-dark & {
        color: $color-primary;
      }
    }

    .close-button-popup {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      @include transition;

      // Light theme
      color: color.adjust($color-primary, $alpha: -0.3);

      // Dark theme
      .theme-dark & {
        color: color.adjust($color-primary, $alpha: -0.3);
      }

      &:hover {
        // Light theme
        color: $color-primary;

        // Dark theme
        .theme-dark & {
          color: $color-primary;
        }
      }
    }
  }

  .popup-content {
    padding: 20px;

    form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;

        label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;

          // Light theme
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }

          svg {
            // Light theme
            color: $color-primary;

            // Dark theme
            .theme-dark & {
              color: $color-primary;
            }
          }
        }

        .macos-input,
        .react-datepicker__input-container input {
          padding: 10px 12px;
          border-radius: 8px;
          font-size: 1rem;
          @include transition;

          // Light theme
          border: 1px solid $light-accent-color-2;
          background-color: $light-background;
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            border: 1px solid $dark-accent-color-2;
            background-color: color.adjust($dark-background, $lightness: 5%);
            color: $dark-primary-text;
          }

          &:focus {
            outline: none;

            // Light theme
            border-color: $color-primary;
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);

            // Dark theme
            .theme-dark & {
              border-color: $color-primary;
              box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
            }
          }
        }
      }

      .curriculum-info {
        border-radius: 12px;
        padding: 0;
        //max-height: 300px;
        overflow-y: auto;
        position: relative;
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

        // Custom macOS scrollbar
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba($system-gray, 0.5);
          border-radius: 4px;

          .theme-dark & {
            background: rgba($system-gray, 0.7);
          }
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba($system-gray, 0.7);

          .theme-dark & {
            background: rgba($system-gray, 0.9);
          }
        }

        // Light theme
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(245, 245, 247, 0.9));
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.5);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05),
        0 1px 3px rgba(0, 0, 0, 0.03),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);

        // Dark theme
        .theme-dark & {
          background: linear-gradient(145deg,
              color.adjust($dark-background, $lightness: 5%),
              color.adjust($dark-background, $lightness: 3%));
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.2),
            0 1px 3px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 1px;
          background: linear-gradient(90deg,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0.8) 50%,
              rgba(255, 255, 255, 0) 100%);
          opacity: 0.7;

          .theme-dark & {
            opacity: 0.2;
          }
        }

        h3 {
          margin: 0 0 12px 0;
          font-size: 1.1rem;
          font-weight: 600;
          letter-spacing: -0.2px;
          display: flex;
          align-items: center;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 0;
            width: 40px;
            height: 2px;
            background: $system-blue;
            border-radius: 1px;
            opacity: 0.7;
          }

          // Light theme
          color: $primary-text;
          text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          }
        }

        h4 {
          margin: 16px 0 10px 0;
          font-size: 0.95rem;
          font-weight: 600;
          letter-spacing: -0.1px;
          display: flex;
          align-items: center;

          svg {
            margin-right: 6px;
            font-size: 1rem;
            color: $system-blue;
          }

          // Light theme
          color: $primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        p {
          margin: 0;
          font-weight: 500;
          line-height: 1.5;

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }

        .loading-spinner {
          @include flex-center;
          height: 100px;
          flex-direction: column;
          gap: 12px;

          &::before {
            content: '';
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid rgba($system-blue, 0.2);
            border-top-color: $system-blue;
            animation: spin 1s linear infinite;
          }

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }

          @keyframes spin {
            to {
              transform: rotate(360deg);
            }
          }
        }

        .no-data-message {
          text-align: center;
          padding: 24px 0;
          font-size: 0.95rem;
          opacity: 0.8;

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }

        .curriculum-content {
          .chapters-list {
            ul {
              list-style-type: none;
              padding: 0;
              margin: 0;

              .chapter-item {
                margin-bottom: 16px;
                padding: .5rem;
                height: fit-content;
                border-radius: 8px;
                transition: all 0.2s ease;
                display: flex;
                flex-direction: column;

                strong {
                  text-align: left;
                  width: 100%;
                }

                // Light theme
                background-color: rgba(255, 255, 255, 0.5);
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

                // Dark theme
                .theme-dark & {
                  background-color: rgba(255, 255, 255, 0.03);
                  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                }

                &:hover {
                  // Light theme
                  background-color: rgba(255, 255, 255, 0.8);
                  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

                  // Dark theme
                  .theme-dark & {
                    background-color: rgba(255, 255, 255, 0.05);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                  }
                }

                strong {
                  display: flex;
                  align-items: center;
                  margin-bottom: 8px;
                  font-size: 0.95rem;
                  font-weight: 600;
                  letter-spacing: -0.1px;

                  &::before {
                    content: '📚';
                    margin-right: 8px;
                    font-size: 1rem;
                  }

                  // Light theme
                  color: $system-blue;
                  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);

                  // Dark theme
                  .theme-dark & {
                    color: $system-blue;
                    text-shadow: none;
                  }
                }

                .objectives-list {
                  list-style-type: none;
                  padding-left: .5rem;
                  height: 100%;
                  overflow: hidden;
                  //padding-bottom: 1rem;
                  margin: 0;

                  //border-left: 2px solid rgba($system-blue, 0.2);

                  .theme-dark & {
                    border-left-color: rgba($system-blue, 0.3);
                  }

                  .objective-item {
                    margin-bottom: 0;
                    padding: .5rem;
                    font-size: 0.85rem;
                    border-radius: 6px;
                    position: relative;
                    transition: all 0.2s ease;

                    &::before {
                      content: '•';
                      position: absolute;
                      left: -6px;
                      color: $system-blue;
                      font-size: 1.2rem;
                      line-height: 0;
                      top: 50%;
                    }

                    &:hover {
                      // Light theme
                      background-color: rgba($system-blue, 0.05);

                      // Dark theme
                      .theme-dark & {
                        background-color: rgba($system-blue, 0.1);
                      }
                    }

                    // Light theme
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                      color: $dark-secondary-text;
                    }

                    .objective-meta {
                      display: inline-block;
                      font-size: 0.75rem;
                      margin-left: 6px;
                      padding: 2px 6px;
                      border-radius: 4px;
                      font-weight: 500;

                      // Light theme
                      background-color: rgba($system-blue, 0.1);
                      color: $system-blue;

                      // Dark theme
                      .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                        color: color.adjust($system-blue, $lightness: 20%);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .macos-button {
        padding: 10px 16px;
        border-radius: 8px;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        @include transition;

        &.primary {
          background-color: $color-primary;
          color: white;

          // Light theme
          box-shadow: 0 2px 5px rgba(0, 122, 255, 0.2);

          // Dark theme
          .theme-dark & {
            box-shadow: 0 2px 5px rgba(0, 122, 255, 0.4);
          }

          &:hover {
            background-color: color.adjust($color-primary, $lightness: -5%);
            transform: translateY(-2px);

            // Light theme
            box-shadow: 0 4px 10px rgba(0, 122, 255, 0.25);

            // Dark theme
            .theme-dark & {
              box-shadow: 0 4px 10px rgba(0, 122, 255, 0.5);
            }
          }

          &:active {
            transform: translateY(0);
          }

          &:disabled {
            background-color: color.adjust($color-primary, $lightness: 20%, $saturation: -30%);
            box-shadow: none;
            cursor: not-allowed;
            opacity: 0.7;
          }
        }

        .spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top-color: white;
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

// Study Plan List - macOS style
.study-plans-container {
  padding: 24px;
  position: relative;

  h2 {
    margin: 0 0 24px 0;
    font-size: 24px;
    font-weight: 600;
    color: $primary-text;
    letter-spacing: -0.2px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -8px;
      width: 40px;
      height: 2px;
      background: $system-blue;
      border-radius: 2px;
      opacity: 0.7;
    }
  }

  &.loading,
  &.error,
  &.empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;

    p {
      margin: 16px 0;
      max-width: 500px;
      color: $secondary-text;
      font-size: 15px;
      line-height: 1.5;
    }

    .macos-button {
      margin-top: 20px;
      padding: 10px 18px;
      background: $system-blue;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        background: lighten($system-blue, 5%);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        background: darken($system-blue, 5%);
      }
    }
  }

  .study-plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;

    .study-plan-card {
      border-radius: 12px;
      padding: 20px;
      @include transition;
      background-color: $window-background;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid $border-color;
      position: relative;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.5) 50%,
            rgba(255, 255, 255, 0) 100%);
        z-index: 1;
      }

      &:hover {
        transform: translateY(-4px) scale(1.01);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: rgba($system-blue, 0.3);
      }

      &.completed {
        background: rgba($system-green, 0.05);
        border: 1px solid rgba($system-green, 0.2);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background: $system-green;
          opacity: 1;
        }
      }

      .plan-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        position: relative;

        h3 {
          margin: 0;
          font-size: 16px;
          flex: 1;
          font-weight: 600;
          color: $primary-text;
          letter-spacing: 0.2px;

          // Add ellipsis for long titles
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-right: 16px;
        }

        .progress-indicator {
          position: relative;
          width: 44px;
          height: 44px;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: rgba($system-blue, 0.05);
            z-index: 0;
          }

          .progress-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: 600;
            color: $system-blue;
            z-index: 2;

            &.complete {
              color: $system-green;
            }
          }
        }
      }

      .plan-details {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid $separator-color;

        .detail-item {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 10px;
          color: $secondary-text;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          svg {
            font-size: 16px;
            color: $system-blue;
            opacity: 0.9;
          }
        }
      }

      .plan-status {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .days-left {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -20px;
            width: 1px;
            height: 24px;
            background: $separator-color;
            transform: translateY(-50%);
          }

          .days {
            font-size: 22px;
            font-weight: 600;
            color: $system-blue;

            &.urgent {
              color: $system-red;
            }

            &.warning {
              color: $system-orange;
            }

            &.completed {
              color: $system-green;
            }
          }

          .label {
            font-size: 12px;
            color: $tertiary-text;
            margin-top: 2px;
          }
        }

        .completed-label {
          padding: 5px 10px;
          border-radius: 6px;
          font-weight: 500;
          font-size: 13px;
          background-color: rgba($system-green, 0.1);
          color: $system-green;
          display: flex;
          align-items: center;

          &::before {
            content: '✓';
            margin-right: 5px;
            font-weight: bold;
          }
        }

        .objectives-count {
          display: flex;
          align-items: baseline;
          gap: 2px;
          background: $secondary-background;
          padding: 6px 10px;
          border-radius: 6px;

          .completed {
            font-weight: 600;
            font-size: 15px;
            color: $system-green;
          }

          .total {
            font-weight: 600;
            font-size: 15px;
            color: $primary-text;
          }

          .separator {
            color: $tertiary-text;
            margin: 0 2px;
          }

          .label {
            margin-left: 5px;
            font-size: 12px;
            color: $tertiary-text;
          }
        }
      }

      .plan-actions {
        display: flex;
        gap: 12px;
        margin-top: 8px;

        .view-button,
        .delete-button {
          flex: 1;
          padding: 10px 12px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
          letter-spacing: 0.2px;

          svg {
            font-size: 16px;
          }
        }

        .view-button {
          background: $system-blue;
          color: white;
          border: none;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            background: lighten($system-blue, 5%);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            background: darken($system-blue, 5%);
          }

          &:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
          }
        }

        .delete-button {
          background: $secondary-background;
          color: $system-red;
          border: 1px solid $border-color;

          svg {
            color: $system-red;
          }

          &:hover {
            transform: translateY(-1px);
            background: rgba($system-red, 0.05);
            border-color: rgba($system-red, 0.3);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            background: rgba($system-red, 0.1);
          }

          &:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba($system-red, 0.2);
          }
        }
      }

      /* New styles for preferred days and unavailable periods */
      .days-of-week-selector {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;

        .day-checkbox {
          display: flex;
          align-items: center;
          padding: 6px 12px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s;

          // Light theme
          border: 1px solid $light-accent-color-2;
          background-color: $light-background;

          // Dark theme
          .theme-dark & {
            border: 1px solid $dark-accent-color-2;
            background-color: color.adjust($dark-background, $lightness: 5%);
          }

          &.selected {
            // Light theme
            background-color: color.adjust($color-primary, $alpha: -0.9);
            border-color: $color-primary;

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($color-primary, $alpha: -0.85);
              border-color: $color-primary;
            }
          }

          .checkbox-icon {
            margin-right: 5px;
            font-size: 1.2rem;

            // Light theme
            color: $color-primary;

            // Dark theme
            .theme-dark & {
              color: $color-primary;
            }
          }

          span {
            // Light theme
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }
          }
        }
      }

      .unavailable-periods-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }

      .unavailable-periods-container {
        border-radius: 8px;
        padding: 15px;

        // Light theme
        background-color: $light-background;
        border: 1px solid $light-accent-color-2;

        // Dark theme
        .theme-dark & {
          background-color: color.adjust($dark-background, $lightness: 3%);
          border: 1px solid $dark-accent-color-2;
        }

        .unavailable-period {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          padding-bottom: 15px;

          // Light theme
          border-bottom: 1px solid $light-accent-color-2;

          // Dark theme
          .theme-dark & {
            border-bottom: 1px solid $dark-accent-color-2;
          }

          &:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
          }

          .period-dates {
            display: flex;
            flex: 1;
            gap: 15px;

            .period-date {
              flex: 1;

              label {
                display: block;
                margin-bottom: 5px;
                font-size: 0.9rem;

                // Light theme
                color: $light-secondary-text;

                // Dark theme
                .theme-dark & {
                  color: $dark-secondary-text;
                }
              }
            }
          }

          .remove-period-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            margin-left: 10px;

            // Light theme
            color: $color-error;

            // Dark theme
            .theme-dark & {
              color: color.adjust($color-error, $lightness: 10%);
            }

            &:hover {
              // Light theme
              color: color.adjust($color-error, $lightness: -10%);

              // Dark theme
              .theme-dark & {
                color: color.adjust($color-error, $lightness: 20%);
              }
            }
          }
        }

        .add-period-button {
          margin-top: 10px;

          // Light theme
          background-color: $color-success;

          // Dark theme
          .theme-dark & {
            background-color: $color-success;
          }

          &:hover {
            // Light theme
            background-color: color.adjust($color-success, $lightness: -5%);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($color-success, $lightness: -5%);
            }
          }
        }

        .no-periods-message {
          // Light theme
          color: $light-secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }

          font-style: italic;
          margin: 10px 0;
        }
      }

      /* Validation feedback */
      .validation-feedback {
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;

        // Light theme
        background-color: $light-background;
        border: 1px solid $light-accent-color-2;

        // Dark theme
        .theme-dark & {
          background-color: color.adjust($dark-background, $lightness: 3%);
          border: 1px solid $dark-accent-color-2;
        }

        &.feasible {
          // Light theme
          border-color: $color-success;

          // Dark theme
          .theme-dark & {
            border-color: $color-success;
          }
        }

        &.not-feasible {
          // Light theme
          border-color: $color-error;

          // Dark theme
          .theme-dark & {
            border-color: $color-error;
          }
        }

        h4 {
          margin-top: 0;
          display: flex;
          align-items: center;

          // Light theme
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }

          svg {
            margin-right: 8px;
          }
        }

        .stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
          margin-top: 15px;

          .stat-item {
            padding: 10px;
            border-radius: 6px;

            // Light theme
            background-color: white;
            border: 1px solid $light-accent-color-2;

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($dark-background, $lightness: 5%);
              border: 1px solid $dark-accent-color-2;
            }

            .stat-label {
              font-size: 0.9rem;
              margin-bottom: 5px;

              // Light theme
              color: $light-secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }

            .stat-value {
              font-size: 1.1rem;
              font-weight: 500;

              // Light theme
              color: $light-primary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-primary-text;
              }
            }
          }
        }
      }
    }
  }
}

// Study Plan Detail
.study-plan-detail-container {
  padding: 20px;

  &.loading,
  &.error,
  &.not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;

    p {
      margin: 16px 0;
      max-width: 500px;

      // Light theme
      color: $light-secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    .macos-button {
      margin-top: 16px;
    }
  }

  .header-section {
    margin-bottom: 30px;

    .back-button {
      margin-bottom: 16px;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      @include transition;

      // Light theme
      border: 1px solid $light-accent-color-2;
      background-color: transparent;
      color: $light-secondary-text;

      // Dark theme
      .theme-dark & {
        border: 1px solid $dark-accent-color-2;
        background-color: transparent;
        color: $dark-secondary-text;
      }

      &:hover {
        transform: translateY(-2px);

        // Light theme
        background-color: $light-background;
        border-color: $color-primary;
        color: $color-primary;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
          background-color: color.adjust($dark-background, $lightness: 5%);
          border-color: $color-primary;
          color: $color-primary;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }

    h1 {
      margin: 0 0 16px 0;
      font-size: 2rem;
      font-weight: 600;

      // Light theme
      color: $light-primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .plan-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }

        svg {
          // Light theme
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            color: $color-primary;
          }
        }
      }
    }

    .progress-section {
      display: flex;
      align-items: center;
      gap: 20px;
      border-radius: 12px;
      padding: 20px;

      // Light theme
      background-color: white;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
      border: 1px solid $light-accent-color-2;

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($dark-background, $lightness: 5%);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border: 1px solid $dark-accent-color-2;
      }

      .progress-circle {
        position: relative;

        .progress-label {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 1.2rem;
          font-weight: 600;

          // Light theme
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            color: $color-primary;
          }
        }
      }

      .progress-text {
        h3 {
          margin: 0 0 8px 0;
          font-size: 1.2rem;
          font-weight: 600;

          // Light theme
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        p {
          margin: 0;

          // Light theme
          color: $light-secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }
  }

  .view-mode-selector {
    display: flex;
    gap: 2px;
    margin-bottom: 20px;
    background-color: $system-light-gray;
    border-radius: 8px;
    padding: 2px;
    width: fit-content;

    .theme-dark & {
      background-color: $dark-secondary-background;
    }

    .view-mode-button {
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);
      border: none;

      // Light theme
      background-color: transparent;
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        background-color: transparent;
        color: $dark-secondary-text;
      }

      &:hover:not(.active) {
        // Light theme
        background-color: rgba($window-background, 0.5);
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          background-color: rgba($dark-window-background, 0.3);
          color: $dark-primary-text;
        }
      }

      &.active {
        // Light theme
        background-color: $window-background;
        color: $system-blue;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
          background-color: $dark-window-background;
          color: $system-blue;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }
      }

      svg {
        font-size: 18px;
        color: inherit;
      }
    }
  }

  .schedule-section {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .date-selector {
      border-radius: 12px;
      padding: 16px;

      // Light theme
      background-color: $window-background;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid $border-color;

      // Dark theme
      .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid $dark-border-color;
      }

      h3 {
        margin: 0 0 16px 0;
        font-size: 1.2rem;
        font-weight: 600;
        letter-spacing: -0.2px;

        // Light theme
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .date-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .date-button {
          padding: 8px 12px;
          border-radius: 8px;
          font-size: 0.9rem;
          text-align: left;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);

          // Light theme
          border: 1px solid $border-color;
          background-color: transparent;
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            border: 1px solid $dark-border-color;
            background-color: transparent;
            color: $dark-secondary-text;
          }

          &:hover {
            // Light theme
            background-color: rgba($system-light-gray, 0.5);
            border-color: $border-color;
            color: $primary-text;

            // Dark theme
            .theme-dark & {
              background-color: rgba($dark-secondary-background, 0.5);
              border-color: $dark-border-color;
              color: $dark-primary-text;
            }
          }

          &.active {
            // Light theme
            background-color: rgba($system-blue, 0.1);
            color: $system-blue;
            border-color: $system-blue;
            font-weight: 500;

            // Dark theme
            .theme-dark & {
              background-color: rgba($system-blue, 0.15);
              color: $system-blue;
              border-color: $system-blue;
            }
          }
        }
      }
    }

    .daily-schedule {
      border-radius: 12px;
      padding: 20px;

      // Light theme
      background-color: $window-background;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid $border-color;

      // Dark theme
      .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid $dark-border-color;
      }

      h3 {
        margin: 0 0 20px 0;
        font-size: 1.2rem;
        font-weight: 600;
        padding-bottom: 12px;
        letter-spacing: -0.2px;

        // Light theme
        color: $primary-text;
        border-bottom: 1px solid $border-color;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
          border-bottom: 1px solid $dark-border-color;
        }
      }

      .objectives-list {
        display: flex;
        flex-direction: column;
        gap: 10px;

        // macOS-style scrollbar
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: $system-gray;
          border-radius: 4px;
          border: 2px solid transparent;
          background-clip: content-box;

          .theme-dark & {
            background-color: $system-dark-gray;
          }
        }

        .objective-item {
          display: flex;
          gap: 12px;
          padding: 14px 16px;
          border-radius: 10px;
          cursor: pointer;
          position: relative;
          backdrop-filter: blur(5px);
          transition: all 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);

          // Light theme
          background-color: rgba($window-background, 0.8);
          border: 1px solid $border-color;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

          // Dark theme
          .theme-dark & {
            background-color: rgba($dark-window-background, 0.8);
            border: 1px solid $dark-border-color;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }

          &:hover {
            transform: translateY(-2px);

            // Light theme
            background-color: $window-background;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-color: rgba($system-blue, 0.3);

            // Dark theme
            .theme-dark & {
              background-color: $dark-window-background;
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
              border-color: rgba($system-blue, 0.4);
            }

            .check-icon {
              transform: scale(1.05);
            }
          }

          &:active {
            transform: translateY(0);
            transition: all 0.1s ease-out;

            // Light theme
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

            // Dark theme
            .theme-dark & {
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
            }
          }

          &.completed {
            // Light theme
            background-color: rgba($window-background, 0.8);
            border: 1px solid rgba($system-green, 0.3);
            box-shadow: 0 1px 3px rgba($system-green, 0.1);

            // Dark theme
            .theme-dark & {
              background-color: rgba($dark-window-background, 0.8);
              border: 1px solid rgba($system-green, 0.3);
              box-shadow: 0 1px 3px rgba($system-green, 0.15);
            }

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 4px;
              border-radius: 4px 0 0 4px;
              background-color: $system-green;
            }

            .objective-text {
              text-decoration: line-through;

              // Light theme
              color: $secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }

            &:hover {
              // Light theme
              border-color: rgba($system-green, 0.5);
              box-shadow: 0 5px 15px rgba($system-green, 0.1);

              // Dark theme
              .theme-dark & {
                border-color: rgba($system-green, 0.5);
                box-shadow: 0 5px 15px rgba($system-green, 0.15);
              }
            }
          }

          .check-icon {
            display: flex;
            align-items: flex-start;
            padding-top: 2px;
            transition: transform 0.2s ease;

            .completed-icon {
              font-size: 22px;
              // Light theme
              color: $system-green;
              filter: drop-shadow(0 1px 2px rgba($system-green, 0.3));

              // Dark theme
              .theme-dark & {
                color: $system-green;
                filter: drop-shadow(0 1px 3px rgba($system-green, 0.4));
              }
            }

            .incomplete-icon {
              font-size: 22px;
              // Light theme
              color: $system-gray;

              // Dark theme
              .theme-dark & {
                color: $system-dark-gray;
              }
            }
          }

          .objective-content {
            flex: 1;

            .objective-text {
              margin: 0 0 10px 0;
              font-weight: 500;
              line-height: 1.5;
              font-size: 0.95rem;
              letter-spacing: -0.01em;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

              // Light theme
              color: $primary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-primary-text;
              }
            }

            .objective-meta {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              align-items: center;

              span {
                font-size: 0.75rem;
                padding: 3px 8px;
                border-radius: 6px;
                font-weight: 500;
                letter-spacing: -0.01em;
                display: inline-flex;
                align-items: center;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
              }

              .subject {
                // Light theme
                background-color: rgba($system-blue, 0.08);
                color: $system-blue;
                border: 1px solid rgba($system-blue, 0.15);

                // Dark theme
                .theme-dark & {
                  background-color: rgba($system-blue, 0.12);
                  color: $system-blue;
                  border: 1px solid rgba($system-blue, 0.2);
                }
              }

              .chapter {
                // Light theme
                background-color: rgba($system-blue, 0.08);
                color: $system-blue;
                border: 1px solid rgba($system-blue, 0.15);

                // Dark theme
                .theme-dark & {
                  background-color: rgba($system-blue, 0.12);
                  color: $system-blue;
                  border: 1px solid rgba($system-blue, 0.2);
                }
              }

              .time {
                // Light theme
                background-color: rgba($system-gray, 0.08);
                color: $system-dark-gray;
                border: 1px solid rgba($system-gray, 0.15);

                // Dark theme
                .theme-dark & {
                  background-color: rgba($system-dark-gray, 0.12);
                  color: $system-light-gray;
                  border: 1px solid rgba($system-dark-gray, 0.2);
                }

                &::before {
                  content: '⏱️';
                  margin-right: 4px;
                  font-size: 0.8rem;
                }
              }

              .difficulty {
                position: relative;
                padding-left: 22px;

                &::before {
                  content: '';
                  position: absolute;
                  left: 8px;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                }

                &.level-1 {
                  // Light theme
                  background-color: rgba($system-green, 0.08);
                  color: darken($system-green, 10%);
                  border: 1px solid rgba($system-green, 0.15);

                  // Dark theme
                  .theme-dark & {
                    background-color: rgba($system-green, 0.12);
                    color: $system-green;
                    border: 1px solid rgba($system-green, 0.2);
                  }

                  &::before {
                    background-color: $system-green;
                    box-shadow: 0 0 4px rgba($system-green, 0.5);
                  }
                }

                &.level-2 {
                  // Light theme
                  background-color: rgba($system-green, 0.08);
                  color: darken($system-green, 10%);
                  border: 1px solid rgba($system-green, 0.15);

                  // Dark theme
                  .theme-dark & {
                    background-color: rgba($system-green, 0.12);
                    color: $system-green;
                    border: 1px solid rgba($system-green, 0.2);
                  }

                  &::before {
                    background-color: $system-green;
                    box-shadow: 0 0 4px rgba($system-green, 0.5);
                  }
                }

                &.level-3 {
                  // Light theme
                  background-color: rgba($system-orange, 0.08);
                  color: darken($system-orange, 10%);
                  border: 1px solid rgba($system-orange, 0.15);

                  // Dark theme
                  .theme-dark & {
                    background-color: rgba($system-orange, 0.12);
                    color: $system-orange;
                    border: 1px solid rgba($system-orange, 0.2);
                  }

                  &::before {
                    background-color: $system-orange;
                    box-shadow: 0 0 4px rgba($system-orange, 0.5);
                  }
                }

                &.level-4,
                &.level-5 {
                  // Light theme
                  background-color: rgba($system-red, 0.08);
                  color: darken($system-red, 10%);
                  border: 1px solid rgba($system-red, 0.15);

                  // Dark theme
                  .theme-dark & {
                    background-color: rgba($system-red, 0.12);
                    color: $system-red;
                    border: 1px solid rgba($system-red, 0.2);
                  }

                  &::before {
                    background-color: $system-red;
                    box-shadow: 0 0 4px rgba($system-red, 0.5);
                  }
                }
              }

              .date {
                // Light theme
                background-color: rgba($system-blue, 0.08);
                color: $system-blue;
                border: 1px solid rgba($system-blue, 0.15);

                // Dark theme
                .theme-dark & {
                  background-color: rgba($system-blue, 0.12);
                  color: $system-blue;
                  border: 1px solid rgba($system-blue, 0.2);
                }

                &::before {
                  content: '📅';
                  margin-right: 4px;
                  font-size: 0.8rem;
                }
              }
            }
          }
        }
      }

      .no-objectives {
        @include flex-center;
        height: 200px;
        font-style: italic;

        // Light theme
        color: $tertiary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }
    }
  }
}

// All Objectives Section
.all-objectives-section {
  border-radius: 12px;
  overflow: hidden;

  // Light theme
  background-color: $window-background;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid $border-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid $dark-border-color;
  }

  .all-objectives-controls {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    backdrop-filter: blur(10px);

    // Light theme
    border-bottom: 1px solid $border-color;
    background-color: rgba($window-background, 0.8);

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-border-color;
      background-color: rgba($dark-window-background, 0.8);
    }

    h3 {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
      letter-spacing: -0.2px;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .controls {
      display: flex;
      gap: 16px;
      align-items: center;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
      }

      .filter-control,
      .sort-control {
        display: flex;
        align-items: center;
        gap: 8px;

        label {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 0.9rem;
          font-weight: 500;

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }

          svg {
            font-size: 18px;
            // Light theme
            color: $system-blue;

            // Dark theme
            .theme-dark & {
              color: $system-blue;
            }
          }
        }

        select {
          padding: 8px 12px;
          border-radius: 8px;
          font-size: 0.9rem;
          cursor: pointer;
          appearance: none;
          background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: right 8px center;
          background-size: 16px;
          padding-right: 32px;

          // Light theme
          border: 1px solid $border-color;
          background-color: $window-background;
          color: $primary-text;

          // Dark theme
          .theme-dark & {
            border: 1px solid $dark-border-color;
            background-color: $dark-window-background;
            color: $dark-primary-text;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23AEAEB2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
          }

          &:focus {
            outline: none;

            // Light theme
            border-color: $system-blue;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.2);

            // Dark theme
            .theme-dark & {
              border-color: $system-blue;
              box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
            }
          }

          &:hover {
            // Light theme
            border-color: color.adjust($border-color, $lightness: -10%);

            // Dark theme
            .theme-dark & {
              border-color: color.adjust($dark-border-color, $lightness: 10%);
            }
          }
        }
      }
    }
  }

  .all-objectives-list {
    max-height: 600px;
    overflow-y: auto;
    padding: 16px;

    // macOS-style scrollbar
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: $system-gray;
      border-radius: 4px;
      border: 2px solid transparent;
      background-clip: content-box;

      .theme-dark & {
        background-color: $system-dark-gray;
      }
    }

    .objective-item {
      margin-bottom: 10px;
      transition: all 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);
      position: relative;
      backdrop-filter: blur(5px);

      // Light theme
      background-color: rgba($window-background, 0.8);
      border: 1px solid $border-color;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      // Dark theme
      .theme-dark & {
        background-color: rgba($dark-window-background, 0.8);
        border: 1px solid $dark-border-color;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      &:hover {
        transform: translateY(-2px);

        // Light theme
        background-color: $window-background;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border-color: rgba($system-blue, 0.3);

        // Dark theme
        .theme-dark & {
          background-color: $dark-window-background;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
          border-color: rgba($system-blue, 0.4);
        }

        .check-icon {
          transform: scale(1.05);
        }
      }

      &.completed {
        // Light theme
        background-color: rgba($window-background, 0.8);
        border: 1px solid rgba($system-green, 0.3);
        box-shadow: 0 1px 3px rgba($system-green, 0.1);

        // Dark theme
        .theme-dark & {
          background-color: rgba($dark-window-background, 0.8);
          border: 1px solid rgba($system-green, 0.3);
          box-shadow: 0 1px 3px rgba($system-green, 0.15);
        }

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          border-radius: 4px 0 0 4px;
          background-color: $system-green;
        }

        &:hover {
          // Light theme
          border-color: rgba($system-green, 0.5);
          box-shadow: 0 5px 15px rgba($system-green, 0.1);

          // Dark theme
          .theme-dark & {
            border-color: rgba($system-green, 0.5);
            box-shadow: 0 5px 15px rgba($system-green, 0.15);
          }
        }
      }

      .check-icon {
        transition: transform 0.2s ease;

        .completed-icon {
          font-size: 22px;
          // Light theme
          color: $system-green;
          filter: drop-shadow(0 1px 2px rgba($system-green, 0.3));

          // Dark theme
          .theme-dark & {
            color: $system-green;
            filter: drop-shadow(0 1px 3px rgba($system-green, 0.4));
          }
        }

        .incomplete-icon {
          font-size: 22px;
        }
      }

      .objective-content {
        .objective-text {
          font-size: 0.95rem;
          letter-spacing: -0.01em;
          line-height: 1.5;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .objective-meta {
          align-items: center;

          span {
            font-size: 0.75rem;
            padding: 3px 8px;
            border-radius: 6px;
            font-weight: 500;
            letter-spacing: -0.01em;
            display: inline-flex;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          }

          .date {
            // Light theme
            background-color: rgba($system-blue, 0.08);
            color: $system-blue;
            border: 1px solid rgba($system-blue, 0.15);

            // Dark theme
            .theme-dark & {
              background-color: rgba($system-blue, 0.12);
              color: $system-blue;
              border: 1px solid rgba($system-blue, 0.2);
            }

            &::before {
              content: '📅';
              margin-right: 4px;
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
}

// Chapters section
.chapters-section {
  border-radius: 12px;
  overflow: hidden;

  // Light theme
  background-color: $window-background;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid $border-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid $dark-border-color;
  }

  h3 {
    margin: 0;
    padding: 16px 20px;
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: -0.2px;

    // Light theme
    border-bottom: 1px solid $border-color;
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-border-color;
      color: $dark-primary-text;
    }
  }

  .chapters-list {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 600px;
    overflow-y: auto;

    // macOS-style scrollbar
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: $system-gray;
      border-radius: 4px;
      border: 2px solid transparent;
      background-clip: content-box;

      .theme-dark & {
        background-color: $system-dark-gray;
      }
    }

    .chapter-item {
      border-radius: 10px;
      overflow: hidden;

      // Light theme
      background-color: $secondary-background;
      border: 1px solid $border-color;

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        border: 1px solid $dark-border-color;
      }

      .chapter-header {
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s ease;

        // Light theme
        background-color: rgba($system-blue, 0.05);
        border-bottom: 1px solid $border-color;

        // Dark theme
        .theme-dark & {
          background-color: rgba($system-blue, 0.1);
          border-bottom: 1px solid $dark-border-color;
        }

        &:hover {
          // Light theme
          background-color: rgba($system-blue, 0.1);

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-blue, 0.15);
          }
        }

        .chapter-header-content {
          flex: 1;

          h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;

            // Light theme
            color: $system-blue;

            // Dark theme
            .theme-dark & {
              color: $system-blue;
            }
          }

          .chapter-meta {
            display: flex;
            gap: 12px;
            margin-top: 4px;
            font-size: 0.85rem;

            // Light theme
            color: $tertiary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-tertiary-text;
            }

            .completion-percentage {
              font-weight: 500;

              // Light theme
              color: $system-green;

              // Dark theme
              .theme-dark & {
                color: $system-green;
              }
            }
          }
        }

        .expand-icon {
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            font-size: 1.5rem;

            // Light theme
            color: $system-blue;

            // Dark theme
            .theme-dark & {
              color: $system-blue;
            }
          }
        }
      }

      .chapter-objectives {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .no-objectives {
        padding: 20px;
        text-align: center;
        font-style: italic;

        // Light theme
        color: $tertiary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }
    }
  }

  .no-chapters {
    padding: 40px 20px;
    text-align: center;
    font-style: italic;

    // Light theme
    color: $tertiary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }
}

// Animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}