#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPythonBackend() {
    const backendPath = path.join(__dirname, '../../Backend/app.py');
    return fs.existsSync(backendPath);
}

function checkNodeServer() {
    const serverPath = path.join(__dirname, '../server.js');
    return fs.existsSync(serverPath);
}

function startProcess(command, args, options, label, color) {
    return new Promise((resolve, reject) => {
        log(`🚀 Starting ${label}...`, color);
        
        const process = spawn(command, args, {
            stdio: 'pipe',
            shell: true,
            ...options
        });

        process.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                log(`[${label}] ${output}`, color);
            }
        });

        process.stderr.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                log(`[${label}] ${output}`, 'red');
            }
        });

        process.on('close', (code) => {
            if (code !== 0) {
                log(`❌ ${label} exited with code ${code}`, 'red');
                reject(new Error(`${label} failed`));
            } else {
                log(`✅ ${label} started successfully`, 'green');
                resolve();
            }
        });

        // Return process for cleanup
        return process;
    });
}

async function startDevelopment() {
    log('🎯 Starting Full Development Environment', 'bright');
    log('=====================================', 'cyan');

    const processes = [];

    try {
        // Check if backend exists
        if (checkPythonBackend()) {
            log('✅ Python backend found', 'green');
        } else {
            log('⚠️  Python backend not found at ../Backend/app.py', 'yellow');
        }

        // Check if Node server exists
        if (checkNodeServer()) {
            log('✅ Node.js server found', 'green');
        } else {
            log('⚠️  Node.js server not found at ./server.js', 'yellow');
        }

        log('', 'reset');

        // Start Vite frontend
        const viteProcess = spawn('npm', ['run', 'dev:frontend'], {
            stdio: 'pipe',
            shell: true,
            cwd: __dirname + '/..'
        });
        processes.push(viteProcess);

        // Start Python backend if it exists
        if (checkPythonBackend()) {
            const pythonProcess = spawn('python', ['app.py'], {
                stdio: 'pipe',
                shell: true,
                cwd: path.join(__dirname, '../../Backend')
            });
            processes.push(pythonProcess);
        }

        // Start Node.js server if it exists
        if (checkNodeServer()) {
            const nodeProcess = spawn('node', ['server.js'], {
                stdio: 'pipe',
                shell: true,
                cwd: __dirname + '/..'
            });
            processes.push(nodeProcess);
        }

        // Handle output from all processes
        processes.forEach((proc, index) => {
            const labels = ['VITE', 'PYTHON', 'NODE'];
            const colors = ['cyan', 'yellow', 'green'];
            
            proc.stdout.on('data', (data) => {
                const output = data.toString().trim();
                if (output) {
                    log(`[${labels[index]}] ${output}`, colors[index]);
                }
            });

            proc.stderr.on('data', (data) => {
                const output = data.toString().trim();
                if (output && !output.includes('DeprecationWarning')) {
                    log(`[${labels[index]}] ${output}`, 'red');
                }
            });
        });

        log('🎉 All services starting...', 'green');
        log('📱 Frontend: http://localhost:5173', 'cyan');
        log('🐍 Python API: http://localhost:5000', 'yellow');
        log('🟢 Node.js API: http://localhost:5001', 'green');
        log('', 'reset');
        log('Press Ctrl+C to stop all services', 'bright');

    } catch (error) {
        log(`❌ Error starting development environment: ${error.message}`, 'red');
        
        // Cleanup processes
        processes.forEach(proc => {
            if (proc && !proc.killed) {
                proc.kill();
            }
        });
        
        process.exit(1);
    }

    // Handle cleanup on exit
    process.on('SIGINT', () => {
        log('\n🛑 Shutting down all services...', 'yellow');
        
        processes.forEach((proc, index) => {
            if (proc && !proc.killed) {
                const labels = ['VITE', 'PYTHON', 'NODE'];
                log(`Stopping ${labels[index]}...`, 'yellow');
                proc.kill();
            }
        });
        
        setTimeout(() => {
            log('👋 All services stopped. Goodbye!', 'green');
            process.exit(0);
        }, 1000);
    });
}

// Start the development environment
startDevelopment().catch(error => {
    log(`❌ Failed to start development environment: ${error.message}`, 'red');
    process.exit(1);
});
