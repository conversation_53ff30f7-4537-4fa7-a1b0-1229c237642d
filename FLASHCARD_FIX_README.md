# Flashcard Duplication Fix

This document explains the issue with flashcards increasing automatically and the implemented fixes.

## Problem

The application had an issue where flashcards kept increasing automatically, growing from 100 to over 6000 without user requests. This was causing:

1. Database bloat with duplicate flashcards
2. Performance issues when loading flashcards
3. 500 Internal Server Error when saving flashcards due to unique constraint violations

## Root Causes

After investigation, we identified several issues:

1. **Duplicate Flashcard Generation**: The document processing code in `app.py` was generating and saving flashcards twice for each document.

2. **No Unique Constraint**: The database didn't have a unique constraint to prevent duplicate flashcards.

3. **No Cleanup**: When generating new flashcards, the code didn't delete existing AI-generated flashcards first.

4. **No Limits**: There was no limit on the number of flashcards that could be generated per chapter.

5. **Client-Side Issues**: The client was sending all flashcards in a single request, which could exceed payload size limits and didn't handle duplicate questions.

## Implemented Fixes

### Backend Fixes

1. **Added Unique Constraint**: Created a unique constraint on `user_id`, `subject_id`, `chapter_id`, and `question` to prevent duplicate flashcards.

2. **Removed Duplicate Code**: Fixed the duplicate flashcard generation in `app.py`.

3. **Added Cleanup**: Added code to delete existing AI-generated flashcards before generating new ones.

4. **Limited Flashcard Generation**: Set a maximum of 20 flashcards per chapter to prevent excessive generation.

### Frontend Fixes

1. **Duplicate Detection**: Modified the `saveCardsToDatabase` function in `FlashCards.jsx` to filter out duplicate questions before sending to the server.

2. **Batch Processing**: Implemented batch processing to send flashcards in smaller groups (20 at a time) to avoid payload size limits.

3. **Error Handling**: Improved error handling to continue processing batches even if one batch fails.

4. **Local Storage Backup**: Ensured flashcards are always saved to local storage as a backup, even if server saving fails.

## Files Modified

1. `Backend/app.py` - Fixed duplicate flashcard generation and added cleanup code
2. `Backend/document_processor.py` - Added limits to flashcard generation
3. `Frontend/src/components/tools/list/FlashCards.jsx` - Improved client-side handling of flashcards

## Additional Scripts

1. `Backend/add_flashcard_constraint.py` - Script to add a unique constraint to the flashcards table and remove existing duplicates
2. `Backend/check_flashcard_error.py` - Script to diagnose issues with saving flashcards
3. `fix_flashcards.py` - Main script to run all the fixes

## How to Apply the Fix

1. Run the `fix_flashcards.py` script from the project root directory:
   ```
   python fix_flashcards.py
   ```

2. This will:
   - Clean up existing duplicate flashcards
   - Add a unique constraint to prevent future duplicates
   - Verify that the code changes were applied correctly

## Testing the Fix

After applying the fixes:

1. Try uploading a document and generating flashcards
2. Verify that only a reasonable number of flashcards are generated (max 20 per chapter)
3. Try saving flashcards from the FlashCards component
4. Verify that no 500 errors occur when saving flashcards

## Monitoring

Keep an eye on:

1. The total number of flashcards in the database
2. Any 500 errors when saving flashcards
3. Performance when loading flashcards

If the issue persists, additional investigation may be needed.

## Technical Details

### Unique Constraint

The unique constraint `unique_flashcard_question` ensures that a user cannot have two flashcards with the same question for the same chapter and subject. This prevents duplicate flashcards from being created.

### Batch Processing

The client now processes flashcards in batches of 20 to avoid exceeding payload size limits. This also helps with performance by breaking up large requests into smaller chunks.

### Duplicate Detection

Before sending flashcards to the server, the client now filters out duplicate questions, keeping only the most recently updated version of each flashcard.

## Future Improvements

1. Add a database migration script to properly manage schema changes
2. Implement server-side pagination for flashcards to improve performance
3. Add more robust error handling and reporting
4. Consider implementing a queue system for processing large numbers of flashcards
