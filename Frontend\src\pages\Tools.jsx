import React from 'react';
import ToolsGrid from '../components/tools/ToolsGrid';
import '../styles/tools.scss';

const Tools = () => {
    // Determine if dark mode is active
    const isDarkMode = document.body.classList.contains('theme-dark');
    const themeClass = isDarkMode ? 'theme-dark' : '';

    return (
        <div className={`tools-container ${themeClass}`}>
            <div className="tools-header">
                <h1>Study Tools</h1>
                <p>Select a tool to enhance your learning experience</p>
            </div>
            <ToolsGrid />
        </div>
    );
};

export default Tools;
