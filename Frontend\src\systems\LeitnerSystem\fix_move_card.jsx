/**
 * This file contains the modified moveCard function for the LeitnerSystem component
 * to fix the 500 error when saving flashcards.
 * 
 * The issue is that cards need to be marked as modified before saving.
 * 
 * Replace the moveCard function in LeitnerSystem.jsx with this version.
 */

// Modified moveCard function to mark cards as modified
const moveCard = (cardId, fromBox, toBox) => {
  if (toBox < 1 || toBox > 3) return;

  // Create a copy of the boxes state
  const updatedBoxes = { ...boxes };

  // Find the card in the fromBox
  const cardIndex = updatedBoxes[fromBox].findIndex(card => card.id === cardId);
  if (cardIndex === -1) {
    console.error(`Card with ID ${cardId} not found in box ${fromBox}`);
    return;
  }

  // Get the card and remove it from the fromBox
  const card = { ...updatedBoxes[fromBox][cardIndex] };
  updatedBoxes[fromBox] = updatedBoxes[fromBox].filter(card => card.id !== cardId);

  // Update the card's box_level and mark it as modified
  card.box_level = toBox;
  card.isModified = true;

  // Add the card to the toBox
  updatedBoxes[toBox] = [...updatedBoxes[toBox], card];

  // Update the state
  setBoxes(updatedBoxes);

  // Save the changes to the server
  saveBoxes();
};
