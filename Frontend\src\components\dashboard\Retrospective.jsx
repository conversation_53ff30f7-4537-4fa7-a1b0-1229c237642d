import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useAuth } from '../../../authContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';
import '../../styles/retrospective_dashboard.scss';

const Retrospective = () => {
    const { token } = useAuth();
    const { darkMode } = useTheme();
    const [showModal, setShowModal] = useState(false);
    const [retrospectives, setRetrospectives] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [subjects, setSubjects] = useState([]);
    const [selectedSubject, setSelectedSubject] = useState('');
    const [formData, setFormData] = useState({
        subject_id: '',
        went_well: '',
        to_improve: '',
        action_items: ''
    });
    const [editingId, setEditingId] = useState(null);
    const [showForm, setShowForm] = useState(false);
    const modalRef = useRef(null);

    const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

    // Fetch retrospectives and subjects
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                // Fetch retrospectives
                const retrospectivesResponse = await axios.get(
                    `${API_URL}/api/retrospectives${selectedSubject ? `?subjectId=${selectedSubject}` : ''}`,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );

                if (retrospectivesResponse.data.success) {
                    setRetrospectives(retrospectivesResponse.data.retrospectives);
                }

                // Fetch subjects
                const subjectsResponse = await axios.get(
                    `${API_URL}/api/subjects`,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );

                if (subjectsResponse.data.success) {
                    setSubjects(subjectsResponse.data.subjects || []);
                }

                setError(null);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        if (token) {
            fetchData();
        }
    }, [token, API_URL, selectedSubject]);

    // Process retrospectives into the format needed for display
    const notes = {
        wentWell: {
            count: retrospectives.filter(r => r.went_well && r.went_well.trim() !== '').length,
            label: 'Went Well',
            items: retrospectives
                .filter(r => r.went_well && r.went_well.trim() !== '')
                .map(r => ({
                    id: r.id,
                    title: r.went_well.split('.')[0] || 'Went Well Item',
                    details: r.went_well,
                    subject_id: r.subject_id,
                    created_at: r.created_at
                }))
        },
        toImprove: {
            count: retrospectives.filter(r => r.to_improve && r.to_improve.trim() !== '').length,
            label: 'To Improve',
            items: retrospectives
                .filter(r => r.to_improve && r.to_improve.trim() !== '')
                .map(r => ({
                    id: r.id,
                    title: r.to_improve.split('.')[0] || 'To Improve Item',
                    details: r.to_improve,
                    subject_id: r.subject_id,
                    created_at: r.created_at
                }))
        },
        actionItems: {
            count: retrospectives.filter(r => r.action_items && r.action_items.trim() !== '').length,
            label: 'Action Items',
            items: retrospectives
                .filter(r => r.action_items && r.action_items.trim() !== '')
                .map(r => ({
                    id: r.id,
                    title: r.action_items.split('.')[0] || 'Action Item',
                    details: r.action_items,
                    subject_id: r.subject_id,
                    created_at: r.created_at
                }))
        }
    };

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Handle subject selection
    const handleSubjectChange = (e) => {
        setSelectedSubject(e.target.value);
        setFormData(prev => ({
            ...prev,
            subject_id: e.target.value || null
        }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            if (editingId) {
                // Update existing retrospective
                await axios.put(
                    `${API_URL}/api/retrospectives/${editingId}`,
                    formData,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );
            } else {
                // Create new retrospective
                await axios.post(
                    `${API_URL}/api/retrospectives`,
                    formData,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );
            }

            // Reset form and refresh data
            setFormData({
                subject_id: selectedSubject || '',
                went_well: '',
                to_improve: '',
                action_items: ''
            });
            setEditingId(null);
            setShowForm(false);

            // Fetch updated data
            const response = await axios.get(
                `${API_URL}/api/retrospectives${selectedSubject ? `?subjectId=${selectedSubject}` : ''}`,
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            if (response.data.success) {
                setRetrospectives(response.data.retrospectives);
            }

        } catch (err) {
            console.error('Error saving retrospective:', err);
            setError('Failed to save retrospective. Please try again.');
        }
    };

    // Handle edit retrospective
    const handleEdit = (retrospective) => {
        setEditingId(retrospective.id);
        setFormData({
            subject_id: retrospective.subject_id || '',
            went_well: retrospective.went_well || '',
            to_improve: retrospective.to_improve || '',
            action_items: retrospective.action_items || ''
        });
        setShowForm(true);
    };

    // Handle delete retrospective
    const handleDelete = async (id) => {
        if (!confirm('Are you sure you want to delete this retrospective?')) return;

        try {
            await axios.delete(
                `${API_URL}/api/retrospectives/${id}`,
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            // Update local state
            setRetrospectives(prev => prev.filter(r => r.id !== id));

        } catch (err) {
            console.error('Error deleting retrospective:', err);
            setError('Failed to delete retrospective. Please try again.');
        }
    };

    // Toggle form visibility
    const toggleForm = () => {
        setShowForm(prev => !prev);
        if (!showForm) {
            setEditingId(null);
            setFormData({
                subject_id: selectedSubject || '',
                went_well: '',
                to_improve: '',
                action_items: ''
            });
        }
    };

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowModal(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowModal(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    return (
        <>
            <div
                className={`retrospective-card ${darkMode ? 'theme-dark' : ''}`}
                onClick={() => setShowModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
                aria-label="Open Retrospective Board"
            >
                <div className="widget-header">
                    <h3 className="widget-title">Learning Retrospective</h3>
                    <div className="total-count">{notes.wentWell.count + notes.toImprove.count + notes.actionItems.count}</div>
                </div>

                <div className="retrospective-preview">
                    <div className="insight-item went-well">
                        <div className="insight-content">
                            <div className="insight-info">
                                <div className="insight-icon">
                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                        <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                    </svg>
                                </div>
                                <div className="insight-details">
                                    <span className="insight-label">Went Well</span>
                                    <span className="insight-desc">Successes</span>
                                </div>
                            </div>
                            <div className="insight-count">{notes.wentWell.count}</div>
                        </div>
                    </div>

                    <div className="insight-item to-improve">
                        <div className="insight-content">
                            <div className="insight-info">
                                <div className="insight-icon">
                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                        <path d="M12 9v3m0 0v3m0-3h3m-3 0H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                    </svg>
                                </div>
                                <div className="insight-details">
                                    <span className="insight-label">To Improve</span>
                                    <span className="insight-desc">Growth Areas</span>
                                </div>
                            </div>
                            <div className="insight-count">{notes.toImprove.count}</div>
                        </div>
                    </div>

                    <div className="insight-item action-items">
                        <div className="insight-content">
                            <div className="insight-info">
                                <div className="insight-icon">
                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </div>
                                <div className="insight-details">
                                    <span className="insight-label">Action Items</span>
                                    <span className="insight-desc">Next Steps</span>
                                </div>
                            </div>
                            <div className="insight-count">{notes.actionItems.count}</div>
                        </div>
                    </div>
                </div>
            </div>
            {showModal && (
                <div className="modal-overlay">
                    <div className="modal-window" ref={modalRef} role="dialog" aria-labelledby="modal-title">
                        <div className={`modal-header ${darkMode ? 'theme-dark' : ''}`}>
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowModal(false)}
                                    aria-label="Close"
                                ></button>
                                <button className="traffic-light yellow" disabled aria-label="Minimize"></button>
                                <button className="traffic-light green" disabled aria-label="Maximize"></button>
                            </div>
                            <div className="header-content">
                                <div className="header-icon">
                                    <svg viewBox="0 0 24 24" width="24" height="24" fill="none">
                                        <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                    </svg>
                                </div>
                                <h2 id="modal-title" className="modal-title">
                                    Learning Retrospective
                                </h2>
                            </div>
                            <div className="header-stats">
                                <div className="stat-item">
                                    <span className="stat-number">{retrospectives.length}</span>
                                    <span className="stat-label">Total</span>
                                </div>
                            </div>
                        </div>
                        <div className={`modal-content ${darkMode ? 'theme-dark' : ''}`}>
                            {/* Enhanced Controls Section */}
                            <div className="controls-section">
                                <div className="controls-left">
                                    <div className="filter-group">
                                        <div className="filter-icon">
                                            <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                                <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            </svg>
                                        </div>
                                        <select
                                            id="subject-filter"
                                            value={selectedSubject}
                                            onChange={handleSubjectChange}
                                            className="filter-select"
                                        >
                                            <option value="">All Subjects</option>
                                            {subjects.map(subject => (
                                                <option key={subject.id} value={subject.id}>
                                                    {subject.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                                <div className="controls-right">
                                    <button
                                        className={`add-button ${showForm ? 'active' : ''}`}
                                        onClick={toggleForm}
                                    >
                                        <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                            {showForm ? (
                                                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            ) : (
                                                <path d="M12 5v14M5 12h14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            )}
                                        </svg>
                                        {showForm ? 'Cancel' : 'Add Retrospective'}
                                    </button>
                                </div>
                            </div>

                            {/* Enhanced Retrospective Form */}
                            {showForm && (
                                <div className="form-container">
                                    <form className="retrospective-form" onSubmit={handleSubmit}>
                                        <div className="form-header">
                                            <div className="form-icon">
                                                <svg viewBox="0 0 24 24" width="20" height="20" fill="none">
                                                    {editingId ? (
                                                        <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    ) : (
                                                        <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8zM14 2v6h6M16 13H8M16 17H8M10 9H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    )}
                                                </svg>
                                            </div>
                                            <h3 className="form-title">{editingId ? 'Edit Retrospective' : 'Create New Retrospective'}</h3>
                                        </div>

                                        <div className="form-grid">
                                            <div className="form-group full-width">
                                                <label htmlFor="subject_id" className="form-label">
                                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                                        <path d="M22 10v6M2 10l10-5 10 5-10 5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        <path d="M6 12v5c3 3 9 3 12 0v-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    </svg>
                                                    Subject (Optional)
                                                </label>
                                                <select
                                                    id="subject_id"
                                                    name="subject_id"
                                                    value={formData.subject_id}
                                                    onChange={handleInputChange}
                                                    className="form-select"
                                                >
                                                    <option value="">-- No Subject --</option>
                                                    {subjects.map(subject => (
                                                        <option key={subject.id} value={subject.id}>
                                                            {subject.name}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label htmlFor="went_well" className="form-label">
                                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                                        <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                                    </svg>
                                                    What Went Well
                                                </label>
                                                <textarea
                                                    id="went_well"
                                                    name="went_well"
                                                    value={formData.went_well}
                                                    onChange={handleInputChange}
                                                    rows="4"
                                                    className="form-textarea"
                                                    placeholder="Describe your successes and positive outcomes..."
                                                ></textarea>
                                            </div>

                                            <div className="form-group">
                                                <label htmlFor="to_improve" className="form-label">
                                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                                        <path d="M12 9v3m0 0v3m0-3h3m-3 0H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                                    </svg>
                                                    Areas for Improvement
                                                </label>
                                                <textarea
                                                    id="to_improve"
                                                    name="to_improve"
                                                    value={formData.to_improve}
                                                    onChange={handleInputChange}
                                                    rows="4"
                                                    className="form-textarea"
                                                    placeholder="Identify challenges and growth opportunities..."
                                                ></textarea>
                                            </div>

                                            <div className="form-group">
                                                <label htmlFor="action_items" className="form-label">
                                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    </svg>
                                                    Action Items
                                                </label>
                                                <textarea
                                                    id="action_items"
                                                    name="action_items"
                                                    value={formData.action_items}
                                                    onChange={handleInputChange}
                                                    rows="4"
                                                    className="form-textarea"
                                                    placeholder="Define specific next steps and commitments..."
                                                ></textarea>
                                            </div>
                                        </div>

                                        <div className="form-actions">
                                            <button type="submit" className="save-button">
                                                <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                                    <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    <polyline points="17,21 17,13 7,13 7,21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    <polyline points="7,3 7,8 15,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                                {editingId ? 'Update' : 'Save'} Retrospective
                                            </button>
                                            {editingId && (
                                                <button
                                                    type="button"
                                                    className="cancel-button"
                                                    onClick={() => {
                                                        setEditingId(null);
                                                        setFormData({
                                                            subject_id: selectedSubject || '',
                                                            went_well: '',
                                                            to_improve: '',
                                                            action_items: ''
                                                        });
                                                    }}
                                                >
                                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                                        <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    </svg>
                                                    Cancel Edit
                                                </button>
                                            )}
                                        </div>
                                    </form>
                                </div>
                            )}

                            {/* Loading State */}
                            {loading && (
                                <div className="loading-indicator">
                                    <div className="loading-spinner"></div>
                                    <span>Loading retrospectives...</span>
                                </div>
                            )}

                            {/* Error Message */}
                            {error && (
                                <div className="error-message">
                                    <svg viewBox="0 0 24 24" width="20" height="20" fill="none">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                        <path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                    {error}
                                </div>
                            )}

                            {/* Enhanced Retrospective Columns */}
                            <div className="retrospective-columns">
                                <div className="retrospective-column went-well">
                                    <div className="column-header">
                                        <div className="column-icon">
                                            <svg viewBox="0 0 24 24" width="20" height="20" fill="none">
                                                <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                            </svg>
                                        </div>
                                        <div className="column-info">
                                            <h3>{notes.wentWell.label}</h3>
                                            <span className="item-count">{notes.wentWell.count} items</span>
                                        </div>
                                    </div>
                                    <div className="note-list">
                                        {notes.wentWell.items.map((note) => (
                                            <div key={note.id} className="note-item">
                                                <div className="note-content">
                                                    <div className="note-text">
                                                        <h4 className="note-title">{note.title}</h4>
                                                        <p className="note-details">{note.details}</p>
                                                    </div>
                                                    <div className="note-meta">
                                                        {note.subject_id && subjects.find(s => s.id === note.subject_id) && (
                                                            <span className="subject-tag">
                                                                <svg viewBox="0 0 24 24" width="12" height="12" fill="none">
                                                                    <path d="M22 10v6M2 10l10-5 10 5-10 5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                                </svg>
                                                                {subjects.find(s => s.id === note.subject_id).name}
                                                            </span>
                                                        )}
                                                        <span className="date-tag">
                                                            <svg viewBox="0 0 24 24" width="12" height="12" fill="none">
                                                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" strokeWidth="2" />
                                                            </svg>
                                                            {new Date(note.created_at).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="note-actions">
                                                    <button
                                                        className="action-button edit-button"
                                                        onClick={() => handleEdit(retrospectives.find(r => r.id === note.id))}
                                                        title="Edit retrospective"
                                                    >
                                                        <svg viewBox="0 0 24 24" width="14" height="14" fill="none">
                                                            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        </svg>
                                                    </button>
                                                    <button
                                                        className="action-button delete-button"
                                                        onClick={() => handleDelete(note.id)}
                                                        title="Delete retrospective"
                                                    >
                                                        <svg viewBox="0 0 24 24" width="14" height="14" fill="none">
                                                            <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                        {notes.wentWell.items.length === 0 && (
                                            <div className="empty-state">
                                                <svg viewBox="0 0 24 24" width="32" height="32" fill="none">
                                                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                                    <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    <line x1="9" y1="9" x2="9.01" y2="9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    <line x1="15" y1="9" x2="15.01" y2="9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                                <p>No successes recorded yet</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="retrospective-column to-improve">
                                    <div className="column-header">
                                        <div className="column-icon">
                                            <svg viewBox="0 0 24 24" width="20" height="20" fill="none">
                                                <path d="M12 9v3m0 0v3m0-3h3m-3 0H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                            </svg>
                                        </div>
                                        <div className="column-info">
                                            <h3>{notes.toImprove.label}</h3>
                                            <span className="item-count">{notes.toImprove.count} items</span>
                                        </div>
                                    </div>
                                    <div className="note-list">
                                        {notes.toImprove.items.map((note) => (
                                            <div key={note.id} className="note-item">
                                                <div className="note-content">
                                                    <div className="note-text">
                                                        <h4 className="note-title">{note.title}</h4>
                                                        <p className="note-details">{note.details}</p>
                                                    </div>
                                                    <div className="note-meta">
                                                        {note.subject_id && subjects.find(s => s.id === note.subject_id) && (
                                                            <span className="subject-tag">
                                                                <svg viewBox="0 0 24 24" width="12" height="12" fill="none">
                                                                    <path d="M22 10v6M2 10l10-5 10 5-10 5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                                </svg>
                                                                {subjects.find(s => s.id === note.subject_id).name}
                                                            </span>
                                                        )}
                                                        <span className="date-tag">
                                                            <svg viewBox="0 0 24 24" width="12" height="12" fill="none">
                                                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" strokeWidth="2" />
                                                            </svg>
                                                            {new Date(note.created_at).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="note-actions">
                                                    <button
                                                        className="action-button edit-button"
                                                        onClick={() => handleEdit(retrospectives.find(r => r.id === note.id))}
                                                        title="Edit retrospective"
                                                    >
                                                        <svg viewBox="0 0 24 24" width="14" height="14" fill="none">
                                                            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        </svg>
                                                    </button>
                                                    <button
                                                        className="action-button delete-button"
                                                        onClick={() => handleDelete(note.id)}
                                                        title="Delete retrospective"
                                                    >
                                                        <svg viewBox="0 0 24 24" width="14" height="14" fill="none">
                                                            <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                        {notes.toImprove.items.length === 0 && (
                                            <div className="empty-state">
                                                <svg viewBox="0 0 24 24" width="32" height="32" fill="none">
                                                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                                    <path d="M12 8v4M12 16h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                                <p>No improvement areas identified</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="retrospective-column action-items">
                                    <div className="column-header">
                                        <div className="column-icon">
                                            <svg viewBox="0 0 24 24" width="20" height="20" fill="none">
                                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            </svg>
                                        </div>
                                        <div className="column-info">
                                            <h3>{notes.actionItems.label}</h3>
                                            <span className="item-count">{notes.actionItems.count} items</span>
                                        </div>
                                    </div>
                                    <div className="note-list">
                                        {notes.actionItems.items.map((note) => (
                                            <div key={note.id} className="note-item">
                                                <div className="note-content">
                                                    <div className="note-text">
                                                        <h4 className="note-title">{note.title}</h4>
                                                        <p className="note-details">{note.details}</p>
                                                    </div>
                                                    <div className="note-meta">
                                                        {note.subject_id && subjects.find(s => s.id === note.subject_id) && (
                                                            <span className="subject-tag">
                                                                <svg viewBox="0 0 24 24" width="12" height="12" fill="none">
                                                                    <path d="M22 10v6M2 10l10-5 10 5-10 5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                                </svg>
                                                                {subjects.find(s => s.id === note.subject_id).name}
                                                            </span>
                                                        )}
                                                        <span className="date-tag">
                                                            <svg viewBox="0 0 24 24" width="12" height="12" fill="none">
                                                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" strokeWidth="2" />
                                                                <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" strokeWidth="2" />
                                                            </svg>
                                                            {new Date(note.created_at).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="note-actions">
                                                    <button
                                                        className="action-button edit-button"
                                                        onClick={() => handleEdit(retrospectives.find(r => r.id === note.id))}
                                                        title="Edit retrospective"
                                                    >
                                                        <svg viewBox="0 0 24 24" width="14" height="14" fill="none">
                                                            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        </svg>
                                                    </button>
                                                    <button
                                                        className="action-button delete-button"
                                                        onClick={() => handleDelete(note.id)}
                                                        title="Delete retrospective"
                                                    >
                                                        <svg viewBox="0 0 24 24" width="14" height="14" fill="none">
                                                            <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                        {notes.actionItems.items.length === 0 && (
                                            <div className="empty-state">
                                                <svg viewBox="0 0 24 24" width="32" height="32" fill="none">
                                                    <path d="M9 11H1v3h8v3l3-4-3-4v2zM22 12l-4-4v3h-8v2h8v3l4-4z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                                <p>No action items defined</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className={`modal-footer ${darkMode ? 'theme-dark' : ''}`}>
                            <button className="close-button" onClick={() => setShowModal(false)}>
                                <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Retrospective;