import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useAuth } from '../../../authContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';
import '../../styles/retrospective_dashboard.scss';

const Retrospective = () => {
    const { token } = useAuth();
    const { darkMode } = useTheme();
    const [showModal, setShowModal] = useState(false);
    const [retrospectives, setRetrospectives] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [subjects, setSubjects] = useState([]);
    const [selectedSubject, setSelectedSubject] = useState('');
    const [formData, setFormData] = useState({
        subject_id: '',
        went_well: '',
        to_improve: '',
        action_items: ''
    });
    const [editingId, setEditingId] = useState(null);
    const [showForm, setShowForm] = useState(false);
    const modalRef = useRef(null);

    const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

    // Fetch retrospectives and subjects
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                // Fetch retrospectives
                const retrospectivesResponse = await axios.get(
                    `${API_URL}/api/retrospectives${selectedSubject ? `?subjectId=${selectedSubject}` : ''}`,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );

                if (retrospectivesResponse.data.success) {
                    setRetrospectives(retrospectivesResponse.data.retrospectives);
                }

                // Fetch subjects
                const subjectsResponse = await axios.get(
                    `${API_URL}/api/subjects`,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );

                if (subjectsResponse.data.success) {
                    setSubjects(subjectsResponse.data.subjects || []);
                }

                setError(null);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        if (token) {
            fetchData();
        }
    }, [token, API_URL, selectedSubject]);

    // Process retrospectives into the format needed for display
    const notes = {
        wentWell: {
            count: retrospectives.filter(r => r.went_well && r.went_well.trim() !== '').length,
            label: 'Went Well',
            items: retrospectives
                .filter(r => r.went_well && r.went_well.trim() !== '')
                .map(r => ({
                    id: r.id,
                    title: r.went_well.split('.')[0] || 'Went Well Item',
                    details: r.went_well,
                    subject_id: r.subject_id,
                    created_at: r.created_at
                }))
        },
        toImprove: {
            count: retrospectives.filter(r => r.to_improve && r.to_improve.trim() !== '').length,
            label: 'To Improve',
            items: retrospectives
                .filter(r => r.to_improve && r.to_improve.trim() !== '')
                .map(r => ({
                    id: r.id,
                    title: r.to_improve.split('.')[0] || 'To Improve Item',
                    details: r.to_improve,
                    subject_id: r.subject_id,
                    created_at: r.created_at
                }))
        },
        actionItems: {
            count: retrospectives.filter(r => r.action_items && r.action_items.trim() !== '').length,
            label: 'Action Items',
            items: retrospectives
                .filter(r => r.action_items && r.action_items.trim() !== '')
                .map(r => ({
                    id: r.id,
                    title: r.action_items.split('.')[0] || 'Action Item',
                    details: r.action_items,
                    subject_id: r.subject_id,
                    created_at: r.created_at
                }))
        }
    };

    // Handle form input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // Handle subject selection
    const handleSubjectChange = (e) => {
        setSelectedSubject(e.target.value);
        setFormData(prev => ({
            ...prev,
            subject_id: e.target.value || null
        }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            if (editingId) {
                // Update existing retrospective
                await axios.put(
                    `${API_URL}/api/retrospectives/${editingId}`,
                    formData,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );
            } else {
                // Create new retrospective
                await axios.post(
                    `${API_URL}/api/retrospectives`,
                    formData,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );
            }

            // Reset form and refresh data
            setFormData({
                subject_id: selectedSubject || '',
                went_well: '',
                to_improve: '',
                action_items: ''
            });
            setEditingId(null);
            setShowForm(false);

            // Fetch updated data
            const response = await axios.get(
                `${API_URL}/api/retrospectives${selectedSubject ? `?subjectId=${selectedSubject}` : ''}`,
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            if (response.data.success) {
                setRetrospectives(response.data.retrospectives);
            }

        } catch (err) {
            console.error('Error saving retrospective:', err);
            setError('Failed to save retrospective. Please try again.');
        }
    };

    // Handle edit retrospective
    const handleEdit = (retrospective) => {
        setEditingId(retrospective.id);
        setFormData({
            subject_id: retrospective.subject_id || '',
            went_well: retrospective.went_well || '',
            to_improve: retrospective.to_improve || '',
            action_items: retrospective.action_items || ''
        });
        setShowForm(true);
    };

    // Handle delete retrospective
    const handleDelete = async (id) => {
        if (!confirm('Are you sure you want to delete this retrospective?')) return;

        try {
            await axios.delete(
                `${API_URL}/api/retrospectives/${id}`,
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            // Update local state
            setRetrospectives(prev => prev.filter(r => r.id !== id));

        } catch (err) {
            console.error('Error deleting retrospective:', err);
            setError('Failed to delete retrospective. Please try again.');
        }
    };

    // Toggle form visibility
    const toggleForm = () => {
        setShowForm(prev => !prev);
        if (!showForm) {
            setEditingId(null);
            setFormData({
                subject_id: selectedSubject || '',
                went_well: '',
                to_improve: '',
                action_items: ''
            });
        }
    };

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowModal(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowModal(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    return (
        <>
            <div
                className={`retrospective-card ${darkMode ? 'theme-dark' : ''}`}
                onClick={() => setShowModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
                aria-label="Open Retrospective Board"
            >
                <div className="widget-header">
                    <h3 className="widget-title">Learning Retrospective</h3>
                    <div className="total-count">{notes.wentWell.count + notes.toImprove.count + notes.actionItems.count}</div>
                </div>

                <div className="retrospective-preview">
                    <div className="insight-item went-well">
                        <div className="insight-content">
                            <div className="insight-info">
                                <div className="insight-icon">
                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                        <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                    </svg>
                                </div>
                                <div className="insight-details">
                                    <span className="insight-label">Went Well</span>
                                    <span className="insight-desc">Successes</span>
                                </div>
                            </div>
                            <div className="insight-count">{notes.wentWell.count}</div>
                        </div>
                    </div>

                    <div className="insight-item to-improve">
                        <div className="insight-content">
                            <div className="insight-info">
                                <div className="insight-icon">
                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                        <path d="M12 9v3m0 0v3m0-3h3m-3 0H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                    </svg>
                                </div>
                                <div className="insight-details">
                                    <span className="insight-label">To Improve</span>
                                    <span className="insight-desc">Growth Areas</span>
                                </div>
                            </div>
                            <div className="insight-count">{notes.toImprove.count}</div>
                        </div>
                    </div>

                    <div className="insight-item action-items">
                        <div className="insight-content">
                            <div className="insight-info">
                                <div className="insight-icon">
                                    <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </div>
                                <div className="insight-details">
                                    <span className="insight-label">Action Items</span>
                                    <span className="insight-desc">Next Steps</span>
                                </div>
                            </div>
                            <div className="insight-count">{notes.actionItems.count}</div>
                        </div>
                    </div>
                </div>
            </div>
            {showModal && (
                <div className="modal-overlay">
                    <div className="modal-window" ref={modalRef} role="dialog" aria-labelledby="modal-title">
                        <div className="modal-header">
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowModal(false)}
                                    aria-label="Close"
                                ></button>
                                <button className="traffic-light yellow" disabled aria-label="Minimize"></button>
                                <button className="traffic-light green" disabled aria-label="Maximize"></button>
                            </div>
                            <h2 id="modal-title" className="modal-title">
                                Retrospective
                            </h2>
                        </div>
                        <div className="modal-content">
                            {/* Subject Filter and Add Button */}
                            <div className="controls-section">
                                <div className="filter-controls">
                                    <label htmlFor="subject-filter">Filter by Subject:</label>
                                    <select
                                        id="subject-filter"
                                        value={selectedSubject}
                                        onChange={handleSubjectChange}
                                    >
                                        <option value="">All Subjects</option>
                                        {subjects.map(subject => (
                                            <option key={subject.id} value={subject.id}>
                                                {subject.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <button
                                    className="add-button"
                                    onClick={toggleForm}
                                >
                                    {showForm ? 'Cancel' : 'Add New Retrospective'}
                                </button>
                            </div>

                            {/* Retrospective Form */}
                            {showForm && (
                                <form className="retrospective-form" onSubmit={handleSubmit}>
                                    <h3>{editingId ? 'Edit Retrospective' : 'New Retrospective'}</h3>

                                    <div className="form-group">
                                        <label htmlFor="subject_id">Subject (Optional):</label>
                                        <select
                                            id="subject_id"
                                            name="subject_id"
                                            value={formData.subject_id}
                                            onChange={handleInputChange}
                                        >
                                            <option value="">-- No Subject --</option>
                                            {subjects.map(subject => (
                                                <option key={subject.id} value={subject.id}>
                                                    {subject.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div className="form-group">
                                        <label htmlFor="went_well">What Went Well:</label>
                                        <textarea
                                            id="went_well"
                                            name="went_well"
                                            value={formData.went_well}
                                            onChange={handleInputChange}
                                            rows="3"
                                            placeholder="Enter what went well..."
                                        ></textarea>
                                    </div>

                                    <div className="form-group">
                                        <label htmlFor="to_improve">What Could Be Improved:</label>
                                        <textarea
                                            id="to_improve"
                                            name="to_improve"
                                            value={formData.to_improve}
                                            onChange={handleInputChange}
                                            rows="3"
                                            placeholder="Enter what could be improved..."
                                        ></textarea>
                                    </div>

                                    <div className="form-group">
                                        <label htmlFor="action_items">Action Items:</label>
                                        <textarea
                                            id="action_items"
                                            name="action_items"
                                            value={formData.action_items}
                                            onChange={handleInputChange}
                                            rows="3"
                                            placeholder="Enter action items..."
                                        ></textarea>
                                    </div>

                                    <div className="form-actions">
                                        <button type="submit" className="save-button">
                                            {editingId ? 'Update' : 'Save'} Retrospective
                                        </button>
                                        {editingId && (
                                            <button
                                                type="button"
                                                className="cancel-button"
                                                onClick={() => {
                                                    setEditingId(null);
                                                    setFormData({
                                                        subject_id: selectedSubject || '',
                                                        went_well: '',
                                                        to_improve: '',
                                                        action_items: ''
                                                    });
                                                }}
                                            >
                                                Cancel Edit
                                            </button>
                                        )}
                                    </div>
                                </form>
                            )}

                            {/* Loading State */}
                            {loading && <div className="loading-indicator">Loading retrospectives...</div>}

                            {/* Error Message */}
                            {error && <div className="error-message">{error}</div>}

                            {/* Retrospective Columns */}
                            <div className="retrospective-columns">
                                <div className="retrospective-column wentwell">
                                    <h3>{notes.wentWell.label}</h3>
                                    <p>{notes.wentWell.count} items</p>
                                    <ul className="note-list">
                                        {notes.wentWell.items.map((note) => (
                                            <li key={note.id} className="note-item">
                                                <div className="note-content">
                                                    <strong>{note.title}</strong>
                                                    <p>{note.details}</p>
                                                    {note.subject_id && subjects.find(s => s.id === note.subject_id) && (
                                                        <span className="subject-tag">
                                                            {subjects.find(s => s.id === note.subject_id).name}
                                                        </span>
                                                    )}
                                                    <span className="date-tag">
                                                        {new Date(note.created_at).toLocaleDateString()}
                                                    </span>
                                                </div>
                                                <div className="note-actions">
                                                    <button
                                                        className="edit-button"
                                                        onClick={() => handleEdit(retrospectives.find(r => r.id === note.id))}
                                                    >
                                                        Edit
                                                    </button>
                                                    <button
                                                        className="delete-button"
                                                        onClick={() => handleDelete(note.id)}
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            </li>
                                        ))}
                                        {notes.wentWell.items.length === 0 && <li>No items</li>}
                                    </ul>
                                </div>
                                <div className="retrospective-column toimprove">
                                    <h3>{notes.toImprove.label}</h3>
                                    <p>{notes.toImprove.count} items</p>
                                    <ul className="note-list">
                                        {notes.toImprove.items.map((note) => (
                                            <li key={note.id} className="note-item">
                                                <div className="note-content">
                                                    <strong>{note.title}</strong>
                                                    <p>{note.details}</p>
                                                    {note.subject_id && subjects.find(s => s.id === note.subject_id) && (
                                                        <span className="subject-tag">
                                                            {subjects.find(s => s.id === note.subject_id).name}
                                                        </span>
                                                    )}
                                                    <span className="date-tag">
                                                        {new Date(note.created_at).toLocaleDateString()}
                                                    </span>
                                                </div>
                                                <div className="note-actions">
                                                    <button
                                                        className="edit-button"
                                                        onClick={() => handleEdit(retrospectives.find(r => r.id === note.id))}
                                                    >
                                                        Edit
                                                    </button>
                                                    <button
                                                        className="delete-button"
                                                        onClick={() => handleDelete(note.id)}
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            </li>
                                        ))}
                                        {notes.toImprove.items.length === 0 && <li>No items</li>}
                                    </ul>
                                </div>
                                <div className="retrospective-column actionitems">
                                    <h3>{notes.actionItems.label}</h3>
                                    <p>{notes.actionItems.count} items</p>
                                    <ul className="note-list">
                                        {notes.actionItems.items.map((note) => (
                                            <li key={note.id} className="note-item">
                                                <div className="note-content">
                                                    <strong>{note.title}</strong>
                                                    <p>{note.details}</p>
                                                    {note.subject_id && subjects.find(s => s.id === note.subject_id) && (
                                                        <span className="subject-tag">
                                                            {subjects.find(s => s.id === note.subject_id).name}
                                                        </span>
                                                    )}
                                                    <span className="date-tag">
                                                        {new Date(note.created_at).toLocaleDateString()}
                                                    </span>
                                                </div>
                                                <div className="note-actions">
                                                    <button
                                                        className="edit-button"
                                                        onClick={() => handleEdit(retrospectives.find(r => r.id === note.id))}
                                                    >
                                                        Edit
                                                    </button>
                                                    <button
                                                        className="delete-button"
                                                        onClick={() => handleDelete(note.id)}
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            </li>
                                        ))}
                                        {notes.actionItems.items.length === 0 && <li>No items</li>}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button className="close-button" onClick={() => setShowModal(false)}>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Retrospective;