@use "variables" as *;
@use "sass:color";
@use "scrollbar";

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
//$system-gold: rgb(230, 195, 0);
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

.login {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  background-color: $secondary-background;
  color: $primary-text;

  // Dark mode
  &.theme-dark {
    background-color: $dark-secondary-background;
    color: $dark-primary-text;
  }

  &_hero {
    width: 100%;
    height: 100dvh;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    border: none;
    border-radius: 12px;
    background: $window-background;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    // macOS-style subtle border
    border: 1px solid $border-color;

    // Dark mode
    .theme-dark & {
      background: $dark-window-background;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      border: 1px solid $dark-border-color;
    }

    .img {
      width: 50dvh;
    }

    .logo {
      h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: $primary-text;
        margin-bottom: 1.5rem;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        span {
          color: $system-blue;
          font-weight: 700;
          color: $system-gold;
        }

        // Dark mode
        .theme-dark & {
          color: $dark-primary-text;
        }
      }
    }

    input {
      border: 1px solid $border-color;
      color: $primary-text;
      padding: 0.75rem 1rem !important;
      transition: all 0.2s ease;
      border-radius: 8px;
      background-color: $secondary-background;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      &::placeholder {
        color: $placeholder-text;
      }

      &:focus {
        outline: none;
        border: 1px solid $system-blue;
        box-shadow: 0 0 0 3px rgba($system-blue, 0.15);
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;
        border: 1px solid $dark-border-color;
        color: $dark-primary-text;

        &::placeholder {
          color: $dark-placeholder-text;
        }

        &:focus {
          border: 1px solid $system-blue;
          box-shadow: 0 0 0 3px rgba($system-blue, 0.25);
        }
      }
    }

    .names {
      margin: 0;
    }

    button {
      background: $system-blue;
      border-radius: 8px;
      margin-top: 2rem;
      padding: 0.75rem;
      font-weight: 600;
      transition: all 0.2s ease;
      border: none;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &:hover {
        background-color: color.adjust($system-blue, $lightness: -5%);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba($system-blue, 0.3);

        &::before {
          opacity: 1;
        }
      }

      &:active {
        transform: translateY(0);
        background-color: color.adjust($system-blue, $lightness: -10%);
        box-shadow: 0 1px 3px rgba($system-blue, 0.2);
      }

      &:disabled {
        background-color: $system-gray;
        transform: none;
        box-shadow: none;
        cursor: not-allowed;
      }

      // Dark mode
      .theme-dark & {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
      }
    }

    .img {
      width: 50dvw;
      height: 100%;
      border-radius: 0;
      background-image: url("../assets/background.jpg");
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba($system-blue, 0.4) 0%,
            rgba(color.adjust($system-blue, $lightness: -30%), 0.7) 100%);
        backdrop-filter: blur(1px);
      }
    }
  }
}

.password-input {
  position: relative !important;
  display: flex;
  flex-direction: column;
  height: fit-content;
  margin-bottom: .5rem;
  width: 100%;

  input {
    //padding-right: 3rem;
    position: relative;
    background-color: $secondary-background !important;

    // Dark mode
    .theme-dark & {
      background-color: $dark-secondary-background !important;
    }
  }

  .visibility-toggle {
    position: absolute;
    top: 40%;
    right: 0.75rem;
    transform: translateY(-50%);
    background-color: transparent !important;
    border: none;
    cursor: pointer;
    color: $tertiary-text;
    display: flex;
    align-items: center;
    padding: 0;
    margin: 0;
    justify-content: center;
    z-index: 10;
    transition: color 0.2s ease;

    &:hover {
      color: $system-blue;
    }

    // Dark mode
    .theme-dark & {
      color: $dark-tertiary-text;

      &:hover {
        color: $system-blue;
      }
    }
  }
}

// Password strength indicator
.password-strength {
  margin: 0.5rem 0 1rem;
  width: 100%;

  .strength-bars {
    display: flex;
    gap: 4px;
    margin-bottom: 0.5rem;

    .strength-bar {
      height: 4px;
      flex: 1;
      background-color: $system-light-gray;
      border-radius: 2px;
      transition: all 0.3s ease;

      &.weak {
        background-color: $system-red;
      }

      &.medium {
        background-color: $system-orange;
      }

      &.strong {
        background-color: $system-green;
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-border-color;

        &.weak {
          background-color: $system-red;
        }

        &.medium {
          background-color: $system-orange;
        }

        &.strong {
          background-color: $system-green;
        }
      }
    }
  }

  .strength-text {
    font-size: 0.8rem;
    color: $tertiary-text;
    margin-bottom: 0.25rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Dark mode
    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }

  .strength-feedback {
    font-size: 0.75rem;
    color: $tertiary-text;
    line-height: 1.4;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Dark mode
    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }
}

form {
  .names {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;

    input {
      flex: 1;
    }
  }

  input[type="password"],
  input[type="email"],
  input[type="text"] {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 1px solid $border-color !important;
    border-radius: 8px;
    background-color: $secondary-background !important;
    color: $primary-text;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    &:focus {
      border-color: $system-blue !important;
      box-shadow: 0 0 0 3px rgba($system-blue, 0.15);
    }

    &::placeholder {
      color: $placeholder-text;
    }

    // Dark mode
    .theme-dark & {
      background-color: $dark-secondary-background !important;
      border-color: $dark-border-color !important;
      color: $dark-primary-text;

      &::placeholder {
        color: $dark-placeholder-text;
      }

      &:focus {
        border-color: $system-blue !important;
        box-shadow: 0 0 0 3px rgba($system-blue, 0.25);
      }
    }
  }
}

.login-container {
  width: 50%;
  height: 100%;
  perspective: 1000px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2.5rem;
  background: $window-background;
  border-radius: 0 12px 12px 0;
  padding: 2.5rem 1rem;

  // Dark mode
  .theme-dark & {
    background: $dark-window-background;
  }

  .logo {
    img {
      width: 100px;
    }
  }

  .error-message {
    color: $system-red;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    width: 100%;
    text-align: left;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }
}

.login-card {
  width: 100%;
  max-width: 400px;
  height: fit-content;
  background: $window-background;
  color: $primary-text;
  padding: 0;
  border-radius: 0 12px 12px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  //gap: 1.5rem;

  // Dark mode
  .theme-dark & {
    background: $dark-window-background;
    color: $dark-primary-text;
  }

  h2 {
    margin: 0 0 2rem 0;
    padding: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: $primary-text;
    width: 100%;
    text-align: left;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Dark mode
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  .media {
    margin: 1.5rem 0;
    width: 100%;
  }

  .question {

    button {
      margin: 0;
      color: $system-blue;
      padding: .2rem .5rem;
    }

    p {
      margin: 0;
      padding: 0;
    }
  }

  .buttons {
    margin-top: 1rem;
    padding: 0;
    width: 100%;

    button {
      border: 1px solid $light-accent-color-2;
      color: $light-primary-text !important;
      margin: 0.5rem 0;
      border-radius: 8px;
      transition: all 0.3s ease;
      background-color: white;

      * {
        color: $light-primary-text !important;
        transition: color 0.3s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

        * {
          color: $color-primary !important;
        }
      }

      &:first-child {
        &:hover {
          border-color: rgba(255, 59, 48, 0.5);
          background: rgba(255, 59, 48, 0.05);
        }
      }

      &:last-child {
        &:hover {
          border-color: $color-primary;
          background: rgba(0, 122, 255, 0.05);
        }
      }
    }
  }
}

h2 {
  width: 100%;
  margin: 0;
  font-size: 1.75rem;
  text-align: left;
  color: $light-primary-text;
  font-weight: 700;
}

form {
  display: flex;
  flex-direction: column;
  width: 100%;

  .names {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  button {
    color: white;
    background: $system-blue;
    border: none;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover {
      background: color.adjust($system-blue, $lightness: -5%);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba($system-blue, 0.3);

      &::before {
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(0);
      background: color.adjust($system-blue, $lightness: -10%);
      box-shadow: 0 1px 3px rgba($system-blue, 0.2);
    }

    &:disabled {
      background: $system-gray;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
      opacity: 0.7;
    }

    // Dark mode
    .theme-dark & {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }
  }
}

p {
  color: $secondary-text;
  margin: 0.5rem 0;
  font-size: 0.95rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Dark mode
  .theme-dark & {
    color: $dark-secondary-text;
  }

  button {
    color: $system-blue;
    background: transparent !important;
    text-decoration: none;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    &:hover {
      background: rgba($system-blue, 0.05) !important;
      color: color.adjust($system-blue, $lightness: -10%);
    }

    // Dark mode
    .theme-dark & {
      color: $system-blue;

      &:hover {
        background: rgba($system-blue, 0.1) !important;
      }
    }
  }
}

.question {
  width: 100%;
  text-align: left;
  color: $secondary-text;
  margin: 0.5rem 0;
  padding: 0 !important;
  font-size: 0.95rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Dark mode
  .theme-dark & {
    color: $dark-secondary-text;
  }
}

input[type="text"],
input[type="email"],
input[type="password"] {
  width: 100%;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid $border-color;
  border-radius: 8px;
  color: $primary-text;
  background-color: $secondary-background;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &::placeholder {
    color: $placeholder-text;
  }

  &:focus {
    outline: none;
    border-color: $system-blue;
    box-shadow: 0 0 0 3px rgba($system-blue, 0.15);
  }

  // Dark mode
  .theme-dark & {
    background-color: $dark-secondary-background;
    border-color: $dark-border-color;
    color: $dark-primary-text;

    &::placeholder {
      color: $dark-placeholder-text;
    }

    &:focus {
      border-color: $system-blue;
      box-shadow: 0 0 0 3px rgba($system-blue, 0.25);
    }
  }
}

label {
  font-size: 0.9rem;
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: $secondary-text;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Dark mode
  .theme-dark & {
    color: $dark-secondary-text;
  }

  a {
    color: $system-blue;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;

    &:hover {
      text-decoration: underline;
      color: color.adjust($system-blue, $lightness: -10%);
    }

    // Dark mode
    .theme-dark & {
      color: $system-blue;
    }
  }

  input[type="checkbox"] {
    accent-color: $system-blue;
    width: 16px;
    height: 16px;
    border-radius: 4px;
  }
}

// Terms checkbox and link
.terms-checkbox {
  margin: 0.75rem 0 1rem;
  position: relative;
  transition: all 0.3s ease;

  &.terms-accepted {
    color: color.adjust($secondary-text, $lightness: -10%);

    // Dark mode
    .theme-dark & {
      color: color.adjust($dark-secondary-text, $lightness: 10%);
    }
  }

  .terms-link {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    color: $system-blue;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    &:hover {
      text-decoration: underline;
      color: color.adjust($system-blue, $lightness: -10%);
    }

    // Dark mode
    .theme-dark & {
      color: $system-blue;

      &:hover {
        color: color.adjust($system-blue, $lightness: 10%);
      }
    }
  }

  .terms-accepted-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    color: $system-green;
    font-weight: bold;
    animation: fade-in 0.3s ease-out;

    // Dark mode
    .theme-dark & {
      color: $system-green;
    }
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.redirect-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($window-background, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  backdrop-filter: blur(10px);

  // Dark mode
  .theme-dark & {
    background-color: rgba($dark-window-background, 0.95);
  }

  .spinner {
    border: 4px solid rgba($system-blue, 0.1);
    border-top: 4px solid $system-blue;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 0.8s linear infinite;
    box-shadow: 0 4px 12px rgba($system-blue, 0.15);

    // Dark mode
    .theme-dark & {
      border: 4px solid rgba($system-blue, 0.2);
      border-top: 4px solid $system-blue;
      box-shadow: 0 4px 12px rgba($system-blue, 0.25);
    }
  }

  p {
    margin-top: 1.5rem;
    font-size: 1.1rem;
    color: $primary-text;
    font-weight: 500;

    // Dark mode
    .theme-dark & {
      color: $dark-primary-text;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Terms and Conditions Modal
.terms-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .terms-modal {
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    background-color: $window-background;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: modal-appear 0.3s ease-out;
    border: 1px solid $border-color;

    // Dark mode
    .theme-dark & {
      background-color: $dark-window-background;
      border-color: $dark-border-color;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .terms-modal-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid $border-color;
      display: flex;
      justify-content: space-between;
      align-items: center;

      // Dark mode
      .theme-dark & {
        border-color: $dark-border-color;
      }

      h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: $primary-text;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        // Dark mode
        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .close-button {
        background: transparent;
        border: none;
        font-size: 1.5rem;
        line-height: 1;
        color: $tertiary-text;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba($system-gray, 0.1);
          color: $secondary-text;
        }

        // Dark mode
        .theme-dark & {
          color: $dark-tertiary-text;

          &:hover {
            background-color: rgba($dark-tertiary-text, 0.2);
            color: $dark-secondary-text;
          }
        }
      }
    }

    .terms-modal-content {
      padding: 1.5rem;
      overflow-y: auto;
      flex: 1;
      color: $primary-text;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      // macOS-style scrollbar
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba($system-gray, 0.5);
        border-radius: 4px;
      }

      // Dark mode
      .theme-dark & {
        color: $dark-primary-text;

        &::-webkit-scrollbar-thumb {
          background-color: rgba($dark-tertiary-text, 0.5);
        }
      }

      h4 {
        margin: 1.5rem 0 0.75rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: $primary-text;

        &:first-child {
          margin-top: 0;
        }

        // Dark mode
        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      p {
        margin: 0.5rem 0 1rem;
        font-size: 0.95rem;
        line-height: 1.5;
        color: $secondary-text;

        // Dark mode
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }

      ul {
        margin: 0.5rem 0 1rem 1.5rem;
        padding: 0;

        li {
          margin-bottom: 0.5rem;
          font-size: 0.95rem;
          line-height: 1.5;
          color: $secondary-text;

          // Dark mode
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .terms-modal-footer {
      padding: 1rem 1.5rem;
      border-top: 1px solid $border-color;
      display: flex;
      justify-content: flex-end;

      // Dark mode
      .theme-dark & {
        border-color: $dark-border-color;
      }

      .accept-button {
        background-color: $system-blue;
        color: white;
        border: none;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.95rem;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        &:hover {
          background-color: color.adjust($system-blue, $lightness: -5%);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba($system-blue, 0.3);
        }

        &:active {
          transform: translateY(0);
          background-color: color.adjust($system-blue, $lightness: -10%);
          box-shadow: 0 1px 3px rgba($system-blue, 0.2);
        }
      }
    }
  }
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.signup {
  padding: 0.25rem 0.5rem;
  background-color: transparent;
  color: $system-blue;
  font-weight: 600;
  border-radius: 4px;
  transition: all 0.2s ease;

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &:hover {
    background-color: rgba($system-blue, 0.05);
    color: color.adjust($system-blue, $lightness: -10%);
  }

  // Dark mode
  .theme-dark & {
    color: $system-blue;

    &:hover {
      background-color: rgba($system-blue, 0.1);
    }
  }
}

.media {
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;

  .option {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: $secondary-text;
    margin-bottom: 1rem;

    // Dark mode
    .theme-dark & {
      color: $dark-secondary-text;
    }

    p {
      width: fit-content;
      padding: 0 1rem;
      white-space: nowrap;
      color: $tertiary-text;
      font-size: 0.9rem;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      // Dark mode
      .theme-dark & {
        color: $dark-tertiary-text;
      }
    }

    hr {
      width: 100%;
      height: 1px;
      margin: auto;
      background-color: $separator-color;
      border: none;

      // Dark mode
      .theme-dark & {
        background-color: $dark-separator-color;
      }
    }
  }

  .buttons {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 1rem;

    button {
      background-color: $window-background;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.5rem;
      color: $primary-text;
      padding: 0.75rem;
      border: 1px solid $border-color;
      border-radius: 8px;
      transition: all 0.2s ease;
      font-weight: 500;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      * {
        color: $primary-text;
        transition: color 0.2s ease;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border-color: $system-blue;

        * {
          color: $system-blue;
        }
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-window-background;
        border-color: $dark-border-color;
        color: $dark-primary-text;

        * {
          color: $dark-primary-text;
        }

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          border-color: $system-blue;

          * {
            color: $system-blue;
          }
        }
      }
    }
  }
}

a {
  color: $system-blue;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &:hover {
    text-decoration: underline;
    color: color.adjust($system-blue, $lightness: -10%);
  }

  // Dark mode
  .theme-dark & {
    color: $system-blue;
  }
}

// Media Queries for Responsiveness
@media (max-width: 768px) {
  .login {
    padding: 1rem;

    &_hero {
      flex-direction: column;
      height: auto;
      width: 100%;
      max-width: 500px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

      // Dark mode
      .theme-dark & {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .img {
        height: 200px;
        width: 100%;
        border-radius: 12px 12px 0 0;
      }

      .login-container {
        width: 100%;
        max-width: 500px;
        border-radius: 0 0 12px 12px;
        padding: 1.5rem;
      }
    }

    .login-card {
      border-radius: 0 0 12px 12px;
      padding: 1rem;
    }

    h2 {
      font-size: 1.5rem;
      text-align: center;
    }

    .logo h2 {
      text-align: center;
    }

    .media .buttons {
      flex-direction: column;
    }

    .question {
      text-align: center;
      //color: red !important;

    }

    p {
      text-align: center;
    }
  }
}

@media (max-width: 480px) {
  .login {
    &_hero {
      padding: 0;
      border-radius: 12px;

      .img {
        height: 150px;
        border-radius: 12px 12px 0 0;
      }

      .login-container {
        border-radius: 0 0 12px 12px;
        padding: 1rem;
      }
    }

    .login-card {
      padding: 0.75rem;
      border-radius: 0 0 12px 12px;
    }

    h2 {
      font-size: 1.4rem;
    }

    input[type="text"],
    input[type="email"],
    input[type="password"] {
      padding: 0.6rem 0.75rem;
      font-size: 0.95rem;
    }

    form button {
      padding: 0.6rem;
      font-size: 0.95rem;
    }

    .media .buttons button {
      padding: 0.6rem;
    }

    .logo h2 {
      font-size: 1.2rem;
    }
  }
}