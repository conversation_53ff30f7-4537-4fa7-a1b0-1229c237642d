import PomodoroTimer from '../components/tools/list/PomodoroTimer.jsx';
import EisenhowerMatrix from '../components/tools/list/EisenhowerMatrix.jsx';
import KanbanBoard from '../components/tools/list/KanbanBoard.jsx';
import CornellNotes from '../components/tools/list/CornellNotes.jsx';
import Doodles from '../components/tools/list/Doodles.jsx';
import Mindmap from '../components/tools/list/Mindmap.jsx';
import Retrospective from '../components/tools/list/Retrospective.jsx';
import MasteryTracker from '../components/tools/list/MasteryTracker.jsx';
import FlashCards from '../components/tools/list/FlashCards.jsx';
import axios from 'axios';
import Quizzes from '../components/tools/list/Quizzes.jsx';
import Calendar from '../components/tools/list/Calendar.jsx';
import DailyObjectives from '../components/tools/list/DailyObjectives.jsx';
import Podcast from '../components/tools/list/Podcast.jsx';
import ExplainerVideos from '../components/tools/list/ExplainerVideos.jsx';
//import Mnemonic from '../components/tools/list/Mnemonic.jsx';

// Import all images
import pomodoroImg from '../assets/pomodoro.png';
import eisenhowerImg from '../assets/eisenhower.png';
import kanbanImg from '../assets/kanban.png';
import cornellImg from '../assets/cornell.png';
import doodlesImg from '../assets/doodles.jpg';
import mindmapImg from '../assets/mindmap.png';
import retrospectiveImg from '../assets/retrospective.png';
import masteryImg from '../assets/mastery.png';
import flashcardsImg from '../assets/flashcards.png';
import quizImg from '../assets/quiz.jpg';
import calendarImg from '../assets/calender.png';
import objectivesImg from '../assets/objectives.png';
import podcastImg from '../assets/podcast.png';
import videoexplainerImg from '../assets/videoexplainer.png';
import mnemonicImg from '../assets/mnemonic.png';

export const tools = [
    {
        id: 'T1',
        name: 'Pomodoro Timer',
        description: 'Time management technique using 25-minute focused intervals',
        image: pomodoroImg,
        component: PomodoroTimer,
        requiresSubject: false, // Pomodoro timer doesn't require subject/chapter selection
        standalone: true, // Can be used independently
    },
    {
        id: 'T2',
        name: 'Eisenhower Matrix',
        description: 'Prioritization tool to categorize tasks by urgency and importance',
        image: eisenhowerImg,
        component: EisenhowerMatrix,
    },
    {
        id: 'T3',
        name: 'Kanban Board',
        description: 'Visual workflow management system with columns for task status',
        image: kanbanImg,
        component: KanbanBoard,
    },
    {
        id: 'T4',
        name: 'Cornell Notes',
        description: 'Note-taking system with cues, notes, and summary sections',
        image: cornellImg,
        component: CornellNotes,
    },
    {
        id: 'T5',
        name: 'Doodles',
        description: 'Visual note-taking with drawings and diagrams',
        image: doodlesImg,
        component: Doodles,
    },
    {
        id: 'T6',
        name: 'Mindmap',
        description: 'Diagram to visually organize information hierarchically',
        image: mindmapImg,
        component: Mindmap,
    },
    {
        id: 'T7',
        name: 'Retrospective',
        description: 'Reflection tool to analyze what went well and what to improve',
        image: retrospectiveImg,
        component: Retrospective,
    },

    {
        id: 'T9',
        name: 'Flash Cards',
        description: 'Learning tool with questions on one side and answers on the other',
        image: flashcardsImg,
        component: FlashCards,
    },
    {
        id: 'T10',
        name: 'Quizzes',
        description: 'Test knowledge with multiple-choice or short answer questions',
        image: quizImg,
        component: Quizzes,
    },
    {
        id: 'T11',
        name: 'Calendar',
        description: 'Schedule and organize study sessions and deadlines',
        image: calendarImg,
        component: Calendar,
    },

    {
        id: 'T13',
        name: 'Podcast',
        description: 'Audio learning content on various subjects',
        image: podcastImg,
        component: Podcast,
    },
    {
        id: 'T14',
        name: 'Explainer Videos',
        description: 'Short educational videos explaining concepts',
        image: videoexplainerImg,
        component: ExplainerVideos,
    },
    {/*{
        id: 'T15',
        name: 'Mnemonic',
        description: 'Memory aids to help retain information',
        image: mnemonicImg,
        component: Mnemonic,
    }*/}
];