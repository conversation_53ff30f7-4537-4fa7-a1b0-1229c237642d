import React from 'react';
import { NavLink } from 'react-router-dom';
import "../styles/sidebar.scss";

const NavItem = ({ icon, text, to, onClick, collapsed, isActive }) => {
    return (
        <li className="nav-item">
            {to ? (
                <NavLink
                    to={to}
                    className={({ isActive: navLinkActive }) =>
                        `nav-link ${navLinkActive || isActive ? 'active' : ''}`
                    }
                    end
                >
                    <span className="nav-icon">{icon}</span>
                    {!collapsed && <span className="nav-text">{text}</span>}
                </NavLink>
            ) : (
                <button className="nav-link" onClick={onClick}>
                    <span className="nav-icon">{icon}</span>
                    {!collapsed && <span className="nav-text">{text}</span>}
                </button>
            )}
        </li>
    );
};

export default NavItem;