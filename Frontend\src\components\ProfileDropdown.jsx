

import React, { useState } from 'react';
import "../styles/profile.scss";
import AdvancedProfileSettings from "../pages/Profile.jsx";
import PreferencesSettings from "../pages/Preferences.jsx";
import {
    AccountCircle,
    Settings,
    ExitToApp,
    HelpOutline,
    School,
    History,
    StarBorder
} from '@mui/icons-material';
import { useAuth } from '../../authContext.jsx'; // Make sure this path is correct

const ProfileDropdown = ({ darkMode }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
    const [showPreferences, setShowPreferences] = useState(false);

    const { user, logout } = useAuth(); // Get user from AuthContext

    const handleLogout = () => {
        logout();
    };

    //const { user } = useAuth();
    console.log("User from AuthContext:", user);

    // Helper to get full name or initials
    const getFullName = () => {
        return user?.fullName || '';
    };

    const getInitials = () => {
        if (!user || !user.fullName) return 'U';

        const fullName = user.fullName;
        const initials = fullName.split(' ')
            .filter(word => word.length > 0)
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);

        return initials || 'U';
    };

    return (
        <div className={`profile-dropdown-container ${darkMode ? 'dark-mode' : ''}`}>
            {/* Avatar or Initials */}
            <div
                className="user-avatar"
                onClick={() => setIsOpen(!isOpen)}
                title={getFullName() || "User"}
            >
                {user?.avatar ? (
                    <img src={user.avatar} alt="Avatar" className="avatar-img" />
                ) : (
                    getInitials()
                )}
            </div>

            {isOpen && (
                <div className="dropdown-menu">
                    <div className="profile-header">
                        {/* Large Avatar or Initials */}
                        <div className="avatar-large">
                            {user?.avatar ? (
                                <img src={user.avatar} alt="Avatar" className="avatar-img" />
                            ) : (
                                getInitials()
                            )}
                        </div>
                        <div className="user-info">
                            <h3>{getFullName() || "User Name"}</h3>
                            <span className="user-email">{user?.email || '<EMAIL>'}</span>
                        </div>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="menu-section">
                        <h4>Learning</h4>
                        <button className="menu-item">
                            <School className="menu-icon" />
                            <span>My Subjects</span>
                        </button>
                        <button className="menu-item">
                            <History className="menu-icon" />
                            <span>Learning History</span>
                        </button>
                        <button className="menu-item">
                            <StarBorder className="menu-icon" />
                            <span>Saved Resources</span>
                        </button>
                    </div>

                    <div className="dropdown-divider"></div>

                    <div className="menu-section">
                        <h4>Account</h4>
                        <button
                            className="menu-item"
                            onClick={() => {
                                setShowAdvancedSettings(true);
                                setIsOpen(false);
                            }}
                        >
                            <Settings className="menu-icon" />
                            <span>Advanced Settings</span>
                        </button>
                        <button
                            className="menu-item"
                            onClick={() => {
                                setShowPreferences(true);
                                setIsOpen(false);
                            }}
                        >
                            <Settings className="menu-icon" />
                            <span>Preferences</span>
                        </button>
                        <button className="menu-item">
                            <HelpOutline className="menu-icon" />
                            <span>Help Center</span>
                        </button>
                    </div>

                    <div className="dropdown-divider"></div>

                    <button className="logout-button" onClick={handleLogout}>
                        <ExitToApp className="menu-icon" />
                        <span>Sign Out</span>
                    </button>
                </div>
            )}

            {/* Modals */}
            {showAdvancedSettings && (
                <>
                    <div className="settings-overlay visible" onClick={() => setShowAdvancedSettings(false)} />
                    <AdvancedProfileSettings
                        darkMode={darkMode}
                        onClose={() => setShowAdvancedSettings(false)}
                    />
                </>
            )}

            {showPreferences && (
                <div className="preferences-modal">
                    <div className="modal-overlay" onClick={() => setShowPreferences(false)} />
                    <div className="modal-content">
                        <PreferencesSettings
                            darkMode={darkMode}
                            onClose={() => setShowPreferences(false)}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProfileDropdown;