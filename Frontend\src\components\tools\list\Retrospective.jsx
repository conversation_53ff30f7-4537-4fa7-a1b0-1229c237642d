import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';


const Retrospective = ({ tool, subject }) => {
    const [retro, setRetro] = useState({
        wentWell: [],
        toImprove: [],
        actionItems: []
    });
    const [newItem, setNewItem] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('wentWell');
    const [editIndex, setEditIndex] = useState(null);
    const [editText, setEditText] = useState('');
    const [loadingError, setLoadingError] = useState(null);

    // Load retrospective from localStorage
    useEffect(() => {
        try {
            const savedRetro = localStorage.getItem(`${subject}-retrospective`);
            if (savedRetro) {
                const parsedRetro = JSON.parse(savedRetro);
                setRetro(parsedRetro);
                setLoadingError(null);
            }
        } catch (error) {
            console.error('Error loading retrospective:', error);
            setLoadingError('Failed to load retrospective data');
        }
    }, [subject]);

    // Save retrospective to localStorage
    useEffect(() => {
        try {
            localStorage.setItem(`${subject}-retrospective`, JSON.stringify(retro));
        } catch (error) {
            console.error('Error saving retrospective:', error);
            setLoadingError('Failed to save retrospective data');
        }
    }, [retro, subject]);

    const addItem = () => {
        if (newItem.trim()) {
            setRetro(prev => ({
                ...prev,
                [selectedCategory]: [...prev[selectedCategory], {
                    text: newItem,
                    id: Date.now(),
                    createdAt: new Date().toISOString()
                }]
            }));
            setNewItem('');
        }
    };

    const removeItem = (category, id) => {
        setRetro(prev => ({
            ...prev,
            [category]: prev[category].filter(item => item.id !== id)
        }));
    };

    const startEdit = (category, index) => {
        setEditIndex({ category, id: retro[category][index].id });
        setEditText(retro[category][index].text);
    };

    const saveEdit = () => {
        if (!editIndex || !editText.trim()) return;

        setRetro(prev => ({
            ...prev,
            [editIndex.category]: prev[editIndex.category].map(item =>
                item.id === editIndex.id ? { ...item, text: editText } : item
            )
        }));
        setEditIndex(null);
        setEditText('');
    };

    const cancelEdit = () => {
        setEditIndex(null);
        setEditText('');
    };

    const moveItem = (fromCategory, toCategory, id) => {
        const itemToMove = retro[fromCategory].find(item => item.id === id);
        if (!itemToMove) return;

        setRetro(prev => ({
            ...prev,
            [fromCategory]: prev[fromCategory].filter(item => item.id !== id),
            [toCategory]: [...prev[toCategory], itemToMove]
        }));
    };

    const resetRetro = () => {
        if (window.confirm('Are you sure you want to reset this retrospective?')) {
            setRetro({
                wentWell: [],
                toImprove: [],
                actionItems: []
            });
            setLoadingError(null);
        }
    };

    const exportRetro = () => {
        const data = JSON.stringify(retro, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${subject}-retrospective-${new Date().toISOString().slice(0,10)}.json`;
        link.click();
        URL.revokeObjectURL(url);
    };

    const importRetro = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const importedRetro = JSON.parse(event.target.result);
                if (importedRetro.wentWell && importedRetro.toImprove && importedRetro.actionItems) {
                    setRetro(importedRetro);
                    setLoadingError(null);
                } else {
                    throw new Error('Invalid retrospective format');
                }
            } catch (error) {
                console.error('Error importing retrospective:', error);
                setLoadingError('Failed to import retrospective. Invalid file format.');
            }
        };
        reader.readAsText(file);
    };

    const getCategoryTitle = (category) => {
        switch (category) {
            case 'wentWell': return 'What went well';
            case 'toImprove': return 'To improve';
            case 'actionItems': return 'Action items';
            default: return category;
        }
    };

    return (
        <div className="retrospective-app">
            <header className="app-header">
                <h1>{subject} Retrospective</h1>
                <div className="app-controls">
                    <button className="icon-button" onClick={resetRetro} title="Reset retrospective">
                        <i className="fas fa-trash-alt"></i>
                    </button>
                    <button className="icon-button" onClick={exportRetro} title="Export retrospective">
                        <i className="fas fa-file-export"></i>
                    </button>
                    <label className="icon-button" title="Import retrospective">
                        <i className="fas fa-file-import"></i>
                        <input type="file" accept=".json" onChange={importRetro} style={{ display: 'none' }} />
                    </label>
                </div>
            </header>

            {loadingError && (
                <div className="error-message">
                    <i className="fas fa-exclamation-triangle"></i> {loadingError}
                </div>
            )}

            <div className="retro-grid">
                {['wentWell', 'toImprove', 'actionItems'].map(category => (
                    <div key={category} className={`category ${category}`}>
                        <div className="category-header">
                            <h3>{getCategoryTitle(category)}</h3>
                            <span className="count-badge">{retro[category].length}</span>
                        </div>
                        <ul>
                            <AnimatePresence>
                                {retro[category].map((item, index) => (
                                    <motion.li
                                        key={item.id}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, x: -50 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        {editIndex?.category === category && editIndex?.id === item.id ? (
                                            <div className="edit-form">
                                                <input
                                                    type="text"
                                                    value={editText}
                                                    onChange={(e) => setEditText(e.target.value)}
                                                    autoFocus
                                                />
                                                <button className="save-button" onClick={saveEdit}>
                                                    <i className="fas fa-check"></i>
                                                </button>
                                                <button className="cancel-button" onClick={cancelEdit}>
                                                    <i className="fas fa-times"></i>
                                                </button>
                                            </div>
                                        ) : (
                                            <>
                                                <span className="item-text">{item.text}</span>
                                                <div className="item-actions">
                                                    <button
                                                        className="edit-button"
                                                        onClick={() => startEdit(category, index)}
                                                        title="Edit"
                                                    >
                                                        <i className="fas fa-edit"></i>
                                                    </button>
                                                    {category !== 'wentWell' && (
                                                        <button
                                                            className="move-button"
                                                            onClick={() => moveItem(category, 'wentWell', item.id)}
                                                            title="Move to Went Well"
                                                        >
                                                            <i className="fas fa-arrow-up"></i>
                                                        </button>
                                                    )}
                                                    {category !== 'toImprove' && (
                                                        <button
                                                            className="move-button"
                                                            onClick={() => moveItem(category, 'toImprove', item.id)}
                                                            title="Move to To Improve"
                                                        >
                                                            <i className="fas fa-arrow-right"></i>
                                                        </button>
                                                    )}
                                                    {category !== 'actionItems' && (
                                                        <button
                                                            className="move-button"
                                                            onClick={() => moveItem(category, 'actionItems', item.id)}
                                                            title="Move to Action Items"
                                                        >
                                                            <i className="fas fa-arrow-down"></i>
                                                        </button>
                                                    )}
                                                    <button
                                                        className="delete-button"
                                                        onClick={() => removeItem(category, item.id)}
                                                        title="Delete"
                                                    >
                                                        <i className="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </>
                                        )}
                                    </motion.li>
                                ))}
                            </AnimatePresence>
                        </ul>
                    </div>
                ))}
            </div>

            <div className="add-item">
                <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="category-select"
                >
                    <option value="wentWell">What went well</option>
                    <option value="toImprove">To improve</option>
                    <option value="actionItems">Action items</option>
                </select>
                <input
                    type="text"
                    value={newItem}
                    onChange={(e) => setNewItem(e.target.value)}
                    placeholder="Enter new item..."
                    onKeyPress={(e) => e.key === 'Enter' && addItem()}
                    className="item-input"
                />
                <button
                    onClick={addItem}
                    disabled={!newItem.trim()}
                    className="add-button"
                >
                    <i className="fas fa-plus"></i> Add
                </button>
            </div>
        </div>
    );
};

export default Retrospective;