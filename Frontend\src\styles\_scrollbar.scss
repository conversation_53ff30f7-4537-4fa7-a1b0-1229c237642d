@use "../styles/variables" as *;
@use "sass:color";

// iOS-like scrollbar mixin
@mixin ios-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba($system-gray, 0.5);
    border-radius: 100px;
    border: 2px solid transparent;
    background-clip: content-box;
    min-height: 44px;
    
    // Dark theme
    .theme-dark & {
      background-color: rgba($dark-tertiary-text, 0.5);
    }
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba($system-gray, 0.7);
    
    // Dark theme
    .theme-dark & {
      background-color: rgba($dark-tertiary-text, 0.7);
    }
  }
}

// iOS-like auto-hiding scrollbar mixin
@mixin ios-autohide-scrollbar {
  @include ios-scrollbar;
  
  // Hide scrollbar when not in use
  &::-webkit-scrollbar-thumb {
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::-webkit-scrollbar-thumb {
    opacity: 1;
  }
}

// Apply iOS scrollbar to all elements by default
* {
  @include ios-scrollbar;
}

// Specific scrollbar styles for different components
.modal-content {
  @include ios-autohide-scrollbar;
}

.chat-messages {
  @include ios-autohide-scrollbar;
}

.content-area {
  @include ios-autohide-scrollbar;
}

// For elements that need scrollbars always visible
.always-visible-scrollbar {
  @include ios-scrollbar;
}
