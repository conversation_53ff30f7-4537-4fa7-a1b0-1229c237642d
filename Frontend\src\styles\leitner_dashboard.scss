@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// Leitner Dashboard Card
.leitner-dashboard-card {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  padding: .5rem;
  display: flex;
  //flex-direction: column;
  flex-wrap: wrap;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  overflow: hidden;

  // Light theme
  background-color: $window-background;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid $border-color;

  .actual-content {
    //background-color: red;
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: .5rem;
    //align-items: center;
    justify-content: space-between;
  }

  // Dark theme
  &.theme-dark {
    background-color: $dark-window-background;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border: 1px solid $dark-border-color;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);

    // Dark theme
    &.theme-dark {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: .5rem;
    padding: 0 0 0.5rem 0;
    position: relative;
    width: 100%;


    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;

      // Light theme
      background-color: $separator-color;

      // Dark theme
      .theme-dark & {
        background-color: $dark-separator-color;
      }
    }

    .card-title {
      font-size: 1.2rem;
      font-weight: 600;
      letter-spacing: -0.2px;
      margin: 0;
      text-align: center;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .loading-indicator {
      width: 16px;
      height: 16px;
      border: 2px solid rgba($system-blue, 0.2);
      border-top-color: $system-blue;
      border-radius: 50%;
      animation: spin 1s linear infinite;

      .theme-dark & {
        border-color: rgba($system-blue, 0.3);
        border-top-color: lighten($system-blue, 10%);
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }
  }

  // Loading container
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba($system-blue, 0.2);
      border-top-color: $system-blue;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;

      .theme-dark & {
        border-color: rgba($system-blue, 0.3);
        border-top-color: lighten($system-blue, 10%);
      }
    }

    p {
      font-size: 0.9rem;
      color: $secondary-text;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  // Error message
  .error-message {
    background-color: rgba($system-red, 0.1);
    border-left: 3px solid $system-red;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 6px;

    .theme-dark & {
      background-color: rgba($system-red, 0.15);
    }

    p {
      color: $system-red;
      margin: 0 0 0.75rem 0;
      font-size: 0.9rem;

      .theme-dark & {
        color: lighten($system-red, 10%);
      }
    }

    .retry-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background-color: $system-red;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: darken($system-red, 5%);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  // Section titles
  .section-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    color: $secondary-text;

    .theme-dark & {
      color: $dark-secondary-text;
    }
  }

  // Leitner Tracker Section
  .leitner-tracker-section {
    //margin-bottom: 1.5rem;
    //padding-bottom: 1.5rem;
    //border-bottom: 1px solid $separator-color;
    width: 50%;

    .theme-dark & {
      border-bottom-color: $dark-separator-color;
    }
  }

  // Leitner Reminder Section
  .leitner-reminder-section {
    margin-bottom: 0.5rem;

    .section-title {
      margin-bottom: 0.5rem;
    }

    .start-review-button {
      display: block;
      width: 100%;
      margin-top: 1rem;
      padding: 0.75rem;
      background-color: $system-blue;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba($system-blue, 0.3);

      &:hover {
        background-color: darken($system-blue, 5%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba($system-blue, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      .theme-dark & {
        background-color: lighten($system-blue, 5%);
        box-shadow: 0 2px 8px rgba(lighten($system-blue, 5%), 0.3);

        &:hover {
          background-color: lighten($system-blue, 10%);
          box-shadow: 0 4px 12px rgba(lighten($system-blue, 10%), 0.4);
        }
      }
    }
  }

  // Total Cards Info
  .total-cards-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: .5rem;
    padding: 0.5rem 0.75rem;
    background-color: $secondary-background;
    border-radius: 8px;
    //width: 50%;

    .theme-dark & {
      background-color: $dark-secondary-background;
    }

    .total-label {
      font-size: 0.9rem;
      font-weight: 500;
      color: $secondary-text;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    .total-count {
      font-size: 1.1rem;
      font-weight: 600;
      color: $system-blue;

      .theme-dark & {
        color: lighten($system-blue, 10%);
      }
    }

    .untracked-info {
      font-size: 0.85rem;
      font-weight: 500;
      color: $system-orange;
      margin-left: 0.5rem;

      .theme-dark & {
        color: lighten($system-orange, 10%);
      }
    }
  }

  // Leitner Stats
  .leitner-stats {
    display: flex;
    flex-direction: column;
    //gap: .2rem;
    //width: 100%;

    // Mastery Progress
    .mastery-progress {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .progress-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        font-weight: 500;

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }

        .percentage {
          font-weight: 600;

          // Light theme
          color: $system-blue;

          // Dark theme
          .theme-dark & {
            color: lighten($system-blue, 10%);
          }
        }
      }

      .progress-bar {
        height: 8px;
        background-color: rgba($system-blue, 0.1);
        border-radius: 4px;
        overflow: hidden;

        // Dark theme
        .theme-dark & {
          background-color: rgba($system-blue, 0.15);
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(to right, $system-blue, lighten($system-blue, 10%));
          border-radius: 4px;
          transition: width 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
          box-shadow: 0 1px 2px rgba($system-blue, 0.3);
        }
      }
    }

    // Box Distribution
    .box-distribution {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .box-row {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .box-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;

          svg {
            font-size: 1.25rem;
            color: white;
          }

          &.box-1 {
            background-color: $system-red;
            box-shadow: 0 2px 6px rgba($system-red, 0.3);
          }

          &.box-2 {
            background-color: $system-orange;
            box-shadow: 0 2px 6px rgba($system-orange, 0.3);
          }

          &.box-3 {
            background-color: $system-green;
            box-shadow: 0 2px 6px rgba($system-green, 0.3);
          }
        }

        .box-bar-container {
          flex: 1;
          height: 8px;
          background-color: $secondary-background;
          border-radius: 4px;
          overflow: hidden;

          // Dark theme
          .theme-dark & {
            background-color: $dark-secondary-background;
          }

          .box-bar {
            height: 100%;
            min-width: 4px;
            border-radius: 4px;
            transition: width 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);

            &.box-1-bar {
              background-color: $system-red;
            }

            &.box-2-bar {
              background-color: $system-orange;
            }

            &.box-3-bar {
              background-color: $system-green;
            }
          }
        }

        .box-count {
          min-width: 30px;
          text-align: right;
          font-weight: 600;
          font-size: 0.9rem;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          // Light theme
          color: $primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }

          .untracked-count {
            font-size: 0.75rem;
            font-weight: 500;
            color: $system-orange;
            margin-left: 0.5rem;
            white-space: nowrap;

            .theme-dark & {
              color: lighten($system-orange, 10%);
            }
          }
        }
      }
    }
  }

  // Due Today Reminder
  .due-today-reminder {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: rgba($system-blue, 0.08);
    border: 1px solid rgba($system-blue, 0.2);

    // Dark theme
    .theme-dark & {
      background-color: rgba($system-blue, 0.15);
      border: 1px solid rgba($system-blue, 0.3);
    }

    &.has-cards {
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba($system-blue, 0.2);

        // Light theme
        background-color: rgba($system-blue, 0.12);

        // Dark theme
        .theme-dark & {
          background-color: rgba($system-blue, 0.2);
          box-shadow: 0 4px 12px rgba($system-blue, 0.3);
        }

        .arrow-icon {
          transform: translateX(4px);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }

    &.no-cards {
      // Light theme
      background-color: rgba($system-green, 0.08);
      border: 1px solid rgba($system-green, 0.2);

      // Dark theme
      .theme-dark & {
        background-color: rgba($system-green, 0.15);
        border: 1px solid rgba($system-green, 0.3);
      }

      .reminder-icon {
        // Light theme
        color: $system-green;

        // Dark theme
        .theme-dark & {
          color: lighten($system-green, 10%);
        }
      }

      .reminder-text h3 {
        // Light theme
        color: $system-green;

        // Dark theme
        .theme-dark & {
          color: lighten($system-green, 10%);
        }
      }
    }

    .reminder-icon {
      font-size: 1.75rem;
      margin-right: 1rem;

      // Light theme
      color: $system-blue;

      // Dark theme
      .theme-dark & {
        color: lighten($system-blue, 10%);
      }
    }

    .reminder-text {
      flex: 1;

      h3 {
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;

        // Light theme
        color: $system-blue;

        // Dark theme
        .theme-dark & {
          color: lighten($system-blue, 10%);
        }
      }

      p {
        font-size: 0.9rem;
        margin: 0;

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    .arrow-icon {
      font-size: 1.25rem;
      transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

      // Light theme
      color: $system-blue;

      // Dark theme
      .theme-dark & {
        color: lighten($system-blue, 10%);
      }
    }
  }
}

// Modal Overlay
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

  // Dark theme
  &.theme-dark {
    background: rgba(0, 0, 0, 0.5);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
}

// Modal Window
.modal-window {
  background-color: $window-background;
  border-radius: 16px;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid $border-color;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(0);
  animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  @include ios-autohide-scrollbar;

  // Dark theme
  &.theme-dark {
    background-color: $dark-window-background;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    border: 1px solid $dark-border-color;
  }

  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }

    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .modal-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    position: sticky;
    top: 0;
    z-index: 10;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    // Light theme
    border-bottom: 1px solid $separator-color;
    background-color: rgba($window-background, 0.8);

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-separator-color;
      background-color: rgba($dark-window-background, 0.8);
    }

    .traffic-lights {
      display: flex;
      gap: 8px;

      .traffic-light {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        &.red {
          background: $system-red;
        }

        &.yellow {
          background: $system-orange;
        }

        &.green {
          background: $system-green;
        }

        &:hover {
          transform: scale(1.15);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        }

        &:active {
          transform: scale(0.95);
        }

        &:disabled {
          opacity: 0.4;
          cursor: default;
        }
      }
    }

    .modal-title {
      font-size: 1.3rem;
      font-weight: 600;
      flex: 1;
      text-align: center;
      margin: 0;
      letter-spacing: -0.2px;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }
  }

  .modal-content {
    padding: 24px;
    @include ios-autohide-scrollbar;

    // Session Progress
    .session-progress {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .progress-bar {
        flex: 1;
        height: 8px;
        background-color: $secondary-background;
        border-radius: 4px;
        overflow: hidden;

        .theme-dark & {
          background-color: $dark-secondary-background;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(to right, $system-blue, lighten($system-blue, 10%));
          border-radius: 4px;
          transition: width 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
        }
      }

      .progress-text {
        font-size: 0.9rem;
        font-weight: 500;

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    // Card Container
    .card-container {
      position: relative;
      perspective: 1000px;
      margin-bottom: 2rem;
      min-height: 250px;

      .study-card {
        width: 100%;
        height: 250px;
        position: relative;
        transform-style: preserve-3d;
        transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
        border-radius: 16px;
        cursor: pointer;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

        .theme-dark & {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }

        &.flipped {
          transform: rotateY(180deg);
        }

        .card-face {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          overflow: hidden;
          border-radius: 16px;
          display: flex;
          flex-direction: column;
          padding: 1.5rem;

          &.card-front {
            background-color: $window-background;
            border: 1px solid $border-color;
            z-index: 2;
            transform: rotateY(0deg);

            .theme-dark & {
              background-color: $dark-window-background;
              border-color: $dark-border-color;
            }
          }

          &.card-back {
            background-color: $window-background;
            border: 1px solid $border-color;
            transform: rotateY(180deg);

            .theme-dark & {
              background-color: $dark-window-background;
              border-color: $dark-border-color;
            }
          }

          .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;

            .card-box-indicator {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              font-size: 0.9rem;
              font-weight: 600;
              margin-bottom: 1rem;
              padding-bottom: 0.75rem;
              border-bottom: 1px solid $separator-color;

              .theme-dark & {
                border-bottom: 1px solid $dark-separator-color;
              }

              svg {
                font-size: 1.25rem;
              }
            }

            .card-text {
              flex: 1;
              font-size: 1.1rem;
              line-height: 1.6;
              overflow-y: auto;
              padding-right: 0.5rem;
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;
            }

            .card-hint {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-top: 1.5rem;
              font-size: 0.9rem;

              // Light theme
              color: $tertiary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-tertiary-text;
              }

              .flip-icon {
                margin-right: 0.5rem;
                font-size: 1.1rem;
              }
            }
          }
        }
      }
    }

    // Answer Controls
    .answer-controls {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .answer-button {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        border-radius: 12px;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

        &.correct {
          background-color: $system-green;
          color: white;
          box-shadow: 0 4px 12px rgba($system-green, 0.3);

          &:hover {
            background-color: darken($system-green, 5%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba($system-green, 0.4);
          }

          &:active {
            transform: translateY(0);
          }
        }

        &.incorrect {
          background-color: $system-red;
          color: white;
          box-shadow: 0 4px 12px rgba($system-red, 0.3);

          &:hover {
            background-color: darken($system-red, 5%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba($system-red, 0.4);
          }

          &:active {
            transform: translateY(0);
          }
        }

        svg {
          margin-right: 0.75rem;
          font-size: 1.25rem;
        }
      }
    }

    // Navigation Controls
    .navigation-controls {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;

      .next-button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.9rem 1.5rem;
        border-radius: 12px;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        background-color: $system-blue;
        color: white;
        box-shadow: 0 4px 12px rgba($system-blue, 0.3);

        &:hover {
          background-color: darken($system-blue, 5%);
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba($system-blue, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        svg {
          margin-left: 0.75rem;
          font-size: 1.25rem;
        }
      }
    }

    // Session Stats
    .session-stats {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid $separator-color;

      .theme-dark & {
        border-top-color: $dark-separator-color;
      }

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .stat-label {
          font-size: 0.9rem;
          margin-bottom: 0.5rem;

          // Light theme
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }

        .stat-value {
          font-size: 1.5rem;
          font-weight: 600;

          &.correct {
            color: $system-green;

            .theme-dark & {
              color: lighten($system-green, 10%);
            }
          }

          &.incorrect {
            color: $system-red;

            .theme-dark & {
              color: lighten($system-red, 10%);
            }
          }
        }
      }
    }

    // Empty Session
    .empty-session {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem 1.5rem;
      text-align: center;

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 1rem 0;

        // Light theme
        color: $system-green;

        // Dark theme
        .theme-dark & {
          color: lighten($system-green, 10%);
        }
      }

      p {
        font-size: 1rem;
        margin: 0 0 1.5rem 0;

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }

      .create-flashcards-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        background-color: $system-blue;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba($system-blue, 0.3);

        &:hover {
          background-color: darken($system-blue, 5%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($system-blue, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}