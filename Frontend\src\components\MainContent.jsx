import React from 'react';
import { Outlet } from 'react-router-dom';
import "../styles/sidebar.scss";

import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';

const MainContent = ({ toggleRightSidebar, rightSidebarCollapsed, children }) => {
    return (
        <div className="content-area">
            {rightSidebarCollapsed && (
                <button
                    className="ai-chat-toggle"
                    onClick={toggleRightSidebar}
                >
                    <span className="custom-icon"> <AutoAwesomeIcon /> AI Assistant</span>
                </button>
            )}
            <div className="content">
                {children || <Outlet />}
            </div>
        </div>
    );
};

export default MainContent;