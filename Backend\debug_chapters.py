#!/usr/bin/env python3
"""
Debug script to check chapters in the database
Run this to see what chapters exist for each subject
"""

import os
import sys
import psycopg2
from psycopg2.extras import DictCursor

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'database': 'blueprint',
    'user': 'postgres',
    'password': 'Lu<PERSON>@1956'
}

def check_database():
    """Check the database for subjects and chapters"""
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor(cursor_factory=DictCursor)
        
        print("=== DATABASE DEBUG REPORT ===\n")
        
        # Check if tables exist
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('subjects', 'chapters', 'quizzes')
            ORDER BY table_name
        """)
        tables = cursor.fetchall()
        print("Available tables:")
        for table in tables:
            print(f"  - {table['table_name']}")
        print()
        
        # Check subjects
        cursor.execute("SELECT id, user_id, name, created_at FROM subjects ORDER BY id")
        subjects = cursor.fetchall()
        print(f"Found {len(subjects)} subjects:")
        for subject in subjects:
            print(f"  Subject ID: {subject['id']}, User ID: {subject['user_id']}, Name: '{subject['name']}'")
        print()
        
        # Check chapters for each subject
        for subject in subjects:
            cursor.execute("""
                SELECT id, subject_id, chapter_number, title, created_at 
                FROM chapters 
                WHERE subject_id = %s 
                ORDER BY created_at
            """, (subject['id'],))
            chapters = cursor.fetchall()
            
            print(f"Chapters for Subject '{subject['name']}' (ID: {subject['id']}):")
            if chapters:
                for chapter in chapters:
                    print(f"  - Chapter ID: {chapter['id']}, Number: '{chapter['chapter_number']}', Title: '{chapter['title']}'")
            else:
                print("  - No chapters found")
            print()
        
        # Check quizzes table structure
        cursor.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'quizzes' 
            ORDER BY ordinal_position
        """)
        quiz_columns = cursor.fetchall()
        print("Quizzes table structure:")
        for col in quiz_columns:
            print(f"  - {col['column_name']}: {col['data_type']} (nullable: {col['is_nullable']})")
        print()
        
        # Check if chapter_id column exists in quizzes
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'quizzes' AND column_name = 'chapter_id'
        """)
        chapter_id_exists = cursor.fetchone()
        print(f"chapter_id column in quizzes table: {'EXISTS' if chapter_id_exists else 'MISSING'}")
        print()
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"Error connecting to database: {e}")
        print("Please check your database connection settings.")

if __name__ == "__main__":
    check_database()
