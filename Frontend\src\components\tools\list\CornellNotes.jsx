import { useState, useRef, useEffect } from 'react';
import { useTheme } from '../../../contexts/ThemeContext';
import "../../../styles/cornell_note.scss";
import axios from 'axios';

// Material UI Icons
import SubjectIcon from '@mui/icons-material/Subject';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import SummarizeIcon from '@mui/icons-material/Summarize';
import SaveIcon from '@mui/icons-material/Save';
import EditIcon from '@mui/icons-material/Edit';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import FormatItalicIcon from '@mui/icons-material/FormatItalic';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import DownloadIcon from '@mui/icons-material/Download';
import PrintIcon from '@mui/icons-material/Print';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import CloseIcon from '@mui/icons-material/Close';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import ListAltIcon from '@mui/icons-material/ListAlt';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import ImageIcon from '@mui/icons-material/Image';
import ClearIcon from '@mui/icons-material/Clear';
import StrikethroughSIcon from '@mui/icons-material/StrikethroughS';
import TitleIcon from '@mui/icons-material/Title';
import FormatQuoteIcon from '@mui/icons-material/FormatQuote';
import CodeIcon from '@mui/icons-material/Code';
import DataObjectIcon from '@mui/icons-material/DataObject';
import LinkIcon from '@mui/icons-material/Link';

const CornellNotes = ({ tool, subjectId, chapterId, initialNote, onSave, onClose }) => {
    const { darkMode } = useTheme();
    const [note, setNote] = useState(initialNote || {
        title: '',
        cues: '',
        notes: '',
        summary: '',
        keyPoints: '',
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        tags: [],
        attachedImage: null
    });

    const [isEditingTitle, setIsEditingTitle] = useState(false);
    const [saveStatus, setSaveStatus] = useState('');
    const [isSaving, setIsSaving] = useState(false);
    const [newKeyPoint, setNewKeyPoint] = useState('');
    const [newTag, setNewTag] = useState('');
    const [activeTab, setActiveTab] = useState('notes'); // 'notes', 'tips', 'export'
    const [showFormatting, setShowFormatting] = useState(false);
    const [previewMode, setPreviewMode] = useState(false);
    const [richTextMode, setRichTextMode] = useState(true); // Enable rich text by default
    const [chapter, setChapter] = useState(null);
    const [subject, setSubject] = useState('');

    // New state variables for enhanced functionality
    const [attachedImage, setAttachedImage] = useState(null);
    const [imagePreview, setImagePreview] = useState(null);
    const [isGeneratingConcepts, setIsGeneratingConcepts] = useState(false);
    const [isSummarizing, setIsSummarizing] = useState(false);
    const [keyPointsInput, setKeyPointsInput] = useState('');

    // State for all notes for this chapter
    const [allNotes, setAllNotes] = useState([]);
    const [showNotesModal, setShowNotesModal] = useState(false);
    const [showPdfViewer, setShowPdfViewer] = useState(false);
    const [selectedNote, setSelectedNote] = useState(null);
    const [isCreatingNewNote, setIsCreatingNewNote] = useState(true);

    // State for custom notification popup
    const [notification, setNotification] = useState({
        show: false,
        message: '',
        type: 'success' // 'success' or 'error'
    });

    // Ref for the container to detect outside clicks
    const containerRef = useRef(null);
    const titleInputRef = useRef(null);
    const notesTextareaRef = useRef(null);
    const cuesTextareaRef = useRef(null);
    const summaryTextareaRef = useRef(null);

    // Fetch chapter and subject info when component mounts
    useEffect(() => {
        if (chapterId) {
            fetchChapterInfo(chapterId);
        }
    }, [chapterId]);

    // Initialize image preview and key points input when note changes
    useEffect(() => {
        if (note.attachedImage) {
            setImagePreview(note.attachedImage);
        }
        if (note.keyPoints) {
            setKeyPointsInput(note.keyPoints);
        }
    }, [note.id]);

    // Handle outside clicks to close the component
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                if (onClose) {
                    // Ask to save if there are unsaved changes
                    if (note.cues || note.notes || note.summary || note.keyPoints.length > 0) {
                        const confirmClose = window.confirm('Do you want to save your notes before closing?');
                        if (confirmClose) {
                            handleSave().then(() => {
                                onClose();
                            });
                        } else {
                            onClose();
                        }
                    } else {
                        onClose();
                    }
                }
            }
        };

        // Add event listener
        document.addEventListener('mousedown', handleClickOutside);

        // Clean up
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose, note]);

    // Fetch chapter info
    // Helper function to show notifications
    const showNotification = (message, type = 'success') => {
        setNotification({
            show: true,
            message,
            type
        });

        // Auto-hide notification after 3 seconds
        setTimeout(() => {
            setNotification(prev => ({
                ...prev,
                show: false
            }));
        }, 3000);
    };

    const fetchChapterInfo = async (id) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            console.log('Fetching chapter info for ID:', id);
            const response = await axios.get(`/api/chapters/${id}`, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
                console.log('Chapter info:', response.data.chapter);
                setChapter(response.data.chapter);

                // Also fetch subject name
                if (response.data.chapter.subject_id) {
                    fetchSubjectName(response.data.chapter.subject_id);
                }

                // Fetch notes for this chapter to populate allNotes (but don't load into current note)
                fetchNotesForList(id);
            }
        } catch (error) {
            console.error('Error fetching chapter info:', error);
            showNotification('Failed to load chapter information', 'error');
        }
    };

    // Fetch subject name
    const fetchSubjectName = async (subjectId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            const response = await axios.get(`/api/subjects/${subjectId}`, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
                setSubject(response.data.subject.name);
            }
        } catch (error) {
            console.error('Error fetching subject name:', error);
        }
    };

    // Fetch notes for the list only (doesn't load into current note)
    const fetchNotesForList = async (chapterId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            console.log('Fetching notes list for chapter ID:', chapterId);
            const response = await axios.get(`/api/chapters/${chapterId}/cornell-notes`, {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('Notes response:', response.data);

            if (response.data.success) {
                // Helper function to safely parse JSON or return string
                const safeJsonParse = (jsonString, fallback = []) => {
                    if (!jsonString) return fallback;

                    // If it's already a string (for comma-separated key points), return as is
                    if (typeof jsonString === 'string' && !jsonString.startsWith('[') && !jsonString.startsWith('{')) {
                        return jsonString;
                    }

                    try {
                        const parsed = JSON.parse(jsonString);
                        return Array.isArray(parsed) ? parsed : fallback;
                    } catch (error) {
                        console.warn('Failed to parse JSON:', jsonString, error);
                        // Try to extract data from malformed JSON if it looks like an array
                        if (typeof jsonString === 'string' && jsonString.includes(',')) {
                            try {
                                // Split by comma and clean up each item
                                const items = jsonString.split(',').map(item =>
                                    item.trim().replace(/^["'\[\]]+|["'\[\]]+$/g, '')
                                ).filter(item => item.length > 0);
                                return items;
                            } catch (e) {
                                console.warn('Failed to extract data from malformed JSON:', e);
                                return fallback;
                            }
                        }
                        return fallback;
                    }
                };

                // Check if notes is an array (multiple notes) or a single object
                if (Array.isArray(response.data.notes)) {
                    setAllNotes(response.data.notes.map(note => ({
                        id: note.id,
                        title: note.title || '',
                        cues: note.cues || '',
                        notes: note.notes || '',
                        summary: note.summary || '',
                        keyPoints: safeJsonParse(note.key_points, ''),
                        tags: safeJsonParse(note.tags, []),
                        createdAt: note.created_at,
                        lastUpdated: note.last_updated,
                        attachedImage: note.attached_image || null
                    })));
                } else if (response.data.notes) {
                    // Handle single note response (backward compatibility)
                    const fetchedNote = response.data.notes;
                    const formattedNote = {
                        id: fetchedNote.id,
                        title: fetchedNote.title || '',
                        cues: fetchedNote.cues || '',
                        notes: fetchedNote.notes || '',
                        summary: fetchedNote.summary || '',
                        keyPoints: safeJsonParse(fetchedNote.key_points, ''),
                        tags: safeJsonParse(fetchedNote.tags, []),
                        createdAt: fetchedNote.created_at,
                        lastUpdated: fetchedNote.last_updated,
                        attachedImage: fetchedNote.attached_image || null
                    };

                    setAllNotes([formattedNote]);
                } else {
                    // No notes found
                    setAllNotes([]);
                }
            }
        } catch (error) {
            console.error('Error fetching Cornell notes list:', error);
            showNotification('Failed to load notes list', 'error');
        }
    };

    // Load existing notes for this chapter (and set current note)
    const fetchNotes = async (chapterId) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            console.log('Fetching notes for chapter ID:', chapterId);
            const response = await axios.get(`/api/chapters/${chapterId}/cornell-notes`, {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('Notes response:', response.data);

            if (response.data.success) {
                // Helper function to safely parse JSON
                const safeJsonParse = (jsonString, fallback = []) => {
                    if (!jsonString) return fallback;
                    try {
                        const parsed = JSON.parse(jsonString);
                        return Array.isArray(parsed) ? parsed : fallback;
                    } catch (error) {
                        console.warn('Failed to parse JSON:', jsonString, error);
                        // Try to extract data from malformed JSON if it looks like an array
                        if (typeof jsonString === 'string' && jsonString.includes(',')) {
                            try {
                                // Split by comma and clean up each item
                                const items = jsonString.split(',').map(item =>
                                    item.trim().replace(/^["'\[\]]+|["'\[\]]+$/g, '')
                                ).filter(item => item.length > 0);
                                return items;
                            } catch (e) {
                                console.warn('Failed to extract data from malformed JSON:', e);
                                return fallback;
                            }
                        }
                        return fallback;
                    }
                };

                // Check if notes is an array (multiple notes) or a single object
                if (Array.isArray(response.data.notes)) {
                    setAllNotes(response.data.notes.map(note => ({
                        id: note.id,
                        title: note.title || '',
                        cues: note.cues || '',
                        notes: note.notes || '',
                        summary: note.summary || '',
                        keyPoints: safeJsonParse(note.key_points, []),
                        tags: safeJsonParse(note.tags, []),
                        createdAt: note.created_at,
                        lastUpdated: note.last_updated
                    })));

                    // If there are notes, set the most recent one as the current note
                    if (response.data.notes.length > 0) {
                        const mostRecentNote = response.data.notes.sort((a, b) =>
                            new Date(b.last_updated) - new Date(a.last_updated)
                        )[0];

                        setNote({
                            id: mostRecentNote.id,
                            title: mostRecentNote.title || '',
                            cues: mostRecentNote.cues || '',
                            notes: mostRecentNote.notes || '',
                            summary: mostRecentNote.summary || '',
                            keyPoints: safeJsonParse(mostRecentNote.key_points, []),
                            tags: safeJsonParse(mostRecentNote.tags, []),
                            createdAt: mostRecentNote.created_at,
                            lastUpdated: mostRecentNote.last_updated
                        });
                        setIsCreatingNewNote(false);
                    } else {
                        setIsCreatingNewNote(true);
                    }
                } else if (response.data.notes) {
                    // Handle single note response (backward compatibility)
                    const fetchedNote = response.data.notes;
                    const formattedNote = {
                        id: fetchedNote.id,
                        title: fetchedNote.title || '',
                        cues: fetchedNote.cues || '',
                        notes: fetchedNote.notes || '',
                        summary: fetchedNote.summary || '',
                        keyPoints: safeJsonParse(fetchedNote.key_points, []),
                        tags: safeJsonParse(fetchedNote.tags, []),
                        createdAt: fetchedNote.created_at,
                        lastUpdated: fetchedNote.last_updated
                    };

                    setNote(formattedNote);
                    setAllNotes([formattedNote]);
                    setIsCreatingNewNote(false);
                } else {
                    // No notes found
                    setAllNotes([]);
                    setIsCreatingNewNote(true);
                }
            }
        } catch (error) {
            console.error('Error fetching Cornell notes:', error);
            showNotification('Failed to load notes', 'error');
        }
    };

    useEffect(() => {
        if (isEditingTitle && titleInputRef.current) {
            titleInputRef.current.focus();
        }
    }, [isEditingTitle]);

    const handleChange = (field, value) => {
        setNote(prev => ({
            ...prev,
            [field]: value,
            lastUpdated: new Date().toISOString()
        }));
    };

    const handleSave = async (forceNewNote = false) => {
        // Check if chapter is available
        if (!chapter || !chapter.id) {
            showNotification('No chapter selected. Please select a chapter first.', 'error');
            setSaveStatus('Error: No chapter selected');
            return;
        }

        setIsSaving(true);
        setSaveStatus('Saving...');

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                showNotification('Not authenticated. Please log in again.', 'error');
                setSaveStatus('Error: Not authenticated');
                setIsSaving(false);
                return;
            }

            console.log('Saving Cornell notes for chapter:', chapter.id);
            console.log('Note data:', {
                title: note.title,
                cues: note.cues,
                notes: note.notes,
                summary: note.summary,
                key_points: note.keyPoints,
                tags: note.tags
            });

            // Determine if we're creating a new note or updating an existing one
            const isNewNote = isCreatingNewNote || forceNewNote || !note.id;
            const endpoint = isNewNote
                ? `/api/chapters/${chapter.id}/cornell-notes/new`
                : `/api/chapters/${chapter.id}/cornell-notes/${note.id}`;

            const response = await axios.post(
                endpoint,
                {
                    title: note.title,
                    cues: note.cues,
                    notes: note.notes,
                    summary: note.summary,
                    key_points: note.keyPoints,
                    tags: note.tags,
                    attached_image: note.attachedImage
                },
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            console.log('Save response:', response.data);

            if (response.data.success) {
                // If we created a new note, update the note ID and add it to allNotes
                if (isNewNote && response.data.note_id) {
                    const newNoteWithId = {
                        ...note,
                        id: response.data.note_id,
                        createdAt: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    };
                    setNote(newNoteWithId);
                    setAllNotes(prev => [...prev, newNoteWithId]);
                    setIsCreatingNewNote(false);
                } else {
                    // Update the note in allNotes
                    const updatedNote = {
                        ...note,
                        lastUpdated: new Date().toISOString()
                    };
                    setNote(updatedNote);
                    setAllNotes(prev =>
                        prev.map(n => n.id === note.id ? updatedNote : n)
                    );
                }

                showNotification('Notes saved successfully!', 'success');
                setSaveStatus('Saved successfully');

                // Refresh notes list from server to ensure we have the latest data
                fetchNotesForList(chapter.id);

                if (onSave) {
                    onSave(note);
                }
            } else {
                const errorMsg = response.data.message || 'Failed to save notes';
                showNotification(errorMsg, 'error');
                setSaveStatus('Error saving notes');
            }
        } catch (error) {
            console.error('Error saving Cornell notes:', error);
            const errorMessage = error.response?.data?.message || 'Failed to save notes';
            showNotification(errorMessage, 'error');
            setSaveStatus('Error saving notes');
        } finally {
            setIsSaving(false);
        }
    };

    const formatDate = (dateString) => {
        const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    // Function to open the notes selection modal
    const openNotesModal = () => {
        // Refresh the notes list when opening the modal
        if (chapter && chapter.id) {
            fetchNotesForList(chapter.id);
        }
        setShowNotesModal(true);
    };

    // Function to close the notes selection modal
    const closeNotesModal = () => {
        setShowNotesModal(false);
    };

    // Function to open the PDF viewer
    const openPdfViewer = (selectedNote = null) => {
        if (selectedNote) {
            setSelectedNote(selectedNote);
        } else {
            setSelectedNote(note);
        }
        setShowPdfViewer(true);
        setShowNotesModal(false);
    };

    // Function to close the PDF viewer
    const closePdfViewer = () => {
        setShowPdfViewer(false);
    };

    // Function to create a new note
    const createNewNote = () => {
        setNote({
            title: '',
            cues: '',
            notes: '',
            summary: '',
            keyPoints: [],
            createdAt: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            tags: []
        });
        setIsCreatingNewNote(true);
        setShowNotesModal(false);
    };

    // Function to select an existing note
    const selectNote = (selectedNote) => {
        setNote(selectedNote);
        setIsCreatingNewNote(false);
        setShowNotesModal(false);
    };

    // Function to delete a note
    const deleteNote = async (noteToDelete) => {
        if (!noteToDelete || !noteToDelete.id) {
            showNotification('Invalid note selected for deletion', 'error');
            return;
        }

        // Show confirmation dialog
        const confirmDelete = window.confirm(
            `Are you sure you want to delete the note "${noteToDelete.title || 'Untitled Note'}"? This action cannot be undone.`
        );

        if (!confirmDelete) return;

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                showNotification('Not authenticated. Please log in again.', 'error');
                return;
            }

            console.log('Deleting Cornell note:', noteToDelete.id);

            const response = await axios.delete(
                `/api/chapters/${chapter.id}/cornell-notes/${noteToDelete.id}`,
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            if (response.data.success) {
                showNotification('Note deleted successfully!', 'success');

                // Remove the note from allNotes
                const updatedNotes = allNotes.filter(n => n.id !== noteToDelete.id);
                setAllNotes(updatedNotes);

                // If the deleted note was the current note, switch to another note or create new
                if (note.id === noteToDelete.id) {
                    if (updatedNotes.length > 0) {
                        // Switch to the most recent remaining note
                        const mostRecentNote = updatedNotes.sort((a, b) =>
                            new Date(b.lastUpdated) - new Date(a.lastUpdated)
                        )[0];
                        setNote(mostRecentNote);
                        setIsCreatingNewNote(false);
                    } else {
                        // No notes left, create a new one
                        createNewNote();
                    }
                }

                // Close PDF viewer if it's showing the deleted note
                if (selectedNote && selectedNote.id === noteToDelete.id) {
                    setShowPdfViewer(false);
                }

            } else {
                const errorMsg = response.data.message || 'Failed to delete note';
                showNotification(errorMsg, 'error');
            }
        } catch (error) {
            console.error('Error deleting Cornell note:', error);
            const errorMessage = error.response?.data?.message || 'Failed to delete note';
            showNotification(errorMessage, 'error');
        }
    };

    const addKeyPoint = () => {
        if (newKeyPoint.trim()) {
            setNote(prev => ({
                ...prev,
                keyPoints: [...prev.keyPoints, newKeyPoint.trim()],
                lastUpdated: new Date().toISOString()
            }));
            setNewKeyPoint('');
        }
    };

    const removeKeyPoint = (index) => {
        setNote(prev => ({
            ...prev,
            keyPoints: prev.keyPoints.filter((_, i) => i !== index),
            lastUpdated: new Date().toISOString()
        }));
    };

    const addTag = () => {
        if (newTag.trim() && !note.tags.includes(newTag.trim())) {
            setNote(prev => ({
                ...prev,
                tags: [...prev.tags, newTag.trim()],
                lastUpdated: new Date().toISOString()
            }));
            setNewTag('');
        }
    };

    const removeTag = (tag) => {
        setNote(prev => ({
            ...prev,
            tags: prev.tags.filter(t => t !== tag),
            lastUpdated: new Date().toISOString()
        }));
    };

    // Image handling functions
    const handleImageUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Check file type
            if (!file.type.startsWith('image/')) {
                showNotification('Please select a valid image file', 'error');
                return;
            }

            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('Image size should be less than 5MB', 'error');
                return;
            }

            setAttachedImage(file);

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target.result);
                setNote(prev => ({
                    ...prev,
                    attachedImage: e.target.result,
                    lastUpdated: new Date().toISOString()
                }));
            };
            reader.readAsDataURL(file);
        }
    };

    const removeImage = () => {
        setAttachedImage(null);
        setImagePreview(null);
        setNote(prev => ({
            ...prev,
            attachedImage: null,
            lastUpdated: new Date().toISOString()
        }));
    };

    // AI integration functions
    const generateKeyConceptsFromFile = async () => {
        if (!chapter || !chapter.id) {
            showNotification('No chapter selected. Please select a chapter first.', 'error');
            return;
        }

        setIsGeneratingConcepts(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                showNotification('Not authenticated. Please log in again.', 'error');
                return;
            }

            const response = await axios.post('http://localhost:5000/api/ai/generate-concepts-from-file', {
                chapter_id: chapter.id,
                subject: subject,
                chapter_title: chapter.title || ''
            }, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
                const generatedConcepts = response.data.concepts;
                setNote(prev => ({
                    ...prev,
                    cues: generatedConcepts,
                    lastUpdated: new Date().toISOString()
                }));
                showNotification('Key concepts generated from file successfully!', 'success');
            } else {
                showNotification('Failed to generate key concepts from file', 'error');
            }
        } catch (error) {
            console.error('Error generating concepts from file:', error);
            showNotification('Error generating key concepts from file. Please try again.', 'error');
        } finally {
            setIsGeneratingConcepts(false);
        }
    };

    const generateKeyConceptsFromNotes = async () => {
        if (!note.notes.trim()) {
            showNotification('Please add some notes first before generating key concepts', 'error');
            return;
        }

        setIsGeneratingConcepts(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                showNotification('Not authenticated. Please log in again.', 'error');
                return;
            }

            const response = await axios.post('http://localhost:5000/api/ai/generate-concepts-from-notes', {
                notes: note.notes,
                subject: subject,
                chapter_title: chapter?.title || ''
            }, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
                const generatedConcepts = response.data.concepts;
                setNote(prev => ({
                    ...prev,
                    cues: generatedConcepts,
                    lastUpdated: new Date().toISOString()
                }));
                showNotification('Key concepts generated from notes successfully!', 'success');
            } else {
                showNotification('Failed to generate key concepts from notes', 'error');
            }
        } catch (error) {
            console.error('Error generating concepts from notes:', error);
            showNotification('Error generating key concepts from notes. Please try again.', 'error');
        } finally {
            setIsGeneratingConcepts(false);
        }
    };

    const summarizeNotes = async () => {
        if (!note.notes.trim()) {
            showNotification('Please add some notes first before summarizing', 'error');
            return;
        }

        setIsSummarizing(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                showNotification('Not authenticated. Please log in again.', 'error');
                return;
            }

            const response = await axios.post('http://localhost:5000/api/ai/summarize-and-extract', {
                notes: note.notes,
                cues: note.cues,
                subject: subject,
                chapter_title: chapter?.title || ''
            }, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
                const summary = response.data.summary;
                const keyPoints = response.data.key_points || '';
                setNote(prev => ({
                    ...prev,
                    summary: summary,
                    keyPoints: keyPoints,
                    lastUpdated: new Date().toISOString()
                }));
                // Update the key points input field as well
                setKeyPointsInput(keyPoints);
                showNotification('Notes summarized and key points extracted successfully!', 'success');
            } else {
                showNotification('Failed to summarize notes', 'error');
            }
        } catch (error) {
            console.error('Error summarizing notes:', error);
            showNotification('Error summarizing notes. Please try again.', 'error');
        } finally {
            setIsSummarizing(false);
        }
    };

    // Handle key points as comma-separated input
    const handleKeyPointsChange = (value) => {
        setKeyPointsInput(value);
        setNote(prev => ({
            ...prev,
            keyPoints: value,
            lastUpdated: new Date().toISOString()
        }));
    };

    const insertTextAtCursor = (textArea, text) => {
        if (!textArea.current) return;

        const textarea = textArea.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const textBefore = textarea.value.substring(0, start);
        const textAfter = textarea.value.substring(end, textarea.value.length);

        const newText = textBefore + text + textAfter;
        const newPosition = start + text.length;

        // Update the textarea value
        if (textarea === notesTextareaRef.current) {
            handleChange('notes', newText);
        } else if (textarea === cuesTextareaRef.current) {
            handleChange('cues', newText);
        } else if (textarea === summaryTextareaRef.current) {
            handleChange('summary', newText);
        }

        // Set cursor position after the inserted text
        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(newPosition, newPosition);
        }, 0);
    };

    const applyFormatting = (type) => {
        let activeTextarea;
        let fieldName;

        // Determine which textarea is active
        if (document.activeElement === notesTextareaRef.current) {
            activeTextarea = notesTextareaRef;
            fieldName = 'notes';
        } else if (document.activeElement === cuesTextareaRef.current) {
            activeTextarea = cuesTextareaRef;
            fieldName = 'cues';
        } else if (document.activeElement === summaryTextareaRef.current) {
            activeTextarea = summaryTextareaRef;
            fieldName = 'summary';
        } else {
            // Default to notes if no textarea is focused
            activeTextarea = notesTextareaRef;
            fieldName = 'notes';
        }

        const textarea = activeTextarea.current;
        if (!textarea) return;

        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        const textBefore = textarea.value.substring(0, start);
        const textAfter = textarea.value.substring(end);

        let newText = '';
        let newCursorPos = start;

        switch (type) {
            case 'bold':
                if (selectedText) {
                    newText = textBefore + `**${selectedText}**` + textAfter;
                    newCursorPos = start + selectedText.length + 4;
                } else {
                    newText = textBefore + '**Bold Text**' + textAfter;
                    newCursorPos = start + 13;
                }
                break;
            case 'italic':
                if (selectedText) {
                    newText = textBefore + `*${selectedText}*` + textAfter;
                    newCursorPos = start + selectedText.length + 2;
                } else {
                    newText = textBefore + '*Italic Text*' + textAfter;
                    newCursorPos = start + 13;
                }
                break;
            case 'underline':
                if (selectedText) {
                    newText = textBefore + `<u>${selectedText}</u>` + textAfter;
                    newCursorPos = start + selectedText.length + 7;
                } else {
                    newText = textBefore + '<u>Underlined Text</u>' + textAfter;
                    newCursorPos = start + 21;
                }
                break;
            case 'strikethrough':
                if (selectedText) {
                    newText = textBefore + `~~${selectedText}~~` + textAfter;
                    newCursorPos = start + selectedText.length + 4;
                } else {
                    newText = textBefore + '~~Strikethrough Text~~' + textAfter;
                    newCursorPos = start + 22;
                }
                break;
            case 'code':
                if (selectedText) {
                    newText = textBefore + `\`${selectedText}\`` + textAfter;
                    newCursorPos = start + selectedText.length + 2;
                } else {
                    newText = textBefore + '`Code Text`' + textAfter;
                    newCursorPos = start + 11;
                }
                break;
            case 'codeblock':
                if (selectedText) {
                    newText = textBefore + `\n\`\`\`\n${selectedText}\n\`\`\`\n` + textAfter;
                    newCursorPos = start + selectedText.length + 10;
                } else {
                    newText = textBefore + '\n```\nCode Block\n```\n' + textAfter;
                    newCursorPos = start + 18;
                }
                break;
            case 'header1':
                newText = textBefore + (start === 0 || textBefore.endsWith('\n') ? '' : '\n') + '# Header 1\n' + textAfter;
                newCursorPos = start + (start === 0 || textBefore.endsWith('\n') ? 10 : 11);
                break;
            case 'header2':
                newText = textBefore + (start === 0 || textBefore.endsWith('\n') ? '' : '\n') + '## Header 2\n' + textAfter;
                newCursorPos = start + (start === 0 || textBefore.endsWith('\n') ? 11 : 12);
                break;
            case 'header3':
                newText = textBefore + (start === 0 || textBefore.endsWith('\n') ? '' : '\n') + '### Header 3\n' + textAfter;
                newCursorPos = start + (start === 0 || textBefore.endsWith('\n') ? 12 : 13);
                break;
            case 'bullet':
                newText = textBefore + (start === 0 || textBefore.endsWith('\n') ? '' : '\n') + '• ' + textAfter;
                newCursorPos = start + (start === 0 || textBefore.endsWith('\n') ? 2 : 3);
                break;
            case 'number':
                newText = textBefore + (start === 0 || textBefore.endsWith('\n') ? '' : '\n') + '1. ' + textAfter;
                newCursorPos = start + (start === 0 || textBefore.endsWith('\n') ? 3 : 4);
                break;
            case 'quote':
                if (selectedText) {
                    const quotedLines = selectedText.split('\n').map(line => `> ${line}`).join('\n');
                    newText = textBefore + quotedLines + textAfter;
                    newCursorPos = start + quotedLines.length;
                } else {
                    newText = textBefore + (start === 0 || textBefore.endsWith('\n') ? '' : '\n') + '> Quote text\n' + textAfter;
                    newCursorPos = start + (start === 0 || textBefore.endsWith('\n') ? 13 : 14);
                }
                break;
            case 'link':
                if (selectedText) {
                    newText = textBefore + `[${selectedText}](URL)` + textAfter;
                    newCursorPos = start + selectedText.length + 3;
                } else {
                    newText = textBefore + '[Link Text](URL)' + textAfter;
                    newCursorPos = start + 16;
                }
                break;
            default:
                return;
        }

        // Update the field value
        handleChange(fieldName, newText);

        // Set cursor position after the formatting
        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(newCursorPos, newCursorPos);
        }, 0);
    };

    // Markdown renderer function
    const renderMarkdown = (text) => {
        if (!text) return '';

        let html = text
            // Headers
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')

            // Bold
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

            // Italic
            .replace(/\*(.*?)\*/g, '<em>$1</em>')

            // Underline
            .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')

            // Strikethrough
            .replace(/~~(.*?)~~/g, '<del>$1</del>')

            // Inline code
            .replace(/`([^`]+)`/g, '<code>$1</code>')

            // Code blocks
            .replace(/```\n([\s\S]*?)\n```/g, '<pre><code>$1</code></pre>')

            // Links
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

            // Quotes
            .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')

            // Bullet lists
            .replace(/^• (.*)$/gim, '<li>$1</li>')

            // Numbered lists
            .replace(/^\d+\. (.*)$/gim, '<li>$1</li>')

            // Line breaks
            .replace(/\n/g, '<br>');

        // Wrap consecutive <li> elements in <ul> or <ol>
        html = html.replace(/(<li>.*?<\/li>)(?:\s*<br>\s*<li>.*?<\/li>)*/g, (match) => {
            const items = match.split('<br>').filter(item => item.trim());
            if (items.length > 0) {
                return '<ul>' + items.join('') + '</ul>';
            }
            return match;
        });

        return html;
    };

    // Component for rendering formatted text
    const FormattedTextDisplay = ({ text, className }) => {
        return (
            <div
                className={`formatted-text-display ${className}`}
                dangerouslySetInnerHTML={{ __html: renderMarkdown(text) }}
            />
        );
    };

    // Rich Text Editor Component
    const RichTextEditor = ({ value, onChange, placeholder, className, fieldRef }) => {
        const editorRef = useRef(null);
        const [isComposing, setIsComposing] = useState(false);

        // Convert markdown to HTML for display
        const markdownToHtml = (text) => {
            if (!text) return '';

            return text
                // Bold
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // Italic
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // Underline
                .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
                // Strikethrough
                .replace(/~~(.*?)~~/g, '<del>$1</del>')
                // Inline code
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                // Headers
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                // Bullet points - convert * to actual bullets
                .replace(/^\* (.*)$/gim, '• $1')
                // Numbered lists
                .replace(/^\d+\. (.*)$/gim, (match, content, offset, string) => {
                    const lines = string.substring(0, offset).split('\n');
                    const currentLineIndex = lines.length - 1;
                    const numberedLines = lines.slice(0, currentLineIndex).filter(line => /^\d+\./.test(line));
                    const number = numberedLines.length + 1;
                    return `${number}. ${content}`;
                })
                // Quotes
                .replace(/^> (.*)$/gim, '<blockquote>$1</blockquote>')
                // Line breaks
                .replace(/\n/g, '<br>');
        };

        // Convert HTML back to markdown for storage
        const htmlToMarkdown = (html) => {
            if (!html) return '';

            return html
                // Remove HTML tags but keep content
                .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
                .replace(/<em>(.*?)<\/em>/g, '*$1*')
                .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
                .replace(/<del>(.*?)<\/del>/g, '~~$1~~')
                .replace(/<code>(.*?)<\/code>/g, '`$1`')
                .replace(/<h1>(.*?)<\/h1>/g, '# $1')
                .replace(/<h2>(.*?)<\/h2>/g, '## $1')
                .replace(/<h3>(.*?)<\/h3>/g, '### $1')
                .replace(/<blockquote>(.*?)<\/blockquote>/g, '> $1')
                // Convert bullet points back
                .replace(/^• (.*)$/gim, '* $1')
                // Remove other HTML tags
                .replace(/<[^>]*>/g, '')
                // Convert <br> to newlines
                .replace(/<br\s*\/?>/gi, '\n')
                // Clean up extra whitespace
                .replace(/\n\s*\n/g, '\n\n')
                .trim();
        };

        // Handle content changes
        const handleInput = () => {
            if (isComposing || !editorRef.current) return;

            const htmlContent = editorRef.current.innerHTML;
            const markdownContent = htmlToMarkdown(htmlContent);
            onChange(markdownContent);
        };

        // Update editor content when value changes externally
        useEffect(() => {
            if (editorRef.current && !isComposing) {
                const htmlContent = markdownToHtml(value || '');
                if (editorRef.current.innerHTML !== htmlContent) {
                    const selection = window.getSelection();
                    const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
                    const startOffset = range ? range.startOffset : 0;

                    editorRef.current.innerHTML = htmlContent;

                    // Restore cursor position
                    if (range && editorRef.current.firstChild) {
                        try {
                            const newRange = document.createRange();
                            const textNode = editorRef.current.firstChild;
                            const maxOffset = textNode.textContent ? textNode.textContent.length : 0;
                            newRange.setStart(textNode, Math.min(startOffset, maxOffset));
                            newRange.collapse(true);
                            selection.removeAllRanges();
                            selection.addRange(newRange);
                        } catch (e) {
                            // Ignore cursor positioning errors
                        }
                    }
                }
            }
        }, [value, isComposing]);

        // Handle paste events
        const handlePaste = (e) => {
            e.preventDefault();
            const text = e.clipboardData.getData('text/plain');
            document.execCommand('insertText', false, text);
        };

        // Expose ref to parent
        useEffect(() => {
            if (fieldRef) {
                fieldRef.current = editorRef.current;
            }
        }, [fieldRef]);

        return (
            <div
                ref={editorRef}
                className={`rich-text-editor ${className}`}
                contentEditable
                suppressContentEditableWarning
                onInput={handleInput}
                onPaste={handlePaste}
                onCompositionStart={() => setIsComposing(true)}
                onCompositionEnd={() => {
                    setIsComposing(false);
                    setTimeout(handleInput, 0);
                }}
                data-placeholder={placeholder}
                style={{
                    minHeight: '12rem',
                    padding: '0.875rem',
                    border: '1px solid var(--border-color)',
                    borderRadius: '0.5rem',
                    fontSize: '0.9375rem',
                    lineHeight: '1.6',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif',
                    outline: 'none',
                    whiteSpace: 'pre-wrap',
                    wordWrap: 'break-word'
                }}
            />
        );
    };

    const exportNotes = (format) => {
        // Handle key points as comma-separated string or array
        const keyPointsArray = typeof note.keyPoints === 'string'
            ? note.keyPoints.split(',').map(point => point.trim()).filter(point => point)
            : Array.isArray(note.keyPoints) ? note.keyPoints : [];
        const safeTags = Array.isArray(note.tags) ? note.tags : [];

        const content = `# ${note.title || 'Untitled Notes'}

## Cues & Questions
${note.cues || 'No cues recorded'}

## Notes
${note.notes || 'No notes recorded'}

## Summary
${note.summary || 'No summary recorded'}

## Key Points
${keyPointsArray.length > 0 ? keyPointsArray.map(point => `- ${point}`).join('\n') : 'No key points recorded'}

Tags: ${safeTags.length > 0 ? safeTags.join(', ') : 'No tags'}
Created: ${formatDate(note.createdAt)}
Last Updated: ${formatDate(note.lastUpdated)}`;

        if (format === 'print') {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>${note.title || 'Cornell Notes'}</title>
                    <style>
                        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; line-height: 1.6; padding: 20px; }
                        h1 { color: #007AFF; }
                        h2 { color: #333; border-bottom: 1px solid #eee; padding-bottom: 5px; }
                        pre { white-space: pre-wrap; }
                    </style>
                </head>
                <body>
                    <h1>${note.title || 'Untitled Notes'}</h1>
                    <p><strong>Subject:</strong> ${subject}</p>
                    <p><strong>Created:</strong> ${formatDate(note.createdAt)} | <strong>Last Updated:</strong> ${formatDate(note.lastUpdated)}</p>

                    <h2>Cues & Questions</h2>
                    <pre>${note.cues}</pre>

                    <h2>Notes</h2>
                    <pre>${note.notes}</pre>

                    <h2>Summary</h2>
                    <pre>${note.summary}</pre>

                    <h2>Key Points</h2>
                    <ul>
                        ${keyPointsArray.length > 0 ? keyPointsArray.map(point => `<li>${point}</li>`).join('') : '<li>No key points recorded</li>'}
                    </ul>

                    <p><strong>Tags:</strong> ${safeTags.length > 0 ? safeTags.join(', ') : 'None'}</p>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        } else if (format === 'download') {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${note.title || 'cornell_notes'}.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    };

    return (
        <div className={`cornell-notes-container ${darkMode ? 'theme-dark' : ''}`} ref={containerRef}>
            {/* macOS-style notification popup */}
            {notification.show && (
                <div className={`macos-notification ${notification.type}`}>
                    <div className="notification-icon">
                        {notification.type === 'success' ? (
                            <CheckCircleIcon />
                        ) : (
                            <span className="error-icon">!</span>
                        )}
                    </div>
                    <div className="notification-content">
                        <div className="notification-title">
                            {notification.type === 'success' ? 'Success' : 'Error'}
                        </div>
                        <div className="notification-message">{notification.message}</div>
                    </div>
                </div>
            )}

            {/* Notes Selection Modal */}
            {showNotesModal && (
                <div className="cornell-notes-modal">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>
                                <ListAltIcon className="modal-icon" />
                                Cornell Notes for {chapter?.title || 'this chapter'}
                            </h3>
                            <button className="close-modal-button" onClick={closeNotesModal}>
                                <CloseIcon />
                            </button>
                        </div>
                        <div className="modal-body">
                            <div className="notes-list-header">
                                <h4>Select a note to view or edit</h4>
                                <button className="new-note-button" onClick={createNewNote}>
                                    <AddIcon /> Create New Note
                                </button>
                            </div>

                            {allNotes.length > 0 ? (
                                <div className="notes-list">
                                    {allNotes.map((noteItem) => (
                                        <div
                                            key={noteItem.id}
                                            className={`note-item ${note.id === noteItem.id ? 'active' : ''}`}
                                            onClick={() => selectNote(noteItem)}
                                        >
                                            <div className="note-item-header">
                                                <h4>{noteItem.title || 'Untitled Note'}</h4>
                                                <span className="note-date">
                                                    Last updated: {formatDate(noteItem.lastUpdated)}
                                                </span>
                                            </div>
                                            <div className="note-item-preview">
                                                <p>{noteItem.summary ? (noteItem.summary.substring(0, 100) + (noteItem.summary.length > 100 ? '...' : '')) : 'No summary available'}</p>
                                            </div>
                                            <div className="note-item-actions">
                                                <button
                                                    className="view-note-button"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        openPdfViewer(noteItem);
                                                    }}
                                                >
                                                    <PictureAsPdfIcon /> View as PDF
                                                </button>
                                                <button
                                                    className="edit-note-button"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        selectNote(noteItem);
                                                    }}
                                                >
                                                    <EditIcon /> Edit
                                                </button>
                                                <button
                                                    className="delete-note-button"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        deleteNote(noteItem);
                                                    }}
                                                    title="Delete this note"
                                                >
                                                    <DeleteIcon /> Delete
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="no-notes-message">
                                    <p>No notes found for this chapter. Create your first note!</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* PDF Viewer Modal */}
            {showPdfViewer && selectedNote && (
                <div className="cornell-notes-pdf-viewer">
                    <div className="pdf-viewer-content">
                        <div className="pdf-viewer-header">
                            <h3>
                                <PictureAsPdfIcon className="pdf-icon" />
                                {selectedNote.title || 'Untitled Note'}
                            </h3>
                            <div className="pdf-viewer-actions">
                                <button
                                    className="edit-in-pdf-button"
                                    onClick={() => {
                                        setNote(selectedNote);
                                        setIsCreatingNewNote(false);
                                        closePdfViewer();
                                    }}
                                >
                                    <EditIcon /> Edit
                                </button>
                                <button
                                    className="save-as-new-button"
                                    onClick={() => {
                                        // Create a copy of the note and save it as new
                                        const noteCopy = {
                                            ...selectedNote,
                                            id: null,
                                            title: `${selectedNote.title || 'Note'} (Copy)`,
                                            createdAt: new Date().toISOString(),
                                            lastUpdated: new Date().toISOString()
                                        };
                                        setNote(noteCopy);
                                        setIsCreatingNewNote(true);
                                        handleSave(true);
                                        closePdfViewer();
                                    }}
                                >
                                    <ContentCopyIcon /> Save as New
                                </button>
                                <button
                                    className="print-pdf-button"
                                    onClick={() => exportNotes('print')}
                                >
                                    <PrintIcon /> Print
                                </button>
                                <button
                                    className="delete-pdf-button"
                                    onClick={() => {
                                        deleteNote(selectedNote);
                                    }}
                                    title="Delete this note"
                                >
                                    <DeleteIcon /> Delete
                                </button>
                                <button className="close-pdf-button" onClick={closePdfViewer}>
                                    <CloseIcon />
                                </button>
                            </div>
                        </div>
                        <div className="pdf-viewer-body">
                            <div className="cornell-pdf-layout">
                                <div className="pdf-metadata">
                                    <div className="pdf-subject">{subject}</div>
                                    <div className="pdf-chapter">{chapter?.title || ''}</div>
                                    <div className="pdf-date">Created: {formatDate(selectedNote.createdAt)}</div>
                                    <div className="pdf-date">Last Updated: {formatDate(selectedNote.lastUpdated)}</div>
                                </div>

                                <div className="pdf-main-content">
                                    <div className="pdf-cues-column">
                                        <h4>Cues & Questions</h4>
                                        <div className="pdf-cues-content">
                                            {selectedNote.cues || 'No cues recorded'}
                                        </div>
                                    </div>

                                    <div className="pdf-notes-column">
                                        <h4>Notes</h4>
                                        <div className="pdf-notes-content">
                                            {selectedNote.notes || 'No notes recorded'}
                                        </div>
                                    </div>

                                    <div className="pdf-summary-section">
                                        <h4>Summary</h4>
                                        <div className="pdf-summary-content">
                                            {selectedNote.summary || 'No summary recorded'}
                                        </div>
                                    </div>

                                    {selectedNote.keyPoints && selectedNote.keyPoints.length > 0 && (
                                        <div className="pdf-key-points">
                                            <h4>Key Points</h4>
                                            <ul>
                                                {selectedNote.keyPoints.map((point, index) => (
                                                    <li key={index}>{point}</li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="cornell-notes-header">
                <div className="header-content">
                    <div className="metadata">
                        <span className="subject">
                            <BookmarkIcon className="icon" />
                            {subject}
                        </span>
                        <div className="title-container">
                            {isEditingTitle ? (
                                <input
                                    ref={titleInputRef}
                                    type="text"
                                    className="title-input"
                                    value={note.title}
                                    onChange={(e) => handleChange('title', e.target.value)}
                                    onBlur={() => setIsEditingTitle(false)}
                                    onKeyPress={(e) => e.key === 'Enter' && setIsEditingTitle(false)}
                                    placeholder="Enter note title..."
                                />
                            ) : (
                                <h2
                                    className="notes-title"
                                    onClick={() => setIsEditingTitle(true)}
                                >
                                    {note.title || 'Click to add title'}
                                    <EditIcon className="edit-icon" />
                                </h2>
                            )}
                        </div>
                    </div>

                    <button className="close-button" onClick={onClose} title="Close">
                        <CloseIcon />
                    </button>
                </div>

                {/* Commented out dates
                <span className="dates">
                    <AccessTimeIcon className="icon" />
                    Created: {formatDate(note.createdAt)} | Last updated: {formatDate(note.lastUpdated)}
                </span>
                */}

                {/* Commented out tags
                <div className="tags-container">
                    {note.tags.map(tag => (
                        <span key={tag} className="tag">
                            {tag}
                            <span className="remove-tag" onClick={() => removeTag(tag)}>×</span>
                        </span>
                    ))}
                    <div className="add-tag">
                        <input
                            type="text"
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && addTag()}
                            placeholder="Add tag..."
                            className="tag-input"
                        />
                        <button className="tag-button" onClick={addTag}>+</button>
                    </div>
                </div>
                */}
            </div>

            <div className="cornell-notes-tabs">
                <button
                    className={`tab-button ${activeTab === 'notes' ? 'active' : ''}`}
                    onClick={() => setActiveTab('notes')}
                >
                    <SubjectIcon /> Notes
                </button>
                <button
                    className={`tab-button ${activeTab === 'tips' ? 'active' : ''}`}
                    onClick={() => setActiveTab('tips')}
                >
                    <LightbulbIcon /> Study Tips
                </button>
                <button
                    className={`tab-button ${activeTab === 'export' ? 'active' : ''}`}
                    onClick={() => setActiveTab('export')}
                >
                    <DownloadIcon /> Export
                </button>
            </div>

            {
                activeTab === 'notes' && (
                    <>
                        <div className="formatting-toolbar">
                            <div className="toolbar-left">
                                <button
                                    className="toggle-formatting"
                                    onClick={() => setShowFormatting(!showFormatting)}
                                >
                                    {showFormatting ? 'Hide Formatting' : 'Show Formatting'}
                                </button>

                                <button
                                    className={`rich-text-toggle ${richTextMode ? 'active' : ''}`}
                                    onClick={() => setRichTextMode(!richTextMode)}
                                    title={richTextMode ? 'Switch to Plain Text' : 'Switch to Rich Text'}
                                >
                                    {richTextMode ? 'Rich Text' : 'Plain Text'}
                                </button>

                                <button
                                    className={`preview-toggle ${previewMode ? 'active' : ''}`}
                                    onClick={() => setPreviewMode(!previewMode)}
                                    title={previewMode ? 'Switch to Edit Mode' : 'Switch to Preview Mode'}
                                >
                                    {previewMode ? 'Edit' : 'Preview'}
                                </button>
                            </div>

                            {showFormatting && (
                                <div className="formatting-buttons">
                                    <div className="formatting-group">
                                        <button onClick={() => applyFormatting('bold')} title="Bold">
                                            <FormatBoldIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('italic')} title="Italic">
                                            <FormatItalicIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('underline')} title="Underline">
                                            <FormatUnderlinedIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('strikethrough')} title="Strikethrough">
                                            <StrikethroughSIcon />
                                        </button>
                                    </div>

                                    <div className="formatting-group">
                                        <button onClick={() => applyFormatting('header1')} title="Header 1">
                                            <TitleIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('header2')} title="Header 2">
                                            <span className="header-icon">H2</span>
                                        </button>
                                        <button onClick={() => applyFormatting('header3')} title="Header 3">
                                            <span className="header-icon">H3</span>
                                        </button>
                                    </div>

                                    <div className="formatting-group">
                                        <button onClick={() => applyFormatting('bullet')} title="Bullet List">
                                            <FormatListBulletedIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('number')} title="Numbered List">
                                            <FormatListNumberedIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('quote')} title="Quote">
                                            <FormatQuoteIcon />
                                        </button>
                                    </div>

                                    <div className="formatting-group">
                                        <button onClick={() => applyFormatting('code')} title="Inline Code">
                                            <CodeIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('codeblock')} title="Code Block">
                                            <DataObjectIcon />
                                        </button>
                                        <button onClick={() => applyFormatting('link')} title="Link">
                                            <LinkIcon />
                                        </button>
                                    </div>
                                </div>
                            )}

                            <div className="save-status-container">
                                {saveStatus && <span className="save-status">{saveStatus}</span>}
                            </div>
                        </div>

                        <div className="cornell-notes-grid">
                            {/* Cues Column */}
                            <div className="cues-column">
                                <div className="section-header">
                                    <h3><HelpOutlineIcon className="section-icon" /> Cues & Questions</h3>
                                    <div className="instructions">
                                        <p>Key concepts, questions</p>
                                    </div>
                                    <div className="ai-buttons-group">
                                        <button
                                            className="ai-generate-button ai-generate-from-file"
                                            onClick={generateKeyConceptsFromFile}
                                            disabled={isGeneratingConcepts || !chapter?.id}
                                            title="Generate key concepts and questions from uploaded file"
                                        >
                                            <AutoAwesomeIcon />
                                            {isGeneratingConcepts ? 'Generating...' : 'From File'}
                                        </button>
                                        <button
                                            className="ai-generate-button ai-generate-from-notes"
                                            onClick={generateKeyConceptsFromNotes}
                                            disabled={isGeneratingConcepts || !note.notes.trim()}
                                            title="Generate key concepts and questions from your notes"
                                        >
                                            <SmartToyIcon />
                                            {isGeneratingConcepts ? 'Generating...' : 'From Notes'}
                                        </button>
                                    </div>
                                </div>
                                {previewMode ? (
                                    <FormattedTextDisplay
                                        text={note.cues}
                                        className="cues-preview"
                                    />
                                ) : richTextMode ? (
                                    <RichTextEditor
                                        value={note.cues}
                                        onChange={(value) => handleChange('cues', value)}
                                        placeholder="Record key questions, terms, or cues here..."
                                        className="cues-editor"
                                        fieldRef={cuesTextareaRef}
                                    />
                                ) : (
                                    <textarea
                                        ref={cuesTextareaRef}
                                        className="cues-textarea"
                                        value={note.cues}
                                        onChange={(e) => handleChange('cues', e.target.value)}
                                        placeholder="Record key questions, terms, or cues here..."
                                    />
                                )}
                            </div>

                            {/* Notes Column */}
                            <div className="notes-column">
                                <div className="section-header">
                                    <h3><SubjectIcon className="section-icon" /> Notes</h3>
                                    <div className="instructions">
                                        <p>Main content, ideas, and details from your source material</p>
                                    </div>
                                    <div className="notes-actions">
                                        <input
                                            type="file"
                                            accept="image/*"
                                            onChange={handleImageUpload}
                                            style={{ display: 'none' }}
                                            id="image-upload"
                                        />
                                        <label htmlFor="image-upload" className="image-upload-button">
                                            <ImageIcon />
                                            Attach Image
                                        </label>
                                    </div>
                                </div>

                                {/* Image Preview */}
                                {imagePreview && (
                                    <div className="image-preview-container">
                                        <div className="image-preview">
                                            <img src={imagePreview} alt="Attached" />
                                            <button
                                                className="remove-image-button"
                                                onClick={removeImage}
                                                title="Remove image"
                                            >
                                                <ClearIcon />
                                            </button>
                                        </div>
                                    </div>
                                )}

                                {previewMode ? (
                                    <FormattedTextDisplay
                                        text={note.notes}
                                        className="notes-preview"
                                    />
                                ) : richTextMode ? (
                                    <RichTextEditor
                                        value={note.notes}
                                        onChange={(value) => handleChange('notes', value)}
                                        placeholder="Record lecture/reading notes here in paragraph or bullet form..."
                                        className="notes-editor"
                                        fieldRef={notesTextareaRef}
                                    />
                                ) : (
                                    <textarea
                                        ref={notesTextareaRef}
                                        className="notes-textarea"
                                        value={note.notes}
                                        onChange={(e) => handleChange('notes', e.target.value)}
                                        placeholder="Record lecture/reading notes here in paragraph or bullet form..."
                                    />
                                )}
                            </div>

                            {/* Summary Section */}
                            <div className="summary-section">
                                <div className="section-header">
                                    <h3><SummarizeIcon className="section-icon" /> Summary</h3>
                                    <div className="instructions">
                                        <p>Synthesize the main ideas in your own words</p>
                                    </div>
                                    <button
                                        className="ai-summarize-button"
                                        onClick={summarizeNotes}
                                        disabled={isSummarizing || !note.notes.trim()}
                                        title="Summarize notes and extract key points using AI"
                                    >
                                        <SmartToyIcon />
                                        {isSummarizing ? 'Processing...' : 'AI Summarize & Extract'}
                                    </button>
                                </div>
                                {previewMode ? (
                                    <FormattedTextDisplay
                                        text={note.summary}
                                        className="summary-preview"
                                    />
                                ) : richTextMode ? (
                                    <RichTextEditor
                                        value={note.summary}
                                        onChange={(value) => handleChange('summary', value)}
                                        placeholder="Summarize the key concepts and how they connect..."
                                        className="summary-editor"
                                        fieldRef={summaryTextareaRef}
                                    />
                                ) : (
                                    <textarea
                                        ref={summaryTextareaRef}
                                        className="summary-textarea"
                                        value={note.summary}
                                        onChange={(e) => handleChange('summary', e.target.value)}
                                        placeholder="Summarize the key concepts and how they connect..."
                                    />
                                )}
                            </div>
                        </div>

                        <div className="key-points-section">
                            <div className="section-header">
                                <h3><CheckCircleIcon className="section-icon" /> Key Points</h3>
                                <div className="instructions">
                                    <p>Enter important points separated by commas</p>
                                </div>
                            </div>
                            <div className="key-points-input">
                                <textarea
                                    value={note.keyPoints}
                                    onChange={(e) => handleKeyPointsChange(e.target.value)}
                                    placeholder="Enter key points separated by commas (e.g., Point 1, Point 2, Point 3...)"
                                    className="key-points-textarea"
                                    rows="3"
                                />
                            </div>
                            {note.keyPoints && (
                                <div className="key-points-preview">
                                    <h4>Preview:</h4>
                                    <ul className="key-points-list">
                                        {note.keyPoints.split(',').filter(point => point.trim()).map((point, index) => (
                                            <li key={index} className="key-point-item">
                                                <span>{point.trim()}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    </>
                )
            }

            {
                activeTab === 'tips' && (
                    <div className="study-tips">
                        <h3>Cornell Note-Taking Method Tips</h3>
                        <div className="tips-grid">
                            <div className="tip-card">
                                <h4>During Class</h4>
                                <ul>
                                    <li>Record notes in the main notes column</li>
                                    <li>Use abbreviations and symbols for efficiency</li>
                                    <li>Focus on capturing key concepts, not every word</li>
                                    <li>Leave space for adding cues later</li>
                                </ul>
                            </div>
                            <div className="tip-card">
                                <h4>After Class</h4>
                                <ul>
                                    <li>Review your notes within 24 hours</li>
                                    <li>Add cues and questions in the left column</li>
                                    <li>Write a summary at the bottom</li>
                                    <li>Highlight or mark important information</li>
                                </ul>
                            </div>
                            <div className="tip-card">
                                <h4>Effective Cues</h4>
                                <ul>
                                    <li>Write questions that your notes answer</li>
                                    <li>Include key terms and concepts</li>
                                    <li>Create memory triggers</li>
                                    <li>Use cues for self-testing later</li>
                                </ul>
                            </div>
                            <div className="tip-card">
                                <h4>Study Strategies</h4>
                                <ul>
                                    <li>Cover the notes column and quiz yourself using cues</li>
                                    <li>Verbally summarize each section</li>
                                    <li>Connect concepts across different notes</li>
                                    <li>Review key points regularly</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                )
            }

            {
                activeTab === 'export' && (
                    <div className="export-options">
                        <h3>Export Your Notes</h3>
                        <p>Choose how you want to export your Cornell notes:</p>

                        <div className="export-buttons">
                            <button className="export-button" onClick={() => exportNotes('download')}>
                                <DownloadIcon /> Download as Markdown
                            </button>
                            <button className="export-button" onClick={() => exportNotes('print')}>
                                <PrintIcon /> Print Notes
                            </button>
                            <button className="export-button" onClick={() => {
                                navigator.clipboard.writeText(`${note.title}\n\nCues & Questions:\n${note.cues}\n\nNotes:\n${note.notes}\n\nSummary:\n${note.summary}`);
                                alert('Notes copied to clipboard!');
                            }}>
                                <ContentCopyIcon /> Copy to Clipboard
                            </button>
                        </div>
                    </div>
                )
            }

            <div className="cornell-notes-footer">
                <div className="chapter-info">
                    {chapter && (
                        <div className="chapter-badge">
                            <MenuBookIcon className="icon" />
                            {chapter.chapter_number}: {chapter.title}
                        </div>
                    )}
                </div>
                <div className="notes-actions">
                    <button
                        className="view-notes-button"
                        onClick={openNotesModal}
                        title="View all notes for this chapter"
                    >
                        <VisibilityIcon /> {allNotes.length > 0 ? `View Notes (${allNotes.length})` : 'View Notes'}
                    </button>
                    <button
                        className="pdf-view-button"
                        onClick={() => openPdfViewer()}
                        title="View current note as PDF"
                    >
                        <PictureAsPdfIcon /> PDF View
                    </button>
                    <button
                        className="save-button"
                        onClick={() => handleSave(false)}
                        disabled={isSaving}
                    >
                        <SaveIcon /> {isSaving ? 'Saving...' : 'Save Note'}
                    </button>
                </div>
            </div>
        </div >
    );
};

export default CornellNotes;