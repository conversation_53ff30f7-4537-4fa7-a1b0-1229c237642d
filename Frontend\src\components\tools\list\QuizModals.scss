// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;
$system-gold: #fbb034;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Quiz Modal Overlay
.quiz-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(0.5rem);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

// Main Quiz Creation Modal
.quiz-modal-content {
    background: $window-background;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.2);
    max-width: 35rem;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1001;

    .form-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem;
        border-bottom: 0.0625rem solid $separator-color;
        background: $secondary-background;
        position: relative;
        z-index: 5;

        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;

            .ai-icon {
                width: 2.5rem;
                height: 2.5rem;
                background: linear-gradient(135deg, $system-blue, $system-blue);
                border-radius: 0.75rem;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;

                svg {
                    width: 1.25rem;
                    height: 1.25rem;
                }
            }

            .header-text {
                h2 {
                    margin: 0;
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: $primary-text;
                }

                p {
                    margin: 0.25rem 0 0 0;
                    font-size: 0.875rem;
                    color: $secondary-text;
                }
            }
        }

        .close-form-btn {
            width: 2.5rem;
            height: 2.5rem;
            border: none;
            background: rgba($system-light-gray, 0.8);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: $secondary-text;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            z-index: 15;
            flex-shrink: 0;
            border: 1px solid rgba($border-color, 0.5);
            backdrop-filter: blur(10px);

            &:hover {
                background: rgba($system-red, 0.1);
                color: $system-red;
                transform: scale(1.1);
                border-color: rgba($system-red, 0.3);
                box-shadow: 0 4px 12px rgba($system-red, 0.2);
            }

            &:active {
                transform: scale(0.95);
            }

            svg {
                width: 1.125rem;
                height: 1.125rem;
            }

            .material-icons {
                font-size: 1.125rem;
                line-height: 1;
                pointer-events: none;
            }

            .theme-dark & {
                background: rgba($dark-border-color, 0.8);
                color: $dark-secondary-text;
                border-color: rgba($dark-border-color, 0.5);

                &:hover {
                    background: rgba($system-red, 0.15);
                    color: $system-red;
                    border-color: rgba($system-red, 0.4);
                }
            }
        }
    }

    .ai-generation-form {
        padding: 1.5rem;
        overflow-y: auto;

        .form-group {
            margin-bottom: 1.5rem;

            label {
                display: block;
                font-size: 0.875rem;
                font-weight: 500;
                color: $primary-text;
                margin-bottom: 0.5rem;
            }

            .checkbox-group {
                display: flex;
                gap: 1rem;

                .checkbox-item {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    cursor: pointer;

                    input[type="checkbox"] {
                        width: 1rem;
                        height: 1rem;
                        accent-color: $system-blue;
                    }

                    span {
                        font-size: 0.875rem;
                        color: $primary-text;
                    }
                }
            }

            .form-select {
                width: 100%;
                padding: 0.75rem;
                border: 0.0625rem solid $border-color;
                border-radius: 0.5rem;
                background: $window-background;
                color: $primary-text;
                font-size: 0.875rem;
                transition: border-color 0.2s ease;

                &:focus {
                    outline: none;
                    border-color: $system-blue;
                }

                &:disabled {
                    background: $secondary-background;
                    color: $tertiary-text;
                }
            }

            .loading-text {
                font-size: 0.75rem;
                color: $tertiary-text;
                margin-top: 0.25rem;
            }
        }

        .form-row {
            display: grid;
            grid-template-rows: auto 1fr;
            gap: 1rem;
        }

        .info-section {
            background: $secondary-background;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.5rem;

            .info-item {
                display: flex;
                align-items: flex-start;
                gap: 0.5rem;
                margin-bottom: 0.75rem;

                &:last-child {
                    margin-bottom: 0;
                }

                svg {
                    width: 1rem;
                    height: 1rem;
                    color: $system-blue;
                    margin-top: 0.125rem;
                    flex-shrink: 0;
                }

                span {
                    font-size: 0.75rem;
                    color: $secondary-text;
                    line-height: 1.4;
                }
            }
        }

        .form-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;

            .cancel-button {
                padding: 0.75rem 1.5rem;
                border: 0.0625rem solid $border-color;
                background: $window-background;
                color: $primary-text;
                border-radius: 0.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    background: $secondary-background;
                }
            }

            .generate-button {
                padding: 0.75rem 1.5rem;
                border: none;
                background: $system-blue;
                color: white;
                border-radius: 0.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;

                &:hover:not(:disabled) {
                    background: darken($system-blue, 10%);
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }

                svg {
                    width: 1rem;
                    height: 1rem;
                }

                .loading-spinner {
                    width: 1rem;
                    height: 1rem;
                    border: 0.125rem solid rgba(255, 255, 255, 0.3);
                    border-top: 0.125rem solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
            }
        }
    }
}

// Generating Modal
.quiz-generating-modal {
    background: $window-background;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.2);
    padding: 2rem;
    text-align: center;
    max-width: 20rem;
    width: 100%;

    .generating-content {
        .generating-icon {
            position: relative;
            width: 4rem;
            height: 4rem;
            margin: 0 auto 1.5rem;

            .loading-spinner {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: 0.25rem solid $system-light-gray;
                border-top: 0.25rem solid $system-blue;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            svg {
                width: 2rem;
                height: 2rem;
                color: $system-blue;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }

        h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: $primary-text;
        }

        p {
            margin: 0 0 1.5rem 0;
            font-size: 0.875rem;
            color: $secondary-text;
            line-height: 1.4;
        }

        .progress-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;

            span {
                width: 0.5rem;
                height: 0.5rem;
                background: $system-blue;
                border-radius: 50%;
                animation: pulse 1.5s ease-in-out infinite;

                &:nth-child(2) {
                    animation-delay: 0.3s;
                }

                &:nth-child(3) {
                    animation-delay: 0.6s;
                }
            }
        }
    }
}

// Success Modal
.quiz-success-modal {
    background: $window-background;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.2);
    padding: 2rem;
    text-align: center;
    max-width: 20rem;
    width: 100%;

    .success-content {
        .success-icon {
            width: 4rem;
            height: 4rem;
            background: $system-green;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;

            svg {
                width: 2rem;
                height: 2rem;
            }
        }

        h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: $primary-text;
        }

        p {
            margin: 0;
            font-size: 0.875rem;
            color: $secondary-text;
            line-height: 1.4;
        }
    }
}

// Confirmation Modal
.quiz-confirm-modal {
    background: $window-background;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.2);
    padding: 2rem;
    text-align: center;
    max-width: 22rem;
    width: 100%;

    .confirm-content {
        .confirm-icon {
            width: 4rem;
            height: 4rem;
            background: $system-orange;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;

            svg {
                width: 2rem;
                height: 2rem;
            }

            .material-icons {
                font-size: 2rem;
                line-height: 1;
            }
        }

        h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: $primary-text;
        }

        p {
            margin: 0 0 1.5rem 0;
            font-size: 0.875rem;
            color: $secondary-text;
            line-height: 1.4;
        }

        .confirm-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: center;

            .cancel-button {
                padding: 0.75rem 1.5rem;
                border: 0.0625rem solid $border-color;
                background: $window-background;
                color: $primary-text;
                border-radius: 0.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    background: $secondary-background;
                }
            }

            .confirm-button {
                padding: 0.75rem 1.5rem;
                border: none;
                background: $system-red;
                color: white;
                border-radius: 0.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    background: darken($system-red, 10%);
                }
            }
        }
    }
}

// Dark mode styles
.theme-dark {
    .quiz-modal-content {
        background: $dark-window-background;

        .form-header {
            background: $dark-secondary-background;
            border-bottom-color: $dark-separator-color;

            .header-text {
                h2 {
                    color: $dark-primary-text;
                }

                p {
                    color: $dark-secondary-text;
                }
            }

            .close-form-btn {
                background: $dark-border-color;
                color: $dark-secondary-text;

                &:hover {
                    background: lighten($dark-border-color, 10%);
                    color: $dark-primary-text;
                }
            }
        }

        .ai-generation-form {
            .form-group {
                label {
                    color: $dark-primary-text;
                }

                .checkbox-item span {
                    color: $dark-primary-text;
                }

                .form-select {
                    background: $dark-secondary-background;
                    border-color: $dark-border-color;
                    color: $dark-primary-text;

                    &:disabled {
                        background: $dark-border-color;
                        color: $dark-tertiary-text;
                    }
                }

                .loading-text {
                    color: $dark-tertiary-text;
                }
            }

            .info-section {
                background: $dark-secondary-background;

                .info-item span {
                    color: $dark-secondary-text;
                }
            }

            .form-actions {
                .cancel-button {
                    background: $dark-secondary-background;
                    border-color: $dark-border-color;
                    color: $dark-primary-text;

                    &:hover {
                        background: $dark-border-color;
                    }
                }
            }
        }
    }

    .quiz-generating-modal,
    .quiz-success-modal,
    .quiz-confirm-modal {
        background: $dark-window-background;

        .generating-content,
        .success-content,
        .confirm-content {
            h3 {
                color: $dark-primary-text;
            }

            p {
                color: $dark-secondary-text;
            }
        }

        .generating-content .loading-spinner {
            border-color: $dark-border-color;
            border-top-color: $system-blue;
        }

        .confirm-content .confirm-actions {
            .cancel-button {
                background: $dark-secondary-background;
                border-color: $dark-border-color;
                color: $dark-primary-text;

                &:hover {
                    background: $dark-border-color;
                }
            }
        }
    }
}

// Animations
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 1;
    }
}