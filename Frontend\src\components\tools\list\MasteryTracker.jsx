import { useState } from 'react';

const MasteryTracker = ({ tool, subject }) => {
    const [skills, setSkills] = useState([]);
    const [newSkill, setNewSkill] = useState('');
    const [masteryLevel, setMasteryLevel] = useState(50);

    const addSkill = () => {
        if (newSkill.trim()) {
            setSkills(prev => [
                ...prev,
                {
                    id: Date.now(),
                    name: newSkill,
                    mastery: masteryLevel,
                    notes: ''
                }
            ]);
            setNewSkill('');
        }
    };

    const updateMastery = (id, value) => {
        setSkills(prev => prev.map(skill =>
            skill.id === id ? { ...skill, mastery: value } : skill
        ));
    };

    const updateNotes = (id, notes) => {
        setSkills(prev => prev.map(skill =>
            skill.id === id ? { ...skill, notes } : skill
        ));
    };

    const removeSkill = (id) => {
        setSkills(prev => prev.filter(skill => skill.id !== id));
    };

    return (
        <div className="mastery-tracker">
            <h2>Mastery Tracker: {subject}</h2>
            <div className="add-skill">
                <input
                    type="text"
                    value={newSkill}
                    onChange={(e) => setNewSkill(e.target.value)}
                    placeholder="Enter skill/concept"
                />
                <div className="slider-container">
                    <label>Initial Mastery: {masteryLevel}%</label>
                    <input
                        type="range"
                        min="0"
                        max="100"
                        value={masteryLevel}
                        onChange={(e) => setMasteryLevel(parseInt(e.target.value))}
                    />
                </div>
                <button onClick={addSkill}>Add Skill</button>
            </div>
            <div className="skills-list">
                {skills.map(skill => (
                    <div key={skill.id} className="skill-item">
                        <div className="skill-header">
                            <h3>{skill.name}</h3>
                            <button onClick={() => removeSkill(skill.id)}>×</button>
                        </div>
                        <div className="mastery-display">
                            <div
                                className="mastery-bar"
                                style={{ width: `${skill.mastery}%` }}
                            ></div>
                            <span>{skill.mastery}%</span>
                        </div>
                        <div className="slider-container">
                            <input
                                type="range"
                                min="0"
                                max="100"
                                value={skill.mastery}
                                onChange={(e) => updateMastery(skill.id, parseInt(e.target.value))}
                            />
                        </div>
                        <textarea
                            value={skill.notes}
                            onChange={(e) => updateNotes(skill.id, e.target.value)}
                            placeholder="Notes about this skill..."
                        />
                    </div>
                ))}
            </div>
        </div>
    );
};

export default MasteryTracker;