import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';
import axios from 'axios';
import '../../styles/leitner.scss';

// Material Icons
import SchoolIcon from '@mui/icons-material/School';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import BarChartIcon from '@mui/icons-material/BarChart';
import InfoIcon from '@mui/icons-material/Info';
import CloseIcon from '@mui/icons-material/Close';
import FilterListIcon from '@mui/icons-material/FilterList';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import Looks4Icon from '@mui/icons-material/Looks4';
import Looks5Icon from '@mui/icons-material/Looks5';
import SettingsIcon from '@mui/icons-material/Settings';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ConfettiExplosion from 'react-confetti-explosion';

// Components
import LeitnerBox from './LeitnerBox';
import LeitnerStudySession from './LeitnerStudySession';
import LeitnerStats from './LeitnerStats';
import LeitnerInfo from './LeitnerInfo';

const LeitnerSystem = ({ chapterId, subjectId, onBack }) => {
  // Use ThemeContext for dark mode
  const { darkMode } = useTheme();

  // State variables
  const [cards, setCards] = useState([]);
  const [boxes, setBoxes] = useState({
    1: [],
    2: [],
    3: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('boxes'); // 'boxes', 'study', 'stats', 'info', 'preview'
  const [selectedBox, setSelectedBox] = useState(null);
  const [isExploding, setIsExploding] = useState(false);
  const [streak, setStreak] = useState(0);
  const [previewCards, setPreviewCards] = useState([]);

  // Define loadCards function outside useEffect so it can be called from other functions
  const loadCards = async () => {
    if (!chapterId) {
      setError('No chapter ID provided');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Fetch flashcards for this chapter
      const response = await axios.get(`http://localhost:5001/api/chapters/${chapterId}/flashcards`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success) {
        const fetchedCards = response.data.flashcards || [];
        setCards(fetchedCards);

        // Distribute cards into Leitner boxes based on box_level
        const newBoxes = {
          1: [],
          2: [],
          3: []
        };

        // Track the highest streak among all cards
        let highestStreak = 0;

        fetchedCards.forEach(card => {
          // Convert old 5-box system to new 3-box system
          let newBoxLevel = 1;
          const oldBoxLevel = card.box_level || 1;

          if (oldBoxLevel <= 1) {
            newBoxLevel = 1; // New & difficult cards
          } else if (oldBoxLevel <= 3) {
            newBoxLevel = 2; // Learning cards
          } else {
            newBoxLevel = 3; // Mastered cards
          }

          // Update the card's box_level
          newBoxes[newBoxLevel].push({ ...card, box_level: newBoxLevel });

          // Update highest streak
          if (card.leitner_streak && card.leitner_streak > highestStreak) {
            highestStreak = card.leitner_streak;
          }
        });

        setBoxes(newBoxes);

        // Set the streak based on the highest streak found
        if (highestStreak > 0) {
          setStreak(highestStreak);
        }

        setError(null);
      } else {
        throw new Error(response.data.message || 'Failed to load flashcards');
      }
    } catch (error) {
      console.error('Failed to load flashcards for Leitner system:', error);
      setError('Failed to load flashcards. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Load cards when component mounts or chapterId changes
  useEffect(() => {
    loadCards();
  }, [chapterId]);

  // Save updated boxes to the server
  const saveBoxes = async () => {
    if (!chapterId) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Flatten all boxes into a single array of cards
      const allCards = Object.values(boxes).flat();

      console.log(`Saving ${allCards.length} flashcards to server...`);
      setLoading(true);

      // Update the server
      const response = await axios.post(`http://localhost:5001/api/chapters/${chapterId}/flashcards`, {
        flashcards: allCards
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        console.log(`Leitner boxes saved successfully. Saved ${response.data.count} flashcards.`);
        // Reload the cards to ensure we have the latest data
        loadCards();
      } else {
        throw new Error(response.data.message || 'Failed to save flashcards');
      }
    } catch (error) {
      console.error('Failed to save Leitner boxes:', error);
      setError('Failed to save changes. Please try again later.');
      setLoading(false);
    }
  };

  // Move a card to a different box
  const moveCard = (cardId, fromBox, toBox) => {
    if (toBox < 1 || toBox > 3) return;

    setBoxes(prevBoxes => {
      // Find the card in the fromBox
      const cardIndex = prevBoxes[fromBox].findIndex(card => card.id === cardId);
      if (cardIndex === -1) return prevBoxes;

      // Create a copy of the card and update its box_level
      const card = {
        ...prevBoxes[fromBox][cardIndex],
        box_level: toBox,
        // Update the last_reviewed timestamp
        last_reviewed: prevBoxes[fromBox][cardIndex].last_reviewed || new Date(),
      };

      // Create new boxes with the card moved
      const newBoxes = { ...prevBoxes };
      newBoxes[fromBox] = newBoxes[fromBox].filter(card => card.id !== cardId);
      newBoxes[toBox] = [...newBoxes[toBox], card];

      // Save changes to server
      saveBoxes();

      return newBoxes;
    });
  };

  // Start a study session for a specific box
  const startStudySession = (boxNumber) => {
    setSelectedBox(boxNumber);
    setView('study');
  };

  // Start a preview session for a specific box
  const startPreviewSession = (boxNumber) => {
    setPreviewCards(boxes[boxNumber]);
    setSelectedBox(boxNumber);
    setView('preview');
  };

  // Handle card review result
  const handleCardReview = (cardId, isCorrect) => {
    // Find which box the card is in
    let fromBox = null;
    let card = null;

    for (const [boxNum, boxCards] of Object.entries(boxes)) {
      const foundCard = boxCards.find(c => c.id === cardId);
      if (foundCard) {
        fromBox = parseInt(boxNum);
        card = foundCard;
        break;
      }
    }

    if (fromBox === null || !card) return;

    // Update card review data
    const now = new Date();
    card.last_reviewed = now;
    card.review_count = (card.review_count || 0) + 1;

    // Calculate success rate
    const successCount = isCorrect ?
      ((card.success_rate || 0) * (card.review_count - 1) + 100) :
      ((card.success_rate || 0) * (card.review_count - 1));
    card.success_rate = card.review_count > 0 ? successCount / card.review_count : 0;

    // Update streak
    if (isCorrect) {
      // Increment card's streak
      card.leitner_streak = (card.leitner_streak || 0) + 1;

      // Move to next box if correct (unless already in box 3)
      if (fromBox < 3) {
        // Calculate next review date based on box level
        const nextReviewDays = fromBox === 1 ? 1 : (fromBox === 2 ? 3 : 7);
        const nextReviewDate = new Date();
        nextReviewDate.setDate(nextReviewDate.getDate() + nextReviewDays);
        card.next_review_date = nextReviewDate;

        moveCard(cardId, fromBox, fromBox + 1);

        // Update streak and show confetti for milestones
        setStreak(prev => {
          const newStreak = prev + 1;
          if (newStreak % 3 === 0) {
            setIsExploding(true);
            setTimeout(() => setIsExploding(false), 2000);
          }
          return newStreak;
        });
      } else if (fromBox === 3) {
        // Card is already mastered, but we'll still update the streak
        // Set next review date for mastered cards (14 days)
        const nextReviewDate = new Date();
        nextReviewDate.setDate(nextReviewDate.getDate() + 14);
        card.next_review_date = nextReviewDate;

        setStreak(prev => {
          const newStreak = prev + 1;
          if (newStreak % 3 === 0) {
            setIsExploding(true);
            setTimeout(() => setIsExploding(false), 2000);
          }
          return newStreak;
        });

        // Save the updated card
        saveBoxes();
      }
    } else {
      // Reset card's streak on incorrect answer
      card.leitner_streak = 0;

      // Set next review date for tomorrow
      const nextReviewDate = new Date();
      nextReviewDate.setDate(nextReviewDate.getDate() + 1);
      card.next_review_date = nextReviewDate;

      // Move back to box 1 if incorrect
      if (fromBox > 1) {
        moveCard(cardId, fromBox, 1);
      } else {
        // If already in box 1, just save the updated card
        saveBoxes();
      }

      // Reset session streak on incorrect answer
      setStreak(0);
    }
  };

  // Render the appropriate view
  const renderView = () => {
    switch (view) {
      case 'study':
        return (
          <LeitnerStudySession
            cards={selectedBox ? boxes[selectedBox] : []}
            boxNumber={selectedBox}
            onBack={() => setView('boxes')}
            onCardReview={handleCardReview}
          />
        );
      case 'stats':
        return (
          <LeitnerStats
            boxes={boxes}
            onBack={() => setView('boxes')}
          />
        );
      case 'info':
        return (
          <LeitnerInfo
            onBack={() => setView('boxes')}
          />
        );
      case 'preview':
        return (
          <div className="leitner-preview">
            <div className="preview-header">
              <h2>
                <VisibilityIcon style={{ marginRight: '0.5rem' }} />
                Box {selectedBox} Preview
              </h2>
              <p>Previewing {previewCards.length} cards</p>
            </div>

            <div className="preview-cards">
              {previewCards.map((card, index) => (
                <motion.div
                  key={card.id}
                  className="preview-card-full"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <div className="card-number">{index + 1}</div>
                  <div className="card-content">
                    <div className="card-question">
                      <h3>Question</h3>
                      <p>{card.question}</p>
                    </div>
                    <div className="card-answer">
                      <h3>Answer</h3>
                      <p>{card.answer}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="preview-actions">
              <button className="primary" onClick={() => startStudySession(selectedBox)}>
                <PlayArrowIcon />
                Study These Cards
              </button>
            </div>
          </div>
        );
      case 'boxes':
      default:
        return (
          <>
            <div className="leitner-boxes">
              <LeitnerBox
                boxNumber={1}
                cards={boxes[1]}
                icon={<LooksOneIcon className="box-icon" />}
                onStudy={() => startStudySession(1)}
                onPreview={() => startPreviewSession(1)}
              />
              <LeitnerBox
                boxNumber={2}
                cards={boxes[2]}
                icon={<LooksTwoIcon className="box-icon" />}
                onStudy={() => startStudySession(2)}
                onPreview={() => startPreviewSession(2)}
              />
              <LeitnerBox
                boxNumber={3}
                cards={boxes[3]}
                icon={<Looks3Icon className="box-icon" />}
                onStudy={() => startStudySession(3)}
                onPreview={() => startPreviewSession(3)}
              />
            </div>

            {streak > 0 && (
              <div className="streak-counter">
                <LocalFireDepartmentIcon className="streak-icon" />
                <span>{streak} card streak</span>
              </div>
            )}
          </>
        );
    }
  };

  return (
    <div className={`leitner-system ${darkMode ? 'theme-dark' : ''}`}>
      {/* Confetti effect for achievements */}
      {isExploding && (
        <div className="confetti-container">
          <ConfettiExplosion
            force={0.8}
            duration={2000}
            particleCount={100}
            width={1600}
          />
        </div>
      )}

      <div className="leitner-header">
        <h1>
          <SchoolIcon style={{ marginRight: '0.5rem' }} />
          Leitner System
        </h1>

        <div className="leitner-controls">
          {view === 'boxes' ? (
            <>
              <button onClick={() => setView('info')}>
                <InfoIcon className="icon" />
                About
              </button>
              <button onClick={() => setView('stats')}>
                <BarChartIcon className="icon" />
                Statistics
              </button>
              <button className="primary" onClick={() => startStudySession(1)}>
                <PlayArrowIcon className="icon" />
                Start Studying
              </button>
              <button onClick={onBack}>
                <ArrowBackIcon className="icon" />
                Back to Flashcards
              </button>
            </>
          ) : (
            <button onClick={() => setView('boxes')}>
              <ArrowBackIcon className="icon" />
              Back to Boxes
            </button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading Leitner system...</p>
        </div>
      ) : error ? (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      ) : (
        renderView()
      )}
    </div>
  );
};

export default LeitnerSystem;
