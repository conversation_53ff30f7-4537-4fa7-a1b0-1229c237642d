import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { tools as allTools } from '../../utils/toolsData.jsx';
import ToolCard from './ToolsCard.jsx';
import ToolModal from './ToolsModal.jsx';
import '../../styles/tools.scss';

const ToolsGrid = () => {
    const [selectedTool, setSelectedTool] = useState(null);
    const [subjects, setSubjects] = useState([]);
    const [chapters, setChapters] = useState([]);
    const [selectedSubject, setSelectedSubject] = useState('');
    const [selectedChapter, setSelectedChapter] = useState('');
    const [step, setStep] = useState(1); // 1: Tool selection, 2: Subject/Chapter selection, 3: Tool usage
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isDarkMode, setIsDarkMode] = useState(false);

    // Check for dark mode on component mount and when theme changes
    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    // Fetch subjects for the current user
    useEffect(() => {
        const fetchSubjects = async () => {
            try {
                setLoading(true);
                const response = await axios.get('/api/subjects');
                setSubjects(response.data.subjects);
                setLoading(false);
            } catch (err) {
                console.error('Error fetching subjects:', err);
                setError('Failed to load subjects. Please try again later.');
                setLoading(false);
            }
        };

        fetchSubjects();
    }, []);

    // Fetch chapters when a subject is selected
    useEffect(() => {
        const fetchChapters = async () => {
            if (!selectedSubject) {
                setChapters([]);
                return;
            }

            try {
                setLoading(true);
                const response = await axios.get(`/api/subjects/${selectedSubject}/chapters`);
                setChapters(response.data.chapters);
                setLoading(false);
            } catch (err) {
                console.error('Error fetching chapters:', err);
                setError('Failed to load chapters. Please try again later.');
                setLoading(false);
            }
        };

        fetchChapters();
    }, [selectedSubject]);

    // Load all tools initially
    useEffect(() => {
        setLoading(false);
    }, []);

    const handleToolClick = (tool) => {
        setSelectedTool(tool);

        // Check if tool is standalone (like Pomodoro Timer)
        if (tool.standalone || tool.requiresSubject === false) {
            // For standalone tools, skip subject/chapter selection and go directly to tool
            setStep(3);
        } else {
            // For other tools, go to subject/chapter selection
            setStep(2);
        }
    };

    const handleSubjectChange = (e) => {
        setSelectedSubject(e.target.value);
        setSelectedChapter('');
    };

    const handleChapterChange = (e) => {
        setSelectedChapter(e.target.value);
    };

    const handleSubjectChapterSubmit = async () => {
        // Check if this is a quiz tool (subject-only) or other tools (subject + chapter)
        const isQuizTool = selectedTool.name === 'Quizzes';

        if (!selectedSubject || (!isQuizTool && !selectedChapter)) {
            const errorMsg = isQuizTool
                ? 'Please select a subject'
                : 'Please select both a subject and a chapter';
            setError(errorMsg);
            return;
        }

        try {
            setLoading(true);

            // First, get all available tools from the database to find the correct ID
            const toolsResponse = await axios.get('/api/study-tools');
            const dbTools = toolsResponse.data.tools || [];

            console.log('All database tools:', dbTools);
            console.log('Selected tool:', selectedTool);

            // Map frontend tool name to database tool ID
            // For Flash Cards, we need to check for both "Flash Cards" and "Flashcards"
            const toolName = selectedTool.name;
            let dbTool = null;

            // Try exact match first
            dbTool = dbTools.find(t => t.name === toolName);

            // If not found, try case-insensitive match
            if (!dbTool) {
                dbTool = dbTools.find(t => t.name.toLowerCase() === toolName.toLowerCase());
            }

            // If still not found, try with special cases
            if (!dbTool && toolName === "Flash Cards") {
                dbTool = dbTools.find(t => t.name === "Flashcards" || t.name.toLowerCase() === "flashcards");
            }

            // Handle Eisenhower Matrix vs Eisenhower naming mismatch
            if (!dbTool && toolName === "Eisenhower Matrix") {
                dbTool = dbTools.find(t => t.name === "Eisenhower" || t.name.toLowerCase() === "eisenhower");
            }

            if (!dbTool) {
                console.error('Tool not found in database:', toolName);
                setError(`Tool "${toolName}" not found in the database. Available tools: ${dbTools.map(t => t.name).join(', ')}`);
                setLoading(false);
                return;
            }

            const dbToolId = parseInt(dbTool.id, 10);
            console.log('Mapped frontend tool to database ID:', toolName, dbToolId);

            if (isNaN(dbToolId)) {
                console.error('Invalid tool ID (not a number):', dbTool.id);
                setError(`Invalid tool ID: ${dbTool.id}`);
                setLoading(false);
                return;
            }

            // For quiz tools, we don't need to add them to chapters since they're subject-only
            if (!isQuizTool) {
                // Check if the tool is already added to this chapter
                const response = await axios.get(`/api/chapters/${selectedChapter}/tools`);
                const existingTools = response.data.tools || [];

                console.log('Existing tools:', existingTools);

                const isToolAlreadyAdded = existingTools.some(
                    tool => parseInt(tool.tool_id, 10) === dbToolId
                );

                if (!isToolAlreadyAdded) {
                    console.log('Adding tool to chapter:', dbToolId);
                    // Add the tool to the chapter if it's not already added
                    await axios.post(`/api/chapters/${selectedChapter}/tools`, {
                        tool_id: dbToolId,
                        settings: {}
                    });
                }
            }

            // Move to the tool usage step
            setLoading(false);
            setStep(3);
        } catch (err) {
            console.error('Error checking/adding tool to chapter:', err);
            setError(`Failed to prepare tool: ${err.message}`);
            setLoading(false);
        }
    };

    const handleClose = () => {
        setSelectedTool(null);
        setStep(1);
        setSelectedSubject('');
        setSelectedChapter('');
    };

    return (
        <div className="tools-grid">
            {loading ? (
                <div className="empty-state">
                    <div className="empty-icon">⏳</div>
                    <h3>Loading...</h3>
                    <p>Please wait while we fetch your data.</p>
                </div>
            ) : error ? (
                <div className="empty-state">
                    <div className="empty-icon">⚠️</div>
                    <h3>Error</h3>
                    <p>{error}</p>
                </div>
            ) : (
                // Display all available tools
                allTools.map((tool) => (
                    <ToolCard
                        key={tool.id}
                        tool={tool}
                        onClick={() => handleToolClick(tool)}
                    />
                ))
            )}

            {selectedTool && (
                <ToolModal key="tool-modal" onClose={handleClose}>
                    {step === 2 ? (
                        // Subject and Chapter Selection Step
                        <div className="tool-settings-modal">
                            <div className="settings-header">
                                <h3>
                                    {selectedTool.name === 'Quizzes'
                                        ? 'Select Subject'
                                        : 'Select Subject and Chapter'
                                    }
                                </h3>
                                <p>
                                    {selectedTool.name === 'Quizzes'
                                        ? `Choose the subject for your ${selectedTool.name}`
                                        : `Choose where to use the ${selectedTool.name} tool`
                                    }
                                </p>
                            </div>

                            <div className="settings-form">
                                <div className="form-group">
                                    <label htmlFor="subject-select">Subject</label>
                                    <select
                                        id="subject-select"
                                        value={selectedSubject}
                                        onChange={handleSubjectChange}
                                        disabled={loading || !subjects.length}
                                    >
                                        <option value="">-- Select a Subject --</option>
                                        {subjects.map(subject => (
                                            <option key={subject.id} value={subject.id}>
                                                {subject.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Only show chapter selection for non-quiz tools */}
                                {selectedTool.name !== 'Quizzes' && (
                                    <div className="form-group">
                                        <label htmlFor="chapter-select">Chapter</label>
                                        <select
                                            id="chapter-select"
                                            value={selectedChapter}
                                            onChange={handleChapterChange}
                                            disabled={loading || !chapters.length}
                                        >
                                            <option value="">-- Select a Chapter --</option>
                                            {chapters.map(chapter => (
                                                <option key={chapter.id} value={chapter.id}>
                                                    {/*Chapter*/} {chapter.chapter_number}: {chapter.title}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                )}

                                <div className="actions">
                                    <button
                                        className="cancel"
                                        onClick={handleClose}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        className="save"
                                        onClick={handleSubjectChapterSubmit}
                                        disabled={
                                            !selectedSubject ||
                                            (selectedTool.name !== 'Quizzes' && !selectedChapter) ||
                                            loading
                                        }
                                    >
                                        {loading ? 'Loading...' : 'Continue'}
                                    </button>
                                </div>

                                {error && (
                                    <div className="error-message">
                                        {error}
                                    </div>
                                )}
                            </div>
                        </div>
                    ) : step === 3 ? (
                        // Tool Usage Step
                        React.createElement(selectedTool.component, {
                            key: `${selectedTool.id}-component`,
                            tool: selectedTool,
                            // For standalone tools, don't pass subject/chapter data
                            ...(!(selectedTool.standalone || selectedTool.requiresSubject === false) && {
                                // For quiz tools, don't pass chapterId since they're subject-only
                                ...(selectedTool.name !== 'Quizzes' && {
                                    chapterId: parseInt(selectedChapter, 10)
                                }),
                                subjectId: parseInt(selectedSubject, 10),
                                subject: subjects.find(s => s.id === parseInt(selectedSubject, 10))?.name || '',
                            }),
                            isDarkMode: isDarkMode,
                            onClose: handleClose
                        })
                    ) : null}
                </ToolModal>
            )}
        </div>
    );
};

export default ToolsGrid;