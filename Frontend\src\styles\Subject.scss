@use "variables" as *;
@use "sass:color";
@use "scrollbar";

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Add Subject Container - macOS style
.add-subject-container {
  position: relative;
  margin-bottom: 1.5rem;
  padding: 0;
  display: flex;
  justify-content: flex-end;

  .add-subject-button {
    padding: 0.5rem 1.2rem;
    border-radius: 8px;
    font-weight: 500;
    @include transition;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
    overflow: hidden;

    // Light theme
    background-color: $system-blue;
    color: white;
    border: none;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    // Dark theme
    .theme-dark & {
      background-color: $system-blue;
      color: white;
    }

    &:hover {
      transform: translateY(-1px);

      // Light theme
      background-color: color.adjust($system-blue, $lightness: -5%);
      box-shadow: 0 2px 8px rgba($system-blue, 0.3);

      &::before {
        opacity: 1;
      }

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($system-blue, $lightness: -5%);
        box-shadow: 0 2px 8px rgba($system-blue, 0.4);
      }
    }

    &:active {
      transform: translateY(0);
      background-color: color.adjust($system-blue, $lightness: -10%);
      box-shadow: 0 1px 3px rgba($system-blue, 0.2);
    }
  }
}

// Modal Overlay - macOS style
.modal-overlay-addsubject {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100000;
  backdrop-filter: blur(10px);

  // Light theme
  background-color: rgba(0, 0, 0, 0.3);

  // Dark theme
  .theme-dark & {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

// Modal Content - macOS style
.modal-content {
  min-width: 800px;
  width: fit-content !important;
  max-width: 90dvw;
  height: fit-content !important;
  border-radius: 12px;
  overflow: hidden !important;
  display: flex;
  flex-direction: column;
  animation: popIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  padding: 0 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  background-color: $window-background;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid $border-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border: 1px solid $dark-border-color;
  }

  @keyframes popIn {
    from {
      transform: scale(0.95);
      opacity: 0;
    }

    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  &.success-popup {
    max-width: 500px;
    text-align: center;
  }
}

// Popup Header - macOS style
.popup-header {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  // Light theme
  border-bottom: 1px solid $separator-color;

  // Dark theme
  .theme-dark & {
    border-bottom: 1px solid $dark-separator-color;
  }

  h3 {
    margin: 0;
    padding: 0;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  .close-button-popup {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    @include transition;

    // Light theme
    color: $tertiary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-tertiary-text;
    }

    &:hover {
      // Light theme
      background-color: $secondary-background;
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        color: $dark-secondary-text;
      }
    }

    &:active {
      // Light theme
      background-color: color.adjust($secondary-background, $lightness: -5%);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($dark-secondary-background, $lightness: 5%);
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Popup Content - macOS style
.popup-content {
  padding: 20px;
  height: fit-content !important;
  flex: 1;
  overflow-y: auto;

  // Light theme
  background-color: $secondary-background;

  // Dark theme
  .theme-dark & {
    background-color: $dark-secondary-background;
  }

  // iOS-like scrollbar is now handled by _scrollbar.scss

  .step-content {
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .success-message {
    font-size: 1.1rem;
    margin: 1rem 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }
}

// Form Group - macOS style
.form-group {
  margin-bottom: 24px;

  label {
    display: block;
    margin: 0 0 8px;
    font-size: 0.95rem;
    font-weight: 500;
    text-align: left;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  .error-message {
    font-size: 0.85rem;
    margin-top: 6px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Light theme
    color: $system-red;

    // Dark theme
    .theme-dark & {
      color: color.adjust($system-red, $lightness: 10%);
    }
  }
}

// Input - macOS style
.macos-input {
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.95rem;
  @include transition;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  border: 1px solid $border-color;
  background-color: $window-background;
  color: $primary-text;

  // Dark theme
  .theme-dark & {
    border: 1px solid $dark-border-color;
    background-color: $dark-window-background;
    color: $dark-primary-text;
  }

  &:focus {
    outline: none;

    // Light theme
    border-color: $system-blue;
    box-shadow: 0 0 0 4px rgba($system-blue, 0.1);

    // Dark theme
    .theme-dark & {
      border-color: $system-blue;
      box-shadow: 0 0 0 4px rgba($system-blue, 0.2);
    }
  }

  &::placeholder {
    // Light theme
    color: $placeholder-text;

    // Dark theme
    .theme-dark & {
      color: $dark-placeholder-text;
    }
  }
}

// Select - macOS style
.macos-select {
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.95rem;
  appearance: none;
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  @include transition;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  border: 1px solid $border-color;
  background-color: $window-background;
  color: $primary-text;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233C3C43' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");

  // Dark theme
  .theme-dark & {
    border: 1px solid $dark-border-color;
    background-color: $dark-window-background;
    color: $dark-primary-text;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23EBEBF5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  }

  &:focus {
    outline: none;

    // Light theme
    border-color: $system-blue;
    box-shadow: 0 0 0 4px rgba($system-blue, 0.1);

    // Dark theme
    .theme-dark & {
      border-color: $system-blue;
      box-shadow: 0 0 0 4px rgba($system-blue, 0.2);
    }
  }
}

// Button - macOS style
.macos-button {
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  @include transition;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
  overflow: hidden;

  // Light theme
  background-color: $system-blue;
  color: white;
  border: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  // Dark theme
  .theme-dark & {
    background-color: $system-blue;
    color: white;
  }

  &:hover {
    transform: translateY(-1px);

    // Light theme
    background-color: color.adjust($system-blue, $lightness: -5%);
    box-shadow: 0 2px 8px rgba($system-blue, 0.3);

    &::before {
      opacity: 1;
    }

    // Dark theme
    .theme-dark & {
      background-color: color.adjust($system-blue, $lightness: -5%);
      box-shadow: 0 2px 8px rgba($system-blue, 0.4);
    }
  }

  &:active {
    transform: translateY(0);
    background-color: color.adjust($system-blue, $lightness: -10%);
    box-shadow: 0 1px 3px rgba($system-blue, 0.2);
  }

  &:disabled {
    // Light theme
    background-color: $system-gray;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.7;

    // Dark theme
    .theme-dark & {
      background-color: $system-gray;
      opacity: 0.7;
    }
  }

  &.primary {
    // Light theme
    background-color: $system-blue;
    color: white;

    // Dark theme
    .theme-dark & {
      background-color: $system-blue;
      color: white;
    }

    &:hover {
      // Light theme
      background-color: color.adjust($system-blue, $lightness: -5%);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($system-blue, $lightness: -5%);
      }
    }
  }

  &.secondary {
    // Light theme
    background-color: $window-background;
    border: 1px solid $border-color;
    color: $secondary-text;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      border: 1px solid $dark-border-color;
      color: $dark-secondary-text;
    }

    &:hover {
      // Light theme
      background-color: $secondary-background;
      color: $primary-text;
      border-color: color.adjust($border-color, $lightness: -5%);

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        color: $dark-primary-text;
        border-color: color.adjust($dark-border-color, $lightness: 5%);
      }
    }
  }

  &.danger {
    // Light theme
    background-color: transparent;
    color: $tertiary-text;
    padding: 8px;

    // Dark theme
    .theme-dark & {
      background-color: transparent;
      color: $dark-tertiary-text;
    }

    &:hover {
      transform: none;
      box-shadow: none;

      // Light theme
      background-color: color.adjust($system-red, $alpha: -0.9);
      color: $system-red;

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($system-red, $alpha: -0.85);
        color: $system-red;
      }
    }
  }

  &.active {
    // Light theme
    background-color: color.adjust($system-blue, $alpha: -0.9);
    color: $system-blue;
    border: 1px solid $system-blue;

    // Dark theme
    .theme-dark & {
      background-color: color.adjust($system-blue, $alpha: -0.85);
      color: $system-blue;
      border: 1px solid $system-blue;
    }
  }
}

.file-type-selector {
  margin-bottom: 1.5rem;

  .options-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-top: 0.5rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }

    button {
      min-width: 120px;
      padding: 1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      transition: all 0.2s ease;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      // Light theme
      background-color: $window-background;
      border: 1px solid $border-color;
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        background-color: $dark-window-background;
        border: 1px solid $dark-border-color;
        color: $dark-primary-text;
      }

      &:hover {
        transform: translateY(-2px);

        // Light theme
        border-color: $system-blue;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
          border-color: $system-blue;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
      }

      .option-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: $system-blue;
      }
    }
  }
}

.drop-zone {
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 1rem;
  @include transition;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  border: 2px dashed $border-color;
  background-color: $secondary-background;

  // Dark theme
  .theme-dark & {
    border: 2px dashed $dark-border-color;
    background-color: $dark-secondary-background;
  }

  &.drag-active {
    // Light theme
    border-color: $system-blue;
    background-color: rgba($system-blue, 0.05);

    // Dark theme
    .theme-dark & {
      border-color: $system-blue;
      background-color: rgba($system-blue, 0.1);
    }
  }

  .drop-area {
    margin-bottom: 1rem;

    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 0.5rem;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    svg {
      width: 48px;
      height: 48px;
      margin-bottom: 0.5rem;
      color: $system-blue;
    }

    .hint {
      font-size: 0.875rem;
      margin: 0.5rem 0 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  input[type="file"] {
    display: none;
  }
}

.selected-files-preview {
  margin-top: 1rem;

  .file-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    @include transition;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Light theme
    background-color: $window-background;
    border: 1px solid $border-color;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      border: 1px solid $dark-border-color;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    &:hover {
      // Light theme
      border-color: $system-blue;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);

      // Dark theme
      .theme-dark & {
        border-color: $system-blue;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
      }
    }

    .file-icon {
      font-size: 1.5rem;
      margin-right: 1rem;
      color: $system-blue;
    }

    .file-info {
      flex: 1;

      .file-name {
        margin: 0;
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        // Light theme
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .file-size {
        margin: 0.25rem 0 0;
        font-size: 0.875rem;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        // Light theme
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }
}

.upload-summary {
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  background-color: $window-background;
  border: 1px solid $border-color;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    border: 1px solid $dark-border-color;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;

    // Light theme
    border-bottom: 1px solid $separator-color;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-separator-color;
    }

    &:last-child {
      border-bottom: none;
    }

    .summary-label {
      font-weight: 500;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .summary-value {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }
}

.upload-progress-container {
  margin-top: 1.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  .progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  .progress-bar-container {
    height: 6px;
    border-radius: 3px;
    overflow: hidden;

    // Light theme
    background-color: $system-light-gray;

    // Dark theme
    .theme-dark & {
      background-color: $dark-accent-color-2;
    }

    .progress-bar {
      height: 100%;
      background-color: $system-blue;
      @include transition(width);

      &.success {
        background-color: $system-green;
      }

      &.warning {
        background-color: $system-orange;
      }

      &.error {
        background-color: $system-red;
      }
    }
  }
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  background-color: $window-background;
  border-top: 1px solid $separator-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-window-background;
    border-top: 1px solid $dark-separator-color;
  }

  button {
    min-width: 120px;
  }

  .step-navigation {
    display: flex;
    gap: 0.75rem;
  }

  .spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;

    // Light theme
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;

    // Dark theme
    .theme-dark & {
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-top-color: white;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  }
}

.error-message {
  font-size: 0.875rem;
  margin: 0.5rem 0;
  padding: 0.75rem;
  border-radius: 8px;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  color: $system-red;
  background-color: color.adjust($system-red, $alpha: -0.9);
  border: 1px solid color.adjust($system-red, $alpha: -0.7);

  // Dark theme
  .theme-dark & {
    color: color.adjust($system-red, $lightness: 10%);
    background-color: color.adjust($system-red, $alpha: -0.85);
    border: 1px solid color.adjust($system-red, $alpha: -0.7);
  }
}




























// Study Page - macOS style
.study-page {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  min-height: 100vh;

  // Light theme
  background-color: $light-background;

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
  }

  // Loading container
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid transparent;
      border-radius: 50%;
      margin-bottom: 16px;
      animation: spin 1s linear infinite;

      // Light theme
      border-top-color: $color-primary;
      border-left-color: $color-primary;

      // Dark theme
      .theme-dark & {
        border-top-color: $color-primary;
        border-left-color: $color-primary;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    }

    p {
      // Light theme
      color: $light-secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  // Error container
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;

    .error-icon {
      font-size: 2rem;
      margin-bottom: 16px;
    }

    .error-actions {
      display: flex;
      gap: 12px;
      margin-top: 16px;

      button {
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;

        &.retry-button {
          // Light theme
          background-color: $system-blue;
          color: white;

          // Dark theme
          .theme-dark & {
            background-color: $system-blue;
            color: white;
          }

          &:hover {
            // Light theme
            background-color: color.adjust($system-blue, $lightness: -5%);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($system-blue, $lightness: -5%);
            }
          }
        }

        &.login-button {
          // Light theme
          background-color: $system-green;
          color: white;

          // Dark theme
          .theme-dark & {
            background-color: $system-green;
            color: white;
          }

          &:hover {
            // Light theme
            background-color: color.adjust($system-green, $lightness: -5%);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($system-green, $lightness: -5%);
            }
          }
        }
      }
    }

    .error-message {
      margin-bottom: 16px;
      text-align: center;

      // Light theme
      color: $light-primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .retry-button {
      padding: 8px 16px;
      border-radius: 8px;
      @include transition;

      // Light theme
      background-color: $color-primary;
      color: white;
      border: none;

      // Dark theme
      .theme-dark & {
        background-color: $color-primary;
        color: white;
      }

      &:hover {
        transform: translateY(-2px);

        // Light theme
        background-color: color.adjust($color-primary, $lightness: -5%);
        box-shadow: 0 4px 8px rgba(0, 122, 255, 0.2);

        // Dark theme
        .theme-dark & {
          background-color: color.adjust($color-primary, $lightness: -5%);
          box-shadow: 0 4px 8px rgba(0, 122, 255, 0.4);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  // Empty state
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;

    .empty-icon {
      font-size: 3rem;
      margin-bottom: 16px;
    }

    .no-subjects {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 8px;

      // Light theme
      color: $light-primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .empty-hint {
      // Light theme
      color: $light-secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }
}

// Subject Navigation - macOS style
.subject-nav {
  display: flex;
  align-items: center;
  padding: 0;
  border-radius: 8px;
  margin-bottom: 20px;
  justify-content: space-between;
  overflow: hidden;

  // Light theme
  background-color: $color-primary;

  // Dark theme
  .theme-dark & {
    background-color: $color-primary;
  }

  .left-button {
    margin-left: 8px;

    svg {
      transform: rotate(180deg);
    }
  }

  .right-button {
    margin-right: 8px;
  }

  .left-button,
  .right-button {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    @include flex-center;
    cursor: pointer;
    @include transition;

    // Light theme
    background-color: color.adjust($color-primary, $lightness: 10%);

    // Dark theme
    .theme-dark & {
      background-color: color.adjust($color-primary, $lightness: 10%);
    }

    &:hover {
      // Light theme
      background-color: color.adjust($color-primary, $lightness: 15%);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($color-primary, $lightness: 15%);
      }
    }

    * {
      color: white;
    }
  }

  .middle-content {
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */

    &::-webkit-scrollbar {
      display: none;
      /* Chrome, Safari, Opera */
    }
  }

  .subject-btn {
    background: none;
    border: none;
    color: white;
    //font-size: 16px;
    padding: 0 1rem;
    margin: 8px 4px;
    cursor: pointer;
    @include transition;
    border-radius: 6px;
    height: 36px;
    text-transform: capitalize;

    &:hover {
      // Light theme
      background-color: color.adjust($color-primary, $lightness: 10%);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($color-primary, $lightness: 10%);
      }
    }

    &:active {
      // Light theme
      background-color: color.adjust($color-primary, $lightness: 5%);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($color-primary, $lightness: 5%);
      }
    }
  }

  .no-subjects {
    color: white;
    padding: 12px 16px;
    text-align: center;
  }
}

// Subjects Container - macOS style
.subjects-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// Subject Section - macOS style
.subject-section {
  border-radius: 12px;
  overflow: hidden;
  padding: 0;

  // Light theme
  background-color: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid $light-accent-color-2;

  // Dark theme
  .theme-dark & {
    background-color: $dark-background;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border: 1px solid $dark-accent-color-2;
  }

  .subject-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 0;

    .subject-title-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0.5rem;
      width: 100%;

      // Light theme
      background-color: white;
      border-bottom: 1px solid $light-accent-color-2;

      // Dark theme
      .theme-dark & {
        background-color: $dark-background;
        border-bottom: 1px solid $dark-accent-color-2;
      }

      h2 {
        margin: 0;
        padding: .5rem;
        font-size: 1.5rem;
        font-weight: 600;
        text-transform: capitalize;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .delete-subject-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
        opacity: 0.7;

        // Light theme
        background-color: transparent;
        color: $system-dark-gray;

        // Dark theme
        .theme-dark & {
          background-color: transparent;
          color: $dark-tertiary-text;
        }

        svg {
          transition: all 0.2s ease;
        }

        &:hover {
          opacity: 1;
          // Light theme
          background-color: rgba($system-light-gray, 0.5);

          // Dark theme
          .theme-dark & {
            background-color: rgba($dark-secondary-background, 0.5);
          }

          svg {
            color: $system-red;

            .theme-dark & {
              color: $system-red;
            }
          }
        }

        &:active {
          // Light theme
          background-color: rgba($system-light-gray, 0.7);

          // Dark theme
          .theme-dark & {
            background-color: rgba($dark-secondary-background, 0.7);
          }
        }
      }
    }

    .chapter-tabs {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      width: 100%;
      padding: 16px 20px;

      // Light theme
      background-color: $light-background;
      border-bottom: 1px solid $light-accent-color-2;

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($dark-background, $lightness: -2%);
        border-bottom: 1px solid $dark-accent-color-2;
      }

      .chapter-tab {
        background: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-size: 0.9rem;
        cursor: pointer;
        @include transition;

        // Light theme
        border: 1px solid $light-accent-color-2;
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
          border: 1px solid $dark-accent-color-2;
          color: $dark-secondary-text;
        }

        &:hover {
          // Light theme
          background-color: $light-background;
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            background-color: color.adjust($dark-background, $lightness: 5%);
            color: $dark-primary-text;
          }
        }

        &.active {
          // Light theme
          background-color: color.adjust($color-primary, $alpha: -0.9);
          color: $color-primary;
          border: 1px solid $color-primary;

          // Dark theme
          .theme-dark & {
            background-color: color.adjust($color-primary, $alpha: -0.85);
            color: $color-primary;
            border: 1px solid $color-primary;
          }
        }
      }

      .no-chapters {
        font-style: italic;
        padding: 8px 0;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }

  .study-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
    padding: 20px;

    .tools-loading {
      grid-column: 1 / -1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 2px solid transparent;
        border-radius: 50%;
        margin-bottom: 12px;
        animation: spin 1s linear infinite;

        // Light theme
        border-top-color: $color-primary;
        border-left-color: $color-primary;

        // Dark theme
        .theme-dark & {
          border-top-color: $color-primary;
          border-left-color: $color-primary;
        }
      }

      p {
        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    .tools-error {
      grid-column: 1 / -1;
      text-align: center;
      padding: 24px;
      color: $system-red;

      // Dark theme
      .theme-dark & {
        color: color.adjust($system-red, $lightness: 10%);
      }
    }

    .study-tool-card {
      position: relative;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      @include transition;
      cursor: pointer;

      // Light theme
      background-color: white;
      border: 1px solid $light-accent-color-2;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($dark-background, $lightness: 5%);
        border: 1px solid $dark-accent-color-2;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      &:hover {
        transform: translateY(-3px);

        // Light theme
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
        border-color: $color-primary;

        // Dark theme
        .theme-dark & {
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
          border-color: $color-primary;
        }
      }

      &.add-tool-card {
        // Light theme
        background-color: rgba($color-primary, 0.05);
        border: 1px dashed $color-primary;

        // Dark theme
        .theme-dark & {
          background-color: rgba($color-primary, 0.1);
          border: 1px dashed $color-primary;
        }

        .tool-icon {
          // Light theme
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            color: $color-primary;
          }
        }
      }

      .tool-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        width: 100%;

        .tool-icon {
          font-size: 28px;

          // Light theme
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            color: $color-primary;
          }
        }

        p {
          margin: 0;
          font-weight: 500;

          // Light theme
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        .tool-description {
          font-size: 0.75rem;
          margin-top: -8px;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;

          // Light theme
          color: $light-secondary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }

        .tool-id {
          font-size: 0.7rem;
          margin-top: 0.25rem;
          opacity: 0.7;

          // Light theme
          color: $tertiary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-tertiary-text;
          }
        }
      }
    }

    p.no-tools-message {
      grid-column: 1 / -1;
      font-style: italic;
      text-align: center;
      padding: 24px;

      // Light theme
      color: $light-secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  // Tool Popup Styles
  .tool-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
    animation: fadeIn 0.2s ease-out;

    // Light theme
    background-color: rgba(0, 0, 0, 0.3);

    // Dark theme
    .theme-dark & {
      background-color: rgba(0, 0, 0, 0.5);
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }

    .tool-popup-content {
      width: 90%;
      max-width: 800px;
      max-height: 90vh;
      border-radius: 12px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      animation: popIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
      position: relative;

      // Light theme
      background-color: $window-background;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      border: 1px solid $border-color;

      // Dark theme
      .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        border: 1px solid $dark-border-color;
      }

      @keyframes popIn {
        from {
          transform: scale(0.95);
          opacity: 0;
        }

        to {
          transform: scale(1);
          opacity: 1;
        }
      }

      .tool-popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;

        // Light theme
        border-bottom: 1px solid $separator-color;

        // Dark theme
        .theme-dark & {
          border-bottom: 1px solid $dark-separator-color;
        }

        h3 {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;

          // Light theme
          color: $primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        .close-button {
          background: none;
          border: none;
          font-size: 24px;
          cursor: pointer;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          @include transition;

          // Light theme
          color: $tertiary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-tertiary-text;
          }

          &:hover {
            // Light theme
            background-color: $secondary-background;
            color: $secondary-text;

            // Dark theme
            .theme-dark & {
              background-color: $dark-secondary-background;
              color: $dark-secondary-text;
            }
          }
        }
      }

      .tool-popup-body {
        flex: 1;
        overflow-y: auto;
        padding: 20px;

        // Light theme
        background-color: $secondary-background;

        // Dark theme
        .theme-dark & {
          background-color: $dark-secondary-background;
        }

        .tool-placeholder,
        .tool-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 20px;
          text-align: center;

          // Light theme
          background-color: $window-background;
          border: 1px solid $border-color;
          border-radius: 8px;

          // Dark theme
          .theme-dark & {
            background-color: $dark-window-background;
            border: 1px solid $dark-border-color;
          }

          .tool-icon {
            font-size: 48px;
            margin-bottom: 16px;

            // Light theme
            color: $color-primary;

            // Dark theme
            .theme-dark & {
              color: $color-primary;
            }
          }

          h4 {
            font-size: 1.2rem;
            margin: 0 0 8px;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }
          }

          p {
            // Light theme
            color: $secondary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .tool-debug-info {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: left;
            width: 100%;
            max-width: 400px;

            // Light theme
            background-color: color.adjust($system-blue, $alpha: -0.9);
            border: 1px solid color.adjust($system-blue, $alpha: -0.7);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($system-blue, $alpha: -0.85);
              border: 1px solid color.adjust($system-blue, $alpha: -0.7);
            }

            p {
              margin: 0 0 8px;
              font-weight: 500;
              font-size: 0.9rem;

              // Light theme
              color: $primary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-primary-text;
              }
            }

            .retry-button {
              padding: 6px 12px;
              border-radius: 6px;
              font-size: 0.85rem;
              font-weight: 500;
              cursor: pointer;
              border: none;
              transition: all 0.2s ease;
              margin-top: 10px;

              // Light theme
              background-color: $system-blue;
              color: white;

              // Dark theme
              .theme-dark & {
                background-color: $system-blue;
                color: white;
              }

              &:hover {
                // Light theme
                background-color: color.adjust($system-blue, $lightness: -5%);

                // Dark theme
                .theme-dark & {
                  background-color: color.adjust($system-blue, $lightness: -5%);
                }
              }
            }
          }
        }

        .tool-error {
          border-color: $system-red;

          // Dark theme
          .theme-dark & {
            border-color: color.adjust($system-red, $lightness: 10%);
          }

          p {
            color: $system-red;
            font-weight: 500;

            // Dark theme
            .theme-dark & {
              color: color.adjust($system-red, $lightness: 10%);
            }
          }

          .error-help {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;

            // Light theme
            background-color: color.adjust($system-red, $alpha: -0.9);
            border: 1px solid color.adjust($system-red, $alpha: -0.7);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($system-red, $alpha: -0.85);
              border: 1px solid color.adjust($system-red, $alpha: -0.7);
            }

            p {
              margin: 0 0 8px;
              font-weight: 500;
              font-size: 0.9rem;
            }

            ul {
              margin: 0 0 12px;
              padding-left: 20px;
              font-size: 0.85rem;

              // Light theme
              color: $secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }

            .retry-button,
            .login-button {
              padding: 6px 12px;
              border-radius: 6px;
              font-size: 0.85rem;
              font-weight: 500;
              cursor: pointer;
              border: none;
              transition: all 0.2s ease;

              // Light theme
              background-color: $system-blue;
              color: white;

              // Dark theme
              .theme-dark & {
                background-color: $system-blue;
                color: white;
              }

              &:hover {
                // Light theme
                background-color: color.adjust($system-blue, $lightness: -5%);

                // Dark theme
                .theme-dark & {
                  background-color: color.adjust($system-blue, $lightness: -5%);
                }
              }
            }
          }
        }

        // Available Tools Grid
        .available-tools-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 16px;

          .available-tool-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border-radius: 8px;
            cursor: pointer;
            @include transition;
            text-align: center;

            // Light theme
            background-color: $window-background;
            border: 1px solid $border-color;

            // Dark theme
            .theme-dark & {
              background-color: $dark-window-background;
              border: 1px solid $dark-border-color;
            }

            &:hover {
              transform: translateY(-3px);

              // Light theme
              box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
              border-color: $color-primary;

              // Dark theme
              .theme-dark & {
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
                border-color: $color-primary;
              }
            }

            .tool-image {
              width: 64px;
              height: 64px;
              object-fit: contain;
              margin-bottom: 12px;
            }

            .tool-icon-placeholder {
              width: 64px;
              height: 64px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 32px;
              margin-bottom: 12px;

              // Light theme
              color: $color-primary;

              // Dark theme
              .theme-dark & {
                color: $color-primary;
              }
            }

            h4 {
              margin: 0 0 8px;
              font-size: 1rem;
              font-weight: 600;

              // Light theme
              color: $primary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-primary-text;
              }
            }

            p {
              margin: 0;
              font-size: 0.85rem;

              // Light theme
              color: $secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }
          }
        }
      }
    }
  }
}

// Selected Chapter Title
.selected-chapter-title {
  margin: 1rem 0;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;

  // Light theme
  background-color: $secondary-background;
  border: 1px solid $border-color;

  // Dark theme
  .theme-dark & {
    background-color: $dark-secondary-background;
    border: 1px solid $dark-border-color;
  }

  h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
    }
  }
}

// Delete confirmation modal
.delete-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(10px);

  // Light theme
  background-color: rgba(255, 255, 255, 0.3);

  // Dark theme
  .theme-dark & {
    background-color: rgba(0, 0, 0, 0.3);
  }

  .delete-confirm-content {
    width: 420px;
    padding: 20px 24px 24px;
    border-radius: 12px;
    text-align: center;
    animation: popIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: $window-background;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid $border-color;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      border: 1px solid $dark-border-color;
    }

    .delete-icon-container {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;

      .delete-icon {
        // Light theme
        color: $system-red;

        // Dark theme
        .theme-dark & {
          color: $system-red;
        }
      }
    }

    h3 {
      margin: 0 0 16px;
      font-size: 1.2rem;
      font-weight: 600;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    p {
      margin: 0 0 16px;
      font-size: 0.95rem;

      // Light theme
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }

      &.warning {
        font-size: 0.85rem;
        margin-top: -8px;

        // Light theme
        color: $system-red;

        // Dark theme
        .theme-dark & {
          color: color.adjust($system-red, $lightness: 10%);
        }
      }
    }

    .delete-confirm-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 24px;

      button {
        padding: 6px 14px;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        &.cancel-btn {
          // Light theme
          background-color: $secondary-background;
          border: 1px solid $border-color;
          color: $secondary-text;

          // Dark theme
          .theme-dark & {
            background-color: $dark-secondary-background;
            border: 1px solid $dark-border-color;
            color: $dark-secondary-text;
          }

          &:hover {
            // Light theme
            background-color: $window-background;

            // Dark theme
            .theme-dark & {
              background-color: $dark-window-background;
            }
          }

          &:active {
            transform: scale(0.98);
          }
        }

        &.delete-btn {
          // Light theme
          background-color: $system-red;
          border: none;
          color: white;
          font-weight: 600;

          // Dark theme
          .theme-dark & {
            background-color: $system-red;
          }

          &:hover {
            // Light theme
            background-color: color.adjust($system-red, $lightness: -5%);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($system-red, $lightness: -5%);
            }
          }

          &:active {
            transform: scale(0.98);
            background-color: color.adjust($system-red, $lightness: -10%);
          }
        }
      }
    }
  }
}

// Loading Overlay
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10001;
  backdrop-filter: blur(5px);

  // Light theme
  background-color: rgba(255, 255, 255, 0.7);

  // Dark theme
  .theme-dark & {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid transparent;
    border-radius: 50%;
    margin-bottom: 16px;
    animation: spin 1s linear infinite;

    // Light theme
    border-top-color: $color-primary;
    border-left-color: $color-primary;

    // Dark theme
    .theme-dark & {
      border-top-color: $color-primary;
      border-left-color: $color-primary;
    }
  }

  p {
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 8px;

    // Light theme
    background-color: rgba(255, 255, 255, 0.9);
    color: $primary-text;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    // Dark theme
    .theme-dark & {
      background-color: rgba(0, 0, 0, 0.8);
      color: $dark-primary-text;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }
}

// Error Notification
.error-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  z-index: 10001;
  animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  max-width: 400px;

  // Light theme
  background-color: rgba($system-red, 0.9);
  color: white;
  box-shadow: 0 4px 12px rgba($system-red, 0.3);

  // Dark theme
  .theme-dark & {
    background-color: rgba($system-red, 0.8);
    box-shadow: 0 4px 12px rgba($system-red, 0.2);
  }

  .error-content {
    flex: 1;

    p {
      margin: 0 0 8px;
      font-weight: 500;
      color: white;
    }

    .login-button {
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 0.85rem;
      font-weight: 500;
      cursor: pointer;
      border: none;
      transition: all 0.2s ease;
      margin-top: 8px;

      // Light theme
      background-color: white;
      color: $system-red;

      // Dark theme
      .theme-dark & {
        background-color: white;
        color: $system-red;
      }

      &:hover {
        // Light theme
        background-color: color.adjust(white, $lightness: -5%);

        // Dark theme
        .theme-dark & {
          background-color: color.adjust(white, $lightness: -5%);
        }
      }
    }
  }

  .close-error {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    opacity: 0.8;

    &:hover {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }

    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

// Status notification
.status-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  z-index: 1000;
  animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  max-width: 400px;
  backdrop-filter: blur(10px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  @keyframes slideIn {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }

    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  p {
    margin: 0;
    font-weight: 500;
    font-size: 0.95rem;
  }

  button {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;

    &:hover {
      opacity: 1;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &.success {
    // Light theme
    background-color: rgba($system-green, 0.9);
    color: white;
    box-shadow: 0 4px 12px rgba($system-green, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);

    // Dark theme
    .theme-dark & {
      background-color: rgba($system-green, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 0 4px 12px rgba($system-green, 0.2);
    }

    p,
    button {
      color: white;
    }
  }

  &.error {
    // Light theme
    background-color: rgba($system-red, 0.9);
    color: white;
    box-shadow: 0 4px 12px rgba($system-red, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);

    // Dark theme
    .theme-dark & {
      background-color: rgba($system-red, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 0 4px 12px rgba($system-red, 0.2);
    }

    p,
    button {
      color: white;
    }
  }
}

// Delete loading overlay
.delete-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Light theme
  background-color: rgba(255, 255, 255, 0.5);

  // Dark theme
  .theme-dark & {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .loading-spinner {
    width: 36px;
    height: 36px;
    border: 2px solid transparent;
    border-radius: 50%;
    margin-bottom: 16px;
    animation: spin 1s linear infinite;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

    // Light theme
    border-top-color: $system-blue;
    border-left-color: $system-blue;

    // Dark theme
    .theme-dark & {
      border-top-color: $system-blue;
      border-left-color: $system-blue;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    }
  }

  p {
    font-weight: 500;
    font-size: 0.95rem;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 8px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    // Light theme
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      color: $dark-primary-text;
      background-color: rgba(0, 0, 0, 0.7);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}