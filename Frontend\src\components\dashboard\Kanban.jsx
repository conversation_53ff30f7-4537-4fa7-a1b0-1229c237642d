import React, { useState, useEffect, useRef } from 'react';
import '../../styles/Dashboard.scss';
import axios from 'axios';
import { useAuth } from '../../../authContext.jsx';

const Kanban = ({ subjectId }) => {
    const [showModal, setShowModal] = useState(false);
    const modalRef = useRef(null);
    const { token } = useAuth(); // Get the token from auth context
    const [tasks, setTasks] = useState({
        todo: {
            count: 0,
            label: 'To Do',
            items: [],
        },
        inProgress: {
            count: 0,
            label: 'In Progress',
            items: [],
        },
        done: {
            count: 0,
            label: 'Done',
            items: [],
        },
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Fetch tasks from the database
    useEffect(() => {
        const fetchTasks = async () => {
            if (!token) {
                setError('Authentication required');
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                console.log(`Fetching kanban tasks for subject: ${subjectId || 'all'}`);

                // Use query parameters instead of path parameters
                const url = subjectId ? `/api/kanban?subjectId=${subjectId}` : '/api/kanban';
                const response = await axios.get(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                console.log('Kanban API response:', response.data);

                // Check if the response is HTML instead of JSON
                if (typeof response.data === 'string' && response.data.includes('<!doctype html>')) {
                    console.error('Received HTML response instead of JSON. API endpoint may be incorrect or server not running.');
                    setError('API connection error. Please check server.');
                    setLoading(false);
                    return;
                }

                if (response.data.success) {
                    // Map database tasks to our columns
                    const tasksByStatus = {
                        todo: [],
                        inProgress: [],
                        done: []
                    };

                    response.data.tasks.forEach(task => {
                        let column;
                        switch (task.status) {
                            case 'To Do':
                                column = 'todo';
                                break;
                            case 'In Progress':
                                column = 'inProgress';
                                break;
                            case 'Done':
                                column = 'done';
                                break;
                            default:
                                column = 'todo';
                        }

                        tasksByStatus[column].push({
                            id: task.id,
                            title: task.task,
                            due: task.created_at ? new Date(task.created_at).toLocaleDateString() : 'No date'
                        });
                    });

                    console.log('Processed kanban tasks:', tasksByStatus);

                    setTasks({
                        todo: {
                            label: 'To Do',
                            count: tasksByStatus.todo.length,
                            items: tasksByStatus.todo
                        },
                        inProgress: {
                            label: 'In Progress',
                            count: tasksByStatus.inProgress.length,
                            items: tasksByStatus.inProgress
                        },
                        done: {
                            label: 'Done',
                            count: tasksByStatus.done.length,
                            items: tasksByStatus.done
                        }
                    });
                }
                setLoading(false);
            } catch (err) {
                console.error('Error fetching Kanban tasks:', err);
                if (err.response?.status === 401) {
                    setError('Authentication required. Please log in again.');
                } else {
                    setError('Failed to load tasks');
                }
                setLoading(false);
            }
        };

        if (showModal || !tasks.todo.items.length) {
            fetchTasks();
        }
    }, [showModal, subjectId, token]);

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowModal(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowModal(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    return (
        <>
            <div
                className="kanban-card"
                onClick={() => setShowModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
                aria-label="Open Kanban Board"
            >
                <h2 className="card-title">Kanban Board</h2>
                <div className="task-grid">
                    <div className="task-item todo-column">
                        <p className="column-title">{tasks.todo.label}</p>
                        <p className="count-number">{tasks.todo.count}</p>
                    </div>
                    <div className="task-item inprogress-column">
                        <p className="column-title">{tasks.inProgress.label}</p>
                        <p className="count-number">{tasks.inProgress.count}</p>
                    </div>
                    <div className="task-item done-column">
                        <p className="column-title">{tasks.done.label}</p>
                        <p className="count-number">{tasks.done.count}</p>
                    </div>
                </div>
            </div>
            {showModal && (
                <div className="modal-overlay">
                    <div className="modal-window" ref={modalRef} role="dialog" aria-labelledby="modal-title">
                        <div className="modal-header">
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowModal(false)}
                                    aria-label="Close"
                                ></button>
                                <button className="traffic-light yellow" disabled aria-label="Minimize"></button>
                                <button className="traffic-light green" disabled aria-label="Maximize"></button>
                            </div>
                            <h2 id="modal-title" className="modal-title">
                                Kanban Board
                            </h2>
                        </div>
                        <div className="modal-content">
                            {loading ? (
                                <p>Loading tasks...</p>
                            ) : error ? (
                                <p className="error-message">{error}</p>
                            ) : (
                                <div className="kanban-columns">
                                    <div className="kanban-column todo">
                                        <h3>{tasks.todo.label}</h3>
                                        <p>{tasks.todo.count} tasks</p>
                                        <ul className="task-list">
                                            {tasks.todo.items.map((task) => (
                                                <li key={task.id}>
                                                    {task.title} <span className="due-date">Due: {task.due}</span>
                                                </li>
                                            ))}
                                            {tasks.todo.items.length === 0 && <li>No tasks</li>}
                                        </ul>
                                    </div>
                                    <div className="kanban-column inprogress">
                                        <h3>{tasks.inProgress.label}</h3>
                                        <p>{tasks.inProgress.count} tasks</p>
                                        <ul className="task-list">
                                            {tasks.inProgress.items.map((task) => (
                                                <li key={task.id}>
                                                    {task.title} <span className="due-date">Due: {task.due}</span>
                                                </li>
                                            ))}
                                            {tasks.inProgress.items.length === 0 && <li>No tasks</li>}
                                        </ul>
                                    </div>
                                    <div className="kanban-column done">
                                        <h3>{tasks.done.label}</h3>
                                        <p>{tasks.done.count} tasks</p>
                                        <ul className="task-list">
                                            {tasks.done.items.map((task) => (
                                                <li key={task.id}>
                                                    {task.title} <span className="due-date">Due: {task.due}</span>
                                                </li>
                                            ))}
                                            {tasks.done.items.length === 0 && <li>No tasks</li>}
                                        </ul>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="modal-footer">
                            <button className="close-button" onClick={() => setShowModal(false)}>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Kanban;
