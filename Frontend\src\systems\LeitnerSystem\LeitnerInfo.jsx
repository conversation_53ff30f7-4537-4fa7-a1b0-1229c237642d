import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

// Material Icons
import InfoIcon from '@mui/icons-material/Info';
import SchoolIcon from '@mui/icons-material/School';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import Looks4Icon from '@mui/icons-material/Looks4';
import Looks5Icon from '@mui/icons-material/Looks5';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';

const LeitnerInfo = ({ onBack }) => {
  const { darkMode } = useTheme();

  return (
    <div className={`leitner-info ${darkMode ? 'theme-dark' : ''}`}>
      <div className="info-header">
        <h2>
          <InfoIcon style={{ marginRight: '0.5rem' }} />
          About the Leitner System
        </h2>
      </div>

      <motion.div
        className="info-section"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="info-icon">
          <SchoolIcon />
        </div>
        <div className="info-content">
          <h3>What is the Leitner System?</h3>
          <p>
            The Leitner System is a widely used method of efficiently using flashcards that was proposed by
            German science journalist Sebastian Leitner in the 1970s. It is a simple implementation of the
            principle of spaced repetition, where cards are reviewed at increasing intervals.
          </p>
        </div>
      </motion.div>

      <motion.div
        className="info-section"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <h3>How it Works</h3>
        <p>
          In this system, flashcards are sorted into boxes based on how well you know each card:
        </p>

        <div className="boxes-explanation">
          <div className="box-explanation">
            <div className="box-icon box-1">
              <LooksOneIcon />
            </div>
            <div className="box-details">
              <h4>Box 1</h4>
              <p>New cards and cards you've answered incorrectly. Review every day.</p>
            </div>
          </div>

          <div className="box-explanation">
            <div className="box-icon box-2">
              <LooksTwoIcon />
            </div>
            <div className="box-details">
              <h4>Box 2</h4>
              <p>Cards you're learning and getting comfortable with. Review every 3 days.</p>
            </div>
          </div>

          <div className="box-explanation">
            <div className="box-icon box-3">
              <Looks3Icon />
            </div>
            <div className="box-details">
              <h4>Box 3</h4>
              <p>Cards you've mastered. Review once a week.</p>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="info-section"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <h3>Card Movement Rules</h3>

        <div className="rules-container">
          <div className="rule">
            <div className="rule-icon correct">
              <CheckCircleIcon />
            </div>
            <div className="rule-content">
              <h4>When you answer correctly</h4>
              <p>The card moves up to the next box.</p>
              <div className="rule-illustration">
                <div className="box-mini box-1">1</div>
                <ArrowUpwardIcon className="arrow" />
                <div className="box-mini box-2">2</div>
              </div>
            </div>
          </div>

          <div className="rule">
            <div className="rule-icon incorrect">
              <CancelIcon />
            </div>
            <div className="rule-content">
              <h4>When you answer incorrectly</h4>
              <p>The card moves back to Box 1, regardless of which box it was in.</p>
              <div className="rule-illustration">
                <div className="box-mini box-3">3</div>
                <ArrowDownwardIcon className="arrow" />
                <div className="box-mini box-1">1</div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="info-section"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <h3>Your Learning Goal</h3>
        <p>
          The ultimate goal of the Leitner System is to move all your flashcards to Box 3,
          indicating that you've mastered all the material. The progress bar at the top of
          the main screen shows how close you are to achieving this goal.
        </p>
        <div className="goal-illustration">
          <div className="goal-step">
            <div className="box-mini box-1">1</div>
            <p>Start here</p>
          </div>
          <ArrowUpwardIcon className="arrow" />
          <div className="goal-step">
            <div className="box-mini box-2">2</div>
            <p>Learning</p>
          </div>
          <ArrowUpwardIcon className="arrow" />
          <div className="goal-step">
            <div className="box-mini box-3">3</div>
            <p>Mastered!</p>
          </div>
        </div>
        <p className="goal-note">
          <strong>Remember:</strong> Consistency is key! Regular review sessions will help you
          move cards to Box 3 and keep them there.
        </p>
      </motion.div>

      <motion.div
        className="info-section"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <h3>Benefits of the Leitner System</h3>

        <ul className="benefits-list">
          <li>
            <strong>Efficient learning:</strong> You spend more time on difficult cards and less time on cards you know well.
          </li>
          <li>
            <strong>Spaced repetition:</strong> The increasing intervals between reviews help move information into long-term memory.
          </li>
          <li>
            <strong>Progress tracking:</strong> You can easily see your progress as cards move to higher boxes.
          </li>
          <li>
            <strong>Focused practice:</strong> The system automatically prioritizes the cards you need to work on most.
          </li>
        </ul>
      </motion.div>

      <div className="info-footer">
        <button onClick={onBack}>Back to Boxes</button>
      </div>
    </div>
  );
};

export default LeitnerInfo;
