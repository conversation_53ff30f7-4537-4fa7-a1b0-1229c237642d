import React, { createContext, useState, useContext, useEffect } from 'react';
import { useAuth } from '../../authContext.jsx';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

// Create the context
const ObjectivesContext = createContext();

// Custom hook to use the objectives context
export const useObjectives = () => {
    const context = useContext(ObjectivesContext);
    if (!context) {
        throw new Error('useObjectives must be used within an ObjectivesProvider');
    }
    return context;
};

// Provider component
export const ObjectivesProvider = ({ children }) => {
    const { user, token, loading: authLoading } = useAuth();
    const [subjects, setSubjects] = useState([]);
    const [objectives, setObjectives] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Calculate progress
    const totalObjectives = objectives.length;
    const completedObjectives = objectives.filter(obj => obj.completed).length;
    const progressPercentage = totalObjectives > 0
        ? Math.round((completedObjectives / totalObjectives) * 100)
        : 0;

    useEffect(() => {
        if (authLoading) return;
        if (!user || !token) {
            setError('Please log in to view objectives.');
            setLoading(false);
            return;
        }

        const fetchData = async () => {
            try {
                // Fetch subjects
                const subjectsResponse = await fetch(`${API_URL}/api/subjects`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
                if (!subjectsResponse.ok) {
                    const errorData = await subjectsResponse.json();
                    console.error('Subjects fetch error:', subjectsResponse.status, errorData);
                    if (subjectsResponse.status === 403) {
                        setError('Session expired. Please log in again.');
                        setLoading(false);
                        return;
                    }
                    throw new Error(errorData.message || `Failed to fetch subjects: ${subjectsResponse.status}`);
                }
                const subjectsData = await subjectsResponse.json();

                if (!subjectsData.success || subjectsData.subjects.length === 0) {
                    setSubjects([]);
                    setObjectives([]);
                    setLoading(false);
                    return;
                }

                setSubjects(subjectsData.subjects);

                // Get today's date in YYYY-MM-DD format
                const today = new Date().toISOString().split('T')[0];

                // Fetch objectives for today only
                const objectivesResponse = await fetch(`${API_URL}/api/daily_objectives?date=${today}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
                if (!objectivesResponse.ok) {
                    const errorData = await objectivesResponse.json();
                    console.error('Objectives fetch error:', objectivesResponse.status, errorData);
                    if (objectivesResponse.status === 403) {
                        setError('Session expired. Please log in again.');
                        setLoading(false);
                        return;
                    }
                    throw new Error(errorData.message || `Failed to fetch objectives: ${objectivesResponse.status}`);
                }
                const objectivesData = await objectivesResponse.json();

                if (objectivesData.success) {
                    setObjectives(objectivesData.objectives || []);
                } else {
                    setObjectives([]);
                }

                setLoading(false);
            } catch (err) {
                console.error('Fetch error:', err);
                setError(err.message);
                setLoading(false);
            }
        };

        fetchData();
    }, [user, token, authLoading]);

    const toggleObjective = async (id, completed) => {
        try {
            const response = await fetch(`${API_URL}/api/daily_objectives/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({ completed: !completed }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Toggle objective error:', response.status, errorData);
                throw new Error(errorData.message || 'Failed to update objective');
            }

            setObjectives(objectives.map(obj =>
                obj.id === id ? { ...obj, completed: !completed } : obj
            ));
        } catch (err) {
            setError(err.message);
        }
    };

    // Value to be provided to consumers
    const value = {
        subjects,
        objectives,
        loading,
        error,
        totalObjectives,
        completedObjectives,
        progressPercentage,
        toggleObjective
    };

    return (
        <ObjectivesContext.Provider value={value}>
            {children}
        </ObjectivesContext.Provider>
    );
};

export default ObjectivesContext;
