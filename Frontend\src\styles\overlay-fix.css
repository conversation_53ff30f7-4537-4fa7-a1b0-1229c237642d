/* Overlay fix for popups */
.message-popup-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 999999 !important;
    /* Extremely high z-index */
    backdrop-filter: blur(5px) !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Upload popup container fix */
.upload-popup-container {
    z-index: 1000000 !important;
    /* Even higher z-index */
    position: relative !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    margin: auto !important;
}

/* Prevent body scrolling when overlay is active */
body.overlay-active {
    overflow: hidden !important;
    position: relative !important;
    height: 100% !important;
}