@use "variables" as *;
@use "sass:color";

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Preferences Settings - macOS style
.preferences-settings {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  max-width: 900px;
  height: 100vh;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  display: flex;
  flex-direction: column;

  // Light theme
  background-color: white;
  color: $light-primary-text;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
    color: $dark-primary-text;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  }

  &.open {
    transform: translateX(0);
  }

  // Settings Header - macOS style
  .settings-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;

    // Light theme
    border-bottom: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-accent-color-2;
    }

    .back-button {
      background: none;
      border: none;
      margin-right: 1rem;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 50%;
      @include flex-center;
      @include transition;

      // Light theme
      color: $color-primary;

      // Dark theme
      .theme-dark & {
        color: $color-primary;
      }

      &:hover {
        // Light theme
        background-color: $light-accent-color-2;

        // Dark theme
        .theme-dark & {
          background-color: $dark-accent-color-2;
        }
      }
    }

    h2 {
      margin: 0;
      font-size: 1.5rem;
      display: flex;
      align-items: center;
      font-weight: 600;

      // Light theme
      color: $light-primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }

      .header-icon {
        margin-right: 1rem;
        color: $color-primary;
      }
    }
  }

  // Settings Container - macOS style
  .settings-container {
    display: flex;
    flex: 1;
    overflow: hidden;

    // Settings Sidebar - macOS style
    .settings-sidebar {
      width: 250px;
      padding: 1rem 0;
      overflow-y: auto;

      // Light theme
      background-color: $light-background;
      border-right: 1px solid $light-accent-color-2;

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($dark-background, $lightness: 5%);
        border-right: 1px solid $dark-accent-color-2;
      }

      // Custom scrollbar for light theme
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 20px;
      }

      // Custom scrollbar for dark theme
      .theme-dark &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);
      }

      // Sidebar Item - macOS style
      .sidebar-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0.75rem 1.5rem;
        background: none;
        border: none;
        text-align: left;
        cursor: pointer;
        gap: 1rem;
        @include transition;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
        }

        &:hover {
          // Light theme
          background-color: $light-accent-color-2;

          // Dark theme
          .theme-dark & {
            background-color: $dark-accent-color-2;
          }
        }

        &.active {
          // Light theme
          background-color: color.adjust($color-primary, $alpha: -0.9);
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            background-color: color.adjust($color-primary, $alpha: -0.85);
            color: $color-primary;
          }
        }
      }
    }

    // Settings Content - macOS style
    .settings-content {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;

      // Light theme
      background-color: white;

      // Dark theme
      .theme-dark & {
        background-color: $dark-background;
      }

      // Custom scrollbar for light theme
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 20px;
      }

      // Custom scrollbar for dark theme
      .theme-dark &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);
      }

      // Settings Section - macOS style
      .settings-section {
        max-width: 600px;
        margin: 0 auto;

        // Preference Group - macOS style
        .preference-group {
          margin-bottom: 2rem;

          h3 {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            font-weight: 600;

            // Light theme
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }

            .icon {
              // Light theme
              color: $color-primary;

              // Dark theme
              .theme-dark & {
                color: $color-primary;
              }
            }
          }

          // Preference Select - macOS style
          .preference-select {
            width: 100%;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 0.95rem;
            @include transition;

            // Light theme
            border: 1px solid $light-accent-color-2;
            background-color: $light-background;
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              border: 1px solid $dark-accent-color-2;
              background-color: color.adjust($dark-background, $lightness: 5%);
              color: $dark-primary-text;
            }

            &:focus {
              outline: none;

              // Light theme
              border-color: $color-primary;
              box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);

              // Dark theme
              .theme-dark & {
                border-color: $color-primary;
                box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
              }
            }
          }

          // Radio Options - macOS style
          .radio-options {
            display: flex;
            gap: 1.5rem;

            .radio-option {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              cursor: pointer;

              // Light theme
              color: $light-primary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-primary-text;
              }

              input[type="radio"] {
                // Light theme
                accent-color: $color-primary;

                // Dark theme
                .theme-dark & {
                  accent-color: $color-primary;
                }
              }
            }
          }

          // Slider Container - macOS style
          .slider-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            .preference-slider {
              width: 100%;
              height: 6px;
              border-radius: 3px;
              -webkit-appearance: none;
              appearance: none;

              // Light theme
              background: $light-accent-color-2;

              // Dark theme
              .theme-dark & {
                background: $dark-accent-color-2;
              }

              &::-webkit-slider-thumb {
                -webkit-appearance: none;
                appearance: none;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                cursor: pointer;

                // Light theme
                background: $color-primary;

                // Dark theme
                .theme-dark & {
                  background: $color-primary;
                }
              }
            }

            .slider-labels {
              display: flex;
              justify-content: space-between;
              font-size: 0.85rem;

              // Light theme
              color: $light-secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }

            .slider-value {
              text-align: center;
              font-weight: 600;

              // Light theme
              color: $light-primary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-primary-text;
              }
            }
          }

          // Toggle Option - macOS style
          .toggle-option {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;

            // Light theme
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }

            input[type="checkbox"] {
              // Light theme
              accent-color: $color-primary;

              // Dark theme
              .theme-dark & {
                accent-color: $color-primary;
              }
            }
          }
        }
      }
    }
  }


}

// Preferences Modal - macOS style
.preferences-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1100;
  display: flex;
  justify-content: center;
  align-items: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(5px);

    // Light theme
    background-color: rgba(0, 0, 0, 0.4);

    // Dark theme
    .theme-dark & {
      background-color: rgba(0, 0, 0, 0.6);
    }
  }

  .modal-content {
    position: relative;
    border-radius: 12px;
    max-width: 900px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: fadeInUp 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
      background-color: $dark-background;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      border: 1px solid $dark-accent-color-2;
    }

    // Custom scrollbar for light theme
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 20px;
    }

    // Custom scrollbar for dark theme
    .theme-dark &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

// Animation for modal content
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}