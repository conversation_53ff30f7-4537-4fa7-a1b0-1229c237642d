#!/usr/bin/env python3
"""
Script to create test chapters for subjects that don't have any
Run this if you need test data for chapter selection
"""

import os
import sys
import psycopg2
from psycopg2.extras import DictCursor

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'database': 'blueprint',
    'user': 'postgres',
    'password': 'Lukind@1956'
}

def create_test_chapters():
    """Create test chapters for subjects that don't have any"""
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor(cursor_factory=DictCursor)
        
        print("=== CREATING TEST CHAPTERS ===\n")
        
        # Get all subjects
        cursor.execute("SELECT id, name FROM subjects ORDER BY id")
        subjects = cursor.fetchall()
        
        if not subjects:
            print("No subjects found. Please create a subject first.")
            return
        
        for subject in subjects:
            subject_id = subject['id']
            subject_name = subject['name']
            
            # Check if subject already has chapters
            cursor.execute("SELECT COUNT(*) as count FROM chapters WHERE subject_id = %s", (subject_id,))
            chapter_count = cursor.fetchone()['count']
            
            if chapter_count > 0:
                print(f"Subject '{subject_name}' already has {chapter_count} chapters. Skipping.")
                continue
            
            print(f"Creating test chapters for subject '{subject_name}' (ID: {subject_id})")
            
            # Create 3 test chapters
            test_chapters = [
                ("Chapter 1", "Introduction and Fundamentals"),
                ("Chapter 2", "Core Concepts and Principles"),
                ("Chapter 3", "Advanced Topics and Applications")
            ]
            
            for chapter_number, chapter_title in test_chapters:
                cursor.execute("""
                    INSERT INTO chapters (subject_id, chapter_number, title, created_at)
                    VALUES (%s, %s, %s, NOW())
                    RETURNING id
                """, (subject_id, chapter_number, chapter_title))
                
                chapter_id = cursor.fetchone()['id']
                print(f"  Created: {chapter_number}: {chapter_title} (ID: {chapter_id})")
            
            print()
        
        # Commit changes
        conn.commit()
        print("Test chapters created successfully!")
        
        # Show final count
        cursor.execute("""
            SELECT s.name, COUNT(c.id) as chapter_count
            FROM subjects s
            LEFT JOIN chapters c ON s.id = c.subject_id
            GROUP BY s.id, s.name
            ORDER BY s.id
        """)
        results = cursor.fetchall()
        
        print("\nFinal chapter counts:")
        for result in results:
            print(f"  {result['name']}: {result['chapter_count']} chapters")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    create_test_chapters()
