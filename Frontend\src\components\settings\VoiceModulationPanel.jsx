import React, { useState, useEffect } from 'react';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import '../../styles/SettingsPanels.scss';

const VoiceModulationPanel = ({ settings }) => {
  const { 
    voiceWarmth, setVoiceWarmth,
    voiceClarity, setVoiceClarity,
    voiceSpeed, setVoiceSpeed,
    isDarkMode
  } = settings;
  
  const [previewActive, setPreviewActive] = useState(false);
  
  // Handle preview button click
  const handlePreview = () => {
    setPreviewActive(true);
    
    // Simulate voice preview for 3 seconds
    setTimeout(() => {
      setPreviewActive(false);
    }, 3000);
  };
  
  return (
    <div className={`detail-panel voice-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Voice Modulation</h2>
        <p className="panel-description">
          Customize how the AI assistant's voice sounds when speaking to you.
        </p>
      </div>
      
      <div className="settings-group">
        <div className="setting-item slider-setting">
          <div className="setting-label">
            <span>Warmth</span>
            <span className="setting-value">{voiceWarmth}%</span>
          </div>
          <div className="slider-container">
            <div className="slider-labels">
              <span>Analytical</span>
              <span>Warm</span>
            </div>
            <input 
              type="range" 
              min="0" 
              max="100" 
              value={voiceWarmth} 
              onChange={(e) => setVoiceWarmth(parseInt(e.target.value))}
              className="slider"
            />
          </div>
        </div>
        
        <div className="setting-item slider-setting">
          <div className="setting-label">
            <span>Clarity</span>
            <span className="setting-value">{voiceClarity}%</span>
          </div>
          <div className="slider-container">
            <div className="slider-labels">
              <span>Natural</span>
              <span>Precise</span>
            </div>
            <input 
              type="range" 
              min="0" 
              max="100" 
              value={voiceClarity} 
              onChange={(e) => setVoiceClarity(parseInt(e.target.value))}
              className="slider"
            />
          </div>
        </div>
        
        <div className="setting-item slider-setting">
          <div className="setting-label">
            <span>Speed</span>
            <span className="setting-value">{voiceSpeed}%</span>
          </div>
          <div className="slider-container">
            <div className="slider-labels">
              <span>Slow</span>
              <span>Fast</span>
            </div>
            <input 
              type="range" 
              min="0" 
              max="100" 
              value={voiceSpeed} 
              onChange={(e) => setVoiceSpeed(parseInt(e.target.value))}
              className="slider"
            />
          </div>
        </div>
      </div>
      
      <div className="preview-section">
        <button 
          className={`preview-button ${previewActive ? 'active' : ''}`}
          onClick={handlePreview}
          disabled={previewActive}
        >
          <VolumeUpIcon />
          <span>{previewActive ? 'Playing...' : 'Preview Voice'}</span>
        </button>
        
        {previewActive && (
          <div className="voice-visualizer">
            {[...Array(20)].map((_, i) => (
              <div 
                key={i} 
                className="visualizer-bar"
                style={{ 
                  height: `${Math.random() * 50 + 10}px`,
                  animationDelay: `${i * 0.05}s`
                }}
              ></div>
            ))}
          </div>
        )}
      </div>
      
      <div className="info-box">
        <p>
          Voice settings affect how the AI sounds when using text-to-speech. 
          These settings don't affect the AI's personality or knowledge.
        </p>
      </div>
    </div>
  );
};

export default VoiceModulationPanel;
