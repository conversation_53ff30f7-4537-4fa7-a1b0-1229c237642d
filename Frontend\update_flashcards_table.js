import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'Mapalo0997',
    database: process.env.DB_NAME || 'blueprint',
    port: process.env.DB_PORT || 5432,
});

async function updateFlashcardsTable() {
    try {
        console.log('Connecting to database...');
        await pool.connect();
        console.log('Connected to database');

        // Check if the flashcards table exists
        const tableCheck = await pool.query(`
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'flashcards'
        `);

        if (tableCheck.rows.length === 0) {
            console.log('Flashcards table does not exist. Creating it...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS flashcards (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    question TEXT NOT NULL,
                    answer TEXT NOT NULL,
                    type VARCHAR(50) DEFAULT 'basic',
                    system flashcard_system DEFAULT 'STIC',
                    box_level INTEGER DEFAULT 1,
                    last_reviewed TIMESTAMP,
                    next_review_date TIMESTAMP,
                    color VARCHAR(50) DEFAULT '#ffffff',
                    difficult BOOLEAN DEFAULT FALSE,
                    review_count INTEGER DEFAULT 0,
                    success_rate FLOAT DEFAULT 0,
                    tags VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            `);
            console.log('Flashcards table created successfully');
        } else {
            console.log('Flashcards table exists. Checking columns...');

            // Check if chapter_id column exists
            const chapterIdCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'chapter_id'
            `);

            if (chapterIdCheck.rows.length === 0) {
                console.log('Adding chapter_id column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE
                `);
                console.log('chapter_id column added successfully');
            } else {
                console.log('chapter_id column already exists');
            }

            // Check if type column exists
            const typeCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'type'
            `);

            if (typeCheck.rows.length === 0) {
                console.log('Adding type column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN type VARCHAR(50) DEFAULT 'basic'
                `);
                console.log('type column added successfully');
            } else {
                console.log('type column already exists');
            }

            // Check if last_reviewed column exists
            const lastReviewedCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'last_reviewed'
            `);

            if (lastReviewedCheck.rows.length === 0) {
                console.log('Adding last_reviewed column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN last_reviewed TIMESTAMP
                `);
                console.log('last_reviewed column added successfully');
            } else {
                console.log('last_reviewed column already exists');
            }

            // Check if next_review_date column exists
            const nextReviewDateCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'next_review_date'
            `);

            if (nextReviewDateCheck.rows.length === 0) {
                console.log('Adding next_review_date column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN next_review_date TIMESTAMP
                `);
                console.log('next_review_date column added successfully');
            } else {
                console.log('next_review_date column already exists');
            }

            // Check if color column exists
            const colorCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'color'
            `);

            if (colorCheck.rows.length === 0) {
                console.log('Adding color column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN color VARCHAR(50) DEFAULT '#ffffff'
                `);
                console.log('color column added successfully');
            } else {
                console.log('color column already exists');
            }

            // Check if difficult column exists
            const difficultCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'difficult'
            `);

            if (difficultCheck.rows.length === 0) {
                console.log('Adding difficult column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN difficult BOOLEAN DEFAULT FALSE
                `);
                console.log('difficult column added successfully');
            } else {
                console.log('difficult column already exists');
            }

            // Check if review_count column exists
            const reviewCountCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'review_count'
            `);

            if (reviewCountCheck.rows.length === 0) {
                console.log('Adding review_count column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN review_count INTEGER DEFAULT 0
                `);
                console.log('review_count column added successfully');
            } else {
                console.log('review_count column already exists');
            }

            // Check if success_rate column exists
            const successRateCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'flashcards'
                AND column_name = 'success_rate'
            `);

            if (successRateCheck.rows.length === 0) {
                console.log('Adding success_rate column to flashcards table...');
                await pool.query(`
                    ALTER TABLE flashcards
                    ADD COLUMN success_rate FLOAT DEFAULT 0
                `);
                console.log('success_rate column added successfully');
            } else {
                console.log('success_rate column already exists');
            }
        }

        console.log('Flashcards table update completed successfully');
    } catch (error) {
        console.error('Error updating flashcards table:', error.message, error.stack);
    } finally {
        await pool.end();
        console.log('Database connection closed');
    }
}

updateFlashcardsTable();
