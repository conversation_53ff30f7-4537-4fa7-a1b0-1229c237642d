@use "variables" as *;
@use "sass:color";

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Advanced Profile Settings - macOS style
.advanced-profile-settings {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  max-width: 900px;
  height: 100vh;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  display: flex;
  flex-direction: column;

  // Light theme
  background-color: white;
  color: $light-primary-text;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
    color: $dark-primary-text;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  }

  &.open {
    transform: translateX(0);
  }

  // Settings Header - macOS style
  .settings-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;

    // Light theme
    border-bottom: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-accent-color-2;
    }

    .back-button {
      background: none;
      border: none;
      margin-right: 1rem;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 50%;
      @include flex-center;
      @include transition;

      // Light theme
      color: $color-primary;

      // Dark theme
      .theme-dark & {
        color: $color-primary;
      }

      &:hover {
        // Light theme
        background-color: $light-accent-color-2;

        // Dark theme
        .theme-dark & {
          background-color: $dark-accent-color-2;
        }
      }
    }

    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;

      // Light theme
      color: $light-primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }
  }

  // Settings Container - macOS style
  .settings-container {
    display: flex;
    flex: 1;
    overflow: hidden;

    // Settings Sidebar - macOS style
    .settings-sidebar {
      width: 250px;
      padding: 1rem 0;
      overflow-y: auto;

      // Light theme
      background-color: $light-background;
      border-right: 1px solid $light-accent-color-2;

      // Dark theme
      .theme-dark & {
        background-color: color.adjust($dark-background, $lightness: 5%);
        border-right: 1px solid $dark-accent-color-2;
      }

      // Custom scrollbar for light theme
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 20px;
      }

      // Custom scrollbar for dark theme
      .theme-dark &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);
      }

      // Sidebar Item - macOS style
      .sidebar-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0.75rem 1.5rem;
        background: none;
        border: none;
        text-align: left;
        cursor: pointer;
        @include transition;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
        }

        .sidebar-icon {
          margin-right: 1rem;

          // Light theme
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            color: $color-primary;
          }
        }

        &:hover {
          // Light theme
          background-color: $light-accent-color-2;

          // Dark theme
          .theme-dark & {
            background-color: $dark-accent-color-2;
          }
        }

        &.active {
          // Light theme
          background-color: color.adjust($color-primary, $alpha: -0.9);
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            background-color: color.adjust($color-primary, $alpha: -0.85);
            color: $color-primary;
          }

          .sidebar-icon {
            // Light theme
            color: $color-primary;

            // Dark theme
            .theme-dark & {
              color: $color-primary;
            }
          }
        }
      }
    }

    // Settings Content - macOS style
    .settings-content {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;

      // Light theme
      background-color: white;

      // Dark theme
      .theme-dark & {
        background-color: $dark-background;
      }

      // Custom scrollbar for light theme
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 20px;
      }

      // Custom scrollbar for dark theme
      .theme-dark &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);
      }

      // Settings Section - macOS style
      .settings-section {
        max-width: 600px;
        margin: 0 auto;

        h3 {
          margin: 1.5rem 0 1rem;
          font-size: 1.1rem;
          font-weight: 600;

          // Light theme
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        // Theme Options - macOS style
        .theme-options {
          display: flex;
          gap: 1rem;
          margin-bottom: 2rem;

          .theme-option {
            flex: 1;
            padding: 1rem;
            border-radius: 12px;
            cursor: pointer;
            @include transition;
            text-align: center;

            // Light theme
            border: 2px solid $light-accent-color-2;

            // Dark theme
            .theme-dark & {
              border: 2px solid $dark-accent-color-2;
            }

            &:hover {
              // Light theme
              border-color: color.adjust($color-primary, $alpha: -0.5);

              // Dark theme
              .theme-dark & {
                border-color: color.adjust($color-primary, $alpha: -0.5);
              }
            }

            &.active {
              // Light theme
              border-color: $color-primary;
              background-color: color.adjust($color-primary, $alpha: -0.95);

              // Dark theme
              .theme-dark & {
                border-color: $color-primary;
                background-color: color.adjust($color-primary, $alpha: -0.9);
              }
            }

            .light-theme-preview,
            .dark-theme-preview,
            .system-theme-preview {
              height: 80px;
              border-radius: 8px;
              margin-bottom: 0.75rem;
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

              // Dark theme
              .theme-dark & {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
              }
            }

            .light-theme-preview {
              background: linear-gradient(to right, #f8f9fa 50%, white 50%);
            }

            .dark-theme-preview {
              background: linear-gradient(to right, #111827 50%, #1a1a2e 50%);
            }

            .system-theme-preview {
              background: linear-gradient(to right, #f8f9fa 50%, #111827 50%);
              position: relative;

              &:after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.05) 100%);
                border-radius: 8px;
              }
            }
          }
        }

        // Color Palette - macOS style
        .color-palette {
          display: flex;
          gap: 1rem;
          margin-bottom: 2rem;

          .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
            @include transition;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            // Dark theme
            .theme-dark & {
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            &:hover {
              transform: scale(1.1);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

              // Dark theme
              .theme-dark & {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
              }
            }

            &:active {
              transform: scale(1);
            }
          }
        }

        // Radio Group - macOS style
        .radio-group {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-bottom: 2rem;

          .radio-option {
            display: flex;
            flex-direction: column;
            padding: 1rem;
            border-radius: 12px;
            cursor: pointer;
            @include transition;

            // Light theme
            border: 1px solid $light-accent-color-2;
            background-color: $light-background;

            // Dark theme
            .theme-dark & {
              border: 1px solid $dark-accent-color-2;
              background-color: color.adjust($dark-background, $lightness: 5%);
            }

            &:hover {
              // Light theme
              border-color: color.adjust($color-primary, $alpha: -0.5);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

              // Dark theme
              .theme-dark & {
                border-color: color.adjust($color-primary, $alpha: -0.5);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
              }
            }

            input[type="radio"] {
              // Light theme
              accent-color: $color-primary;

              // Dark theme
              .theme-dark & {
                accent-color: $color-primary;
              }
            }

            span {
              // Light theme
              color: $light-primary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-primary-text;
              }
            }

            .option-description {
              margin: 0.5rem 0 0;
              font-size: 0.85rem;

              // Light theme
              color: $light-secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }
          }
        }

        // Dropdown Select - macOS style
        .dropdown-select {
          width: 100%;
          padding: 12px 16px;
          border-radius: 8px;
          margin-bottom: 2rem;
          cursor: pointer;
          font-size: 0.95rem;
          @include transition;

          // Light theme
          border: 1px solid $light-accent-color-2;
          background-color: $light-background;
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            border: 1px solid $dark-accent-color-2;
            background-color: color.adjust($dark-background, $lightness: 5%);
            color: $dark-primary-text;
          }

          &:focus {
            outline: none;

            // Light theme
            border-color: $color-primary;
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);

            // Dark theme
            .theme-dark & {
              border-color: $color-primary;
              box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
            }
          }
        }

        // Slider Container - macOS style
        .slider-container {
          margin-bottom: 2rem;

          .slider {
            width: 100%;
            margin: 1rem 0;
            height: 6px;
            border-radius: 3px;
            -webkit-appearance: none;
            appearance: none;

            // Light theme
            background: $light-accent-color-2;

            // Dark theme
            .theme-dark & {
              background: $dark-accent-color-2;
            }

            &::-webkit-slider-thumb {
              -webkit-appearance: none;
              appearance: none;
              width: 18px;
              height: 18px;
              border-radius: 50%;
              cursor: pointer;

              // Light theme
              background: $color-primary;

              // Dark theme
              .theme-dark & {
                background: $color-primary;
              }
            }
          }

          .slider-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;

            // Light theme
            color: $light-secondary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .slider-value {
            text-align: center;
            font-weight: 600;

            // Light theme
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }
          }
        }

        // Toggle Option - macOS style
        .toggle-option {
          display: flex;
          align-items: center;
          margin-bottom: 1rem;

          // Light theme
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }

          .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            margin-right: 1rem;

            input {
              opacity: 0;
              width: 0;
              height: 0;

              &:checked+.slider {
                // Light theme
                background-color: $color-primary;

                // Dark theme
                .theme-dark & {
                  background-color: $color-primary;
                }
              }

              &:checked+.slider:before {
                transform: translateX(26px);
              }
            }

            .slider {
              position: absolute;
              cursor: pointer;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              @include transition;

              // Light theme
              background-color: $light-accent-color-2;

              // Dark theme
              .theme-dark & {
                background-color: $dark-accent-color-2;
              }

              &:before {
                position: absolute;
                content: "";
                height: 16px;
                width: 16px;
                left: 4px;
                bottom: 4px;
                background-color: white;
                @include transition;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                // Dark theme
                .theme-dark & {
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }
              }

              &.round {
                border-radius: 34px;

                &:before {
                  border-radius: 50%;
                }
              }
            }
          }

          .toggle-label {
            display: flex;
            flex-direction: column;

            // Light theme
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }

            .option-description {
              font-size: 0.85rem;
              margin-top: 0.25rem;

              // Light theme
              color: $light-secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }
          }
        }

        // Shortcut List - macOS style
        .shortcut-list {
          border-radius: 12px;
          overflow: hidden;

          // Light theme
          border: 1px solid $light-accent-color-2;

          // Dark theme
          .theme-dark & {
            border: 1px solid $dark-accent-color-2;
          }

          .shortcut-item {
            display: flex;
            justify-content: space-between;
            padding: 1rem;

            // Light theme
            color: $light-primary-text;
            border-bottom: 1px solid $light-accent-color-2;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
              border-bottom: 1px solid $dark-accent-color-2;
            }

            &:last-child {
              border-bottom: none;
            }

            .shortcut-key {
              padding: 0.25rem 0.5rem;
              border-radius: 4px;
              font-family: monospace;
              font-weight: 600;

              // Light theme
              background-color: $light-accent-color-2;
              color: $light-primary-text;

              // Dark theme
              .theme-dark & {
                background-color: $dark-accent-color-2;
                color: $dark-primary-text;
              }
            }
          }
        }

        // Time Picker - macOS style
        .time-picker {
          display: flex;
          align-items: center;
          gap: 1rem;

          // Light theme
          color: $light-primary-text;

          // Dark theme
          .theme-dark & {
            color: $dark-primary-text;
          }

          select {
            padding: 8px 12px;
            border-radius: 8px;
            @include transition;

            // Light theme
            border: 1px solid $light-accent-color-2;
            background-color: $light-background;
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              border: 1px solid $dark-accent-color-2;
              background-color: color.adjust($dark-background, $lightness: 5%);
              color: $dark-primary-text;
            }

            &:focus {
              outline: none;

              // Light theme
              border-color: $color-primary;
              box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);

              // Dark theme
              .theme-dark & {
                border-color: $color-primary;
                box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
              }
            }
          }
        }

        // Danger Button - macOS style
        .danger-button {
          background-color: transparent;
          padding: 12px 16px;
          border-radius: 8px;
          cursor: pointer;
          margin-bottom: 1rem;
          display: block;
          width: 100%;
          font-size: 0.95rem;
          font-weight: 500;
          @include transition;

          // Light theme
          color: $color-error;
          border: 1px solid $color-error;

          // Dark theme
          .theme-dark & {
            color: $color-error;
            border: 1px solid $color-error;
          }

          &:hover {
            transform: translateY(-2px);

            // Light theme
            background-color: color.adjust($color-error, $alpha: -0.95);
            box-shadow: 0 4px 8px rgba($color-error, 0.1);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($color-error, $alpha: -0.9);
              box-shadow: 0 4px 8px rgba($color-error, 0.2);
            }
          }

          &:active {
            transform: translateY(0);
          }
        }

        // Secondary Button - macOS style
        .secondary-button {
          background-color: transparent;
          padding: 12px 16px;
          border-radius: 8px;
          cursor: pointer;
          margin-bottom: 1rem;
          display: block;
          width: 100%;
          font-size: 0.95rem;
          font-weight: 500;
          @include transition;

          // Light theme
          color: $color-primary;
          border: 1px solid $color-primary;

          // Dark theme
          .theme-dark & {
            color: $color-primary;
            border: 1px solid $color-primary;
          }

          &:hover {
            transform: translateY(-2px);

            // Light theme
            background-color: color.adjust($color-primary, $alpha: -0.95);
            box-shadow: 0 4px 8px rgba($color-primary, 0.1);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($color-primary, $alpha: -0.9);
              box-shadow: 0 4px 8px rgba($color-primary, 0.2);
            }
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

// Animation for UI elements
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}