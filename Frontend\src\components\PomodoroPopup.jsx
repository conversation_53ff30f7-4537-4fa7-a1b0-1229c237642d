import React, { useRef, useEffect } from 'react';
import { usePomodoroContext } from '../contexts/pomodoroContext';
import PomodoroTimer from './tools/list/PomodoroTimer';
import '../styles/PomodoroPopup.scss';

/**
 * Global Pomodoro Popup Component
 * A popup that can be opened from anywhere in the app to show the full Pomodoro timer
 */
const PomodoroPopup = () => {
    const {
        showPomodoroPopup,
        setShowPomodoroPopup
    } = usePomodoroContext();

    const modalRef = useRef(null);

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowPomodoroPopup(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowPomodoroPopup(false);
            }
        };

        if (showPomodoroPopup) {
            document.addEventListener('mousedown', handleClickOutside);
            document.addEventListener('keydown', handleKeyDown);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [showPomodoroPopup, setShowPomodoroPopup]);

    if (!showPomodoroPopup) return null;

    return (
        <div className="pomodoro-popup-overlay">
            <div className="pomodoro-popup-content" ref={modalRef}>
                <div className="pomodoro-popup-header">
                    <h2>Pomodoro Timer</h2>
                    <button
                        className="close-button"
                        onClick={() => setShowPomodoroPopup(false)}
                        aria-label="Close Pomodoro Timer"
                    >
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                        </svg>
                    </button>
                </div>
                <div className="pomodoro-popup-body">
                    <PomodoroTimer />
                </div>
            </div>
        </div>
    );
};

export default PomodoroPopup;
