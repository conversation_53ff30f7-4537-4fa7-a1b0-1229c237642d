import React, { useState, useEffect, useRef } from 'react';
import GaugeComponent from 'react-gauge-component';
import '../../styles/Dashboard.scss';

const MasteryTracker = () => {
    const [showModal, setShowModal] = useState(false);
    const modalRef = useRef(null);

    // PCLHdM learning stages
    const learningStages = [
        { id: 'p', name: 'Priming', color: '#ff3b30', description: 'First exposure to new information' },
        { id: 'c', name: 'Comprehension', color: '#ff9500', description: 'Beginning to understand concepts' },
        { id: 'l', name: 'Learning', color: '#ffcc00', description: 'Testing knowledge and applying it' },
        { id: 'hd', name: 'Higher Development', color: '#34c759', description: 'Making connections and solving complex problems' },
        { id: 'm', name: 'Mastery', color: '#007aff', description: 'Complete fluency and expertise' }
    ];

    // Mock subject data with learning stage progress
    const subjects = {
        mathematics: {
            name: 'Mathematics',
            currentStage: 'l', // Learning
            progress: 65, // Progress within current stage (0-100)
            description: 'Algebra, Calculus, and Statistics',
            stageHistory: [
                { stage: 'p', completedAt: '2023-09-01' },
                { stage: 'c', completedAt: '2023-09-15' },
                { stage: 'l', completedAt: null }, // Current stage (in progress)
            ],
        },
        physics: {
            name: 'Physics',
            currentStage: 'hd', // Higher Development
            progress: 90, // Progress within current stage (0-100)
            description: 'Mechanics, Thermodynamics, and Electromagnetism',
            stageHistory: [
                { stage: 'p', completedAt: '2023-08-10' },
                { stage: 'c', completedAt: '2023-08-25' },
                { stage: 'l', completedAt: '2023-09-20' },
                { stage: 'hd', completedAt: null }, // Current stage (in progress)
            ],
        },
        programming: {
            name: 'Programming',
            currentStage: 'm', // Mastery
            progress: 100, // Progress within current stage (0-100)
            description: 'Python, JavaScript, and Data Structures',
            stageHistory: [
                { stage: 'p', completedAt: '2023-07-05' },
                { stage: 'c', completedAt: '2023-07-20' },
                { stage: 'l', completedAt: '2023-08-15' },
                { stage: 'hd', completedAt: '2023-09-10' },
                { stage: 'm', completedAt: null }, // Current stage (in progress)
            ],
        },
    };

    // Helper function to get stage index
    const getStageIndex = (stageId) => {
        return learningStages.findIndex(stage => stage.id === stageId);
    };

    // Helper function to calculate overall progress for a subject
    const calculateOverallProgress = (subject) => {
        const currentStageIndex = getStageIndex(subject.currentStage);
        const completedStages = currentStageIndex; // Number of fully completed stages
        const totalStages = learningStages.length;
        const stageProgress = subject.progress / 100; // Convert to 0-1 range

        // Each stage is worth 20% (for 5 stages), plus partial progress in current stage
        return ((completedStages / totalStages) + (stageProgress / totalStages)) * 100;
    };

    // Calculate overall progress across all subjects (for future use)
    // const calculateTotalProgress = () => {
    //     const subjectEntries = Object.entries(subjects);
    //     if (subjectEntries.length === 0) return 0;
    //
    //     const totalProgress = subjectEntries.reduce((sum, [_, subject]) => {
    //         return sum + calculateOverallProgress(subject);
    //     }, 0);
    //
    //     return totalProgress / subjectEntries.length;
    // };

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowModal(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowModal(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    // Render gauge for a subject
    const renderGauge = (subject) => {
        const stageIndex = getStageIndex(subject.currentStage);
        const stageColor = learningStages[stageIndex].color;
        const overallProgress = calculateOverallProgress(subject);

        // Create color array for the gauge with enhanced glow effect
        // We'll use a gradient that transitions through all PCLHdM stages
        const colorArray = [
            '#ff3b30', // P - Priming (Red)
            '#ff9500', // C - Comprehension (Orange)
            '#ffcc00', // L - Learning (Yellow)
            '#34c759', // Hd - Higher Development (Green)
            '#007aff'  // M - Mastery (Blue)
        ];

        // Create sub-arcs for each learning stage with exact boundaries
        const subArcs = [
            { limit: 20, color: colorArray[0] },  // P: 0-20%
            { limit: 40, color: colorArray[1] },  // C: 20-40%
            { limit: 60, color: colorArray[2] },  // L: 40-60%
            { limit: 80, color: colorArray[3] },  // Hd: 60-80%
            { limit: 100, color: colorArray[4] }, // M: 80-100%
        ];

        // Create tick labels for each learning stage
        const ticks = [
            { value: 20, label: 'P' },
            { value: 40, label: 'C' },
            { value: 60, label: 'L' },
            { value: 80, label: 'HD' },
            { value: 100, label: 'M' }
        ];

        return (
            <div className="mastery-gauge-container">
                <div className="gauge-and-legend-wrapper">
                    <div className="gauge-wrapper">
                        <GaugeComponent
                            id={`gauge-component-${subject.name}`}
                            className="futuristic-gauge pclhdm-gauge"
                            value={Math.round(overallProgress)}
                            type="radial"
                            labels={{
                                tickLabels: {
                                    type: "outer",
                                    ticks,
                                    fontSize: '12px',
                                    color: '#ffffff'
                                },
                                valueLabel: {
                                    formatTextValue: value => `${value}%`,
                                    style: {
                                        fontSize: '28px',
                                        fill: stageColor,
                                        textShadow: `0 0 10px ${stageColor}, 0 0 20px ${stageColor}`
                                    }
                                }
                            }}
                            arc={{
                                colorArray,
                                subArcs,
                                padding: 0.03,
                                width: 0.3,
                                gradient: true
                            }}
                            pointer={{
                                elastic: true,
                                animationDelay: 0,
                                color: stageColor,
                                type: 'needle',
                                width: 5,
                                length: 0.8
                            }}
                        />
                        {/* Stage name inside the gauge */}
                        <div className="inner-stage-indicator" style={{ color: stageColor }}>
                            {learningStages[stageIndex].name}
                        </div>
                    </div>

                    {/* Legend on the right side */}
                    <div className="pclhdm-legend vertical">
                        {learningStages.map((stage, index) => (
                            <div
                                key={stage.id}
                                className={`legend-item ${index === stageIndex ? 'active' : ''}`}
                                style={{
                                    '--stage-color': stage.color,
                                    opacity: index === stageIndex ? 1 : 0.6
                                }}
                            >
                                <span className="legend-dot"></span>
                                <span className="legend-label">{stage.id.toUpperCase()}</span>
                            </div>
                        ))}
                    </div>
                </div>

                <span className="tooltip">{Math.round(overallProgress)}% complete - {learningStages[stageIndex].description}</span>
            </div>
        );
    };

    return (
        <>
            <div
                className="mastery-card"
                onClick={() => setShowModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
                aria-label="Open Mastery Tracker"
            >
                <h2 className="card-title">PCLHdM Mastery Tracker</h2>
                <div className="mastery-progress-container">
                    {Object.entries(subjects).slice(0, 1).map(([key, subject]) => (
                        <div key={key} className="mastery-progress-item">

                            {renderGauge(subject)}

                        </div>
                    ))}
                </div>
            </div>
            {showModal && (
                <div className="modal-overlay">
                    <div className="modal-window" ref={modalRef} role="dialog" aria-labelledby="modal-title">
                        <div className="modal-header">
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowModal(false)}
                                    aria-label="Close"
                                ></button>
                                <button className="traffic-light yellow" disabled aria-label="Minimize"></button>
                                <button className="traffic-light green" disabled aria-label="Maximize"></button>
                            </div>
                            <h2 id="modal-title" className="modal-title">
                                PCLHdM Mastery Tracker
                            </h2>
                        </div>
                        <div className="modal-content">
                            <div className="pclhdm-explanation">
                                <h3>The PCLHdM Learning Journey</h3>
                                <p>Track your progress through the five stages of learning mastery:</p>
                                <div className="stages-legend">
                                    {learningStages.map(stage => (
                                        <div key={stage.id} className="stage-legend-item">
                                            <div className="stage-color" style={{ backgroundColor: stage.color }}></div>
                                            <div className="stage-info">
                                                <strong>{stage.name} ({stage.id.toUpperCase()})</strong>
                                                <p>{stage.description}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            <div className="subjects-progress">
                                <h3>Your Subjects</h3>
                                {Object.entries(subjects).map(([key, subject]) => (
                                    <div key={key} className="subject-progress-item">
                                        <div className="subject-header">
                                            <h4>{subject.name}</h4>
                                            <p>{subject.description}</p>
                                        </div>
                                        {renderGauge(subject)}
                                        <div className="stage-history">
                                            <h5>Progress History</h5>
                                            <ul>
                                                {subject.stageHistory.map((history, index) => (
                                                    <li key={index} className={history.completedAt ? 'completed' : 'in-progress'}>
                                                        <span className="stage-name">
                                                            {learningStages.find(s => s.id === history.stage).name}
                                                        </span>
                                                        {history.completedAt ?
                                                            <span className="completion-date">Completed on {new Date(history.completedAt).toLocaleDateString()}</span> :
                                                            <span className="in-progress-label">In Progress ({subject.progress}%)</span>
                                                        }
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button className="close-button" onClick={() => setShowModal(false)}>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default MasteryTracker;