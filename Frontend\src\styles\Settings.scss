@use "variables" as *;
@use "sass:color";

// Settings Component Styles
.settings-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $window-background;
  color: $primary-text;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
  
  // Dark mode
  &.theme-dark {
    background-color: $dark-window-background;
    color: $dark-primary-text;
  }
  
  // Header
  .settings-header {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid $border-color;
    background-color: $window-background;
    height: 60px;
    
    // Dark mode
    .theme-dark & {
      background-color: $dark-window-background;
      border-color: $dark-border-color;
    }
    
    .back-button {
      background: transparent;
      border: none;
      color: $system-blue;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.5rem;
      margin-right: 1rem;
      border-radius: 50%;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: rgba($system-blue, 0.1);
      }
      
      // Dark mode
      .theme-dark & {
        color: $system-blue;
        
        &:hover {
          background-color: rgba($system-blue, 0.2);
        }
      }
    }
    
    h1 {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
    }
  }
  
  // Main content area
  .settings-content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }
  
  // First column - Categories
  .settings-categories {
    width: 80px;
    border-right: 1px solid $border-color;
    display: flex;
    flex-direction: column;
    background-color: $secondary-background;
    
    // Dark mode
    .theme-dark & {
      background-color: $dark-secondary-background;
      border-color: $dark-border-color;
    }
    
    .category-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 1rem 0.5rem;
      cursor: pointer;
      transition: all 0.2s ease;
      color: $tertiary-text;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 3px;
        background-color: transparent;
        transition: all 0.2s ease;
      }
      
      &:hover {
        color: $secondary-text;
        background-color: rgba($system-blue, 0.05);
      }
      
      &.active {
        color: $system-blue;
        
        &::after {
          background-color: $system-blue;
        }
      }
      
      // Dark mode
      .theme-dark & {
        color: $dark-tertiary-text;
        
        &:hover {
          color: $dark-secondary-text;
          background-color: rgba($system-blue, 0.1);
        }
        
        &.active {
          color: $system-blue;
        }
      }
      
      .category-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }
      
      .category-label {
        font-size: 0.75rem;
        text-align: center;
        font-weight: 500;
      }
    }
  }
  
  // Second column - Sections
  .settings-sections {
    width: 220px;
    border-right: 1px solid $border-color;
    overflow-y: auto;
    background-color: $window-background;
    
    // Dark mode
    .theme-dark & {
      background-color: $dark-window-background;
      border-color: $dark-border-color;
    }
    
    .section-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 1rem;
      cursor: pointer;
      transition: all 0.2s ease;
      color: $secondary-text;
      border-radius: 8px;
      margin: 0.5rem;
      
      &:hover {
        background-color: rgba($system-blue, 0.05);
      }
      
      &.active {
        background-color: rgba($system-blue, 0.1);
        color: $system-blue;
      }
      
      // Dark mode
      .theme-dark & {
        color: $dark-secondary-text;
        
        &:hover {
          background-color: rgba($system-blue, 0.1);
        }
        
        &.active {
          background-color: rgba($system-blue, 0.15);
          color: $system-blue;
        }
      }
      
      .section-icon {
        margin-right: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .section-label {
        font-size: 0.9rem;
        font-weight: 500;
      }
    }
  }
  
  // Third column - Detail Panel
  .settings-details {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    background-color: $window-background;
    
    // Dark mode
    .theme-dark & {
      background-color: $dark-window-background;
    }
  }
  
  // Action bar at the bottom
  .settings-action-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.75rem;
    border-top: 1px solid $border-color;
    background-color: $window-background;
    
    // Dark mode
    .theme-dark & {
      background-color: $dark-window-background;
      border-color: $dark-border-color;
    }
    
    .action-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 1rem;
      margin: 0 0.5rem;
      border-radius: 6px;
      color: $system-blue;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: rgba($system-blue, 0.1);
      }
      
      span {
        margin-left: 0.5rem;
        font-size: 0.9rem;
        font-weight: 500;
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .settings-container {
    .settings-content {
      flex-direction: column;
    }
    
    .settings-categories {
      width: 100%;
      height: auto;
      flex-direction: row;
      overflow-x: auto;
      border-right: none;
      border-bottom: 1px solid $border-color;
      
      .category-item {
        flex-direction: row;
        padding: 0.75rem 1rem;
        
        &::after {
          width: 100%;
          height: 3px;
          bottom: 0;
          top: auto;
        }
        
        .category-icon {
          margin-bottom: 0;
          margin-right: 0.5rem;
        }
      }
    }
    
    .settings-sections {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid $border-color;
    }
  }
}
