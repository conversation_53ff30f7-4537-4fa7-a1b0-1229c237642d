import React, { createContext, useState, useContext, useEffect, useRef } from 'react';
import axios from 'axios';
import { useAuth } from '../../authContext';

const PomodoroContext = createContext();

export const usePomodoroContext = () => useContext(PomodoroContext);

export const PomodoroProvider = ({ children }) => {
    const { token } = useAuth();
    const [isRunning, setIsRunning] = useState(false);
    const [isWorkPhase, setIsWorkPhase] = useState(true);
    const [workDuration, setWorkDuration] = useState(25 * 60); // 25 minutes in seconds
    const [breakDuration, setBreakDuration] = useState(5 * 60); // 5 minutes in seconds
    const [timeLeft, setTimeLeft] = useState(25 * 60); // Current timer in seconds
    const [sessionCount, setSessionCount] = useState(0);
    const [history, setHistory] = useState([]);
    const [showMiniTimer, setShowMiniTimer] = useState(false);
    const [showPomodoroPopup, setShowPomodoroPopup] = useState(false);
    const [subjects, setSubjects] = useState([]);
    const [selectedSubject, setSelectedSubject] = useState('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Use global audio elements
    const audioRef = useRef(null);
    const tickSoundRef = useRef(null);

    const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

    // Set up audio refs
    useEffect(() => {
        audioRef.current = document.getElementById('pomodoro-complete');
        tickSoundRef.current = document.getElementById('pomodoro-tick');

        // Set volume for tick sound
        if (tickSoundRef.current) {
            tickSoundRef.current.volume = 0.2;
        }
    }, []);

    // Format time in MM:SS
    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    // Load pomodoro settings and history from API
    useEffect(() => {
        const fetchPomodoroData = async () => {
            if (!token) return;

            setLoading(true);
            try {
                // Fetch pomodoro data
                const pomodoroResponse = await axios.get(
                    `${API_URL}/api/pomodoro`,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );

                if (pomodoroResponse.data.success) {
                    const settings = pomodoroResponse.data.settings;
                    if (settings) {
                        setWorkDuration(settings.duration_minutes * 60);
                        setBreakDuration(settings.break_duration_minutes * 60);
                        setSessionCount(settings.sessions_completed);

                        // Only set timeLeft if timer is not running
                        if (!isRunning) {
                            setTimeLeft(settings.duration_minutes * 60);
                        }
                    }

                    // Convert API sessions to history format
                    const sessions = pomodoroResponse.data.sessions || [];
                    const formattedHistory = sessions.map(session => ({
                        type: session.type === 'work' ? 'Work' : 'Break',
                        duration: session.duration_minutes * 60,
                        completed: session.completed,
                        completedAt: new Date(session.created_at).toLocaleString()
                    }));

                    setHistory(formattedHistory);
                }

                // Fetch subjects for dropdown
                const subjectsResponse = await axios.get(
                    `${API_URL}/api/subjects`,
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );

                if (subjectsResponse.data.success) {
                    setSubjects(subjectsResponse.data.subjects || []);
                }

                setError(null);
            } catch (err) {
                console.error('Error fetching pomodoro data:', err);
                setError('Failed to load pomodoro data. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        fetchPomodoroData();
    }, [token, API_URL]);

    // Timer logic
    useEffect(() => {
        let timer;
        if (isRunning && timeLeft > 0) {
            timer = setInterval(() => {
                setTimeLeft((prev) => prev - 1);

                // Play tick sound every second if timer is running
                if (tickSoundRef.current && isRunning) {
                    tickSoundRef.current.currentTime = 0;
                    tickSoundRef.current.volume = 0.2;
                    tickSoundRef.current.play().catch(error => console.error('Tick sound error:', error));
                }
            }, 1000);
        } else if (timeLeft === 0) {
            // Play completion sound
            if (audioRef.current) {
                audioRef.current.play().catch((error) => console.error('Audio play error:', error));
            }

            if (isWorkPhase) {
                // Save completed work session
                saveSession('work', workDuration, true);

                // Update history
                setHistory((prev) => [
                    {
                        type: 'Work',
                        duration: workDuration,
                        completed: true,
                        completedAt: new Date().toLocaleString()
                    },
                    ...prev
                ]);

                // Update session count
                setSessionCount((prev) => prev + 1);

                // Switch to break phase
                setTimeLeft(breakDuration);
                setIsWorkPhase(false);
            } else {
                // Save completed break session
                saveSession('break', breakDuration, true);

                // Update history
                setHistory((prev) => [
                    {
                        type: 'Break',
                        duration: breakDuration,
                        completed: true,
                        completedAt: new Date().toLocaleString()
                    },
                    ...prev
                ]);

                // Switch to work phase but don't auto-start
                setTimeLeft(workDuration);
                setIsWorkPhase(true);
                setIsRunning(false);
            }
        }
        return () => clearInterval(timer);
    }, [isRunning, timeLeft, isWorkPhase, workDuration, breakDuration]);

    // Save session to API
    const saveSession = async (type, duration, completed) => {
        if (!token) return;

        try {
            await axios.post(
                `${API_URL}/api/pomodoro/sessions`,
                {
                    duration_minutes: Math.floor(duration / 60),
                    type: type.toLowerCase(),
                    completed,
                    subject_id: selectedSubject || null
                },
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );
        } catch (error) {
            console.error('Error saving pomodoro session:', error);
            setError('Failed to save session. Your progress may not be recorded.');
        }
    };

    // Save settings to API
    const saveSettings = async (workMins, breakMins) => {
        if (!token) return;

        try {
            await axios.post(
                `${API_URL}/api/pomodoro/settings`,
                {
                    duration_minutes: workMins,
                    break_duration_minutes: breakMins
                },
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );
        } catch (error) {
            console.error('Error saving pomodoro settings:', error);
            setError('Failed to save settings. Your preferences may not be saved.');
        }
    };

    // Toggle timer
    const toggleTimer = () => {
        setIsRunning((prev) => !prev);
    };

    // Toggle mini timer
    const toggleMiniTimer = () => {
        setShowMiniTimer((prev) => !prev);
    };

    // Show mini timer and close popup
    const showMiniTimerAndClosePopup = () => {
        setShowMiniTimer(true);
        setShowPomodoroPopup(false);
    };

    // Show popup and close mini timer
    const showPomodoroPopupAndCloseMini = () => {
        setShowPomodoroPopup(true);
        setShowMiniTimer(false);
    };

    // Close both popup and mini timer
    const closePomodoroInterface = () => {
        setShowPomodoroPopup(false);
        setShowMiniTimer(false);
    };

    // Update durations
    const updateWorkDuration = (minutes) => {
        const newDuration = Math.max(1, minutes) * 60;
        setWorkDuration(newDuration);
        if (isWorkPhase && !isRunning) {
            setTimeLeft(newDuration);
        }
    };

    const updateBreakDuration = (minutes) => {
        const newDuration = Math.max(1, minutes) * 60;
        setBreakDuration(newDuration);
        if (!isWorkPhase && !isRunning) {
            setTimeLeft(newDuration);
        }
    };

    // Reset timer
    const resetTimer = () => {
        setIsRunning(false);
        setTimeLeft(isWorkPhase ? workDuration : breakDuration);
    };

    // Skip to next phase
    const skipPhase = () => {
        if (isWorkPhase) {
            setTimeLeft(breakDuration);
            setIsWorkPhase(false);
        } else {
            setTimeLeft(workDuration);
            setIsWorkPhase(true);
        }
    };

    const value = {
        isRunning,
        isWorkPhase,
        workDuration,
        breakDuration,
        timeLeft,
        sessionCount,
        history,
        showMiniTimer,
        showPomodoroPopup,
        subjects,
        selectedSubject,
        loading,
        error,
        audioRef,
        tickSoundRef,
        formatTime,
        toggleTimer,
        toggleMiniTimer,
        showMiniTimerAndClosePopup,
        showPomodoroPopupAndCloseMini,
        closePomodoroInterface,
        updateWorkDuration,
        updateBreakDuration,
        resetTimer,
        skipPhase,
        saveSettings,
        setSelectedSubject,
        setShowMiniTimer,
        setShowPomodoroPopup
    };

    return (
        <PomodoroContext.Provider value={value}>
            {children}
        </PomodoroContext.Provider>
    );
};

export default PomodoroContext;
