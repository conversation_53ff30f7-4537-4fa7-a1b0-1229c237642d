-- Migration: Add attached_image field to cornell_notes table
-- This field will store base64 encoded image data for Cornell Notes

-- Add the attached_image column to cornell_notes table
ALTER TABLE cornell_notes 
ADD COLUMN IF NOT EXISTS attached_image TEXT;

-- Add a comment to document the column
COMMENT ON COLUMN cornell_notes.attached_image IS 'Base64 encoded image data attached to Cornell Notes';

-- Create an index for better performance when querying notes with images
CREATE INDEX IF NOT EXISTS idx_cornell_notes_has_image ON cornell_notes(attached_image) 
WHERE attached_image IS NOT NULL;
