# Unique Constraint Fix for Leitner System

This document explains the fix for the unique constraint violation in the Leitner System component.

## Problem

We've identified the exact error from the server:

```
Error saving flashcards: duplicate key value violates unique constraint "unique_flashcard_question"
```

This means there's a unique constraint on the `question` field in the flashcards table, and we're trying to insert a card with a question that already exists in the database.

## Root Cause

The database has a unique constraint on the `question` field, which means you can't have two flashcards with the same question. When the Leitner System tries to save a card, it's trying to create a new card with a question that already exists in the database.

## Solution

We've implemented a fix that:

1. Checks if a card with the same question already exists in our original cards
2. If it does, uses that card's ID to update it instead of creating a new one

```jsx
// Handle unique constraint on question field
// If this is a new card (no ID), check if we already have a card with the same question
if (!cardToSave.id) {
  // Look for an existing card with the same question in our original cards
  const existingCard = originalCards.find(oc => 
    oc.question === cardToSave.question && 
    oc.chapter_id === chapterId
  );
  
  if (existingCard) {
    // If we found an existing card, use its ID to update it instead of creating a new one
    console.log(`Found existing card with question "${cardToSave.question.substring(0, 30)}...". Using its ID for update.`);
    cardToSave.id = existingCard.id;
  }
}
```

## Server-Side Handling

The server is already set up to handle this situation:

```javascript
// If the card has the same question as an existing card, use that ID
if (!flashcardId && existingFlashcardMap[question]) {
    flashcardId = existingFlashcardMap[question];

    // Update the existing flashcard
    await pool.query(
        `UPDATE flashcards SET
            answer = $1,
            type = $2,
            box_level = $3,
            last_reviewed = $4,
            next_review_date = $5,
            color = $6,
            difficult = $7,
            review_count = $8,
            success_rate = $9,
            tags = $10,
            updated_at = NOW()
        WHERE id = $11`,
        [
            answer, type, box_level, last_reviewed, next_review_date,
            color, difficult, review_count, success_rate, tagsString,
            flashcardId
        ]
    );
}
```

However, there seems to be a disconnect between the client-side and server-side handling of existing cards.

## Expected Behavior After Fix

After applying this fix:

1. When a user clicks on the "Correct" or "Incorrect" button, the component will check if a card with the same question already exists
2. If it does, it will use that card's ID to update it instead of creating a new one
3. This will prevent the unique constraint violation
4. The streak counter will update properly
5. Cards will move between boxes as expected

## Technical Details

The fix addresses the unique constraint violation by:

1. **Client-Side Check**: Before sending a card to the server, we check if a card with the same question already exists in our original cards.
2. **ID Reuse**: If we find an existing card, we use its ID to update it instead of creating a new one.
3. **Server-Side Handling**: The server is already set up to handle this situation by checking if a card with the same question exists and updating it instead of creating a new one.

This approach ensures that we don't violate the unique constraint on the `question` field while still allowing users to update cards and move them between boxes.

## Troubleshooting

If you still encounter issues after applying this fix:

1. Check the browser console for detailed error messages
2. Look for any cards with duplicate questions
3. Verify that the server is correctly identifying existing cards
4. Consider adding a more robust unique identifier for cards, such as a combination of user_id, chapter_id, and question
