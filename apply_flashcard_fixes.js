/**
 * <PERSON><PERSON><PERSON> to apply fixes to the FlashCards and LeitnerSystem components
 * to resolve the 500 Internal Server Error when saving flashcards.
 * 
 * This script:
 * 1. Reads the current FlashCards.jsx and LeitnerSystem.jsx files
 * 2. Applies the necessary fixes
 * 3. Writes the updated files
 * 
 * Run this script from the project root directory:
 *   node apply_flashcard_fixes.js
 */

const fs = require('fs');
const path = require('path');

// Paths to the files
const flashcardsPath = path.join(__dirname, 'Frontend', 'src', 'components', 'tools', 'list', 'FlashCards.jsx');
const leitnerSystemPath = path.join(__dirname, 'Frontend', 'src', 'systems', 'LeitnerSystem', 'LeitnerSystem.jsx');

// Backup files before modifying them
function backupFile(filePath) {
  const backupPath = `${filePath}.bak`;
  console.log(`Backing up ${filePath} to ${backupPath}`);
  fs.copyFileSync(filePath, backupPath);
}

// Apply fixes to FlashCards.jsx
function fixFlashCards() {
  console.log('Applying fixes to FlashCards.jsx...');
  
  try {
    // Check if the file exists
    if (!fs.existsSync(flashcardsPath)) {
      console.error(`File not found: ${flashcardsPath}`);
      return false;
    }
    
    // Backup the file
    backupFile(flashcardsPath);
    
    // Read the file
    let content = fs.readFileSync(flashcardsPath, 'utf8');
    
    // Check if the fix has already been applied
    if (content.includes('// Track original cards loaded from the database to avoid re-saving them')) {
      console.log('FlashCards.jsx has already been fixed. Skipping...');
      return true;
    }
    
    // Apply the fixes
    
    // 1. Add originalCards state
    content = content.replace(
      /const \[cards, setCards\] = useState\(\[\]\);/,
      'const [cards, setCards] = useState([]);\n    // Track original cards loaded from the database to avoid re-saving them\n    const [originalCards, setOriginalCards] = useState([]);'
    );
    
    // 2. Add cardsModified state
    content = content.replace(
      /useEffect\(\(\) => \{/,
      '// Track if cards have been modified to avoid unnecessary auto-saves\n    const [cardsModified, setCardsModified] = useState(false);\n    \n    useEffect(() => {'
    );
    
    // 3. Update the auto-save effect
    content = content.replace(
      /useEffect\(\(\) => \{\s+if \(!chapterId \|\| cards\.length === 0\) return;\s+const timeoutId = setTimeout\(\(\) => saveCardsToDatabase\(\), 1000\);\s+return \(\) => clearTimeout\(timeoutId\);\s+\}, \[cards, chapterId\]\);/,
      'useEffect(() => {\n        if (!chapterId || cards.length === 0 || !cardsModified) return;\n        \n        console.log("Auto-save triggered due to card modifications");\n        const timeoutId = setTimeout(() => {\n            saveCardsToDatabase();\n            setCardsModified(false); // Reset the modified flag after saving\n        }, 1000);\n        \n        return () => clearTimeout(timeoutId);\n    }, [cards, chapterId, cardsModified]);'
    );
    
    // 4. Update the saveCardsToDatabase function
    content = content.replace(
      /const saveCardsToDatabase = async \(cardsToSave = cards\) => \{[\s\S]+?return false;\s+\}\s+\};/,
      `const saveCardsToDatabase = async (cardsToSave = cards) => {
        if (!chapterId || cardsToSave.length === 0) return true;

        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication token not found');

            // Step 1: Identify only new or modified cards that need to be saved
            const cardsToUpdate = [];
            
            for (const card of cardsToSave) {
                // Skip cards that were originally loaded from the database and haven't been modified
                const originalCard = originalCards.find(oc => oc.id === card.id);
                
                // Include card if:
                // 1. It's a new card (not in originalCards)
                // 2. It has been modified (different from original)
                // 3. It has the isModified flag set
                if (!originalCard || 
                    card.isModified || 
                    card.question !== originalCard.question || 
                    card.answer !== originalCard.answer ||
                    card.type !== originalCard.type ||
                    card.boxLevel !== originalCard.boxLevel ||
                    card.color !== originalCard.color ||
                    card.difficult !== originalCard.difficult) {
                    
                    // Mark as modified so we know to save it
                    card.isModified = true;
                    cardsToUpdate.push(card);
                }
            }
            
            // If no cards need to be updated, return success
            if (cardsToUpdate.length === 0) {
                console.log("No cards need to be saved - all cards are unchanged from database");
                return true;
            }
            
            console.log(\`Found \${cardsToUpdate.length} cards that need to be saved (new or modified)\`);

            // Step 2: Ensure all cards have unique questions for this chapter
            const uniqueCards = [];
            const questionMap = new Map();

            for (const card of cardsToUpdate) {
                // Create a unique key for each card based on the question
                const questionKey = card.question.trim();
                
                // If we haven't seen this question before, add it to our unique cards
                if (!questionMap.has(questionKey)) {
                    questionMap.set(questionKey, card);
                    uniqueCards.push(card);
                } else {
                    // If we have seen this question, update the existing card instead of adding a duplicate
                    const existingCard = questionMap.get(questionKey);
                    // Only update if the card has a newer timestamp
                    if (card.updatedAt && existingCard.updatedAt && 
                        new Date(card.updatedAt) > new Date(existingCard.updatedAt)) {
                        // Replace the existing card with this one
                        const index = uniqueCards.findIndex(c => c.question === questionKey);
                        if (index !== -1) {
                            uniqueCards[index] = card;
                            questionMap.set(questionKey, card);
                        }
                    }
                }
            }

            console.log(\`Filtered \${cardsToUpdate.length} cards down to \${uniqueCards.length} unique cards\`);

            // Step 3: Process cards in smaller batches to avoid payload size issues
            const BATCH_SIZE = 20; // Process 20 cards at a time
            const batches = [];
            
            for (let i = 0; i < uniqueCards.length; i += BATCH_SIZE) {
                batches.push(uniqueCards.slice(i, i + BATCH_SIZE));
            }
            
            console.log(\`Processing \${uniqueCards.length} cards in \${batches.length} batches\`);
            
            // Step 4: Process each batch
            let successCount = 0;
            
            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                console.log(\`Processing batch \${i + 1} of \${batches.length} (\${batch.length} cards)\`);
                
                try {
                    const response = await axios.post(
                        \`http://localhost:5001/api/chapters/\${chapterId}/flashcards\`,
                        { flashcards: batch },
                        {
                            headers: {
                                Authorization: \`Bearer \${token}\`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );
                    
                    if (response.data.success) {
                        successCount += batch.length;
                        console.log(\`Successfully saved batch \${i + 1} (\${batch.length} cards)\`);
                    } else {
                        console.error(\`Failed to save batch \${i + 1}:\`, response.data.message);
                    }
                } catch (batchError) {
                    console.error(\`Error saving batch \${i + 1}:\`, batchError);
                    // Continue with the next batch even if this one fails
                }
            }
            
            // Step 5: Save to local storage regardless of server success
            localStorage.setItem(\`chapter-\${chapterId}-flashcards\`, JSON.stringify(cardsToSave));
            
            // Step 6: Report success if at least some cards were saved
            if (successCount > 0) {
                // Update originalCards to include the newly saved cards
                const updatedOriginalCards = [...originalCards];
                
                // Add any new cards to originalCards
                for (const card of uniqueCards) {
                    const existingIndex = updatedOriginalCards.findIndex(c => c.id === card.id);
                    if (existingIndex >= 0) {
                        // Update existing card
                        updatedOriginalCards[existingIndex] = {...card, isModified: false};
                    } else {
                        // Add new card
                        updatedOriginalCards.push({...card, isModified: false});
                    }
                }
                
                setOriginalCards(updatedOriginalCards);
                
                setLoadingError(successCount < uniqueCards.length 
                    ? \`Partially saved: \${successCount} of \${uniqueCards.length} cards saved.\` 
                    : null);
                return true;
            } else if (uniqueCards.length > 0) {
                throw new Error(\`Failed to save any of the \${uniqueCards.length} cards\`);
            } else {
                // No cards needed saving
                return true;
            }
        } catch (error) {
            handleError(error, 'saving flashcards');
            // Still save to local storage as a backup
            localStorage.setItem(\`chapter-\${chapterId}-flashcards\`, JSON.stringify(cardsToSave));
            return false;
        }
    };`
    );
    
    // 5. Update the loadCards function
    content = content.replace(
      /const fetchedCards = \(response\.data\.flashcards \|\| \[\]\)\.map\(card => \(\{\s+\.\.\.card,\s+tags: Array\.isArray\(card\.tags\) \? card\.tags : \[\]\s+\}\)\);/,
      `const fetchedCards = (response.data.flashcards || []).map(card => ({
                        ...card,
                        tags: Array.isArray(card.tags) ? card.tags : [],
                        isModified: false // Mark cards from database as not modified
                    }));

                    // Store the original cards to avoid re-saving them later
                    setOriginalCards(fetchedCards);`
    );
    
    // 6. Update the addCard function
    content = content.replace(
      /const newCardData = \{\s+\.\.\.newCard,\s+id: Date\.now\(\),\s+createdAt: new Date\(\)\.toISOString\(\),\s+lastReviewed: null,\s+nextReviewDate: calculateNextReviewDate\(1\)\.toISOString\(\),\s+color: cardColor,\s+difficult: false,\s+boxLevel: 1,\s+reviewCount: 0,\s+successRate: 0,\s+type: cardType,\s+chapter_id: chapterId,\s+subject_id: subjectId\s+\};/,
      `const newCardData = {
            ...newCard,
            id: Date.now(),
            createdAt: new Date().toISOString(),
            lastReviewed: null,
            nextReviewDate: calculateNextReviewDate(1).toISOString(),
            color: cardColor,
            difficult: false,
            boxLevel: 1,
            reviewCount: 0,
            successRate: 0,
            type: cardType,
            chapter_id: chapterId,
            subject_id: subjectId,
            isModified: true // Mark as modified so it will be saved
        };`
    );
    
    // 7. Update the addCard function to set cardsModified
    content = content.replace(
      /setCurrentIndex\(updatedCards\.length - 1\);/,
      `setCurrentIndex(updatedCards.length - 1);
        
        // Mark cards as modified to trigger auto-save
        setCardsModified(true);`
    );
    
    // 8. Update the updateCard function
    content = content.replace(
      /card\.id === editCard\.id \? \{\s+\.\.\.editCard,\s+color: cardColor,\s+type: cardType,\s+updatedAt: new Date\(\)\.toISOString\(\),\s+chapter_id: chapterId,\s+subject_id: subjectId\s+\} : card/,
      `card.id === editCard.id ? {
                ...editCard,
                color: cardColor,
                type: cardType,
                updatedAt: new Date().toISOString(),
                chapter_id: chapterId,
                subject_id: subjectId,
                isModified: true // Mark as modified so it will be saved
            } : card`
    );
    
    // 9. Update the updateCard function to set cardsModified
    content = content.replace(
      /setNewCard\(\{ question: '', answer: '', type: 'basic', media: null, tags: \[\] \}\);/,
      `setNewCard({ question: '', answer: '', type: 'basic', media: null, tags: [] });
        
        // Mark cards as modified to trigger auto-save
        setCardsModified(true);`
    );
    
    // 10. Update the removeCard function
    content = content.replace(
      /const removeCard = \(id\) => \{\s+const updatedCards = cards\.filter\(card => card\.id !== id\);\s+setCards\(updatedCards\);\s+setCurrentIndex\(Math\.max\(0, Math\.min\(currentIndex, updatedCards\.length - 1\)\)\);\s+saveCardsToDatabase\(updatedCards\);\s+\};/,
      `const removeCard = (id) => {
        // We need to handle removal differently since we need to track deleted cards
        // First, check if this card exists in originalCards (was loaded from DB)
        const cardInOriginal = originalCards.find(card => card.id === id);
        
        if (cardInOriginal) {
            // This is a card from the database that needs to be deleted
            // In a real implementation, you would make a DELETE API call here
            // For now, we'll just remove it from our local state
            console.log(\`Card \${id} exists in database and should be deleted on the server\`);
            // TODO: Add API call to delete the card from the server
        }
        
        const updatedCards = cards.filter(card => card.id !== id);
        setCards(updatedCards);
        setCurrentIndex(Math.max(0, Math.min(currentIndex, updatedCards.length - 1)));
        
        // Mark cards as modified to trigger auto-save
        setCardsModified(true);
        
        // Save immediately for deleted cards
        saveCardsToDatabase(updatedCards);
    };`
    );
    
    // 11. Update the reviewCard function
    content = content.replace(
      /return \{\s+\.\.\.card,\s+boxLevel: newBoxLevel,\s+lastReviewed: now\.toISOString\(\),\s+nextReviewDate: calculateNextReviewDate\(newBoxLevel\)\.toISOString\(\),\s+reviewCount,\s+successCount,\s+successRate: Math\.round\(\(successCount \/ reviewCount\) \* 100\),\s+chapter_id: chapterId,\s+subject_id: subjectId\s+\};/,
      `return {
                ...card,
                boxLevel: newBoxLevel,
                lastReviewed: now.toISOString(),
                nextReviewDate: calculateNextReviewDate(newBoxLevel).toISOString(),
                reviewCount,
                successCount,
                successRate: Math.round((successCount / reviewCount) * 100),
                chapter_id: chapterId,
                subject_id: subjectId,
                isModified: true // Mark as modified so it will be saved
            };`
    );
    
    // 12. Update the reviewCard function to set cardsModified
    content = content.replace(
      /setCards\(updatedCards\);/,
      `setCards(updatedCards);
        
        // Mark cards as modified to trigger auto-save
        setCardsModified(true);`
    );
    
    // Write the updated content back to the file
    fs.writeFileSync(flashcardsPath, content);
    
    console.log('Successfully applied fixes to FlashCards.jsx');
    return true;
  } catch (error) {
    console.error('Error applying fixes to FlashCards.jsx:', error);
    return false;
  }
}

// Apply fixes to LeitnerSystem.jsx
function fixLeitnerSystem() {
  console.log('Applying fixes to LeitnerSystem.jsx...');
  
  try {
    // Check if the file exists
    if (!fs.existsSync(leitnerSystemPath)) {
      console.error(`File not found: ${leitnerSystemPath}`);
      return false;
    }
    
    // Backup the file
    backupFile(leitnerSystemPath);
    
    // Read the file
    let content = fs.readFileSync(leitnerSystemPath, 'utf8');
    
    // Check if the fix has already been applied
    if (content.includes('// Track original cards loaded from the database to avoid re-saving them')) {
      console.log('LeitnerSystem.jsx has already been fixed. Skipping...');
      return true;
    }
    
    // Apply the fixes
    
    // 1. Add originalCards state
    content = content.replace(
      /const \[cards, setCards\] = useState\(\[\]\);/,
      'const [cards, setCards] = useState([]);\n  // Track original cards loaded from the database to avoid re-saving them\n  const [originalCards, setOriginalCards] = useState([]);'
    );
    
    // 2. Update the loadCards function
    content = content.replace(
      /const fetchedCards = response\.data\.flashcards \|\| \[\];/,
      `const fetchedCards = response.data.flashcards || [];
        
        // Store original cards to avoid re-saving them
        setOriginalCards(fetchedCards.map(card => ({
          ...card,
          isModified: false // Mark cards from database as not modified
        })));`
    );
    
    // 3. Update the saveBoxes function
    content = content.replace(
      /const saveBoxes = async \(\) => \{[\s\S]+?setLoading\(false\);\s+\}\s+\};/,
      `const saveBoxes = async () => {
    if (!chapterId) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Flatten all boxes into a single array of cards
      const allCards = Object.values(boxes).flat();
      
      // Step 1: Identify only new or modified cards that need to be saved
      const cardsToUpdate = [];
      
      for (const card of allCards) {
        // Skip cards that were originally loaded from the database and haven't been modified
        const originalCard = originalCards.find(oc => oc.id === card.id);
        
        // Include card if:
        // 1. It's a new card (not in originalCards)
        // 2. It has been modified (different from original)
        // 3. It has the isModified flag set
        if (!originalCard || 
            card.isModified || 
            card.box_level !== originalCard.box_level ||
            card.leitner_streak !== originalCard.leitner_streak ||
            card.next_review_date !== originalCard.next_review_date) {
          
          // Mark as modified so we know to save it
          card.isModified = true;
          cardsToUpdate.push(card);
        }
      }
      
      // If no cards need to be updated, return success
      if (cardsToUpdate.length === 0) {
        console.log("No cards need to be saved - all cards are unchanged from database");
        return;
      }
      
      console.log(\`Saving \${cardsToUpdate.length} modified flashcards to server...\`);
      setLoading(true);

      // Process cards in smaller batches to avoid payload size issues
      const BATCH_SIZE = 20; // Process 20 cards at a time
      const batches = [];
      
      for (let i = 0; i < cardsToUpdate.length; i += BATCH_SIZE) {
        batches.push(cardsToUpdate.slice(i, i + BATCH_SIZE));
      }
      
      console.log(\`Processing \${cardsToUpdate.length} cards in \${batches.length} batches\`);
      
      // Process each batch
      let successCount = 0;
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(\`Processing batch \${i + 1} of \${batches.length} (\${batch.length} cards)\`);
        
        try {
          const response = await axios.post(\`http://localhost:5001/api/chapters/\${chapterId}/flashcards\`, {
            flashcards: batch
          }, {
            headers: {
              Authorization: \`Bearer \${token}\`,
              'Content-Type': 'application/json'
            }
          });
          
          if (response.data.success) {
            successCount += batch.length;
            console.log(\`Successfully saved batch \${i + 1} (\${batch.length} cards)\`);
          } else {
            console.error(\`Failed to save batch \${i + 1}:\`, response.data.message);
          }
        } catch (batchError) {
          console.error(\`Error saving batch \${i + 1}:\`, batchError);
          // Continue with the next batch even if this one fails
        }
      }
      
      // Update originalCards to include the newly saved cards
      if (successCount > 0) {
        const updatedOriginalCards = [...originalCards];
        
        // Add any new cards to originalCards
        for (const card of cardsToUpdate) {
          const existingIndex = updatedOriginalCards.findIndex(c => c.id === card.id);
          if (existingIndex >= 0) {
            // Update existing card
            updatedOriginalCards[existingIndex] = {...card, isModified: false};
          } else {
            // Add new card
            updatedOriginalCards.push({...card, isModified: false});
          }
        }
        
        setOriginalCards(updatedOriginalCards);
        
        console.log(\`Leitner boxes saved successfully. Saved \${successCount} flashcards.\`);
        
        // No need to reload cards since we've already updated our state
        setLoading(false);
      } else {
        throw new Error(\`Failed to save any of the \${cardsToUpdate.length} cards\`);
      }
    } catch (error) {
      console.error('Failed to save Leitner boxes:', error);
      setError('Failed to save changes. Please try again later.');
      setLoading(false);
    }
  };`
    );
    
    // 4. Update the moveCard function
    content = content.replace(
      /card\.box_level = toBox;/,
      `card.box_level = toBox;
  card.isModified = true; // Mark as modified so it will be saved`
    );
    
    // 5. Update the answerCard function
    content = content.replace(
      /if \(fromBox === null\) return;/,
      `if (fromBox === null || !card) {
    console.error(\`Card with ID \${cardId} not found in any box\`);
    return;
  }

  // Mark the card as modified
  card.isModified = true;`
    );
    
    // Write the updated content back to the file
    fs.writeFileSync(leitnerSystemPath, content);
    
    console.log('Successfully applied fixes to LeitnerSystem.jsx');
    return true;
  } catch (error) {
    console.error('Error applying fixes to LeitnerSystem.jsx:', error);
    return false;
  }
}

// Main function
function main() {
  console.log('Starting to apply flashcard fixes...');
  
  const flashcardsFixed = fixFlashCards();
  const leitnerSystemFixed = fixLeitnerSystem();
  
  if (flashcardsFixed && leitnerSystemFixed) {
    console.log('Successfully applied all fixes!');
    console.log('Please restart your development server for the changes to take effect.');
  } else {
    console.log('Some fixes could not be applied. Please check the error messages above.');
  }
}

// Run the main function
main();
