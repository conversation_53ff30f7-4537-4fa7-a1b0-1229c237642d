import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../authContext';
import { useNavigate } from 'react-router-dom';
import '../styles/StudyPlan.scss';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import AddIcon from '@mui/icons-material/Add';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import TodayIcon from '@mui/icons-material/Today';
import ListAltIcon from '@mui/icons-material/ListAlt';
import SchoolIcon from '@mui/icons-material/School';

const MessagePopup = ({ message, isError = false, onClose }) => {
    useEffect(() => {
        let timer;
        if (!isError) {
            timer = setTimeout(() => {
                onClose();
            }, 2000); // Auto-close after 2 seconds for success messages
        }
        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [isError, onClose]);

    return (
        <div className="message-popup-overlay">
            <div className="message-popup-container">
                <div className="popup-header">
                    <h2>{isError ? 'Error' : 'Success'}</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>
                <div className="popup-content">
                    <p>{message}</p>
                </div>
            </div>
        </div>
    );
};

const StudyPlanCreator = ({ onClose, curriculumId, curriculumTitle }) => {
    const { user, token } = useAuth();
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        name: `Study Plan for ${curriculumTitle || 'Curriculum'}`,
        start_date: new Date(),
        end_date: new Date(new Date().setDate(new Date().getDate() + 30)), // Default to 30 days
        daily_study_time_minutes: 60
    });

    // Days of week: 0 = Monday, 6 = Sunday (following JavaScript Date.getDay() convention)
    const [preferredDays, setPreferredDays] = useState([0, 1, 2, 3, 4, 5, 6]); // Default to all days
    const [unavailablePeriods, setUnavailablePeriods] = useState([]);
    const [showUnavailablePeriods, setShowUnavailablePeriods] = useState(false);

    const [messagePopup, setMessagePopup] = useState({ show: false, message: '', isError: false });
    const [isCreating, setIsCreating] = useState(false);
    const [curriculumData, setCurriculumData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [validationFeedback, setValidationFeedback] = useState(null);

    // Force using port 5000 for the Gemini AI backend
    const API_URL = 'http://127.0.0.1:5000';

    // Days of the week for UI
    const daysOfWeek = [
        { value: 0, label: 'Monday' },
        { value: 1, label: 'Tuesday' },
        { value: 2, label: 'Wednesday' },
        { value: 3, label: 'Thursday' },
        { value: 4, label: 'Friday' },
        { value: 5, label: 'Saturday' },
        { value: 6, label: 'Sunday' }
    ];

    // Fetch curriculum data when component mounts
    useEffect(() => {
        const fetchCurriculumData = async () => {
            if (!curriculumId || !token) return;

            setIsLoading(true);
            try {
                // Call the process-syllabus endpoint to get chapters and objectives
                const response = await axios.post(
                    `${API_URL}/api/process-syllabus`,
                    { subject_id: curriculumId },
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );

                if (response.data.success) {
                    setCurriculumData(response.data.data);
                    console.log('Fetched curriculum data:', response.data.data);
                } else {
                    console.error('Failed to fetch curriculum data:', response.data.error);
                }
            } catch (error) {
                console.error('Error fetching curriculum data:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchCurriculumData();
    }, [curriculumId, token, API_URL]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleDateChange = (date, field) => {
        setFormData(prev => ({ ...prev, [field]: date }));
    };

    const handleMessagePopupClose = () => {
        setMessagePopup({ show: false, message: '', isError: false });
        if (!messagePopup.isError) {
            onClose();
            navigate('/study-plans');
        }
    };

    // Handle preferred days selection
    const togglePreferredDay = (dayValue) => {
        setPreferredDays(prev => {
            if (prev.includes(dayValue)) {
                // Remove day if already selected
                return prev.filter(day => day !== dayValue);
            } else {
                // Add day if not selected
                return [...prev, dayValue].sort();
            }
        });
    };

    // Handle unavailable periods
    const addUnavailablePeriod = () => {
        const newPeriod = {
            id: Date.now(), // Unique ID for the period
            start_date: new Date(),
            end_date: new Date(new Date().setDate(new Date().getDate() + 7))
        };
        setUnavailablePeriods(prev => [...prev, newPeriod]);
    };

    const removeUnavailablePeriod = (id) => {
        setUnavailablePeriods(prev => prev.filter(period => period.id !== id));
    };

    const updateUnavailablePeriod = (id, field, value) => {
        setUnavailablePeriods(prev =>
            prev.map(period =>
                period.id === id ? { ...period, [field]: value } : period
            )
        );
    };

    // Validate unavailable periods
    const validateUnavailablePeriods = () => {
        for (const period of unavailablePeriods) {
            if (period.end_date < period.start_date) {
                return "End date must be after start date in unavailable periods";
            }
        }
        return null;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.name || !formData.start_date || !formData.end_date || !formData.daily_study_time_minutes) {
            setMessagePopup({
                show: true,
                message: 'Please fill in all required fields',
                isError: true
            });
            return;
        }

        // Validate dates
        if (formData.end_date < formData.start_date) {
            setMessagePopup({
                show: true,
                message: 'End date must be after start date',
                isError: true
            });
            return;
        }

        // Validate preferred days
        if (preferredDays.length === 0) {
            setMessagePopup({
                show: true,
                message: 'Please select at least one preferred study day',
                isError: true
            });
            return;
        }

        // Validate unavailable periods
        const unavailablePeriodsError = validateUnavailablePeriods();
        if (unavailablePeriodsError) {
            setMessagePopup({
                show: true,
                message: unavailablePeriodsError,
                isError: true
            });
            return;
        }

        setIsCreating(true);
        setValidationFeedback(null);

        try {
            console.log('Creating study plan with data:', formData);
            console.log('Preferred days:', preferredDays);
            console.log('Unavailable periods:', unavailablePeriods);

            // Format dates for API
            const formatDate = (date) => {
                const d = new Date(date);
                return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
            };

            // Format unavailable periods for API
            const formattedUnavailablePeriods = unavailablePeriods.map(period => ({
                start_date: formatDate(period.start_date),
                end_date: formatDate(period.end_date)
            }));

            // Prepare request payload
            const payload = {
                subject_id: parseInt(curriculumId), // Use the curriculumId prop as subject_id
                user_id: user.id, // Add user_id for the backend
                name: formData.name,
                start_date: formatDate(formData.start_date),
                end_date: formatDate(formData.end_date),
                daily_study_time_minutes: parseInt(formData.daily_study_time_minutes),
                preferred_days: preferredDays.length === 7 ? null : preferredDays, // Send null if all days are selected
                unavailable_periods: unavailablePeriods.length > 0 ? formattedUnavailablePeriods : null
            };

            console.log('Sending study plan request:', payload);
            console.log('Using API URL:', API_URL);
            console.log('Token available:', !!token);
            console.log('User ID:', user.id);

            // First, validate the study plan with the AI
            const validationResponse = await axios.post(`${API_URL}/api/validate_study_plan`, payload, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Validation response:', validationResponse.data);

            // Store validation feedback
            if (validationResponse.data.validation_result) {
                setValidationFeedback(validationResponse.data.validation_result);
            }

            // If plan is not feasible and user hasn't confirmed
            if (validationResponse.data.validation_result &&
                !validationResponse.data.validation_result.is_feasible &&
                !payload.user_accepted_suggestion) {

                const validation = validationResponse.data.validation_result;

                // Show suggestions to the user
                const message = `Your study plan may not be feasible. You need ${validation.total_time_needed_minutes} minutes of study time, but only have ${validation.total_available_time_minutes} minutes available.`;

                // Create suggestion message
                let suggestionMessage = "Suggestions:\n";

                if (validation.suggestions.option_1) {
                    const option = validation.suggestions.option_1;
                    suggestionMessage += `1. Extend end date by ${option.additional_days} days (to ${option.suggested_end_date})\n`;
                }

                if (validation.suggestions.option_2) {
                    const option = validation.suggestions.option_2;
                    suggestionMessage += `2. Increase daily study time to ${option.suggested_daily_time} minutes (${option.additional_minutes_per_day} more minutes per day)\n`;
                }

                const confirmAdjust = window.confirm(`${message}\n\n${suggestionMessage}\n\nWould you like to adjust your study plan?`);

                if (confirmAdjust) {
                    // Ask which option they prefer
                    const option = prompt("Enter 1 to extend end date or 2 to increase daily study time:");

                    if (option === "1" && validation.suggestions.option_1) {
                        // Extend end date
                        const newEndDate = new Date(validation.suggestions.option_1.suggested_end_date);
                        setFormData(prev => ({
                            ...prev,
                            end_date: newEndDate
                        }));

                        setMessagePopup({
                            show: true,
                            message: 'End date adjusted. Please review and submit again.',
                            isError: false
                        });
                        setIsCreating(false);
                        return;
                    }
                    else if (option === "2" && validation.suggestions.option_2) {
                        // Increase daily study time
                        setFormData(prev => ({
                            ...prev,
                            daily_study_time_minutes: validation.suggestions.option_2.suggested_daily_time
                        }));

                        setMessagePopup({
                            show: true,
                            message: 'Daily study time adjusted. Please review and submit again.',
                            isError: false
                        });
                        setIsCreating(false);
                        return;
                    }
                } else {
                    // User wants to proceed anyway
                    payload.user_accepted_suggestion = true;
                }
            }

            // Make API call to create study plan
            const response = await axios.post(`${API_URL}/api/create_study_plan`, payload, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Study plan creation response:', response.data);

            if (response.data.success) {
                // Check if the timeframe is realistic
                if (response.data.is_realistic === false && response.data.suggestions) {
                    // Ask user if they want to adjust the plan
                    const confirmAdjust = window.confirm(
                        `${response.data.suggestions.message}\n\nWould you like to adjust your study plan?`
                    );

                    if (confirmAdjust) {
                        // User wants to adjust the plan
                        let updatedFormData = { ...formData };
                        let changesApplied = false;

                        if (response.data.suggestions.suggested_end_date) {
                            updatedFormData.end_date = new Date(response.data.suggestions.suggested_end_date);
                            changesApplied = true;
                        }

                        if (response.data.suggestions.suggested_daily_time_minutes) {
                            updatedFormData.daily_study_time_minutes = response.data.suggestions.suggested_daily_time_minutes;
                            changesApplied = true;
                        }

                        if (changesApplied) {
                            setFormData(updatedFormData);
                            setMessagePopup({
                                show: true,
                                message: 'Study plan adjusted. Please review and submit again.',
                                isError: false
                            });
                            return; // Don't navigate away, let user resubmit
                        }
                    } else {
                        // User wants to keep the current plan
                        setMessagePopup({
                            show: true,
                            message: `Study plan created with the original timeframe.`,
                            isError: false
                        });
                    }
                } else if (response.data.suggestions && response.data.suggestions.message) {
                    // Show feedback for realistic plans
                    setMessagePopup({
                        show: true,
                        message: `Study plan created successfully! ${response.data.suggestions.message}`,
                        isError: false
                    });
                } else {
                    // Show success message
                    setMessagePopup({
                        show: true,
                        message: 'Study plan created successfully!',
                        isError: false
                    });
                }
            } else {
                throw new Error(response.data.message || 'Failed to create study plan');
            }
        } catch (error) {
            console.error('Error creating study plan:', error);
            console.error('Error details:', error.response?.data || error.message);
            console.error('Status code:', error.response?.status);
            console.error('Headers:', error.response?.headers);

            // Provide a more user-friendly error message
            let errorMessage = "Error creating study plan. Please try again.";

            // Check for error message in different possible locations
            if (error.response?.data?.error) {
                // If there's a specific error message from the server, use it
                errorMessage = error.response.data.error;
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            }

            // Add specific guidance for common errors
            if (errorMessage.includes("No chapters found")) {
                errorMessage = `${errorMessage} Please go back and process the document first.`;
            } else if (errorMessage.includes("No document found")) {
                errorMessage = `${errorMessage} Please go back and upload a document first.`;
            }

            setMessagePopup({
                show: true,
                message: errorMessage,
                isError: true
            });
        } finally {
            setIsCreating(false);
        }
    };

    return (
        <div className="study-plan-creator-overlay">
            <div className="study-plan-creator-container">
                <div className="popup-header-main">
                    <h2>Create Study Plan</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>
                <div className="popup-content">
                    <form onSubmit={handleSubmit}>
                        <div className="form-group">
                            <label>
                                <MenuBookIcon /> Study Plan Name
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleInputChange}
                                className="macos-input"
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <CalendarTodayIcon /> Start Date
                            </label>
                            <DatePicker
                                selected={formData.start_date}
                                onChange={(date) => handleDateChange(date, 'start_date')}
                                className="macos-input"
                                dateFormat="yyyy-MM-dd"
                                minDate={new Date()}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <CalendarTodayIcon /> End Date
                            </label>
                            <DatePicker
                                selected={formData.end_date}
                                onChange={(date) => handleDateChange(date, 'end_date')}
                                className="macos-input"
                                dateFormat="yyyy-MM-dd"
                                minDate={formData.start_date}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <AccessTimeIcon /> Daily Study Time (minutes)
                            </label>
                            <input
                                type="number"
                                name="daily_study_time_minutes"
                                value={formData.daily_study_time_minutes}
                                onChange={handleInputChange}
                                className="macos-input"
                                min="15"
                                max="480"
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label>
                                <TodayIcon /> Preferred Study Days
                            </label>
                            <div className="days-of-week-selector">
                                {daysOfWeek.map(day => (
                                    <div
                                        key={day.value}
                                        className={`day-checkbox ${preferredDays.includes(day.value) ? 'selected' : ''}`}
                                        onClick={() => togglePreferredDay(day.value)}
                                    >
                                        {preferredDays.includes(day.value) ?
                                            <CheckBoxIcon className="checkbox-icon" /> :
                                            <CheckBoxOutlineBlankIcon className="checkbox-icon" />
                                        }
                                        <span>{day.label}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="form-group">
                            <div className="unavailable-periods-header">
                                <label>
                                    <EventBusyIcon /> Unavailable Periods
                                </label>
                                <button
                                    type="button"
                                    className="macos-button small"
                                    onClick={() => setShowUnavailablePeriods(!showUnavailablePeriods)}
                                >
                                    {showUnavailablePeriods ? 'Hide' : 'Show'}
                                </button>
                            </div>

                            {showUnavailablePeriods && (
                                <div className="unavailable-periods-container">
                                    {unavailablePeriods.length === 0 ? (
                                        <p className="no-periods-message">No unavailable periods specified.</p>
                                    ) : (
                                        unavailablePeriods.map(period => (
                                            <div key={period.id} className="unavailable-period">
                                                <div className="period-dates">
                                                    <div className="period-date">
                                                        <label>From:</label>
                                                        <DatePicker
                                                            selected={period.start_date}
                                                            onChange={(date) => updateUnavailablePeriod(period.id, 'start_date', date)}
                                                            className="macos-input"
                                                            dateFormat="yyyy-MM-dd"
                                                            minDate={formData.start_date}
                                                            maxDate={formData.end_date}
                                                        />
                                                    </div>
                                                    <div className="period-date">
                                                        <label>To:</label>
                                                        <DatePicker
                                                            selected={period.end_date}
                                                            onChange={(date) => updateUnavailablePeriod(period.id, 'end_date', date)}
                                                            className="macos-input"
                                                            dateFormat="yyyy-MM-dd"
                                                            minDate={period.start_date}
                                                            maxDate={formData.end_date}
                                                        />
                                                    </div>
                                                </div>
                                                <button
                                                    type="button"
                                                    className="remove-period-button"
                                                    onClick={() => removeUnavailablePeriod(period.id)}
                                                >
                                                    <DeleteOutlineIcon />
                                                </button>
                                            </div>
                                        ))
                                    )}

                                    <button
                                        type="button"
                                        className="macos-button small add-period-button"
                                        onClick={addUnavailablePeriod}
                                    >
                                        <AddIcon /> Add Period
                                    </button>
                                </div>
                            )}
                        </div>

                        {validationFeedback && (
                            <div className={`validation-feedback ${validationFeedback.is_feasible ? 'feasible' : 'not-feasible'}`}>
                                <h4>
                                    {validationFeedback.is_feasible ? (
                                        <>
                                            <span role="img" aria-label="check">✅</span> Your study plan is feasible
                                        </>
                                    ) : (
                                        <>
                                            <span role="img" aria-label="warning">⚠️</span> Your study plan may not be feasible
                                        </>
                                    )}
                                </h4>

                                <div className="stats">
                                    <div className="stat-item">
                                        <div className="stat-label">Total Objectives</div>
                                        <div className="stat-value">{validationFeedback.total_objectives}</div>
                                    </div>

                                    <div className="stat-item">
                                        <div className="stat-label">Time Needed</div>
                                        <div className="stat-value">{Math.round(validationFeedback.total_time_needed_minutes / 60)} hours</div>
                                    </div>

                                    <div className="stat-item">
                                        <div className="stat-label">Available Time</div>
                                        <div className="stat-value">{Math.round(validationFeedback.total_available_time_minutes / 60)} hours</div>
                                    </div>

                                    <div className="stat-item">
                                        <div className="stat-label">Available Days</div>
                                        <div className="stat-value">{validationFeedback.available_days} days</div>
                                    </div>

                                    {!validationFeedback.is_feasible && (
                                        <div className="stat-item">
                                            <div className="stat-label">Time Deficit</div>
                                            <div className="stat-value">{Math.round(validationFeedback.time_deficit_minutes / 60)} hours</div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        <div className="curriculum-info">
                            <h3><SchoolIcon style={{ marginRight: '8px' }} /> Curriculum: {curriculumTitle}</h3>

                            {isLoading ? (
                                <div className="loading-spinner">Loading curriculum data...</div>
                            ) : curriculumData && curriculumData.subjects && curriculumData.subjects.length > 0 ? (
                                <div className="curriculum-content">
                                    {curriculumData.subjects[0].chapters.length > 0 ? (
                                        <div className="chapters-list">
                                            <h4><ListAltIcon /> Chapters and Objectives</h4>
                                            <ul>
                                                {curriculumData.subjects[0].chapters.map((chapter, chIndex) => (
                                                    <li key={`chapter-${chIndex}`} className="chapter-item">
                                                        <strong>{chapter.name}</strong>
                                                        {chapter.objectives && chapter.objectives.length > 0 && (
                                                            <ul className="objectives-list">
                                                                {chapter.objectives.map((objective, objIndex) => (
                                                                    <li key={`obj-${chIndex}-${objIndex}`} className="objective-item">
                                                                        {objective.text}
                                                                        {/*<span className="objective-meta">
                                                                            ({objective.estimated_time_minutes} min,
                                                                            Difficulty: {objective.difficulty_level}/5)
                                                                        </span>*/}
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        )}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    ) : (
                                        <p className="no-data-message">No chapters found for this curriculum. Please process the document first.</p>
                                    )}
                                </div>
                            ) : (
                                <p className="no-data-message">No curriculum data available.</p>
                            )}
                        </div>

                        <button
                            type="submit"
                            className="macos-button primary"
                            disabled={isCreating}
                        >
                            {isCreating ? (
                                <>
                                    <span className="spinner"></span> Creating...
                                </>
                            ) : (
                                <>Create Study Plan <ArrowForwardIcon /></>
                            )}
                        </button>
                    </form>
                </div>
            </div>
            {messagePopup.show && (
                <MessagePopup
                    message={messagePopup.message}
                    isError={messagePopup.isError}
                    onClose={handleMessagePopupClose}
                />
            )}
        </div>
    );
};

export default StudyPlanCreator;
