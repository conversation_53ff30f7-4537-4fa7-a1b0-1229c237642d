import React from 'react';

const ToolModal = ({ children, onClose }) => {
    // Determine if dark mode is active
    const isDarkMode = document.body.classList.contains('theme-dark');
    const themeClass = isDarkMode ? 'theme-dark' : '';

    return (
        <div className={`modal-overlay active ${themeClass}`}>
            <div className="modal-content">
                <button className="close-button" onClick={onClose}>×</button>
                {children}
            </div>
        </div>
    );
};

export default ToolModal;