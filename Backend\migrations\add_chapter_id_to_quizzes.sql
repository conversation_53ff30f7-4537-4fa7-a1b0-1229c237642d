-- Migration: Add chapter_id column to quizzes table
-- This allows quizzes to be associated with specific chapters

-- Add chapter_id column to quizzes table
ALTER TABLE quizzes 
ADD COLUMN chapter_id INTEGER REFERENCES chapters(id) ON DELETE SET NULL;

-- Add index for better performance when querying by chapter
CREATE INDEX IF NOT EXISTS idx_quizzes_chapter_id ON quizzes(chapter_id);

-- Add comment to document the purpose
COMMENT ON COLUMN quizzes.chapter_id IS 'Optional reference to the chapter this quiz is focused on';
