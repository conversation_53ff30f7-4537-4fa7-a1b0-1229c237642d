import google.generativeai as genai
import json
import logging
import os
import time
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Tuple, Optional, Union
import PyPDF2
import docx
import re
import math

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def estimate_token_count(text: str) -> int:
    """
    Estimate the number of tokens in a text string for Gemini models.
    This is a rough approximation as Gemini's exact tokenization is not public.

    Args:
        text: The text to estimate token count for

    Returns:
        Estimated token count
    """
    # Simple estimation: 1 token ≈ 4 characters for English text
    # This is a rough approximation for Gemini models
    char_count = len(text)
    estimated_tokens = char_count // 4

    # Count newlines separately as they often count as separate tokens
    newline_count = text.count('\n')

    # Add newlines to the token count
    total_estimated_tokens = estimated_tokens + newline_count

    return total_estimated_tokens

# Configure Gemini
api_key = os.getenv('GOOGLE_API_KEY')
if not api_key:
    logging.error("No GOOGLE_API_KEY found in environment variables")
    api_key = "AIzaSyBoB0L-siR7N2ICc-IkOWv-jeRJFoFIMSo"  # Fallback to default key if not found

genai.configure(api_key=api_key)

# Try to initialize the best available model
try:
    model = genai.GenerativeModel('gemini-1.5-pro')  # Best for document processing
    logging.info("Successfully initialized Gemini 1.5 Pro model")
except Exception as e:
    logging.warning(f"Failed to initialize gemini-1.5-pro model: {str(e)}")
    try:
        model = genai.GenerativeModel('gemini-1.5-flash')  # Alternative model
        logging.info("Successfully initialized Gemini 1.5 Flash model")
    except Exception as e:
        logging.warning(f"Failed to initialize gemini-1.5-flash model: {str(e)}")
        try:
            model = genai.GenerativeModel('gemini-pro')  # Older model
            logging.info("Successfully initialized Gemini Pro model")
        except Exception as e:
            logging.error(f"Failed to initialize any Gemini model: {str(e)}")
            raise RuntimeError("Failed to initialize Gemini AI model")

def extract_text_from_document(file) -> str:
    """
    Extract text from various document formats (PDF, DOCX, TXT)
    """
    filename = file.filename.lower()
    content = ""

    try:
        logging.info(f"Extracting text from {filename}")

        if filename.endswith('.pdf'):
            # Create a temporary file to save the uploaded PDF
            temp_path = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
            file.save(temp_path)

            logging.info(f"Saved PDF to temporary file: {temp_path}")

            # Extract text from PDF
            with open(temp_path, 'rb') as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                num_pages = len(pdf_reader.pages)
                logging.info(f"PDF has {num_pages} pages")

                for page_num in range(num_pages):
                    page_text = pdf_reader.pages[page_num].extract_text()
                    content += page_text + "\n\n"
                    logging.info(f"Extracted {len(page_text)} characters from page {page_num+1}")

            # Remove the temporary file
            os.remove(temp_path)
            logging.info(f"Removed temporary file: {temp_path}")

        elif filename.endswith('.docx'):
            # Create a temporary file to save the uploaded DOCX
            temp_path = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}.docx"
            file.save(temp_path)

            logging.info(f"Saved DOCX to temporary file: {temp_path}")

            # Extract text from DOCX
            doc = docx.Document(temp_path)
            para_count = len(doc.paragraphs)
            logging.info(f"DOCX has {para_count} paragraphs")

            for para in doc.paragraphs:
                if para.text.strip():  # Only add non-empty paragraphs
                    content += para.text + "\n"

            # Also extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = ""
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text += cell.text + " | "
                    if row_text:
                        content += row_text.rstrip(" | ") + "\n"

            # Remove the temporary file
            os.remove(temp_path)
            logging.info(f"Removed temporary file: {temp_path}")

        elif filename.endswith('.txt'):
            # Read text file directly
            content = file.read().decode('utf-8')
            logging.info(f"Extracted {len(content)} characters from text file")

        else:
            error_msg = f"Unsupported file format: {filename}"
            logging.error(error_msg)
            raise ValueError(error_msg)

        # Log the amount of text extracted
        logging.info(f"Total extracted text: {len(content)} characters")

        # If no text was extracted, raise an error
        if not content.strip():
            error_msg = f"No text could be extracted from {filename}"
            logging.error(error_msg)
            raise ValueError(error_msg)

    except Exception as e:
        logging.error(f"Error extracting text from document: {str(e)}")
        raise

    return content

def process_document_with_gemini(content: str) -> Dict:
    """
    Process document content with Gemini AI to extract subjects, chapters, and objectives
    """
    print("\n\n==== PROCESSING DOCUMENT WITH GEMINI AI ====")
    print(f"Document content length: {len(content)} characters")
    print(f"First 200 characters: {content[:200]}...")

    try:
        # Import required libraries
        import google.generativeai as genai
        import time
        from google.api_core.exceptions import ResourceExhausted

        # Configure the API key
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

        # Try different models in order of preference (flash is more efficient for rate limits)
        models = ['gemini-1.5-flash', 'gemini-1.0-pro', 'gemini-1.5-pro']
        model = None

        # Try each model until one works
        for model_name in models:
            try:
                print(f"Attempting to use model: {model_name}")
                model = genai.GenerativeModel(model_name)
                break
            except Exception as model_error:
                print(f"Error initializing model {model_name}: {str(model_error)}")
                continue

        if not model:
            print("Failed to initialize any Gemini model")
            raise Exception("Failed to initialize any Gemini model")

        # Use the full content without truncation
        truncated_content = content

        # Estimate token count
        estimated_tokens = estimate_token_count(content)
        char_count = len(content)

        print(f"Using full content with {char_count} characters (estimated {estimated_tokens} tokens)")
        logging.info(f"Using full content with {char_count} characters (estimated {estimated_tokens} tokens)")

        # Log token count for monitoring
        print(f"TOKEN COUNT ESTIMATE: {estimated_tokens} tokens")
        logging.warning(f"TOKEN COUNT ESTIMATE: {estimated_tokens} tokens")

        prompt = f"""
        You are an expert educational content analyzer. Your task is to analyze the following document and extract:
        1. The main subject or topic of the document
        2. Logical chapters or sections with their complete actual content
        3. Specific learning objectives for each chapter

        IMPORTANT: For each chapter, you must extract the COMPLETE, FULL content that belongs to that chapter from the document. This includes:
        - All paragraphs, text, and information that belongs to that chapter
        - All relevant details, explanations, examples, and notes
        - The entire content section for that chapter, not just a summary
        - Preserve the original text structure and formatting as much as possible

        For each learning objective:
        - Estimate the time needed (in minutes) to complete it
        - Assign a difficulty level (1-5, where 1 is easiest and 5 is most difficult)

        Guidelines:
        - Create 3-10 meaningful chapters based on the content
        - Each chapter should have 3-5 learning objectives
        - Learning objectives should be specific, measurable, and focused on what the learner will be able to do
        - Use action verbs like "explain", "identify", "analyze", "compare", etc.
        - Ensure objectives are appropriate for the subject matter
        - Estimate realistic time requirements for each objective (typically 15-60 minutes)
        - Assign difficulty levels based on complexity and prior knowledge required

        Document Content:
        {truncated_content}

        Return ONLY valid JSON with the following structure:
        {{
            "subjects": [
                {{
                    "name": "Subject Name",
                    "chapters": [
                        {{
                            "name": "Chapter 1: Chapter Title",
                            "chapter_number": 1,
                            "title": "Chapter Title",
                            "content": "The actual text content of this chapter extracted from the document. This should include all the relevant text, paragraphs, and information that belongs to this chapter section.",
                            "objectives": [
                                {{
                                    "text": "Objective description",
                                    "estimated_time_minutes": 30,
                                    "difficulty_level": 2
                                }},
                                ...
                            ]
                        }},
                        ...
                    ]
                }}
            ]
        }}

        IMPORTANT:
        - Always format chapter names exactly as 'Chapter X: Title', where X is the chapter number (e.g., Chapter 1: Introduction)
        - Ensure the numeric value in 'chapter_number' matches X in the title
        - Always include a separate 'title' field containing ONLY the title part (without "Chapter X:")
        - The 'name' field should contain the full "Chapter X: Title" format
        - The 'chapter_number' field should contain ONLY the number (1, 2, 3, etc.)

        Do not include any explanations, markdown formatting, or additional text outside the JSON structure.
        """

        # Log the prompt length
        print(f"Gemini prompt length: {len(prompt)} characters")
        print(f"Sending prompt to Gemini AI...")
        logging.info(f"Gemini prompt length: {len(prompt)} characters")

        # Implement retry logic with exponential backoff
        max_retries = 5
        retry_count = 0
        base_delay = 2  # seconds
        response = None

        while retry_count < max_retries:
            try:
                # Call the Gemini model
                print(f"Calling Gemini model (attempt {retry_count + 1}/{max_retries})...")
                response = model.generate_content(prompt)
                print(f"Received response from Gemini AI")
                # If we get here, the call was successful
                break
            except ResourceExhausted as quota_error:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"Maximum retries ({max_retries}) exceeded. Giving up.")
                    raise

                # Calculate delay with exponential backoff
                delay = base_delay * (2 ** (retry_count - 1))
                print(f"Hit rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                logging.warning(f"Hit Gemini API rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                time.sleep(delay)
            except Exception as e:
                # For other errors, don't retry
                print(f"Error calling Gemini API: {str(e)}")
                raise

        if not response:
            raise Exception("Failed to get response from Gemini API after retries")

        # Extract JSON from Gemini's response
        json_str = response.text.replace('```json', '').replace('```', '').strip()
        print(f"Raw response from Gemini AI (first 500 chars):\n{json_str[:500]}...")

        # Clean up any potential issues with the JSON
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)

        # Log the response length
        print(f"Gemini response length: {len(json_str)} characters")
        logging.info(f"Gemini response length: {len(json_str)} characters")

        # Parse the JSON
        try:
            print(f"Parsing JSON response...")
            parsed_json = json.loads(json_str)
            print(f"Successfully parsed JSON response")

            # Validate the structure
            if 'subjects' not in parsed_json or not parsed_json['subjects']:
                print(f"WARNING: Missing or empty 'subjects' in Gemini response")
                logging.warning("Missing or empty 'subjects' in Gemini response")
                # Create a default structure
                default_response = {
                    "subjects": [
                        {
                            "name": "Extracted Content",
                            "chapters": [
                                {
                                    "name": "Chapter 1: Introduction to Content",
                                    "chapter_number": 1,
                                    "title": "Introduction to Content",
                                    "content": "This chapter introduces the main concepts and topics covered in the document.",
                                    "objectives": [
                                        {
                                            "text": "Understand the main concepts presented in the document",
                                            "estimated_time_minutes": 30,
                                            "difficulty_level": 2
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
                print(f"Using default response structure")
                return default_response

            # Ensure all chapters have chapter_number, title, and proper naming format
            for subject in parsed_json['subjects']:
                for i, chapter in enumerate(subject.get('chapters', [])):
                    # Assign chapter number if not present
                    if 'chapter_number' not in chapter:
                        chapter['chapter_number'] = i + 1

                    # Extract chapter number and title from name if it exists (e.g., "Chapter 3: Introduction")
                    chapter_num_match = re.search(r'chapter\s+(\d+)(?:\s*:\s*(.+))?', chapter['name'], re.IGNORECASE)

                    if chapter_num_match:
                        # Update chapter_number based on the name
                        chapter['chapter_number'] = int(chapter_num_match.group(1))

                        # Extract title from the name if available
                        if chapter_num_match.group(2):
                            chapter_title = chapter_num_match.group(2).strip()
                            # Add or update the title field
                            chapter['title'] = chapter_title
                    else:
                        # If name doesn't contain chapter number, update the name to include it
                        chapter_num = chapter['chapter_number']
                        original_name = chapter['name']

                        # Check if name already starts with "Chapter X"
                        if not re.match(r'^chapter\s+\d+', original_name, re.IGNORECASE):
                            # If name contains a colon, keep the part after it
                            if ':' in original_name:
                                chapter_title = original_name.split(':', 1)[1].strip()
                                chapter['name'] = f"Chapter {chapter_num}: {chapter_title}"
                                chapter['title'] = chapter_title
                            else:
                                chapter['name'] = f"Chapter {chapter_num}: {original_name}"
                                chapter['title'] = original_name

                    # If title is still missing, extract it from the name
                    if 'title' not in chapter:
                        # Try to extract title from name
                        title_match = re.search(r'chapter\s+\d+\s*:\s*(.+)', chapter['name'], re.IGNORECASE)
                        if title_match:
                            chapter['title'] = title_match.group(1).strip()
                        else:
                            # Fallback: use a generic title
                            chapter['title'] = f"Chapter {chapter['chapter_number']} Content"

            # Print the extracted structure
            print(f"Extracted structure:")
            print(f"- Number of subjects: {len(parsed_json['subjects'])}")
            for subject in parsed_json['subjects']:
                print(f"  - Subject: {subject['name']}")
                print(f"    - Number of chapters: {len(subject.get('chapters', []))}")
                for chapter in subject.get('chapters', []):
                    print(f"      - Chapter: {chapter['name']} (Number: {chapter['chapter_number']}, Title: {chapter['title']})")
                    print(f"        - Number of objectives: {len(chapter.get('objectives', []))}")

            return parsed_json

        except json.JSONDecodeError as json_err:
            print(f"ERROR: JSON parsing error: {str(json_err)}")
            print(f"Raw JSON string: {json_str[:500]}...")
            logging.error(f"JSON parsing error: {str(json_err)}")
            logging.error(f"Raw JSON string: {json_str[:500]}...")

            # Return a default structure
            default_response = {
                "subjects": [
                    {
                        "name": "Extracted Content",
                        "chapters": [
                            {
                                "name": "Chapter 1: Introduction to Content",
                                "chapter_number": 1,
                                "title": "Introduction to Content",
                                "content": "This chapter introduces the main concepts and topics covered in the document.",
                                "objectives": [
                                    {
                                        "text": "Understand the main concepts presented in the document",
                                        "estimated_time_minutes": 30,
                                        "difficulty_level": 2
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
            print(f"Using default response structure due to JSON parsing error")
            return default_response

    except Exception as e:
        print(f"ERROR: Error processing document with Gemini: {str(e)}")
        print(f"Exception type: {type(e).__name__}")
        import traceback
        print(f"Exception traceback: {traceback.format_exc()}")
        logging.error(f"Error processing document with Gemini: {str(e)}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")

        # Return a default structure
        default_response = {
            "subjects": [
                {
                    "name": "Extracted Content",
                    "chapters": [
                        {
                            "name": "Chapter 1: Introduction to Content",
                            "chapter_number": 1,
                            "title": "Introduction to Content",
                            "content": "This chapter introduces the main concepts and topics covered in the document.",
                            "objectives": [
                                {
                                    "text": "Understand the main concepts presented in the document",
                                    "estimated_time_minutes": 30,
                                    "difficulty_level": 2
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        print(f"Using default response structure due to general exception")
        return default_response

def filter_document_content(content: str) -> str:
    """
    Filter out non-educational and irrelevant material from a raw document.

    This function processes the full text extracted from an educational document
    and removes sections that do not contribute directly to learning outcomes or
    subject matter comprehension.

    Specifically, it excludes:
        - Cover pages and table of contents
        - Legal disclaimers, licensing info, copyright
        - Publisher information, author bios, footers, headers
        - Marketing content, advertisements, and page numbers
        - Generic introductions or forewords not tied to specific learning objectives

    Args:
        content (str): The raw text content extracted from the document.

    Returns:
        str: Cleaned and filtered content containing only instructional or educationally relevant material.
    """
    logging.info("Filtering document content to remove non-relevant material")

    try:
        # Import required libraries
        import re

        # Define patterns for common non-relevant content
        patterns_to_remove = [
            # Cover page patterns
            r'(?i)confidential document',
            r'(?i)all rights reserved',
            r'(?i)copyright \d{4}',

            # Legal disclaimers
            r'(?i)this document is provided for informational purposes only',
            r'(?i)disclaimer:.*?(?:\n\n|\Z)',

            # Headers and footers
            r'(?i)page \d+ of \d+',
            r'(?im)^\d+$',  # Page numbers on their own line

            # Metadata
            r'(?i)created by:.*?(?:\n|\Z)',
            r'(?i)last modified:.*?(?:\n|\Z)',

            # Advertisements
            r'(?i)sponsored by.*?(?:\n\n|\Z)',
            r'(?i)advertisement.*?(?:\n\n|\Z)',
        ]

        # Apply filters
        filtered_content = content
        for pattern in patterns_to_remove:
            filtered_content = re.sub(pattern, '', filtered_content)

        # Remove excessive whitespace
        filtered_content = re.sub(r'\n{3,}', '\n\n', filtered_content)
        filtered_content = re.sub(r' {2,}', ' ', filtered_content)

        # Log the amount of content removed
        original_length = len(content)
        filtered_length = len(filtered_content)
        percent_removed = ((original_length - filtered_length) / original_length) * 100 if original_length > 0 else 0

        logging.info(f"Content filtering removed {original_length - filtered_length} characters ({percent_removed:.2f}%)")

        return filtered_content

    except Exception as e:
        logging.error(f"Error filtering document content: {str(e)}")
        # Return original content if filtering fails
        return content

def intelligent_content_splitter(content: str) -> Dict:
    """
    Use AI to intelligently split document content into chapters with complete content extraction

    Args:
        content: Full document content

    Returns:
        Dict with subjects, chapters (including full content), and objectives
    """
    logging.info("Using intelligent content splitter for complete chapter extraction")

    try:
        # Import required libraries
        import google.generativeai as genai
        import time
        from google.api_core.exceptions import ResourceExhausted

        # Configure the API key
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

        # Try different models in order of preference
        models = ['gemini-1.5-flash', 'gemini-1.0-pro', 'gemini-1.5-pro']
        model = None

        # Try each model until one works
        for model_name in models:
            try:
                print(f"Attempting to use model: {model_name}")
                model = genai.GenerativeModel(model_name)
                break
            except Exception as model_error:
                print(f"Error initializing model {model_name}: {str(model_error)}")
                continue

        if not model:
            print("Failed to initialize any Gemini model")
            raise Exception("Failed to initialize any Gemini model")

        # Filter content first
        filtered_content = filter_document_content(content)

        # Estimate token count
        estimated_tokens = estimate_token_count(filtered_content)
        char_count = len(filtered_content)

        print(f"Processing content with {char_count} characters (estimated {estimated_tokens} tokens)")
        logging.info(f"Processing content with {char_count} characters (estimated {estimated_tokens} tokens)")

        # Create specialized prompt for content splitting
        prompt = f"""
        You are an expert educational content analyzer specializing in intelligent document segmentation and content extraction.

        Your task is to analyze the following educational document and perform COMPLETE CONTENT EXTRACTION for each chapter:

        1. IDENTIFY the main subject/topic of the document
        2. DIVIDE the document into 3-7 logical chapters based on natural content boundaries
        3. EXTRACT the COMPLETE, FULL content for each chapter from the original document

        CRITICAL REQUIREMENTS for content extraction:
        - Extract ALL text, paragraphs, and content that belongs to each chapter
        - Include complete explanations, examples, details, and notes for each chapter
        - Preserve the original text structure and formatting
        - Do NOT summarize or shorten the content - extract the full text
        - Each chapter's content should be comprehensive and complete
        - Include all relevant information that a student would need to study that chapter

        Document Content:
        {filtered_content}

        Return ONLY valid JSON with this exact structure:
        {{
            "subjects": [
                {{
                    "name": "Subject Name",
                    "chapters": [
                        {{
                            "name": "Chapter 1: Chapter Title",
                            "chapter_number": 1,
                            "title": "Chapter Title",
                            "content": "THE COMPLETE, FULL CONTENT OF THIS CHAPTER EXTRACTED FROM THE DOCUMENT. This should include all paragraphs, explanations, examples, and details that belong to this chapter. Do not summarize - extract the complete text.",
                            "objectives": [
                                {{
                                    "text": "Specific learning objective",
                                    "estimated_time_minutes": 30,
                                    "difficulty_level": 2
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}

        IMPORTANT: The "content" field must contain the COMPLETE, FULL text content for each chapter, not a summary.
        """

        # Log the prompt length
        print(f"Content splitter prompt length: {len(prompt)} characters")
        logging.info(f"Content splitter prompt length: {len(prompt)} characters")

        # Implement retry logic with exponential backoff
        max_retries = 3
        retry_count = 0
        base_delay = 2
        response = None

        while retry_count < max_retries:
            try:
                print(f"Calling Gemini model for content splitting (attempt {retry_count + 1}/{max_retries})...")
                response = model.generate_content(prompt)
                print(f"Received response from Gemini AI")
                break
            except ResourceExhausted as quota_error:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"Maximum retries ({max_retries}) exceeded. Giving up.")
                    raise

                delay = base_delay * (2 ** (retry_count - 1))
                print(f"Hit rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                logging.warning(f"Hit Gemini API rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                time.sleep(delay)
            except Exception as e:
                print(f"Error calling Gemini API: {str(e)}")
                raise

        if not response:
            raise Exception("Failed to get response from Gemini API after retries")

        # Extract JSON from response
        json_str = response.text.replace('```json', '').replace('```', '').strip()

        # Clean up JSON
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)

        # Parse JSON
        try:
            parsed_json = json.loads(json_str)

            # Validate structure
            if 'subjects' not in parsed_json or not parsed_json['subjects']:
                logging.warning("Missing or empty 'subjects' in content splitter response")
                return create_default_content_structure(filtered_content)

            # Validate that chapters have content
            for subject in parsed_json['subjects']:
                for chapter in subject.get('chapters', []):
                    if not chapter.get('content') or len(chapter.get('content', '').strip()) < 100:
                        logging.warning(f"Chapter '{chapter.get('title', 'Unknown')}' has insufficient content")
                        # Try to extract content from the original document
                        chapter['content'] = extract_chapter_content_fallback(filtered_content, chapter.get('title', ''))

            print(f"Successfully extracted {len(parsed_json['subjects'])} subjects with complete content")
            return parsed_json

        except json.JSONDecodeError as e:
            logging.error(f"JSON parsing error in content splitter: {str(e)}")
            return create_default_content_structure(filtered_content)

    except Exception as e:
        logging.error(f"Error in intelligent content splitter: {str(e)}")
        return create_default_content_structure(content)


def create_default_content_structure(content: str) -> Dict:
    """Create a default structure when AI processing fails"""
    return {
        "subjects": [
            {
                "name": "Extracted Content",
                "chapters": [
                    {
                        "name": "Chapter 1: Complete Document Content",
                        "chapter_number": 1,
                        "title": "Complete Document Content",
                        "content": content,  # Use the full content as a single chapter
                        "objectives": [
                            {
                                "text": "Understand and analyze the main concepts presented in the document",
                                "estimated_time_minutes": 45,
                                "difficulty_level": 2
                            },
                            {
                                "text": "Apply the knowledge gained from the document content",
                                "estimated_time_minutes": 30,
                                "difficulty_level": 3
                            }
                        ]
                    }
                ]
            }
        ]
    }


def extract_chapter_content_fallback(content: str, chapter_title: str) -> str:
    """Fallback method to extract chapter content based on title"""
    # Simple heuristic to extract content around the chapter title
    lines = content.split('\n')
    chapter_lines = []
    found_chapter = False

    for i, line in enumerate(lines):
        if chapter_title.lower() in line.lower() or found_chapter:
            found_chapter = True
            chapter_lines.append(line)

            # Stop if we find the next chapter
            if i > 0 and any(keyword in line.lower() for keyword in ['chapter', 'section']) and chapter_title.lower() not in line.lower():
                break

    return '\n'.join(chapter_lines) if chapter_lines else content[:5000]  # Fallback to first 5000 chars


def enhanced_process_document_with_gemini(content: str) -> Dict:
    """
    Enhanced version of process_document_with_gemini that focuses on educational content
    and provides more detailed learning objectives

    Args:
        content: Filtered document content

    Returns:
        Structured data with subjects, chapters, and enhanced learning objectives
    """
    logging.info("Processing document with enhanced Gemini AI prompt")

    # First filter the content
    filtered_content = filter_document_content(content)

    # Use the existing process_document_with_gemini function but with an enhanced prompt
    try:
        # Import required libraries
        import google.generativeai as genai
        import time
        from google.api_core.exceptions import ResourceExhausted

        # Configure the API key
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

        # Try different models in order of preference
        models = ['gemini-1.5-flash', 'gemini-1.0-pro', 'gemini-1.5-pro']
        model = None

        # Try each model until one works
        for model_name in models:
            try:
                print(f"Attempting to use model: {model_name}")
                model = genai.GenerativeModel(model_name)
                break
            except Exception as model_error:
                print(f"Error initializing model {model_name}: {str(model_error)}")
                continue

        if not model:
            print("Failed to initialize any Gemini model")
            raise Exception("Failed to initialize any Gemini model")

        # Use the full content without truncation
        truncated_content = filtered_content

        # Estimate token count
        estimated_tokens = estimate_token_count(filtered_content)
        char_count = len(filtered_content)

        print(f"Using full content with {char_count} characters (estimated {estimated_tokens} tokens)")
        logging.info(f"Using full content with {char_count} characters (estimated {estimated_tokens} tokens)")

        # Log token count for monitoring
        print(f"TOKEN COUNT ESTIMATE: {estimated_tokens} tokens")
        logging.warning(f"TOKEN COUNT ESTIMATE: {estimated_tokens} tokens")

        # Enhanced prompt for better educational content extraction
        prompt = f"""
        You are an expert AI-powered educational analyst and curriculum designer. Your role is to transform raw educational material into an optimized, structured learning experience that reflects best practices in instructional design and adaptive tutoring.

Your task is to analyze the following document and extract:

1. Core Subject Area
- Determine the main subject or discipline of the document (e.g., Organic Chemistry, Microeconomics, Web Development).
- If the subject is interdisciplinary, clarify both the primary and secondary disciplines.

2. Chapter Breakdown with Complete Content Extraction
- Divide the document into 3 to 7 logical chapters based exclusively on actual educational content.
- For each chapter, extract the COMPLETE, FULL content that belongs to that chapter from the document.
- Include ALL paragraphs, text, explanations, examples, and details that belong to each chapter.
- Preserve the original text structure and formatting as much as possible.
- Ignore all non-instructional elements such as cover pages, metadata, disclaimers, or introductions.
- Format each chapter title as: Chapter X (chapter number): Title (e.g., Chapter 1: Introduction to Algorithms)

3. Learning Objectives per Chapter
For each chapter, identify 3–5 specific learning objectives. For each objective, include:
- A concise and clear statement of what the student will learn, starting with an action verb (e.g., explain, identify, analyze, construct).
- An estimated time to complete (in minutes), typically between 15–60 minutes.
- A difficulty level from 1 (easiest) to 5 (most difficult).
- Each objective should be a meaningful and assessable unit of learning.

4. Enhanced Learning Design (If content permits)
- Ensure objectives progress from basic to advanced in logical order.
- Cross-reference objectives that depend on concepts from earlier chapters.
- Suggest an assessment method for each objective (e.g., multiple choice, short answer, diagram labeling).
- Recommend an engagement format to support the learning objective (e.g., flashcards, visual map, interactive tool).

5. Output Formatting
- Use clear, structured formatting suitable for integration in a digital AI Tutor system.
- Use bullet points or subheaders where needed for readability and clarity.

IMPORTANT: Focus strictly on educational content. Do not include legal disclaimers, metadata, advertisements, or introductory fluff. The goal is to extract the true curriculum value from the material and structure it for optimal teaching and learning.

        Document Content:
        {truncated_content}

        Return ONLY valid JSON with the following structure:
        {{
            "subjects": [
                {{
                    "name": "Subject Name",
                    "chapters": [
                        {{
                            "name": "Chapter 1: Chapter Title",
                            "chapter_number": 1,
                            "title": "Chapter Title",
                            "content": "The actual text content of this chapter extracted from the document. This should include all the relevant text, paragraphs, and information that belongs to this chapter section.",
                            "objectives": [
                                {{
                                    "text": "Objective description",
                                    "estimated_time_minutes": 30,
                                    "difficulty_level": 2
                                }},
                                ...
                            ]
                        }},
                        ...
                    ]
                }}
            ]
        }}

        IMPORTANT:
        - Always format chapter names exactly as 'Chapter X: Title', where X is the chapter number (e.g., Chapter 1: Introduction)
        - Ensure the numeric value in 'chapter_number' matches X in the title
        - Always include a separate 'title' field containing ONLY the title part (without "Chapter X:")
        - The 'name' field should contain the full "Chapter X: Title" format
        - The 'chapter_number' field should contain ONLY the number (1, 2, 3, etc.)

        Output must be in pure JSON—do not include explanations, markdown, or any extra text outside the JSON structure.
        """

        # Log the prompt length
        print(f"Enhanced Gemini prompt length: {len(prompt)} characters")
        logging.info(f"Enhanced Gemini prompt length: {len(prompt)} characters")

        # Implement retry logic with exponential backoff
        max_retries = 5
        retry_count = 0
        base_delay = 2  # seconds
        response = None

        while retry_count < max_retries:
            try:
                # Call the Gemini model
                print(f"Calling Gemini model (attempt {retry_count + 1}/{max_retries})...")
                response = model.generate_content(prompt)
                print(f"Received response from Gemini AI")
                # If we get here, the call was successful
                break
            except ResourceExhausted as quota_error:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"Maximum retries ({max_retries}) exceeded. Giving up.")
                    raise

                # Calculate delay with exponential backoff
                delay = base_delay * (2 ** (retry_count - 1))
                print(f"Hit rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                logging.warning(f"Hit Gemini API rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                time.sleep(delay)
            except Exception as e:
                # For other errors, don't retry
                print(f"Error calling Gemini API: {str(e)}")
                raise

        if not response:
            raise Exception("Failed to get response from Gemini API after retries")

        # Extract JSON from Gemini's response
        json_str = response.text.replace('```json', '').replace('```', '').strip()

        # Clean up any potential issues with the JSON
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)

        # Parse the JSON
        try:
            parsed_json = json.loads(json_str)

            # Validate the structure
            if 'subjects' not in parsed_json or not parsed_json['subjects']:
                logging.warning("Missing or empty 'subjects' in Gemini response")
                # Create a default structure
                default_response = {
                    "subjects": [
                        {
                            "name": "Extracted Content",
                            "chapters": [
                                {
                                    "name": "Chapter 1: Introduction to Content",
                                    "chapter_number": 1,
                                    "title": "Introduction to Content",
                                    "content": "This chapter introduces the main concepts and topics covered in the document.",
                                    "objectives": [
                                        {
                                            "text": "Understand the main concepts presented in the document",
                                            "estimated_time_minutes": 30,
                                            "difficulty_level": 2
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
                return default_response

            # Ensure all chapters have chapter_number, title, and proper naming format
            for subject in parsed_json['subjects']:
                for i, chapter in enumerate(subject.get('chapters', [])):
                    # Assign chapter number if not present
                    if 'chapter_number' not in chapter:
                        chapter['chapter_number'] = i + 1

                    # Extract chapter number and title from name if it exists (e.g., "Chapter 3: Introduction")
                    chapter_num_match = re.search(r'chapter\s+(\d+)(?:\s*:\s*(.+))?', chapter['name'], re.IGNORECASE)

                    if chapter_num_match:
                        # Update chapter_number based on the name
                        chapter['chapter_number'] = int(chapter_num_match.group(1))

                        # Extract title from the name if available
                        if chapter_num_match.group(2):
                            chapter_title = chapter_num_match.group(2).strip()
                            # Add or update the title field
                            chapter['title'] = chapter_title
                    else:
                        # If name doesn't contain chapter number, update the name to include it
                        chapter_num = chapter['chapter_number']
                        original_name = chapter['name']

                        # Check if name already starts with "Chapter X"
                        if not re.match(r'^chapter\s+\d+', original_name, re.IGNORECASE):
                            # If name contains a colon, keep the part after it
                            if ':' in original_name:
                                chapter_title = original_name.split(':', 1)[1].strip()
                                chapter['name'] = f"Chapter {chapter_num}: {chapter_title}"
                                chapter['title'] = chapter_title
                            else:
                                chapter['name'] = f"Chapter {chapter_num}: {original_name}"
                                chapter['title'] = original_name

                    # If title is still missing, extract it from the name
                    if 'title' not in chapter:
                        # Try to extract title from name
                        title_match = re.search(r'chapter\s+\d+\s*:\s*(.+)', chapter['name'], re.IGNORECASE)
                        if title_match:
                            chapter['title'] = title_match.group(1).strip()
                        else:
                            # Fallback: use a generic title
                            chapter['title'] = f"Chapter {chapter['chapter_number']} Content"

            return parsed_json

        except json.JSONDecodeError as json_err:
            logging.error(f"JSON parsing error: {str(json_err)}")

            # Return a default structure
            default_response = {
                "subjects": [
                    {
                        "name": "Extracted Content",
                        "chapters": [
                            {
                                "name": "Chapter 1: Introduction to Content",
                                "chapter_number": 1,
                                "title": "Introduction to Content",
                                "content": "This chapter introduces the main concepts and topics covered in the document.",
                                "objectives": [
                                    {
                                        "text": "Understand the main concepts presented in the document",
                                        "estimated_time_minutes": 30,
                                        "difficulty_level": 2
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
            return default_response

    except Exception as e:
        logging.error(f"Error processing document with enhanced Gemini: {str(e)}")

        # Return a default structure
        default_response = {
            "subjects": [
                {
                    "name": "Extracted Content",
                    "chapters": [
                        {
                            "name": "Chapter 1: Introduction to Content",
                            "chapter_number": 1,
                            "title": "Introduction to Content",
                            "objectives": [
                                {
                                    "text": "Understand the main concepts presented in the document",
                                    "estimated_time_minutes": 30,
                                    "difficulty_level": 2
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        return default_response

def validate_study_plan(
    subjects_data: Dict,
    start_date: date,
    end_date: date,
    daily_study_time: int,
    preferred_days: List[int] = None,
    unavailable_periods: List[Dict] = None
) -> Dict:
    """
    Validate if a study plan is feasible based on content and user constraints

    Args:
        subjects_data: Dictionary containing subjects, chapters, and objectives
        start_date: Start date for the study plan
        end_date: End date for the study plan
        daily_study_time: Daily study time in minutes
        preferred_days: List of preferred days of week (0=Monday, 6=Sunday)
        unavailable_periods: List of periods when user is unavailable

    Returns:
        Dictionary with validation results and suggestions
    """
    logging.info(f"Validating study plan: {start_date} to {end_date}, {daily_study_time} min/day")

    try:
        # Flatten all objectives into a single list
        all_objectives = []
        for subject in subjects_data.get('subjects', []):
            for chapter in subject.get('chapters', []):
                for objective in chapter.get('objectives', []):
                    # Store the objective along with its subject and chapter info
                    all_objectives.append({
                        'subject': subject['name'],
                        'chapter': chapter['name'],
                        'chapter_number': chapter.get('chapter_number', 1),
                        'text': objective['text'],
                        'estimated_time_minutes': objective.get('estimated_time_minutes', 30),
                        'difficulty_level': objective.get('difficulty_level', 1)
                    })

        # Calculate total time needed for all objectives
        total_needed_time = sum(obj['estimated_time_minutes'] for obj in all_objectives)

        # Calculate available days
        total_days = (end_date - start_date).days + 1

        # If preferred days are specified, adjust available days
        available_days = total_days
        if preferred_days:
            # Count how many of the days in the range are preferred days
            current_date = start_date
            preferred_day_count = 0
            while current_date <= end_date:
                if current_date.weekday() in preferred_days:
                    preferred_day_count += 1
                current_date += timedelta(days=1)
            available_days = preferred_day_count

        # Adjust for unavailable periods
        if unavailable_periods:
            unavailable_days = 0
            for period in unavailable_periods:
                period_start = period.get('start_date')
                period_end = period.get('end_date')
                if period_start and period_end:
                    # Convert string dates to date objects if needed
                    if isinstance(period_start, str):
                        period_start = datetime.strptime(period_start, '%Y-%m-%d').date()
                    if isinstance(period_end, str):
                        period_end = datetime.strptime(period_end, '%Y-%m-%d').date()

                    # Calculate overlap with study period
                    overlap_start = max(start_date, period_start)
                    overlap_end = min(end_date, period_end)
                    if overlap_end >= overlap_start:
                        overlap_days = (overlap_end - overlap_start).days + 1
                        unavailable_days += overlap_days

            available_days -= unavailable_days

        # Calculate total available study time
        total_available_time = available_days * daily_study_time

        # Check if plan is feasible
        is_feasible = total_available_time >= total_needed_time

        # Calculate time deficit if not feasible
        time_deficit = max(0, total_needed_time - total_available_time)

        # Calculate suggestions if not feasible
        suggestions = {}
        if not is_feasible:
            # Option 1: Extend end date
            additional_days_needed = math.ceil(time_deficit / daily_study_time)
            suggested_end_date = end_date + timedelta(days=additional_days_needed)

            # Option 2: Increase daily study time
            if available_days > 0:
                suggested_daily_time = math.ceil(total_needed_time / available_days)
            else:
                suggested_daily_time = daily_study_time  # Fallback

            suggestions = {
                "option_1": {
                    "type": "extend_end_date",
                    "suggested_end_date": suggested_end_date,
                    "additional_days": additional_days_needed
                },
                "option_2": {
                    "type": "increase_daily_time",
                    "suggested_daily_time": suggested_daily_time,
                    "additional_minutes_per_day": suggested_daily_time - daily_study_time
                }
            }

        # Return validation results
        return {
            "is_feasible": is_feasible,
            "total_objectives": len(all_objectives),
            "total_time_needed_minutes": total_needed_time,
            "available_days": available_days,
            "total_available_time_minutes": total_available_time,
            "time_deficit_minutes": time_deficit if not is_feasible else 0,
            "suggestions": suggestions if not is_feasible else {}
        }

    except Exception as e:
        logging.error(f"Error validating study plan: {str(e)}")
        # Return a basic error response
        return {
            "is_feasible": False,
            "error": str(e),
            "suggestions": {
                "option_1": {
                    "type": "extend_end_date",
                    "suggested_end_date": end_date + timedelta(days=7),
                    "additional_days": 7
                }
            }
        }

from datetime import date, datetime, timedelta
from typing import Dict, List, Optional
import logging
from sqlalchemy.orm import Session

def generate_study_schedule(
    subjects_data: Dict,
    start_date: date,
    end_date: date,
    daily_study_time: int,
    preferred_days: Optional[List[int]] = None,
    unavailable_periods: Optional[List[Dict]] = None,
    db_session: Optional[Session] = None
) -> Dict:
    """
    Generate and store a study schedule that:
    1. Properly distributes objectives across available days
    2. Avoids duplicate scheduling of objectives
    3. Updates all objectives in the database
    """
    try:
        # Validate input dates
        if end_date < start_date:
            raise ValueError("End date must be after start date")

        # Calculate total available study time
        total_days = (end_date - start_date).days + 1
        total_available_time = total_days * daily_study_time

        # Collect and deduplicate objectives
        objectives_map = {}  # objective_id -> objective_data
        unique_objectives = []

        for subject in subjects_data.get('subjects', []):
            for chapter in subject.get('chapters', []):
                for objective in chapter.get('objectives', []):
                    obj_id = objective.get('objective_id')
                    if not obj_id:
                        continue

                    if obj_id not in objectives_map:
                        objectives_map[obj_id] = {
                            'subject': subject['name'],
                            'chapter': chapter['name'],
                            'chapter_number': chapter.get('chapter_number', 1),
                            'text': objective['text'],
                            'estimated_time_minutes': objective.get('estimated_time_minutes', 30),
                            'difficulty_level': objective.get('difficulty_level', 1),
                            'objective_id': obj_id
                        }
                        unique_objectives.append(objectives_map[obj_id])

        # Sort objectives by chapter then difficulty (high difficulty first)
        unique_objectives.sort(key=lambda x: (x['chapter_number'], x['difficulty_level']), reverse=True)

        # Calculate total needed time
        total_needed_time = sum(obj['estimated_time_minutes'] for obj in unique_objectives)

        # Check feasibility
        if total_needed_time > total_available_time:
            # Calculate scale factor if needed
            scale_factor = total_available_time / total_needed_time
            for obj in unique_objectives:
                obj['estimated_time_minutes'] = int(obj['estimated_time_minutes'] * scale_factor)

        # Generate schedule
        schedule_items = []
        current_date = start_date
        remaining_time_today = daily_study_time
        update_batch = []

        for obj in unique_objectives:
            obj_time = obj['estimated_time_minutes']

            # Find next available date considering preferences/unavailability
            current_date = get_next_available_date(
                current_date,
                end_date,
                preferred_days,
                unavailable_periods
            )

            # If objective doesn't fit in remaining time, move to next day
            if obj_time > remaining_time_today and current_date < end_date:
                current_date += timedelta(days=1)
                current_date = get_next_available_date(
                    current_date,
                    end_date,
                    preferred_days,
                    unavailable_periods
                )
                remaining_time_today = daily_study_time

            # Schedule the objective
            schedule_items.append({
                'objective_data': obj,
                'scheduled_date': current_date
            })

            # Prepare database update
            update_batch.append({
                'objective_id': obj['objective_id'],
                'scheduled_date': current_date
            })

            # Update remaining time
            remaining_time_today -= obj_time

            # Move to next day if no time left
            if remaining_time_today <= 0 and current_date < end_date:
                current_date += timedelta(days=1)
                remaining_time_today = daily_study_time

        # Update database if session provided
        if db_session:
            try:
                update_count = update_objectives_in_database(db_session, update_batch)
                logging.info(f"Successfully updated {update_count} objectives in database")
            except Exception as db_error:
                logging.error(f"Database update failed: {str(db_error)}")
                raise

        return {
            'schedule_items': schedule_items,
            'metadata': {
                'total_objectives': len(unique_objectives),
                'start_date': start_date,
                'end_date': end_date,
                'daily_study_time': daily_study_time,
                'objectives_updated': len(update_batch)
            }
        }

    except Exception as e:
        logging.error(f"Study schedule generation failed: {str(e)}")
        raise

def get_next_available_date(
    current_date: date,
    end_date: date,
    preferred_days: Optional[List[int]] = None,
    unavailable_periods: Optional[List[Dict]] = None
) -> date:
    """Find the next available date considering constraints"""
    while current_date <= end_date:
        # Check if date is in unavailable period
        if unavailable_periods:
            unavailable = False
            for period in unavailable_periods:
                start = parse_date(period.get('start_date'))
                end = parse_date(period.get('end_date'))
                if start and end and start <= current_date <= end:
                    unavailable = True
                    break
            if unavailable:
                current_date += timedelta(days=1)
                continue

        # Check if date is preferred day (if preferences exist)
        if preferred_days and current_date.weekday() not in preferred_days:
            current_date += timedelta(days=1)
            continue

        return current_date

    return end_date

def parse_date(date_input) -> Optional[date]:
    """Parse date from string or return date object"""
    if isinstance(date_input, str):
        try:
            return datetime.strptime(date_input, '%Y-%m-%d').date()
        except ValueError:
            return None
    elif isinstance(date_input, date):
        return date_input
    return None

def update_objectives_in_database(db_session: Session, update_batch: List[Dict]) -> int:
    """Batch update objectives in database"""
    from models import Objective  # Import your SQLAlchemy model

    update_count = 0
    try:
        for item in update_batch:
            obj = db_session.get(Objective, item['objective_id'])
            if obj:
                obj.scheduled_date = item['scheduled_date']
                update_count += 1

        db_session.commit()
        return update_count
    except Exception as e:
        db_session.rollback()
        raise

def generate_flashcards_with_ai(content: str, subject_id: int, chapters_data: List[Dict]) -> List[Dict]:
    """
    Generate flashcards from document content using Gemini AI

    Args:
        content: Document content
        subject_id: Subject ID
        chapters_data: List of chapters with their IDs

    Returns:
        List of flashcards with chapter associations
    """
    print(f"\n\n==== STARTING FLASHCARD GENERATION ====")
    print(f"Subject ID: {subject_id}")
    print(f"Number of chapters: {len(chapters_data)}")
    print(f"Chapters data: {json.dumps(chapters_data, indent=2)}")
    logging.info(f"Generating flashcards for subject ID: {subject_id} with {len(chapters_data)} chapters")

    try:
        # Import required libraries
        import google.generativeai as genai
        import time
        from google.api_core.exceptions import ResourceExhausted

        # Configure the API key
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

        # Try different models in order of preference
        models = ['gemini-1.5-flash', 'gemini-1.0-pro', 'gemini-1.5-pro']
        model = None

        # Try each model until one works
        for model_name in models:
            try:
                print(f"Attempting to use model: {model_name}")
                model = genai.GenerativeModel(model_name)
                break
            except Exception as model_error:
                print(f"Error initializing model {model_name}: {str(model_error)}")
                continue

        if not model:
            print("Failed to initialize any Gemini model")
            raise Exception("Failed to initialize any Gemini model")

        # Filter the content
        filtered_content = filter_document_content(content)

        # Use the full content without truncation
        truncated_content = filtered_content

        # Estimate token count
        estimated_tokens = estimate_token_count(filtered_content)
        char_count = len(filtered_content)

        print(f"Using full content with {char_count} characters (estimated {estimated_tokens} tokens)")
        logging.info(f"Using full content with {char_count} characters (estimated {estimated_tokens} tokens)")

        # Log token count for monitoring
        print(f"TOKEN COUNT ESTIMATE: {estimated_tokens} tokens")
        logging.warning(f"TOKEN COUNT ESTIMATE: {estimated_tokens} tokens")

        # Create a mapping of chapter numbers to chapter IDs
        chapter_id_map = {}
        for chapter in chapters_data:
            chapter_number = chapter.get('chapter_number')
            chapter_id = chapter.get('id')
            if chapter_number and chapter_id:
                chapter_id_map[chapter_number] = chapter_id

        # Generate flashcards for each chapter
        all_flashcards = []

        # Set a maximum number of flashcards per chapter to prevent excessive generation
        MAX_FLASHCARDS_PER_CHAPTER = 20

        for chapter_number, chapter_id in chapter_id_map.items():
            chapter_info = next((c for c in chapters_data if c.get('id') == chapter_id), None)
            if not chapter_info:
                continue

            chapter_title = chapter_info.get('title', f"Chapter {chapter_number}")

            # Log the chapter we're processing
            print(f"Generating flashcards for Chapter {chapter_number}: {chapter_title} (ID: {chapter_id})")

            # Create prompt for flashcard generation
            prompt = f"""
            You are an expert educational content creator specializing in creating high-quality flashcards for students.

            Based on the following educational content about {chapter_title}, create 10 effective flashcards that will help students master the key concepts.

            Document Content:
            {truncated_content}

            For each flashcard:
            1. Create a clear, concise question that tests understanding (not just memorization)
            2. Provide a comprehensive but concise answer
            3. Focus on the most important concepts from Chapter {chapter_number}: {chapter_title}
            4. Include a mix of different question types (definitions, explanations, applications, etc.)
            5. Ensure the flashcards progress from basic to more advanced concepts

            Return ONLY valid JSON with the following structure:
            {{
                "flashcards": [
                    {{
                        "question": "Question text here",
                        "answer": "Answer text here",
                        "type": "basic",
                        "tags": ["key concept", "important term"]
                    }},
                    ...
                ]
            }}

            Do not include any explanations, markdown formatting, or additional text outside the JSON structure.
            """

            # Log the prompt length
            print(f"Flashcard generation prompt length: {len(prompt)} characters")
            logging.info(f"Flashcard generation prompt length: {len(prompt)} characters")

            # Implement retry logic with exponential backoff
            max_retries = 5
            retry_count = 0
            base_delay = 2  # seconds
            response = None

            while retry_count < max_retries:
                try:
                    # Call the Gemini model
                    print(f"Calling Gemini model for flashcards (attempt {retry_count + 1}/{max_retries})...")
                    response = model.generate_content(prompt)
                    print(f"Received response from Gemini AI for flashcards")
                    # If we get here, the call was successful
                    break
                except ResourceExhausted as quota_error:
                    retry_count += 1
                    if retry_count >= max_retries:
                        print(f"Maximum retries ({max_retries}) exceeded. Giving up.")
                        raise

                    # Calculate delay with exponential backoff
                    delay = base_delay * (2 ** (retry_count - 1))
                    print(f"Hit rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                    logging.warning(f"Hit Gemini API rate limit. Retrying in {delay} seconds... (Attempt {retry_count}/{max_retries})")
                    time.sleep(delay)
                except Exception as e:
                    # For other errors, don't retry
                    print(f"Error calling Gemini API: {str(e)}")
                    raise

            if not response:
                raise Exception("Failed to get response from Gemini API after retries")

            # Extract JSON from Gemini's response
            json_str = response.text.replace('```json', '').replace('```', '').strip()

            # Clean up any potential issues with the JSON
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            # Parse the JSON
            try:
                parsed_json = json.loads(json_str)

                # Validate the structure
                if 'flashcards' not in parsed_json or not parsed_json['flashcards']:
                    logging.warning(f"Missing or empty 'flashcards' in Gemini response for chapter {chapter_number}")
                    continue

                # Add chapter and subject IDs to each flashcard
                # Limit the number of flashcards per chapter
                chapter_flashcard_count = 0

                for flashcard in parsed_json['flashcards']:
                    # Skip if we've reached the maximum for this chapter
                    if chapter_flashcard_count >= MAX_FLASHCARDS_PER_CHAPTER:
                        print(f"Reached maximum of {MAX_FLASHCARDS_PER_CHAPTER} flashcards for Chapter {chapter_number}. Skipping remaining cards.")
                        break

                    flashcard['chapter_id'] = chapter_id
                    flashcard['subject_id'] = subject_id
                    flashcard['ai_generated'] = True

                    # Ensure required fields are present
                    if 'type' not in flashcard:
                        flashcard['type'] = 'basic'
                    if 'tags' not in flashcard:
                        flashcard['tags'] = []

                    # Add to the list of all flashcards
                    all_flashcards.append(flashcard)
                    chapter_flashcard_count += 1

            except json.JSONDecodeError as json_err:
                logging.error(f"JSON parsing error for flashcards: {str(json_err)}")
                continue

        logging.info(f"Generated {len(all_flashcards)} flashcards across {len(chapter_id_map)} chapters")
        return all_flashcards

    except Exception as e:
        logging.error(f"Error generating flashcards with AI: {str(e)}")
        import traceback
        logging.error(f"Exception traceback: {traceback.format_exc()}")
        return []

def prepare_database_data(
    user_id: int,
    subjects_data: Dict,
    study_plan_data: Dict,
    file_info: Dict = None
) -> Dict:
    """
    Prepare data for insertion into the database

    Args:
        user_id: ID of the user
        subjects_data: Dictionary containing subjects, chapters, and objectives
        study_plan_data: Dictionary containing study plan details
        file_info: Optional dictionary with file information

    Returns:
        Dictionary with structured data ready for database insertion
    """
    logging.info(f"Preparing database data for user ID: {user_id}")

    try:
        # Extract schedule items and metadata
        schedule_items = study_plan_data.get('schedule_items', [])
        objective_display_dates = study_plan_data.get('objective_display_dates', {})
        metadata = study_plan_data.get('metadata', {})

        # Prepare curriculum data
        curriculum_data = {
            'user_id': user_id,
            'title': subjects_data.get('subjects', [{}])[0].get('name', 'Untitled Curriculum'),
            'content': json.dumps(subjects_data),  # Store the full JSON for reference
            'uploaded_at': datetime.now()
        }

        # Prepare study plan data according to actual table structure
        study_plan = {
            'user_id': user_id,
            'curriculum_id': file_info.get('id') if file_info else None,  # Reference to files table
            'name': f"Study Plan for {curriculum_data['title']}",
            'start_date': metadata.get('start_date', datetime.now().date()),
            'end_date': metadata.get('end_date', (datetime.now() + timedelta(days=30)).date()),
            'daily_study_time_minutes': metadata.get('daily_study_time', 60),
            'preferred_days': metadata.get('preferred_days', None),  # VARCHAR(50) field for preferred days
            'unavailable_periods': json.dumps(metadata.get('unavailable_periods', [])) if metadata.get('unavailable_periods') else None,  # TEXT field for JSON string
            'created_at': datetime.now()
        }

        # Prepare subjects, chapters, and objectives data
        subjects = []
        for subject_data in subjects_data.get('subjects', []):
            subject = {
                'name': subject_data['name'],
                'chapters': []
            }

            for chapter_data in subject_data.get('chapters', []):
                chapter = {
                    'name': chapter_data['name'],
                    'chapter_number': chapter_data.get('chapter_number', 1),
                    'objectives': []
                }

                for objective_data in chapter_data.get('objectives', []):
                    # Find the scheduled date for this objective
                    scheduled_date = None
                    for item in schedule_items:
                        if item['objective_data']['text'] == objective_data['text']:
                            scheduled_date = item['scheduled_date']
                            break

                    objective = {
                        'text': objective_data['text'],
                        'estimated_time_minutes': objective_data.get('estimated_time_minutes', 30),
                        'difficulty_level': objective_data.get('difficulty_level', 1),
                        'display_date': scheduled_date
                    }

                    chapter['objectives'].append(objective)

                subject['chapters'].append(chapter)

            subjects.append(subject)

        # Prepare file data if provided
        file_data = None
        if file_info:
            file_data = {
                'filename': file_info.get('filename', 'unknown.txt'),
                'originalname': file_info.get('originalname', 'unknown.txt'),
                'filetype': file_info.get('filetype', 'text/plain'),
                'filepath': file_info.get('filepath', '/unknown.txt'),
                'uploaded_at': datetime.now()
            }

        # Return the complete structure
        return {
            'curriculum': curriculum_data,
            'study_plan': study_plan,
            'subjects': subjects,
            'file': file_data,
            'user_accepted_suggestion': metadata.get('user_accepted_suggestion', False)
        }

    except Exception as e:
        logging.error(f"Error preparing database data: {str(e)}")
        raise


def generate_quiz_with_ai_enhanced(content: str, subject_id: int, question_types: List[str], difficulty: str = 'medium', chapters_info: List[Dict] = None, is_all_chapters: bool = False) -> Dict:
    """
    Generate a comprehensive quiz from document content using Gemini AI with support for multiple chapters

    Args:
        content: Document content (either full document or chapter-specific content)
        subject_id: Subject ID
        question_types: List of question types to include ['multiple_choice', 'short_answer']
        difficulty: Overall difficulty level ('easy', 'medium', 'hard')
        chapters_info: List of chapter dictionaries with id, chapter_number, and title
        is_all_chapters: Whether all chapters are selected (affects processing limits)

    Returns:
        Dict with success status and quiz data
    """

    # Apply different limits based on whether all chapters are selected
    if is_all_chapters:
        # More generous limits for comprehensive quiz generation
        MAX_CHAPTERS = 25  # Allow more chapters for full document processing
        MAX_CONTENT_LENGTH = 100000  # ~100KB for full document
        logging.info("Processing all chapters - using extended limits for comprehensive quiz generation")
    else:
        # Stricter limits for specific chapter selection
        MAX_CHAPTERS = 5
        MAX_CONTENT_LENGTH = 50000  # ~50KB of text
        logging.info("Processing specific chapters - using standard limits")

    # Apply chapter limits only if not processing all chapters
    if not is_all_chapters and chapters_info and len(chapters_info) > MAX_CHAPTERS:
        logging.warning(f"Too many chapters selected ({len(chapters_info)}). Limiting to first {MAX_CHAPTERS} chapters to prevent timeout.")
        chapters_info = chapters_info[:MAX_CHAPTERS]

    # Apply content limits
    if len(content) > MAX_CONTENT_LENGTH:
        logging.warning(f"Content too large ({len(content)} chars). Truncating to {MAX_CONTENT_LENGTH} chars to prevent timeout.")
        content = content[:MAX_CONTENT_LENGTH] + "\n\n[Content truncated for processing...]"

    try:
        # Get subject name
        from models import Subject
        from app import db
        subject = db.session.get(Subject, subject_id)
        if not subject:
            return {"success": False, "error": "Subject not found"}

        subject_name = subject.name

        # Build chapter information string and processing mode
        chapter_info = ""
        processing_mode = ""

        if chapters_info and len(chapters_info) > 0:
            if is_all_chapters:
                chapter_titles = [f"{ch['chapter_number']}: {ch['title']}" for ch in chapters_info]
                chapter_info = f" covering all chapters: {', '.join(chapter_titles)}"
                processing_mode = "comprehensive"
            elif len(chapters_info) == 1:
                chapter = chapters_info[0]
                chapter_info = f" for {chapter['chapter_number']}: {chapter['title']}"
                processing_mode = "focused"
            else:
                chapter_titles = [f"{ch['chapter_number']}: {ch['title']}" for ch in chapters_info]
                chapter_info = f" for selected chapters: {', '.join(chapter_titles)}"
                processing_mode = "multi-chapter"

        # Create chapter-specific instruction based on processing mode
        chapter_instruction = ""
        if chapters_info:
            if processing_mode == "comprehensive":
                chapter_list = [f"{ch['chapter_number']}: {ch['title']}" for ch in chapters_info]
                chapter_instruction = f"""
        8. COMPREHENSIVE COVERAGE: Generate questions that cover content from ALL chapters in the document
        9. Distribute questions evenly across all chapters: {', '.join(chapter_list)}
        10. Include questions that test understanding of connections between different chapters
        11. Cover the full breadth of the subject material"""
            elif processing_mode == "focused":
                chapter = chapters_info[0]
                chapter_instruction = f"""
        8. FOCUSED CONTENT: Generate questions ONLY from {chapter['chapter_number']}: {chapter['title']}
        9. Deep dive into this specific chapter's content
        10. Ignore content from other chapters in the document
        11. Test detailed understanding of this chapter's concepts"""
            else:  # multi-chapter
                chapter_list = [f"{ch['chapter_number']}: {ch['title']}" for ch in chapters_info]
                chapter_instruction = f"""
        8. SELECTIVE COVERAGE: Generate questions ONLY from these selected chapters: {', '.join(chapter_list)}
        9. Distribute questions across the selected chapters
        10. Ignore content from non-selected chapters
        11. Focus on the most important concepts from each selected chapter"""

        # Create the AI prompt for quiz generation
        prompt = f"""
        You are an expert educational content creator. Generate a comprehensive quiz from the following document content for the subject "{subject_name}"{chapter_info}.

        CRITICAL REQUIREMENTS:
        1. Generate EXACTLY 20 high-quality questions - NO MORE, NO LESS
        2. Include the following question types: {', '.join(question_types)}
        3. Overall difficulty level: {difficulty}
        4. Questions should progress from easier to harder
        5. Cover diverse topics from the document content
        6. Each question should test understanding, not just memorization
        7. Provide clear explanations for all answers{chapter_instruction}

        MANDATORY: You MUST create exactly 20 questions. If you create fewer than 20, the quiz will be rejected.

        QUESTION TYPE SPECIFICATIONS:
        - Multiple Choice: 4 options (A, B, C, D) with exactly one correct answer
        - Short Answer: Open-ended questions requiring 1-3 sentence responses

        QUESTION DISTRIBUTION (EXACTLY 20 QUESTIONS):
        - Questions 1-6: Easy difficulty (1-2 points each)
        - Questions 7-14: Medium difficulty (2-3 points each)
        - Questions 15-20: Hard difficulty (3-5 points each)

        DIFFICULTY PROGRESSION:
        - Easy (1-2 points): Basic recall and understanding
        - Medium (2-3 points): Application and analysis
        - Hard (3-5 points): Synthesis and evaluation

        DOCUMENT CONTENT:
        {content}

        Generate the quiz in the following JSON format with EXACTLY 20 questions:
        {{
            "title": "Quiz Title",
            "description": "Brief description of the quiz",
            "difficulty": "{difficulty}",
            "time_limit": 30,
            "total_questions": 20,
            "questions": [
                {{
                    "question_number": 1,
                    "question_text": "Question text here",
                    "question_type": "multiple_choice",
                    "options": ["Option A", "Option B", "Option C", "Option D"],
                    "correct_answer": "Option A",
                    "explanation": "Explanation of why this is correct",
                    "points": 1,
                    "difficulty_level": "easy"
                }},
                {{
                    "question_number": 2,
                    "question_text": "Question text here",
                    "question_type": "short_answer",
                    "correct_answer": "Expected answer or key points",
                    "explanation": "Explanation of the expected answer",
                    "points": 2,
                    "difficulty_level": "easy"
                }},
                ... (continue until question 20)
            ]
        }}

        REMEMBER: The questions array must contain exactly 20 question objects numbered 1 through 20.

        IMPORTANT:
        - Ensure JSON is valid and properly formatted
        - For multiple choice, correct_answer should match exactly one of the options
        - For short answer, correct_answer should be the expected response or key points
        - Points should increase with difficulty (easy: 1-2, medium: 2-3, hard: 3-5)
        - Cover diverse topics from the document content
        """

        # Generate the quiz
        logging.info(f"Generating quiz for subject: {subject_name} with chapters: {chapters_info}")
        logging.info(f"Processing mode: {processing_mode}, Content length: {len(content)} chars")

        # Configure generation with timeout settings
        generation_config = genai.types.GenerationConfig(
            temperature=0.7,
            top_p=0.8,
            top_k=40,
            max_output_tokens=8192,
        )

        # Add timeout handling (cross-platform)
        import threading
        import time

        # Use threading for cross-platform timeout
        response = None
        exception_occurred = None

        def generate_with_timeout():
            nonlocal response, exception_occurred
            try:
                response = model.generate_content(prompt, generation_config=generation_config)
            except Exception as e:
                exception_occurred = e

        # Start the generation in a separate thread
        thread = threading.Thread(target=generate_with_timeout)
        thread.daemon = True
        thread.start()

        # Wait for completion or timeout (45 seconds)
        thread.join(timeout=45)

        if thread.is_alive():
            # Thread is still running, meaning it timed out
            raise TimeoutError("Gemini API call timed out after 45 seconds")

        if exception_occurred:
            raise exception_occurred

        if response is None:
            raise Exception("No response received from Gemini API")

        if not response or not response.text:
            return {"success": False, "error": "No response from AI model"}

        # Clean and parse the response
        response_text = response.text.strip()
        logging.info(f"Raw AI response length: {len(response_text)} chars")
        logging.info(f"Raw AI response preview: {response_text[:200]}...")

        # Remove markdown code blocks if present
        if response_text.startswith('```json'):
            response_text = response_text[7:]
        if response_text.startswith('```'):
            response_text = response_text[3:]
        if response_text.endswith('```'):
            response_text = response_text[:-3]

        response_text = response_text.strip()
        logging.info(f"Cleaned response preview: {response_text[:200]}...")

        try:
            quiz_data = json.loads(response_text)

            # Validate the quiz structure
            if not isinstance(quiz_data, dict) or 'questions' not in quiz_data:
                return {"success": False, "error": "Invalid quiz structure in AI response"}

            if not isinstance(quiz_data['questions'], list) or len(quiz_data['questions']) == 0:
                return {"success": False, "error": "No questions generated"}

            # Check if we got the required number of questions
            questions_count = len(quiz_data['questions'])
            if questions_count < 15:
                return {"success": False, "error": f"Insufficient questions generated: {questions_count}. Expected at least 15 questions."}

            # If we got fewer than 20 questions, log a warning but continue
            if questions_count < 20:
                logging.warning(f"Generated {questions_count} questions instead of 20 for chapters: {chapters_info}")

            # Ensure all questions have required fields
            for i, question in enumerate(quiz_data['questions']):
                if 'question_text' not in question:
                    question['question_text'] = f"Question {i+1}"
                if 'question_type' not in question:
                    question['question_type'] = 'multiple_choice'
                if 'correct_answer' not in question:
                    question['correct_answer'] = "No answer provided"
                if 'points' not in question:
                    question['points'] = 1
                if 'difficulty_level' not in question:
                    question['difficulty_level'] = difficulty
                if 'question_number' not in question:
                    question['question_number'] = i + 1

            # Calculate total points
            total_points = sum(q.get('points', 1) for q in quiz_data['questions'])
            quiz_data['total_points'] = total_points
            quiz_data['total_questions'] = len(quiz_data['questions'])

            logging.info(f"Successfully generated quiz with {len(quiz_data['questions'])} questions for chapters: {chapters_info}")
            return {"success": True, "quiz_data": quiz_data}

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse AI response as JSON: {str(e)}")
            logging.error(f"AI Response: {response.text[:500]}...")
            return {"success": False, "error": "Invalid response format from AI"}

    except TimeoutError as e:
        logging.error(f"Quiz generation timed out: {str(e)}")
        return {"success": False, "error": "Quiz generation timed out. Please try with fewer chapters or a smaller document."}
    except Exception as e:
        logging.error(f"Error generating quiz: {str(e)}")
        # Check if it's a specific Gemini API error
        if "504" in str(e) or "Deadline Exceeded" in str(e):
            return {"success": False, "error": "Request timed out. Please try with fewer chapters or a smaller document."}
        return {"success": False, "error": f"Error generating quiz: {str(e)}"}


def generate_quiz_with_ai(content: str, subject_id: int, question_types: List[str], difficulty: str = 'medium', chapter_id: int = None) -> Dict:
    """
    Generate a comprehensive quiz from document content using Gemini AI

    Args:
        content: Document content
        subject_id: Subject ID
        question_types: List of question types to include ['multiple_choice', 'short_answer']
        difficulty: Overall difficulty level ('easy', 'medium', 'hard')
        chapter_id: Optional chapter ID to focus quiz on specific chapter content

    Returns:
        Dict with success status and quiz data or error message
    """
    try:
        # Initialize Gemini model
        import google.generativeai as genai

        # Configure the API key
        genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))

        # Try different models in order of preference
        models = ['gemini-1.5-flash', 'gemini-1.0-pro', 'gemini-1.5-pro']
        model = None

        # Try each model until one works
        for model_name in models:
            try:
                model = genai.GenerativeModel(model_name)
                logging.info(f"Successfully initialized {model_name} model for quiz generation")
                break
            except Exception as model_error:
                logging.warning(f"Error initializing model {model_name}: {str(model_error)}")
                continue

        if not model:
            return {"success": False, "error": "Failed to initialize Gemini model"}

        # Get subject information
        from models import Subject, Chapter
        from app import db

        subject = db.session.get(Subject, subject_id)
        if not subject:
            return {"success": False, "error": "Subject not found"}

        subject_name = subject.name

        # Get chapter information if chapter_id is provided
        chapter_info = ""
        chapter_name = ""
        if chapter_id:
            chapter = db.session.get(Chapter, chapter_id)
            if chapter and chapter.subject_id == subject_id:
                chapter_name = chapter.title
                chapter_number = chapter.chapter_number or ""
                chapter_info = f" for {chapter_number}: {chapter_name}" if chapter_number else f" for {chapter_name}"
            else:
                return {"success": False, "error": "Chapter not found or doesn't belong to this subject"}

        # Determine question distribution based on types requested
        total_questions = 20  # Maximum as requested
        question_distribution = {}

        if len(question_types) == 1:
            question_distribution[question_types[0]] = total_questions
        else:
            # Split evenly between types
            per_type = total_questions // len(question_types)
            remainder = total_questions % len(question_types)

            for i, q_type in enumerate(question_types):
                question_distribution[q_type] = per_type + (1 if i < remainder else 0)

        # Create the AI prompt for quiz generation
        chapter_instruction = ""
        if chapter_id and chapter_name:
            chapter_instruction = f"""
        8. FOCUS SPECIFICALLY on content related to {chapter_info}
        9. Generate questions that are relevant to this specific chapter only
        10. If the document contains multiple chapters, prioritize content from the specified chapter"""

        prompt = f"""
        You are an expert educational content creator. Generate a comprehensive quiz from the following document content for the subject "{subject_name}"{chapter_info}.

        REQUIREMENTS:
        1. Create exactly {total_questions} questions total
        2. Question types to include: {', '.join(question_types)}
        3. Question distribution: {question_distribution}
        4. Overall difficulty: {difficulty}
        5. Progress from easy to difficult questions
        6. Cover the most important concepts from the document
        7. Ensure questions test understanding, not just memorization{chapter_instruction}

        DIFFICULTY PROGRESSION:
        - Questions 1-7: Easy level (basic concepts, definitions)
        - Questions 8-14: Medium level (application, analysis)
        - Questions 15-20: Hard level (synthesis, evaluation, complex scenarios)

        QUESTION TYPES:
        - Multiple Choice: Provide 4 options (A, B, C, D) with only one correct answer
        - Short Answer: Questions requiring 1-3 sentence responses

        CONTENT TO ANALYZE:
        {content[:8000]}  # Limit content to avoid token limits

        Return ONLY valid JSON with this exact structure:
        {{
            "title": "Quiz title based on document content{' - ' + chapter_name if chapter_name else ''}",
            "description": "Brief description of what the quiz covers{chapter_info if chapter_info else ''}",
            "difficulty": "{difficulty}",
            "total_questions": {total_questions},
            "question_types": {question_types},
            "questions": [
                {{
                    "question_number": 1,
                    "question_text": "Question text here",
                    "question_type": "multiple_choice",
                    "options": ["Option A", "Option B", "Option C", "Option D"],
                    "correct_answer": "Option A",
                    "explanation": "Explanation of why this is correct",
                    "points": 1,
                    "difficulty_level": "easy"
                }},
                {{
                    "question_number": 2,
                    "question_text": "Question text here",
                    "question_type": "short_answer",
                    "options": null,
                    "correct_answer": "Expected answer or key points",
                    "explanation": "What makes a good answer",
                    "points": 2,
                    "difficulty_level": "easy"
                }}
            ]
        }}

        IMPORTANT:
        - Ensure JSON is valid and properly formatted
        - For multiple choice, correct_answer should match exactly one of the options
        - For short answer, correct_answer should be the expected response or key points
        - Points should increase with difficulty (easy: 1-2, medium: 2-3, hard: 3-5)
        - Cover diverse topics from the document content
        """

        # Generate the quiz
        logging.info(f"Generating quiz for subject: {subject_name}")

        response = model.generate_content(prompt)

        if not response or not response.text:
            return {"success": False, "error": "No response from AI model"}

        # Parse the JSON response
        try:
            # Clean up the response - remove markdown formatting and fix common JSON issues
            json_str = response.text.strip()

            # Remove markdown code blocks
            if json_str.startswith('```'):
                lines = json_str.split('\n')
                # Remove first line if it's ```json or ```
                if lines[0].strip().startswith('```'):
                    lines = lines[1:]
                # Remove last line if it's ```
                if lines and lines[-1].strip() == '```':
                    lines = lines[:-1]
                json_str = '\n'.join(lines)

            # Fix common JSON formatting issues
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            # Try to parse the JSON
            quiz_data = json.loads(json_str)

            # Validate the response structure
            required_fields = ['title', 'description', 'questions']
            for field in required_fields:
                if field not in quiz_data:
                    return {"success": False, "error": f"Missing required field: {field}"}

            # Validate questions
            if not isinstance(quiz_data['questions'], list) or len(quiz_data['questions']) == 0:
                return {"success": False, "error": "No questions generated"}

            # Ensure we have the right number of questions
            if len(quiz_data['questions']) > total_questions:
                quiz_data['questions'] = quiz_data['questions'][:total_questions]

            # Calculate total points
            total_points = sum(q.get('points', 1) for q in quiz_data['questions'])
            quiz_data['total_points'] = total_points
            quiz_data['total_questions'] = len(quiz_data['questions'])

            logging.info(f"Successfully generated quiz with {len(quiz_data['questions'])} questions")
            return {"success": True, "quiz_data": quiz_data}

        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse AI response as JSON: {str(e)}")
            logging.error(f"AI Response: {response.text[:500]}...")
            return {"success": False, "error": "Invalid response format from AI"}

    except Exception as e:
        logging.error(f"Error generating quiz: {str(e)}")
        return {"success": False, "error": f"Error generating quiz: {str(e)}"}


def generate_flashcards_with_ai_enhanced(content: str, subject_id: int, chapters_data: List[Dict]) -> List[Dict]:
    """
    Enhanced version of generate_flashcards_with_ai that includes chapter information in the response

    Args:
        content: Document content
        subject_id: Subject ID
        chapters_data: List of chapters with their IDs and titles

    Returns:
        List of flashcard dictionaries with chapter information included
    """
    print("\n\n==== GENERATING ENHANCED FLASHCARDS WITH AI ====")
    print(f"Subject ID: {subject_id}")
    print(f"Chapters: {[f'{ch['chapter_number']}: {ch['title']}' for ch in chapters_data]}")

    try:
        # Configure Gemini AI
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))
        model = genai.GenerativeModel('gemini-1.5-flash')

        all_flashcards = []

        # Generate flashcards for each chapter
        for chapter in chapters_data:
            chapter_id = chapter['id']
            chapter_number = chapter['chapter_number']
            chapter_title = chapter['title']

            print(f"\nGenerating flashcards for Chapter {chapter_number}: {chapter_title}")

            # Create a focused prompt for this specific chapter
            prompt = f"""
            You are an expert educational content creator specializing in flashcard generation.

            Analyze the following document content and create exactly 8-12 high-quality flashcards specifically for Chapter {chapter_number}: {chapter_title}.

            IMPORTANT GUIDELINES:
            1. Focus ONLY on content related to Chapter {chapter_number}: {chapter_title}
            2. Create questions that test understanding, not just memorization
            3. Include a mix of question types: definitions, explanations, applications, comparisons
            4. Make answers comprehensive but concise (2-4 sentences max)
            5. Include relevant tags for each flashcard
            6. Ensure questions are clear and unambiguous

            DOCUMENT CONTENT:
            {content[:8000]}  # Limit content to avoid token limits

            Return ONLY valid JSON with this exact structure:
            {{
                "flashcards": [
                    {{
                        "question": "Clear, specific question text",
                        "answer": "Comprehensive but concise answer",
                        "type": "basic",
                        "chapter_id": {chapter_id},
                        "chapter_title": "{chapter_title}",
                        "chapter_number": "{chapter_number}",
                        "tags": ["relevant", "topic", "tags"]
                    }}
                ]
            }}

            Focus on the most important concepts from Chapter {chapter_number}: {chapter_title}.
            Ensure each flashcard is educational and helps with learning retention.
            """

            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    print(f"Calling Gemini model for Chapter {chapter_number} (attempt {retry_count + 1}/{max_retries})...")
                    response = model.generate_content(prompt)
                    print(f"Received response from Gemini AI for Chapter {chapter_number}")
                    break
                except Exception as quota_error:
                    retry_count += 1
                    if retry_count >= max_retries:
                        print(f"Maximum retries ({max_retries}) exceeded for Chapter {chapter_number}. Skipping.")
                        continue
                    print(f"Error calling Gemini API for Chapter {chapter_number}, retrying in 2 seconds...")
                    time.sleep(2)

            if retry_count >= max_retries:
                print(f"Failed to generate flashcards for Chapter {chapter_number} after {max_retries} attempts")
                continue

            try:
                # Extract and parse the JSON response
                response_text = response.text.strip()
                print(f"Raw response for Chapter {chapter_number}: {response_text[:200]}...")

                # Clean up the response
                json_str = response_text.replace('```json', '').replace('```', '').strip()

                # Parse the JSON
                flashcard_data = json.loads(json_str)

                if 'flashcards' in flashcard_data and isinstance(flashcard_data['flashcards'], list):
                    chapter_flashcards = flashcard_data['flashcards']

                    # Ensure each flashcard has the chapter information
                    for flashcard in chapter_flashcards:
                        flashcard['chapter_id'] = chapter_id
                        flashcard['chapter_title'] = chapter_title
                        flashcard['chapter_number'] = chapter_number
                        flashcard['subject_id'] = subject_id

                        # Ensure required fields exist
                        if 'type' not in flashcard:
                            flashcard['type'] = 'basic'
                        if 'tags' not in flashcard:
                            flashcard['tags'] = []
                        if 'color' not in flashcard:
                            flashcard['color'] = '#ffffff'

                    all_flashcards.extend(chapter_flashcards)
                    print(f"Successfully generated {len(chapter_flashcards)} flashcards for Chapter {chapter_number}")
                else:
                    print(f"Invalid response format for Chapter {chapter_number}: missing 'flashcards' array")

            except json.JSONDecodeError as e:
                print(f"Error parsing JSON response for Chapter {chapter_number}: {str(e)}")
                print(f"Response text: {response_text}")
                continue
            except Exception as e:
                print(f"Error processing response for Chapter {chapter_number}: {str(e)}")
                continue

        print(f"\n==== FLASHCARD GENERATION COMPLETE ====")
        print(f"Total flashcards generated: {len(all_flashcards)}")
        print(f"Chapters processed: {len([ch for ch in chapters_data])}")

        return all_flashcards

    except Exception as e:
        print(f"Error in generate_flashcards_with_ai_enhanced: {str(e)}")
        logging.error(f"Error in generate_flashcards_with_ai_enhanced: {str(e)}")
        return []
