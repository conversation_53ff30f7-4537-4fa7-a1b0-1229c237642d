import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';
import axios from 'axios';
import '../../styles/leitner.scss';

// Material Icons
import SchoolIcon from '@mui/icons-material/School';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import BarChartIcon from '@mui/icons-material/BarChart';
import InfoIcon from '@mui/icons-material/Info';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import Looks4Icon from '@mui/icons-material/Looks4';
import Looks5Icon from '@mui/icons-material/Looks5';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import ConfettiExplosion from 'react-confetti-explosion';

const LeitnerSystem = ({ chapterId, subjectId, onBack }) => {
  // Use ThemeContext for dark mode
  const { darkMode } = useTheme();
  
  // State variables
  const [cards, setCards] = useState([]);
  const [boxes, setBoxes] = useState({
    1: [],
    2: [],
    3: [],
    4: [],
    5: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('boxes'); // 'boxes', 'study', 'stats', 'info'
  const [selectedBox, setSelectedBox] = useState(null);
  const [isExploding, setIsExploding] = useState(false);
  const [streak, setStreak] = useState(0);
  
  // Load cards from API
  useEffect(() => {
    const loadCards = async () => {
      if (!chapterId) {
        setError('No chapter ID provided');
        setLoading(false);
        return;
      }
      
      try {
        setLoading(true);
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('Authentication token not found');
        }
        
        // Fetch flashcards for this chapter
        const response = await axios.get(`http://localhost:5001/api/chapters/${chapterId}/flashcards`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        if (response.data.success) {
          const fetchedCards = response.data.flashcards || [];
          setCards(fetchedCards);
          
          // Distribute cards into Leitner boxes based on box_level
          const newBoxes = {
            1: [],
            2: [],
            3: [],
            4: [],
            5: []
          };
          
          fetchedCards.forEach(card => {
            const boxLevel = card.box_level || 1;
            if (boxLevel >= 1 && boxLevel <= 5) {
              newBoxes[boxLevel].push(card);
            } else {
              // If box_level is invalid, put in box 1
              newBoxes[1].push({...card, box_level: 1});
            }
          });
          
          setBoxes(newBoxes);
          setError(null);
        } else {
          throw new Error(response.data.message || 'Failed to load flashcards');
        }
      } catch (error) {
        console.error('Failed to load flashcards for Leitner system:', error);
        setError('Failed to load flashcards. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    loadCards();
  }, [chapterId]);
  
  // Save updated boxes to the server
  const saveBoxes = async () => {
    if (!chapterId) return;
    
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }
      
      // Flatten all boxes into a single array of cards
      const allCards = Object.values(boxes).flat();
      
      // Update the server
      await axios.post(`http://localhost:5001/api/chapters/${chapterId}/flashcards`, {
        flashcards: allCards
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Leitner boxes saved successfully');
    } catch (error) {
      console.error('Failed to save Leitner boxes:', error);
      setError('Failed to save changes. Please try again later.');
    }
  };
  
  // Move a card to a different box
  const moveCard = (cardId, fromBox, toBox) => {
    if (toBox < 1 || toBox > 5) return;
    
    setBoxes(prevBoxes => {
      // Find the card in the fromBox
      const cardIndex = prevBoxes[fromBox].findIndex(card => card.id === cardId);
      if (cardIndex === -1) return prevBoxes;
      
      // Create a copy of the card and update its box_level
      const card = {...prevBoxes[fromBox][cardIndex], box_level: toBox};
      
      // Create new boxes with the card moved
      const newBoxes = {...prevBoxes};
      newBoxes[fromBox] = newBoxes[fromBox].filter(card => card.id !== cardId);
      newBoxes[toBox] = [...newBoxes[toBox], card];
      
      // Save changes to server
      saveBoxes();
      
      return newBoxes;
    });
  };
  
  // Start a study session for a specific box
  const startStudySession = (boxNumber) => {
    setSelectedBox(boxNumber);
    setView('study');
  };
  
  // Handle card review result
  const handleCardReview = (cardId, isCorrect) => {
    // Find which box the card is in
    let fromBox = null;
    for (const [boxNum, boxCards] of Object.entries(boxes)) {
      if (boxCards.some(card => card.id === cardId)) {
        fromBox = parseInt(boxNum);
        break;
      }
    }
    
    if (fromBox === null) return;
    
    if (isCorrect) {
      // Move to next box if correct (unless already in box 5)
      if (fromBox < 5) {
        moveCard(cardId, fromBox, fromBox + 1);
        
        // Update streak and show confetti for milestones
        setStreak(prev => {
          const newStreak = prev + 1;
          if (newStreak % 5 === 0) {
            setIsExploding(true);
            setTimeout(() => setIsExploding(false), 2000);
          }
          return newStreak;
        });
      }
    } else {
      // Move back to box 1 if incorrect
      if (fromBox > 1) {
        moveCard(cardId, fromBox, 1);
      }
      // Reset streak on incorrect answer
      setStreak(0);
    }
  };
  
  // Render the boxes view
  const renderBoxes = () => {
    return (
      <>
        <div className="leitner-boxes">
          {[1, 2, 3, 4, 5].map(boxNumber => {
            // Get the appropriate icon for each box
            let BoxIcon;
            switch (boxNumber) {
              case 1: BoxIcon = LooksOneIcon; break;
              case 2: BoxIcon = LooksTwoIcon; break;
              case 3: BoxIcon = Looks3Icon; break;
              case 4: BoxIcon = Looks4Icon; break;
              case 5: BoxIcon = Looks5Icon; break;
              default: BoxIcon = LooksOneIcon;
            }
            
            // Get the description for each box
            let boxDescription;
            switch (boxNumber) {
              case 1: boxDescription = 'New & difficult cards'; break;
              case 2: boxDescription = 'Learning cards'; break;
              case 3: boxDescription = 'Review cards'; break;
              case 4: boxDescription = 'Almost mastered'; break;
              case 5: boxDescription = 'Mastered cards'; break;
              default: boxDescription = '';
            }
            
            // Get the review schedule for each box
            let reviewSchedule;
            switch (boxNumber) {
              case 1: reviewSchedule = 'Every day'; break;
              case 2: reviewSchedule = 'Every 2 days'; break;
              case 3: reviewSchedule = 'Every 4 days'; break;
              case 4: reviewSchedule = 'Every week'; break;
              case 5: reviewSchedule = 'Every month'; break;
              default: reviewSchedule = 'Unknown';
            }
            
            return (
              <motion.div 
                key={boxNumber}
                className={`leitner-box box-${boxNumber}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: boxNumber * 0.1 }}
              >
                <div className="box-header">
                  <h3>
                    <BoxIcon className="box-icon" />
                    Box {boxNumber}
                  </h3>
                  <div className="card-count">{boxes[boxNumber].length}</div>
                </div>
                
                <div className="box-description">
                  <p>{boxDescription}</p>
                  <p className="review-schedule">Review: {reviewSchedule}</p>
                </div>
                
                {boxes[boxNumber].length > 0 ? (
                  <div className="box-preview">
                    {boxes[boxNumber].slice(0, 3).map((card, index) => (
                      <div 
                        key={card.id} 
                        className="preview-card"
                        style={{ 
                          transform: `translateY(${index * 5}px)`,
                          zIndex: 3 - index
                        }}
                      >
                        <div className="preview-content">
                          {card.question.length > 50 
                            ? card.question.substring(0, 50) + '...' 
                            : card.question}
                        </div>
                      </div>
                    ))}
                    
                    {boxes[boxNumber].length > 3 && (
                      <div className="more-cards">+{boxes[boxNumber].length - 3} more</div>
                    )}
                  </div>
                ) : (
                  <div className="empty-box">
                    <p>No cards in this box</p>
                  </div>
                )}
                
                <div className="box-actions">
                  <button 
                    className="study-button"
                    onClick={() => startStudySession(boxNumber)}
                    disabled={boxes[boxNumber].length === 0}
                  >
                    <PlayArrowIcon />
                    Study
                  </button>
                </div>
              </motion.div>
            );
          })}
        </div>
        
        {streak > 0 && (
          <div className="streak-counter">
            <LocalFireDepartmentIcon className="streak-icon" />
            <span>{streak} card streak</span>
          </div>
        )}
      </>
    );
  };
  
  return (
    <div className={`leitner-system ${darkMode ? 'theme-dark' : ''}`}>
      {/* Confetti effect for achievements */}
      {isExploding && (
        <div className="confetti-container">
          <ConfettiExplosion
            force={0.8}
            duration={2000}
            particleCount={100}
            width={1600}
          />
        </div>
      )}
      
      <div className="leitner-header">
        <h1>
          <SchoolIcon style={{ marginRight: '0.5rem' }} />
          Leitner System
        </h1>
        
        <div className="leitner-controls">
          {view === 'boxes' ? (
            <>
              <button onClick={() => setView('info')}>
                <InfoIcon className="icon" />
                About
              </button>
              <button onClick={() => setView('stats')}>
                <BarChartIcon className="icon" />
                Statistics
              </button>
              <button className="primary" onClick={() => startStudySession(1)}>
                <PlayArrowIcon className="icon" />
                Start Studying
              </button>
              <button onClick={onBack}>
                <ArrowBackIcon className="icon" />
                Back to Flashcards
              </button>
            </>
          ) : (
            <button onClick={() => setView('boxes')}>
              <ArrowBackIcon className="icon" />
              Back to Boxes
            </button>
          )}
        </div>
      </div>
      
      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading Leitner system...</p>
        </div>
      ) : error ? (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      ) : (
        view === 'boxes' && renderBoxes()
      )}
    </div>
  );
};

export default LeitnerSystem;
