@use "sass:color";
@use "_scrollbar";
@use "_mixins" as *;

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-purple: #AF52DE;
$system-teal: #5AC8FA;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Flashcards App - macOS style
.flashcards-app {
    max-width: 1000px;
    margin: 0 auto;
    padding: 1.5rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;

    // Light theme
    background-color: $window-background;
    color: $primary-text;

    // Dark theme
    &.theme-dark {
        background-color: $dark-window-background;
        color: $dark-primary-text;
    }

    // App Header
    .app-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;

        // Light theme
        // border-bottom: 1px solid $border-color;

        // Dark theme
        .theme-dark & {
            border-bottom: 1px solid $dark-border-color;
        }

        h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        .app-controls {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .icon-button {
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                border: none;
                cursor: pointer;
                @include transition;

                // Light theme
                background-color: $secondary-background;
                color: $system-blue;

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $system-blue;
                }

                &:hover {
                    // Light theme
                    background-color: darken($secondary-background, 5%);

                    // Dark theme
                    .theme-dark & {
                        background-color: lighten($dark-secondary-background, 5%);
                    }
                }
            }

            .control-group {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .mode-select,
            .filter-select {
                padding: 0.5rem 1rem;
                border-radius: 8px;
                font-size: 0.875rem;
                @include transition;

                // Light theme
                background-color: $secondary-background;
                color: $primary-text;
                border: 1px solid $border-color;

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $dark-primary-text;
                    border: 1px solid $dark-border-color;
                }

                &:focus {
                    outline: none;
                    // Light theme
                    border-color: $system-blue;

                    // Dark theme
                    .theme-dark & {
                        border-color: $system-blue;
                    }
                }
            }

            .stats-button {
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                border: none;
                cursor: pointer;
                @include transition;

                // Light theme
                background-color: $secondary-background;
                color: $system-blue;

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $system-blue;
                }

                &:hover {
                    // Light theme
                    background-color: darken($secondary-background, 5%);

                    // Dark theme
                    .theme-dark & {
                        background-color: lighten($dark-secondary-background, 5%);
                    }
                }
            }
        }
    }

    // Error Message
    .error-message {
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        // Light theme
        background-color: rgba($system-red, 0.1);
        color: $system-red;
        border: 1px solid rgba($system-red, 0.2);

        // Dark theme
        .theme-dark & {
            background-color: rgba($system-red, 0.15);
            border: 1px solid rgba($system-red, 0.3);
        }

        i {
            color: $system-red;
        }
    }

    // Stats Panel
    .stats-panel {
        margin-bottom: 1.5rem;
        border-radius: 12px;
        overflow: hidden;

        // Light theme
        background-color: $secondary-background;
        border: 1px solid $border-color;

        // Dark theme
        .theme-dark & {
            background-color: $dark-secondary-background;
            border: 1px solid $dark-border-color;
        }

        .stats-header {
            padding: 0.75rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;

            // Light theme
            border-bottom: 1px solid $border-color;

            // Dark theme
            .theme-dark & {
                border-bottom: 1px solid $dark-border-color;
            }

            h3 {
                margin: 0;
                font-size: 1rem;
                font-weight: 600;

                // Light theme
                color: $primary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .close-stats {
                width: 1.5rem;
                height: 1.5rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                border: none;
                cursor: pointer;
                @include transition;

                // Light theme
                background-color: transparent;
                color: $tertiary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-tertiary-text;
                }

                &:hover {
                    // Light theme
                    background-color: rgba($system-gray, 0.1);

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-gray, 0.2);
                    }
                }
            }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            padding: 1rem;

            .stat-item {
                text-align: center;

                .stat-value {
                    font-size: 1.5rem;
                    font-weight: 700;
                    margin-bottom: 0.25rem;

                    // Light theme
                    color: $system-blue;

                    // Dark theme
                    .theme-dark & {
                        color: $system-blue;
                    }
                }

                .stat-label {
                    font-size: 0.875rem;

                    // Light theme
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }
            }
        }
    }

    // Flashcard Container
    .flashcard-container {
        position: relative;
        perspective: 1000px;
        margin-bottom: 1.5rem;
        min-height: 300px;

        // Error message
        .flashcard-error-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 8px;
            text-align: center;

            // Light theme
            background-color: rgba(255, 59, 48, 0.1);
            border: 1px solid rgba(255, 59, 48, 0.3);
            color: $system-red;

            // Dark theme
            .theme-dark & {
                background-color: rgba(255, 69, 58, 0.15);
                border: 1px solid rgba(255, 69, 58, 0.3);
                color: $system-red;
            }

            .error-icon {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }

            p {
                margin: 0;
                font-size: 0.9rem;
            }
        }

        // Scene container for 3D effect
        .scene {
            perspective: 1000px;
            width: 100%;
            max-width: 600px;
            height: 300px;
            margin: 0 auto;
            transform-style: preserve-3d;
        }

        // Card
        .card {
            width: 100%;
            height: 300px;
            position: relative;
            transform-style: preserve-3d;
            transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
            -webkit-transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
            border-radius: 12px;
            cursor: pointer;
            overflow: hidden;
            will-change: transform;
            perspective: 1000px;

            // Light theme
            background-color: $window-background;
            border: 1px solid $border-color;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

            // Dark theme
            .theme-dark & {
                background-color: $dark-secondary-background;
                border: 1px solid $dark-border-color;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            }

            &.flipped {
                transform: rotateY(180deg);
                -webkit-transform: rotateY(180deg);
            }

            // Card themes
            &.minimal {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                border: 1px solid $border-color;

                .theme-dark & {
                    border: 1px solid $dark-border-color;
                }

                .card-header h3 {
                    font-size: 0.9rem;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
            }

            &.colorful {
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

                .theme-dark & {
                    background: linear-gradient(135deg, #2d3436 0%, #000000 100%);
                }

                .card-face {
                    background-color: transparent;
                }

                &.cloze {
                    background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);

                    .theme-dark & {
                        background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
                    }
                }

                &.image {
                    background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);

                    .theme-dark & {
                        background: linear-gradient(135deg, #f83600 0%, #f9d423 100%);
                    }
                }
            }

            &.dark {
                background-color: #2d3436;
                color: white;

                .card-face {
                    background-color: #2d3436;
                    color: white;
                }

                .card-header h3 {
                    color: #f0f0f0;
                }

                .theme-dark & {
                    background-color: #000000;

                    .card-face {
                        background-color: #000000;
                    }
                }
            }

            // Card types
            &.cloze {
                // Light theme
                border-left: 3px solid $system-orange;

                // Dark theme
                .theme-dark & {
                    border-left: 3px solid $system-orange;
                }
            }

            &.image,
            &.audio {
                // Light theme
                border-left: 3px solid $system-green;

                // Dark theme
                .theme-dark & {
                    border-left: 3px solid $system-green;
                }
            }

            &.multi-step {
                // Light theme
                border-left: 3px solid $system-blue;

                // Dark theme
                .theme-dark & {
                    border-left: 3px solid $system-blue;
                }
            }

            // Card Face
            .card-face {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                backface-visibility: hidden;
                -webkit-backface-visibility: hidden;
                /* Safari */
                display: flex;
                flex-direction: column;
                padding: 1.5rem;
                box-sizing: border-box;
                transform-style: preserve-3d;
                will-change: transform;
                transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
                -webkit-transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);

                // Light theme
                background-color: $window-background;
                color: $primary-text;
                border-radius: 12px;

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $dark-primary-text;
                }
            }

            .card-front {
                z-index: 1;
                transform: rotateY(0deg);
                -webkit-transform: rotateY(0deg);

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $dark-primary-text;
                }
            }

            .card-back {
                transform: rotateY(180deg);
                -webkit-transform: rotateY(180deg);

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $dark-primary-text;
                }
            }

            // Ensure the card flips properly
            &.flipped {
                .card-front {
                    transform: rotateY(180deg);
                    -webkit-transform: rotateY(180deg);
                }

                .card-back {
                    transform: rotateY(0deg);
                    -webkit-transform: rotateY(0deg);
                }
            }

            // Card Content
            .card-content {
                flex: 1;
                display: flex;
                flex-direction: column;

                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 1rem;

                    h3 {
                        margin: 0;
                        font-size: 1.125rem;
                        font-weight: 600;

                        // Light theme
                        color: $primary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-primary-text;
                        }
                    }

                    .card-chapter-info {
                        margin-bottom: 0.5rem;

                        .chapter-badge {
                            display: inline-block;
                            padding: 0.25rem 0.5rem;
                            border-radius: 0.375rem;
                            font-size: 0.75rem;
                            font-weight: 600;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;

                            // Light theme
                            background-color: rgba($system-purple, 0.1);
                            color: $system-purple;
                            border: 1px solid rgba($system-purple, 0.2);

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-purple, 0.2);
                                color: lighten($system-purple, 15%);
                                border-color: rgba($system-purple, 0.3);
                            }
                        }
                    }

                    .card-tags {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 0.5rem;

                        .tag {
                            padding: 0.25rem 0.5rem;
                            border-radius: 4px;
                            font-size: 0.75rem;

                            // Light theme
                            background-color: rgba($system-blue, 0.1);
                            color: $system-blue;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-blue, 0.2);
                            }
                        }
                    }
                }

                textarea {
                    flex: 1;
                    resize: none;
                    padding: 0.75rem;
                    border-radius: 8px;
                    font-size: 1rem;
                    line-height: 1.5;
                    margin-bottom: 1rem;

                    // Light theme
                    background-color: $secondary-background;
                    color: $primary-text;
                    border: 1px solid $border-color;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-secondary-background;
                        color: $dark-primary-text;
                        border: 1px solid $dark-border-color;
                    }

                    &:focus {
                        outline: none;
                        // Light theme
                        border-color: $system-blue;

                        // Dark theme
                        .theme-dark & {
                            border-color: $system-blue;
                        }
                    }
                }

                // Cloze Content
                .cloze-content {
                    flex: 1;
                    padding: 0.75rem;
                    border-radius: 8px;
                    font-size: 1rem;
                    line-height: 1.5;
                    margin-bottom: 1rem;

                    // Light theme
                    background-color: $secondary-background;
                    color: $primary-text;
                    border: 1px solid $border-color;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-secondary-background;
                        color: $dark-primary-text;
                        border: 1px solid $dark-border-color;
                    }

                    .cloze-blank {
                        padding: 0 0.25rem;
                        border-bottom: 2px solid $system-blue;
                        font-weight: bold;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-primary-text;
                        }
                    }

                    .cloze-answer {
                        padding: 0 0.25rem;
                        background-color: rgba($system-green, 0.15);
                        border-radius: 4px;
                        font-weight: bold;
                        color: $system-green;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($system-green, 0.2);
                            color: $system-green;
                        }
                    }
                }

                // Media Content
                .media-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 1rem;
                    margin-bottom: 1rem;

                    .card-image {
                        max-width: 100%;
                        max-height: 200px;
                        object-fit: contain;
                        border-radius: 8px;
                    }

                    .card-audio {
                        width: 100%;
                    }

                    .question-text {
                        padding: 0.75rem;
                        border-radius: 8px;
                        font-size: 1rem;

                        // Light theme
                        background-color: $secondary-background;
                        color: $primary-text;

                        // Dark theme
                        .theme-dark & {
                            background-color: $dark-secondary-background;
                            color: $dark-primary-text;
                        }
                    }
                }

                // Multi-step Content
                .multi-step-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 0.75rem;
                    margin-bottom: 1rem;

                    .step {
                        display: flex;
                        gap: 0.75rem;
                        padding: 0.75rem;
                        border-radius: 8px;

                        // Light theme
                        background-color: $secondary-background;

                        // Dark theme
                        .theme-dark & {
                            background-color: $dark-secondary-background;
                        }

                        .step-number {
                            width: 1.5rem;
                            height: 1.5rem;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                            font-size: 0.875rem;

                            // Light theme
                            background-color: $system-blue;
                            color: white;

                            // Dark theme
                            .theme-dark & {
                                background-color: $system-blue;
                            }
                        }

                        .step-content {
                            flex: 1;
                            font-size: 0.9375rem;
                            line-height: 1.5;

                            // Light theme
                            color: $primary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-primary-text;
                            }
                        }
                    }
                }

                // Card Footer
                .card-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .hint-container {
                        display: flex;
                        align-items: center;

                        .hint {
                            font-size: 0.875rem;
                            display: flex;
                            align-items: center;
                            gap: 0.35rem;

                            // Light theme
                            color: $tertiary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-tertiary-text;
                            }

                            .flip-icon {
                                font-size: 1rem;
                                opacity: 0.8;

                                // Light theme
                                color: $system-blue;

                                // Dark theme
                                .theme-dark & {
                                    color: $system-blue;
                                }
                            }
                        }

                        .hint-text {
                            display: flex;
                            align-items: center;
                            gap: 0.35rem;
                            font-size: 0.875rem;
                            padding: 0.35rem 0.75rem;
                            border-radius: 6px;

                            // Light theme
                            background-color: rgba($system-yellow, 0.15);
                            color: darken($system-yellow, 25%);

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-yellow, 0.2);
                                color: $system-yellow;
                            }

                            .hint-icon {
                                font-size: 1rem;

                                // Light theme
                                color: darken($system-yellow, 15%);

                                // Dark theme
                                .theme-dark & {
                                    color: $system-yellow;
                                }
                            }
                        }
                    }

                    .show-answer-button {
                        padding: 0.5rem 1rem;
                        border-radius: 8px;
                        font-size: 0.875rem;
                        font-weight: 500;
                        border: none;
                        cursor: pointer;
                        position: relative;
                        z-index: 10;
                        /* Ensure button is above card for proper click handling */
                        @include transition;
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                        // macOS-style button
                        background: linear-gradient(to bottom, lighten($system-blue, 5%) 0%, $system-blue 100%);
                        color: white;
                        border-radius: 6px;

                        .button-icon {
                            font-size: 1rem;
                        }

                        // Dark theme
                        .theme-dark & {
                            background: linear-gradient(to bottom, lighten($system-blue, 3%) 0%, darken($system-blue, 3%) 100%);
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                        }

                        &:hover {
                            // Light theme
                            background: linear-gradient(to bottom, lighten($system-blue, 8%) 0%, lighten($system-blue, 3%) 100%);
                            transform: translateY(-1px);
                            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);

                            // Dark theme
                            .theme-dark & {
                                background: linear-gradient(to bottom, lighten($system-blue, 5%) 0%, $system-blue 100%);
                                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
                            }
                        }

                        &:active {
                            // Light theme
                            background: linear-gradient(to bottom, darken($system-blue, 3%) 0%, $system-blue 100%);
                            transform: translateY(0);
                            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                            // Dark theme
                            .theme-dark & {
                                background: linear-gradient(to bottom, darken($system-blue, 5%) 0%, darken($system-blue, 2%) 100%);
                                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                            }
                        }
                    }

                    // Rating Buttons
                    .rating-buttons {
                        display: flex;
                        gap: 0.5rem;

                        .rating-btn {
                            width: 2rem;
                            height: 2rem;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: 600;
                            border: none;
                            cursor: pointer;
                            @include transition;

                            &.rating-1 {
                                background-color: $system-red;
                                color: white;

                                &:hover {
                                    background-color: darken($system-red, 5%);
                                }
                            }

                            &.rating-2 {
                                background-color: $system-orange;
                                color: white;

                                &:hover {
                                    background-color: darken($system-orange, 5%);
                                }
                            }

                            &.rating-3 {
                                background-color: $system-yellow;
                                color: $primary-text;

                                &:hover {
                                    background-color: darken($system-yellow, 5%);
                                }

                                .theme-dark & {
                                    color: $dark-window-background;
                                }
                            }

                            &.rating-4 {
                                background-color: $system-green;
                                color: white;

                                &:hover {
                                    background-color: darken($system-green, 5%);
                                }
                            }

                            &.rating-5 {
                                background-color: $system-blue;
                                color: white;

                                &:hover {
                                    background-color: darken($system-blue, 5%);
                                }
                            }
                        }
                    }
                }
            }

            // Card Level Indicator
            .card-level {
                position: absolute;
                top: 1rem;
                right: 1rem;

                .level-indicator {
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    font-size: 0.75rem;
                    font-weight: 600;

                    // Light theme
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                    }
                }
            }

            // Review Info
            .review-info {
                position: absolute;
                bottom: 1rem;
                right: 1rem;
                font-size: 0.75rem;

                // Light theme
                color: $tertiary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }

            // Answer Preview
            .answer-preview {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                padding: 1.25rem;
                max-height: 200px;
                overflow-y: auto;
                z-index: 10;
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
                border-radius: 12px 12px 0 0;
                animation: slideUp 0.3s ease-out forwards;

                // Light theme
                background-color: rgba($window-background, 0.85);
                color: $primary-text;
                border-top: 1px solid rgba($border-color, 0.5);

                // Dark theme
                .theme-dark & {
                    background-color: rgba($dark-secondary-background, 0.85);
                    color: $dark-primary-text;
                    border-top: 1px solid rgba($dark-border-color, 0.5);
                    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
                }

                .preview-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 0.75rem;
                    padding-bottom: 0.75rem;
                    border-bottom: 1px solid rgba($border-color, 0.5);

                    // Dark theme
                    .theme-dark & {
                        border-bottom: 1px solid rgba($dark-border-color, 0.5);
                    }

                    span {
                        font-weight: 600;
                        font-size: 0.95rem;

                        // Light theme
                        color: $primary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-primary-text;
                        }
                    }

                    .close-preview {
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: none;
                        cursor: pointer;

                        // Light theme
                        background-color: rgba($system-gray, 0.1);
                        color: $tertiary-text;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($system-gray, 0.2);
                            color: $dark-tertiary-text;
                        }

                        &:hover {
                            // Light theme
                            background-color: rgba($system-gray, 0.2);

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-gray, 0.3);
                            }
                        }
                    }
                }

                .preview-content {
                    font-size: 1rem;
                    line-height: 1.5;
                    white-space: pre-wrap;

                    // Light theme
                    color: $primary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }

                @keyframes slideUp {
                    from {
                        transform: translateY(100%);
                        opacity: 0;
                    }

                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
            }
        }

        // Empty State
        .no-cards {
            width: 100%;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;

            // Light theme
            background-color: $secondary-background;
            border: 1px solid $border-color;

            // Dark theme
            .theme-dark & {
                background-color: $dark-secondary-background;
                border: 1px solid $dark-border-color;
            }

            .empty-state {
                text-align: center;

                i {
                    font-size: 3rem;
                    margin-bottom: 1rem;

                    // Light theme
                    color: $tertiary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }

                p {
                    margin: 0.5rem 0;

                    // Light theme
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }
            }
        }

        // Card Navigation
        .card-navigation {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 1rem;

            .nav-button {
                width: 2.5rem;
                height: 2.5rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                border: none;
                cursor: pointer;
                @include transition;

                // Light theme
                background-color: $system-blue;
                color: white;

                // Dark theme
                .theme-dark & {
                    background-color: $system-blue;
                }

                &:hover:not(:disabled) {
                    // Light theme
                    background-color: darken($system-blue, 5%);

                    // Dark theme
                    .theme-dark & {
                        background-color: lighten($system-blue, 5%);
                    }
                }

                &:disabled {
                    // Light theme
                    background-color: $system-light-gray;
                    color: $tertiary-text;
                    cursor: not-allowed;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-secondary-background;
                        color: $dark-tertiary-text;
                    }
                }
            }

            .card-counter {
                font-size: 0.9375rem;
                font-weight: 500;
                min-width: 4rem;
                text-align: center;

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }
        }

        // Theme Selector
        .theme-selector {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;

            .theme-option {
                width: 2rem;
                height: 2rem;
                border-radius: 50%;
                padding: 0;
                border: 2px solid transparent;
                cursor: pointer;
                overflow: hidden;
                @include transition;

                &.active {
                    border-color: $system-blue;

                    .theme-dark & {
                        border-color: $system-blue;
                    }
                }

                .theme-preview {
                    width: 100%;
                    height: 100%;

                    &.default {
                        background-color: $window-background;
                        border: 1px solid $border-color;

                        .theme-dark & {
                            background-color: $dark-secondary-background;
                            border: 1px solid $dark-border-color;
                        }
                    }

                    &.minimal {
                        background-color: #f8f9fa;
                        border: 1px solid #dee2e6;

                        .theme-dark & {
                            background-color: #343a40;
                            border: 1px solid #495057;
                        }
                    }

                    &.colorful {
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

                        .theme-dark & {
                            background: linear-gradient(135deg, #2d3436 0%, #000000 100%);
                        }
                    }

                    &.dark {
                        background-color: #2d3436;

                        .theme-dark & {
                            background-color: #000000;
                        }
                    }
                }
            }
        }
    }

    // Tutorial Overlay
    .tutorial-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;

        .tutorial-content {
            width: 90%;
            max-width: 600px;
            background-color: $window-background;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);

            .theme-dark & {
                background-color: $dark-window-background;
            }

            h3 {
                text-align: center;
                margin-top: 0;
                margin-bottom: 1.5rem;
                font-size: 1.5rem;
                color: $primary-text;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;

                .theme-dark & {
                    color: $dark-primary-text;
                }

                .tutorial-icon {
                    color: $system-blue;
                }
            }

            .tutorial-steps {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1.5rem;
                margin-bottom: 2rem;

                .tutorial-step {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;

                    .step-number {
                        width: 2.5rem;
                        height: 2.5rem;
                        border-radius: 50%;
                        background-color: $system-blue;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.25rem;
                        font-weight: bold;
                        margin-bottom: 1rem;
                    }

                    .step-content {
                        h4 {
                            margin: 0 0 0.5rem 0;
                            color: $primary-text;

                            .theme-dark & {
                                color: $dark-primary-text;
                            }
                        }

                        p {
                            margin: 0;
                            color: $secondary-text;
                            font-size: 0.9rem;

                            .theme-dark & {
                                color: $dark-secondary-text;
                            }
                        }
                    }
                }
            }

            .tutorial-close {
                display: block;
                width: 100%;
                max-width: 200px;
                margin: 0 auto;
                padding: 0.75rem 1.5rem;
                background-color: $system-blue;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 1rem;
                font-weight: 500;
                cursor: pointer;
                @include transition;

                &:hover {
                    background-color: darken($system-blue, 5%);
                }
            }
        }
    }

    // Cards List Container
    .cards-list-container {
        margin-top: 2rem;
        padding: 1.5rem;
        border-radius: 12px;

        // Light theme
        background-color: $secondary-background;
        border: 1px solid $border-color;

        // Dark theme
        .theme-dark & {
            background-color: $dark-secondary-background;
            border: 1px solid $dark-border-color;
        }

        .cards-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;

            h3 {
                margin: 0;
                font-size: 1.125rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.5rem;

                // Light theme
                color: $primary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-primary-text;
                }

                .header-icon {
                    color: $system-blue;
                }
            }

            .cards-filter {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .filter-icon {
                    // Light theme
                    color: $tertiary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }

                .filter-select {
                    padding: 0.5rem;
                    border-radius: 6px;
                    font-size: 0.875rem;

                    // Light theme
                    background-color: $window-background;
                    color: $primary-text;
                    border: 1px solid $border-color;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-window-background;
                        color: $dark-primary-text;
                        border: 1px solid $dark-border-color;
                    }

                    &:focus {
                        outline: none;
                        border-color: $system-blue;
                    }
                }
            }
        }

        .cards-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;

            .card-item {
                position: relative;
                border-radius: 10px;
                padding: 1rem;
                cursor: pointer;
                transition: all 0.2s ease;
                transform-style: preserve-3d;
                perspective: 1000px;

                // Light theme
                background-color: $window-background;
                border: 1px solid $border-color;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

                // Dark theme
                .theme-dark & {
                    background-color: $dark-window-background;
                    border: 1px solid $dark-border-color;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                }

                &:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);

                    // Dark theme
                    .theme-dark & {
                        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
                    }
                }

                &.active {
                    // Light theme
                    border-color: $system-blue;
                    box-shadow: 0 0 0 2px rgba($system-blue, 0.3);

                    // Dark theme
                    .theme-dark & {
                        border-color: $system-blue;
                        box-shadow: 0 0 0 2px rgba($system-blue, 0.4);
                    }
                }

                &.difficult {
                    // Light theme
                    border-left: 3px solid $system-red;

                    // Dark theme
                    .theme-dark & {
                        border-left: 3px solid $system-red;
                    }
                }

                .card-type-indicator {
                    position: absolute;
                    top: 0.75rem;
                    right: 0.75rem;

                    // Light theme
                    color: $tertiary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }

                .card-preview {
                    min-height: 80px;
                    padding: 0.75rem;
                    margin-bottom: 0.75rem;
                    border-radius: 6px;
                    overflow: hidden;
                    position: relative;

                    // Light theme
                    border: 1px solid rgba($border-color, 0.5);
                    color: $primary-text;

                    // Dark theme
                    .theme-dark & {
                        border: 1px solid rgba($dark-border-color, 0.5);
                        color: $dark-primary-text;
                        background-color: $dark-secondary-background !important;
                    }

                    .card-preview-chapter {
                        position: absolute;
                        top: 0.25rem;
                        right: 0.25rem;
                        z-index: 2;

                        .chapter-badge-small {
                            display: inline-block;
                            padding: 0.125rem 0.375rem;
                            border-radius: 0.25rem;
                            font-size: 0.625rem;
                            font-weight: 600;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;

                            // Light theme
                            background-color: rgba($system-purple, 0.1);
                            color: $system-purple;
                            border: 1px solid rgba($system-purple, 0.2);

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-purple, 0.2);
                                color: lighten($system-purple, 15%);
                                border-color: rgba($system-purple, 0.3);
                            }
                        }
                    }

                    .card-preview-front,
                    .card-preview-back {
                        backface-visibility: hidden;
                        transition: transform 0.6s;
                        transform-style: preserve-3d;
                    }

                    .card-preview-back {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        transform: rotateY(180deg);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 0.75rem;

                        // Light theme
                        background-color: rgba($secondary-background, 0.8);

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($dark-secondary-background, 0.8);
                        }
                    }

                    &.flipped {
                        .card-preview-front {
                            transform: rotateY(180deg);
                        }

                        .card-preview-back {
                            transform: rotateY(0);
                        }
                    }
                }
            }
        }
    }

    // Card Controls - macOS-inspired design
    .card-controls {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border-radius: 12px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        background-color: rgba($secondary-background, 0.7);
        border: 1px solid rgba($border-color, 0.5);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
            background-color: rgba($dark-secondary-background, 0.7);
            border: 1px solid rgba($dark-border-color, 0.5);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        // Section title
        h3 {
            margin-top: 0;
            margin-bottom: 1.25rem;
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }

            .editor-icon {
                color: $system-blue;
            }
        }

        .control-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
            padding-bottom: 1.25rem;

            // Light theme
            border-bottom: 1px solid rgba($border-color, 0.5);

            // Dark theme
            .theme-dark & {
                border-bottom: 1px solid rgba($dark-border-color, 0.5);
            }

            &:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }

            .color-picker {
                display: flex;
                align-items: center;
                gap: 0.75rem;

                label {
                    font-size: 0.9375rem;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    gap: 0.35rem;

                    // Light theme
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-secondary-text;
                    }

                    .control-icon {
                        font-size: 1.25rem;
                        color: $system-blue;
                    }
                }

                input[type="color"] {
                    width: 2.25rem;
                    height: 2.25rem;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    padding: 0;
                    background: none;
                    overflow: hidden;
                    transition: all 0.2s ease;

                    // Light theme
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

                    // Dark theme
                    .theme-dark & {
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
                    }

                    &:hover {
                        transform: translateY(-2px);

                        // Light theme
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

                        // Dark theme
                        .theme-dark & {
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
                        }
                    }

                    &:active {
                        transform: translateY(0);

                        // Light theme
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

                        // Dark theme
                        .theme-dark & {
                            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                        }
                    }

                    &::-webkit-color-swatch-wrapper {
                        padding: 0;
                    }

                    &::-webkit-color-swatch {
                        border: none;
                        border-radius: 6px;
                    }
                }
            }

            .difficult-button {
                padding: 0.5rem 1rem;
                border-radius: 8px;
                font-size: 0.875rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                border: none;
                cursor: pointer;
                transition: all 0.2s ease;

                // Light theme - macOS style gradient
                background: linear-gradient(to bottom, lighten($system-red, 5%) 0%, $system-red 100%);
                color: white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                // Dark theme
                .theme-dark & {
                    background: linear-gradient(to bottom, lighten($system-red, 3%) 0%, darken($system-red, 3%) 100%);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }

                &:hover {
                    transform: translateY(-1px);

                    // Light theme
                    background: linear-gradient(to bottom, lighten($system-red, 8%) 0%, lighten($system-red, 3%) 100%);
                    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);

                    // Dark theme
                    .theme-dark & {
                        background: linear-gradient(to bottom, lighten($system-red, 5%) 0%, $system-red 100%);
                        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
                    }
                }

                &:active {
                    transform: translateY(0);

                    // Light theme
                    background: linear-gradient(to bottom, darken($system-red, 3%) 0%, $system-red 100%);
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                    // Dark theme
                    .theme-dark & {
                        background: linear-gradient(to bottom, darken($system-red, 5%) 0%, darken($system-red, 2%) 100%);
                        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                    }
                }

                &.active {
                    // Light theme
                    background: linear-gradient(to bottom, darken($system-red, 5%) 0%, darken($system-red, 10%) 100%);

                    // Dark theme
                    .theme-dark & {
                        background: linear-gradient(to bottom, darken($system-red, 8%) 0%, darken($system-red, 15%) 100%);
                    }

                    .button-icon {
                        color: rgba(255, 255, 255, 0.9);
                    }
                }

                .button-icon {
                    font-size: 1rem;
                }
            }
        }

        // Card meta info
        .card-meta-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1.25rem;
            padding: 1rem;
            border-radius: 8px;

            // Light theme
            background-color: rgba($window-background, 0.5);
            border: 1px solid rgba($border-color, 0.3);

            // Dark theme
            .theme-dark & {
                background-color: rgba($dark-window-background, 0.3);
                border: 1px solid rgba($dark-border-color, 0.3);
            }

            .meta-item {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;

                .meta-label {
                    font-size: 0.75rem;
                    font-weight: 500;

                    // Light theme
                    color: $tertiary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }

                .meta-value {
                    font-size: 1rem;
                    font-weight: 600;

                    // Light theme
                    color: $primary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }

                .level-progress,
                .success-progress {
                    height: 4px;
                    width: 100%;
                    border-radius: 2px;
                    overflow: hidden;
                    margin-top: 0.25rem;

                    // Light theme
                    background-color: rgba($system-light-gray, 0.5);

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($dark-secondary-background, 0.5);
                    }

                    .level-bar,
                    .success-bar {
                        height: 100%;
                        border-radius: 2px;

                        // Light theme
                        background-color: $system-blue;

                        // Dark theme
                        .theme-dark & {
                            background-color: $system-blue;
                        }
                    }
                }
            }
        }

        // Study session stats
        .study-session-stats {
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;

            // Light theme
            background-color: rgba($secondary-background, 0.5);
            border: 1px solid rgba($border-color, 0.3);

            // Dark theme
            .theme-dark & {
                background-color: rgba($dark-secondary-background, 0.3);
                border: 1px solid rgba($dark-border-color, 0.3);
            }

            h4 {
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-size: 1rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.35rem;

                // Light theme
                color: $primary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-primary-text;
                }

                .stats-icon {
                    color: $system-blue;
                }
            }

            .session-stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
                gap: 1rem;

                .session-stat {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .stat-label {
                        font-size: 0.75rem;

                        // Light theme
                        color: $tertiary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-tertiary-text;
                        }
                    }

                    .stat-value {
                        font-size: 1.125rem;
                        font-weight: 600;

                        // Light theme
                        color: $system-blue;

                        // Dark theme
                        .theme-dark & {
                            color: $system-blue;
                        }
                    }
                }
            }
        }

        // Card type selector
        .card-type-selector {
            margin-bottom: 1.25rem;

            label {
                display: block;
                margin-bottom: 0.5rem;
                font-size: 0.9375rem;
                font-weight: 500;

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }

            .type-options {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;

                .type-option {
                    padding: 0.5rem 0.75rem;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    display: flex;
                    align-items: center;
                    gap: 0.35rem;
                    border: none;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    // Light theme
                    background-color: rgba($secondary-background, 0.8);
                    color: $secondary-text;
                    border: 1px solid rgba($border-color, 0.5);

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($dark-secondary-background, 0.8);
                        color: $dark-secondary-text;
                        border: 1px solid rgba($dark-border-color, 0.5);
                    }

                    &:hover {
                        // Light theme
                        background-color: rgba($secondary-background, 1);
                        transform: translateY(-1px);

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($dark-secondary-background, 1);
                        }
                    }

                    &.active {
                        // Light theme
                        background-color: rgba($system-blue, 0.1);
                        color: $system-blue;
                        border-color: rgba($system-blue, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($system-blue, 0.2);
                            color: $system-blue;
                            border-color: rgba($system-blue, 0.4);
                        }
                    }
                }
            }
        }

        // Form groups
        .form-group {
            margin-bottom: 1.25rem;

            label {
                display: block;
                margin-bottom: 0.5rem;
                font-size: 0.9375rem;
                font-weight: 500;

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }

            textarea,
            input[type="text"] {
                width: 100%;
                padding: 0.75rem;
                border-radius: 8px;
                font-size: 0.9375rem;
                line-height: 1.5;
                transition: all 0.2s ease;

                // Light theme
                background-color: $window-background;
                color: $primary-text;
                border: 1px solid rgba($border-color, 0.7);

                // Dark theme
                .theme-dark & {
                    background-color: $dark-window-background;
                    color: $dark-primary-text;
                    border: 1px solid rgba($dark-border-color, 0.7);
                }

                &:focus {
                    outline: none;

                    // Light theme
                    border-color: $system-blue;
                    box-shadow: 0 0 0 2px rgba($system-blue, 0.2);

                    // Dark theme
                    .theme-dark & {
                        border-color: $system-blue;
                        box-shadow: 0 0 0 2px rgba($system-blue, 0.3);
                    }
                }
            }
        }

        // Form actions
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;

            button {
                padding: 0.5rem 1rem;
                border-radius: 8px;
                font-size: 0.875rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                border: none;
                cursor: pointer;
                transition: all 0.2s ease;

                .button-icon {
                    font-size: 1rem;
                }

                &.cancel-button {
                    // Light theme
                    background-color: rgba($system-light-gray, 0.8);
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($dark-secondary-background, 0.8);
                        color: $dark-secondary-text;
                    }

                    &:hover {
                        // Light theme
                        background-color: rgba($system-light-gray, 1);

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($dark-secondary-background, 1);
                        }
                    }
                }

                &.save-button,
                &.add-button {
                    // Light theme - macOS style gradient
                    background: linear-gradient(to bottom, lighten($system-blue, 5%) 0%, $system-blue 100%);
                    color: white;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                    // Dark theme
                    .theme-dark & {
                        background: linear-gradient(to bottom, lighten($system-blue, 3%) 0%, darken($system-blue, 3%) 100%);
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                    }

                    &:hover {
                        transform: translateY(-1px);

                        // Light theme
                        background: linear-gradient(to bottom, lighten($system-blue, 8%) 0%, lighten($system-blue, 3%) 100%);
                        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);

                        // Dark theme
                        .theme-dark & {
                            background: linear-gradient(to bottom, lighten($system-blue, 5%) 0%, $system-blue 100%);
                            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
                        }
                    }

                    &:active {
                        transform: translateY(0);

                        // Light theme
                        background: linear-gradient(to bottom, darken($system-blue, 3%) 0%, $system-blue 100%);
                        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                        // Dark theme
                        .theme-dark & {
                            background: linear-gradient(to bottom, darken($system-blue, 5%) 0%, darken($system-blue, 2%) 100%);
                            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                        }
                    }

                    &:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                        transform: none;
                        box-shadow: none;
                    }
                }
            }
        }
    }
}

// AI Generation Button and Modal Styles
.ai-generate-button {
    background: linear-gradient(135deg, $system-green, $system-teal);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
    }

    svg {
        font-size: 1rem;
    }
}

// AI Generation Modal
.ai-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);

    .theme-dark & {
        background-color: rgba(0, 0, 0, 0.7);
    }
}

.ai-modal-container {
    background-color: $window-background;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .ai-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem;
        border-bottom: 1px solid rgba($system-gray, 0.2);

        .theme-dark & {
            border-bottom-color: rgba($system-gray, 0.3);
        }

        h2 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: $primary-text;
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .theme-dark & {
                color: $dark-primary-text;
            }

            svg {
                color: $system-green;
                font-size: 1.5rem;
            }
        }

        .close-button {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            border: none;
            background-color: rgba($system-gray, 0.1);
            color: $secondary-text;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;

            .theme-dark & {
                background-color: rgba($system-gray, 0.2);
                color: $dark-secondary-text;
            }

            &:hover:not(:disabled) {
                background-color: rgba($system-red, 0.1);
                color: $system-red;

                .theme-dark & {
                    background-color: rgba($system-red, 0.2);
                }
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }
    }

    .ai-modal-content {
        flex: 1;
        overflow-y: auto;
        padding: 1.5rem;

        .chapter-selection {
            h3 {
                margin: 0 0 0.5rem 0;
                font-size: 1.125rem;
                font-weight: 600;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            p {
                margin: 0 0 1.5rem 0;
                color: $secondary-text;
                font-size: 0.875rem;

                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }

            .selection-controls {
                display: flex;
                gap: 0.75rem;
                margin-bottom: 1rem;

                button {
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    border: none;
                    font-size: 0.875rem;
                    font-weight: 500;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    transition: all 0.2s ease;

                    &.select-all-btn {
                        background-color: rgba($system-blue, 0.1);
                        color: $system-blue;

                        .theme-dark & {
                            background-color: rgba($system-blue, 0.2);
                        }

                        &:hover:not(:disabled) {
                            background-color: rgba($system-blue, 0.2);

                            .theme-dark & {
                                background-color: rgba($system-blue, 0.3);
                            }
                        }
                    }

                    &.deselect-all-btn {
                        background-color: rgba($system-orange, 0.1);
                        color: $system-orange;

                        .theme-dark & {
                            background-color: rgba($system-orange, 0.2);
                        }

                        &:hover:not(:disabled) {
                            background-color: rgba($system-orange, 0.2);

                            .theme-dark & {
                                background-color: rgba($system-orange, 0.3);
                            }
                        }
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }

                    svg {
                        font-size: 1rem;
                    }
                }
            }

            .chapters-list {
                max-height: 300px;
                overflow-y: auto;
                border: 1px solid rgba($system-gray, 0.2);
                border-radius: 0.75rem;
                padding: 0.5rem;

                .theme-dark & {
                    border-color: rgba($system-gray, 0.3);
                }

                .no-chapters {
                    padding: 2rem;
                    text-align: center;
                    color: $secondary-text;

                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }

                .chapter-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.75rem;
                    border-radius: 0.5rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    margin-bottom: 0.25rem;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &:hover {
                        background-color: rgba($system-blue, 0.05);

                        .theme-dark & {
                            background-color: rgba($system-blue, 0.1);
                        }
                    }

                    &.selected {
                        background-color: rgba($system-blue, 0.1);
                        border: 1px solid rgba($system-blue, 0.3);

                        .theme-dark & {
                            background-color: rgba($system-blue, 0.2);
                            border-color: rgba($system-blue, 0.4);
                        }
                    }

                    .chapter-checkbox {
                        color: $system-blue;
                        display: flex;
                        align-items: center;

                        svg {
                            font-size: 1.25rem;
                        }
                    }

                    .chapter-info {
                        flex: 1;

                        .chapter-number {
                            font-size: 0.75rem;
                            font-weight: 600;
                            color: $system-blue;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                            margin-bottom: 0.25rem;
                        }

                        .chapter-title {
                            font-size: 0.875rem;
                            font-weight: 500;
                            color: $primary-text;

                            .theme-dark & {
                                color: $dark-primary-text;
                            }
                        }
                    }
                }
            }

            .error-message {
                margin-top: 1rem;
                padding: 0.75rem;
                background-color: rgba($system-red, 0.1);
                border: 1px solid rgba($system-red, 0.3);
                border-radius: 0.5rem;
                color: $system-red;
                font-size: 0.875rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .theme-dark & {
                    background-color: rgba($system-red, 0.2);
                    border-color: rgba($system-red, 0.4);
                }

                svg {
                    font-size: 1rem;
                }
            }
        }

        .generation-progress {
            text-align: center;
            padding: 2rem 0;

            .progress-header {
                margin-bottom: 2rem;

                h3 {
                    margin: 0 0 0.5rem 0;
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: $primary-text;

                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }

                p {
                    margin: 0;
                    color: $secondary-text;
                    font-size: 0.875rem;

                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }
            }

            .progress-bar-container {
                margin-bottom: 1rem;

                .progress-bar {
                    width: 100%;
                    height: 8px;
                    background-color: rgba($system-gray, 0.2);
                    border-radius: 4px;
                    overflow: hidden;
                    margin-bottom: 0.5rem;

                    .theme-dark & {
                        background-color: rgba($system-gray, 0.3);
                    }

                    .progress-fill {
                        height: 100%;
                        background: linear-gradient(90deg, $system-green, $system-blue);
                        border-radius: 4px;
                        transition: width 0.3s ease;
                    }
                }

                .progress-text {
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: $system-blue;
                }
            }
        }

        .ai-modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba($system-gray, 0.2);

            .theme-dark & {
                border-top-color: rgba($system-gray, 0.3);
            }

            button {
                padding: 0.75rem 1.5rem;
                border-radius: 0.5rem;
                border: none;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                transition: all 0.2s ease;

                &.cancel-btn {
                    background-color: rgba($system-gray, 0.1);
                    color: $secondary-text;

                    .theme-dark & {
                        background-color: rgba($system-gray, 0.2);
                        color: $dark-secondary-text;
                    }

                    &:hover {
                        background-color: rgba($system-gray, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-gray, 0.3);
                        }
                    }
                }

                &.generate-btn {
                    background: linear-gradient(135deg, $system-green, $system-teal);
                    color: white;

                    &:hover:not(:disabled) {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                        transform: none;
                        box-shadow: none;
                    }
                }

                svg {
                    font-size: 1rem;
                }
            }
        }
    }
}