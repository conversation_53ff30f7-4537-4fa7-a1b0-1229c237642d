import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../../contexts/ThemeContext';
import axios from 'axios';
import LeitnerSystem from '../../../systems/LeitnerSystem';
import ConfettiExplosion from 'react-confetti-explosion';

// Material Icons
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import FlipIcon from '@mui/icons-material/Flip';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CloseIcon from '@mui/icons-material/Close';
import FlagIcon from '@mui/icons-material/Flag';
import BarChartIcon from '@mui/icons-material/BarChart';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ImageIcon from '@mui/icons-material/Image';
import AudiotrackIcon from '@mui/icons-material/Audiotrack';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import TextFormatIcon from '@mui/icons-material/TextFormat';
import ColorLensIcon from '@mui/icons-material/ColorLens';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import TimerIcon from '@mui/icons-material/Timer';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import FilterListIcon from '@mui/icons-material/FilterList';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import LocalLibraryIcon from '@mui/icons-material/LocalLibrary';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import SchoolIcon from '@mui/icons-material/School';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import SelectAllIcon from '@mui/icons-material/SelectAll';
import DeselectIcon from '@mui/icons-material/Deselect';

const FlashCards = ({ tool, chapterId, subjectId, chapterTitle, isDarkMode }) => {
    const { darkMode: contextDarkMode } = useTheme();
    const darkMode = isDarkMode !== undefined ? isDarkMode : contextDarkMode;

    // State management
    const [cards, setCards] = useState([]);
    const [newCard, setNewCard] = useState({
        question: '',
        answer: '',
        type: 'basic',
        media: null,
        tags: []
    });
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isFlipped, setIsFlipped] = useState(false);
    const [showAnswer, setShowAnswer] = useState(false);
    const [cardColor, setCardColor] = useState('#ffffff');
    const [isEditing, setIsEditing] = useState(false);
    const [editCard, setEditCard] = useState(null);
    const [loadingError, setLoadingError] = useState(null);
    const [cardType, setCardType] = useState('basic');
    const [studyMode, setStudyMode] = useState('sequential');
    const [showStats, setShowStats] = useState(false);
    const [loading, setLoading] = useState(true);
    const [isExploding, setIsExploding] = useState(false);
    const [streak, setStreak] = useState(0);
    const [showConfetti, setShowConfetti] = useState(false);
    const [cardTheme, setCardTheme] = useState('default');
    const [showTutorial, setShowTutorial] = useState(false);
    const [showLeitnerSystem, setShowLeitnerSystem] = useState(false);
    const [studySession, setStudySession] = useState({
        startTime: null,
        cardsReviewed: 0,
        correctAnswers: 0
    });

    // AI Generation states
    const [showAIModal, setShowAIModal] = useState(false);
    const [availableChapters, setAvailableChapters] = useState([]);
    const [selectedChapters, setSelectedChapters] = useState([]);
    const [isGenerating, setIsGenerating] = useState(false);
    const [generationProgress, setGenerationProgress] = useState(0);
    const [generationMessage, setGenerationMessage] = useState('');
    const [aiGenerationError, setAiGenerationError] = useState(null);

    const cardFrontRef = useRef(null);
    const cardBackRef = useRef(null);
    const cardRef = useRef(null);

    // Chapter name handling
    const [chapterName, setChapterName] = useState(chapterTitle || '');

    // Utility functions
    const calculateNextReviewDate = (level) => {
        const today = new Date();
        const nextDate = new Date();
        const intervals = {
            1: 1,    // New card or failed
            2: 3,    // Learning
            3: 7,    // Review
            4: 14,   // Mastered
            5: 30    // Long-term memory
        };
        nextDate.setDate(today.getDate() + (intervals[level] || 1));
        return nextDate;
    };

    // Track original cards loaded from the database to avoid re-saving them
    const [originalCards, setOriginalCards] = useState([]);

    const saveCardsToDatabase = async (cardsToSave = cards) => {
        if (!chapterId || cardsToSave.length === 0) return true;

        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication token not found');

            // Step 1: Identify only new or modified cards that need to be saved
            const cardsToUpdate = [];

            for (const card of cardsToSave) {
                // Skip cards that were originally loaded from the database and haven't been modified
                const originalCard = originalCards.find(oc => oc.id === card.id);

                // Include card if:
                // 1. It's a new card (not in originalCards)
                // 2. It has been modified (different from original)
                // 3. It has the isModified flag set
                if (!originalCard ||
                    card.isModified ||
                    card.question !== originalCard.question ||
                    card.answer !== originalCard.answer ||
                    card.type !== originalCard.type ||
                    card.boxLevel !== originalCard.boxLevel ||
                    card.color !== originalCard.color ||
                    card.difficult !== originalCard.difficult) {

                    // Mark as modified so we know to save it
                    card.isModified = true;
                    cardsToUpdate.push(card);
                }
            }

            // If no cards need to be updated, return success
            if (cardsToUpdate.length === 0) {
                console.log("No cards need to be saved - all cards are unchanged from database");
                return true;
            }

            console.log(`Found ${cardsToUpdate.length} cards that need to be saved (new or modified)`);

            // Step 2: Ensure all cards have unique questions for this chapter
            const uniqueCards = [];
            const questionMap = new Map();

            for (const card of cardsToUpdate) {
                // Create a unique key for each card based on the question
                const questionKey = card.question.trim();

                // If we haven't seen this question before, add it to our unique cards
                if (!questionMap.has(questionKey)) {
                    questionMap.set(questionKey, card);
                    uniqueCards.push(card);
                } else {
                    // If we have seen this question, update the existing card instead of adding a duplicate
                    const existingCard = questionMap.get(questionKey);
                    // Only update if the card has a newer timestamp
                    if (card.updatedAt && existingCard.updatedAt &&
                        new Date(card.updatedAt) > new Date(existingCard.updatedAt)) {
                        // Replace the existing card with this one
                        const index = uniqueCards.findIndex(c => c.question === questionKey);
                        if (index !== -1) {
                            uniqueCards[index] = card;
                            questionMap.set(questionKey, card);
                        }
                    }
                }
            }

            console.log(`Filtered ${cardsToUpdate.length} cards down to ${uniqueCards.length} unique cards`);

            // Step 3: Process cards in smaller batches to avoid payload size issues
            const BATCH_SIZE = 20; // Process 20 cards at a time
            const batches = [];

            for (let i = 0; i < uniqueCards.length; i += BATCH_SIZE) {
                batches.push(uniqueCards.slice(i, i + BATCH_SIZE));
            }

            console.log(`Processing ${uniqueCards.length} cards in ${batches.length} batches`);

            // Step 4: Process each batch
            let successCount = 0;

            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} cards)`);

                try {
                    const response = await axios.post(
                        `http://localhost:5001/api/chapters/${chapterId}/flashcards`,
                        { flashcards: batch },
                        {
                            headers: {
                                Authorization: `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );

                    if (response.data.success) {
                        successCount += batch.length;
                        console.log(`Successfully saved batch ${i + 1} (${batch.length} cards)`);
                    } else {
                        console.error(`Failed to save batch ${i + 1}:`, response.data.message);
                    }
                } catch (batchError) {
                    console.error(`Error saving batch ${i + 1}:`, batchError);
                    // Continue with the next batch even if this one fails
                }
            }

            // Step 5: Save to local storage regardless of server success
            localStorage.setItem(`chapter-${chapterId}-flashcards`, JSON.stringify(cardsToSave));

            // Step 6: Report success if at least some cards were saved
            if (successCount > 0) {
                // Update originalCards to include the newly saved cards
                const updatedOriginalCards = [...originalCards];

                // Add any new cards to originalCards
                for (const card of uniqueCards) {
                    const existingIndex = updatedOriginalCards.findIndex(c => c.id === card.id);
                    if (existingIndex >= 0) {
                        // Update existing card
                        updatedOriginalCards[existingIndex] = { ...card, isModified: false };
                    } else {
                        // Add new card
                        updatedOriginalCards.push({ ...card, isModified: false });
                    }
                }

                setOriginalCards(updatedOriginalCards);

                setLoadingError(successCount < uniqueCards.length
                    ? `Partially saved: ${successCount} of ${uniqueCards.length} cards saved.`
                    : null);
                return true;
            } else if (uniqueCards.length > 0) {
                throw new Error(`Failed to save any of the ${uniqueCards.length} cards`);
            } else {
                // No cards needed saving
                return true;
            }
        } catch (error) {
            handleError(error, 'saving flashcards');
            // Still save to local storage as a backup
            localStorage.setItem(`chapter-${chapterId}-flashcards`, JSON.stringify(cardsToSave));
            return false;
        }
    };

    const handleError = (error, context) => {
        console.error(`Error ${context}:`, error);
        if (error.response) {
            const { status, data } = error.response;
            if (status === 403) setLoadingError('Permission denied: You do not have access to this chapter.');
            else if (status === 404) setLoadingError('Chapter not found. It may have been deleted.');
            else if (status === 401) {
                setLoadingError('Your session has expired. Please log in again.');
                localStorage.removeItem('token');
            } else setLoadingError(`Server error: ${data.message || 'Unknown error'}`);
        } else if (error.request) {
            setLoadingError('Network error: Could not connect to the server.');
        } else {
            setLoadingError(`Error: ${error.message}`);
        }
    };

    // Effects
    useEffect(() => {
        const loadCards = async () => {
            if (!chapterId) {
                setLoadingError('No chapterId provided');
                setLoading(false);
                return;
            }

            setLoading(true);
            try {
                const token = localStorage.getItem('token');
                if (!token) throw new Error('Authentication token not found');

                const response = await axios.get(
                    `http://localhost:5001/api/chapters/${chapterId}/flashcards`,
                    { headers: { Authorization: `Bearer ${token}` } }
                );

                if (response.data.success) {
                    const fetchedCards = (response.data.flashcards || []).map(card => ({
                        ...card,
                        tags: Array.isArray(card.tags) ? card.tags : [],
                        isModified: false // Mark cards from database as not modified
                    }));

                    // Store the original cards to avoid re-saving them later
                    setOriginalCards(fetchedCards);

                    // Set the cards for display
                    setCards(fetchedCards);

                    setLoadingError(fetchedCards.length === 0 ? 'No flashcards found. Create your first flashcard below.' : null);

                    if (fetchedCards.length > 0 && !studySession.startTime) {
                        setStudySession({
                            ...studySession,
                            startTime: new Date(),
                            cardsReviewed: 0,
                            correctAnswers: 0
                        });
                    }

                    console.log(`Loaded ${fetchedCards.length} cards from database`);
                }
            } catch (error) {
                handleError(error, 'loading flashcards');
                try {
                    const savedCards = localStorage.getItem(`chapter-${chapterId}-flashcards`);
                    if (savedCards) {
                        const parsedCards = JSON.parse(savedCards).map(card => ({
                            ...card,
                            tags: Array.isArray(card.tags) ? card.tags : []
                        }));
                        setCards(parsedCards);
                        // Also set as original cards since we're using local storage as fallback
                        setOriginalCards(parsedCards);
                        setLoadingError('Using locally saved cards.');
                    } else {
                        setCards([]);
                        setOriginalCards([]);
                        setLoadingError('Failed to load flashcards. Starting with empty set.');
                    }
                } catch (localError) {
                    setCards([]);
                    setOriginalCards([]);
                    setLoadingError('Failed to load flashcards.');
                }
            } finally {
                setLoading(false);
            }
        };

        loadCards();

        const hasSeenTutorial = localStorage.getItem('flashcards-tutorial-seen');
        if (!hasSeenTutorial) {
            setShowTutorial(true);
            localStorage.setItem('flashcards-tutorial-seen', 'true');
        }
    }, [chapterId]);

    useEffect(() => {
        if (!chapterId || !chapterTitle) {
            const fetchChapterName = async () => {
                try {
                    const token = localStorage.getItem('token');
                    if (!token) throw new Error('Authentication token not found');

                    const response = await axios.get(
                        `http://localhost:5001/api/chapters/${chapterId}`,
                        { headers: { Authorization: `Bearer ${token}` } }
                    );

                    if (response.data.success) {
                        setChapterName(response.data.chapter.title || 'Untitled Chapter');
                    }
                } catch (error) {
                    setChapterName('Flashcards');
                }
            };
            fetchChapterName();
        }
    }, [chapterId, chapterTitle]);

    // Track if cards have been modified to avoid unnecessary auto-saves
    const [cardsModified, setCardsModified] = useState(false);

    useEffect(() => {
        if (!chapterId || cards.length === 0 || !cardsModified) return;

        console.log("Auto-save triggered due to card modifications");
        const timeoutId = setTimeout(() => {
            saveCardsToDatabase();
            setCardsModified(false); // Reset the modified flag after saving
        }, 1000);

        return () => clearTimeout(timeoutId);
    }, [cards, chapterId, cardsModified]);

    useEffect(() => {
        resizeTextarea(cardFrontRef);
        resizeTextarea(cardBackRef);
    }, [isFlipped, currentIndex]);

    // Card manipulation functions
    const addCard = () => {
        if (!newCard.question.trim() || (cardType !== 'cloze' && !newCard.answer.trim())) return;

        const newCardData = {
            ...newCard,
            id: Date.now(),
            createdAt: new Date().toISOString(),
            lastReviewed: null,
            nextReviewDate: calculateNextReviewDate(1).toISOString(),
            color: cardColor,
            difficult: false,
            boxLevel: 1,
            reviewCount: 0,
            successRate: 0,
            type: cardType,
            chapter_id: chapterId,
            subject_id: subjectId,
            isModified: true // Mark as modified so it will be saved
        };

        const updatedCards = [...cards, newCardData];
        setCards(updatedCards);
        setNewCard({ question: '', answer: '', type: 'basic', media: null, tags: [] });
        setCurrentIndex(updatedCards.length - 1);

        // Mark cards as modified to trigger auto-save
        setCardsModified(true);

        // Save immediately for new cards
        saveCardsToDatabase(updatedCards);
    };

    const updateCard = () => {
        if (!editCard || !editCard.question.trim() || (cardType !== 'cloze' && !editCard.answer.trim())) return;

        const updatedCards = cards.map(card =>
            card.id === editCard.id ? {
                ...editCard,
                color: cardColor,
                type: cardType,
                updatedAt: new Date().toISOString(),
                chapter_id: chapterId,
                subject_id: subjectId,
                isModified: true // Mark as modified so it will be saved
            } : card
        );

        setCards(updatedCards);
        setIsEditing(false);
        setEditCard(null);
        setNewCard({ question: '', answer: '', type: 'basic', media: null, tags: [] });

        // Mark cards as modified to trigger auto-save
        setCardsModified(true);

        // Save immediately for edited cards
        saveCardsToDatabase(updatedCards);
    };

    const removeCard = (id) => {
        // We need to handle removal differently since we need to track deleted cards
        // First, check if this card exists in originalCards (was loaded from DB)
        const cardInOriginal = originalCards.find(card => card.id === id);

        if (cardInOriginal) {
            // This is a card from the database that needs to be deleted
            // In a real implementation, you would make a DELETE API call here
            // For now, we'll just remove it from our local state
            console.log(`Card ${id} exists in database and should be deleted on the server`);
            // TODO: Add API call to delete the card from the server
        }

        const updatedCards = cards.filter(card => card.id !== id);
        setCards(updatedCards);
        setCurrentIndex(Math.max(0, Math.min(currentIndex, updatedCards.length - 1)));

        // Mark cards as modified to trigger auto-save
        setCardsModified(true);

        // Save immediately for deleted cards
        saveCardsToDatabase(updatedCards);
    };

    const flipCard = (e) => {
        if (e?.target?.closest('.show-answer-button, .answer-preview')) return;
        setIsFlipped(prev => !prev);
        setShowAnswer(false);
        if (cardRef.current) {
            cardRef.current.style.transform = !isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)';
        }
    };

    const nextCard = () => {
        if (isFlipped) flipCard();
        else setShowAnswer(false);

        setStudySession(prev => ({
            ...prev,
            cardsReviewed: prev.cardsReviewed + 1
        }));

        setCurrentIndex(studyMode === 'random'
            ? Math.floor(Math.random() * cards.length)
            : (currentIndex + 1) % cards.length);
    };

    const prevCard = () => {
        if (isFlipped) flipCard();
        else setShowAnswer(false);
        setCurrentIndex((currentIndex - 1 + cards.length) % cards.length);
    };

    const reviewCard = (id, quality) => {
        const updatedCards = cards.map(card => {
            if (card.id !== id) return card;

            const now = new Date();
            let newBoxLevel = card.boxLevel || 1;

            if (quality <= 1) {
                newBoxLevel = 1;
                setStreak(0);
            } else if (quality >= 4) {
                newBoxLevel = Math.min(5, card.boxLevel + 1);
                setStreak(prev => prev + 1);
                setStudySession(prev => ({
                    ...prev,
                    correctAnswers: prev.correctAnswers + 1
                }));
                if (quality === 5 || (streak + 1) % 5 === 0) {
                    setIsExploding(true);
                    setTimeout(() => setIsExploding(false), 2000);
                }
            }

            if (newBoxLevel === 5 && card.boxLevel !== 5) {
                setShowConfetti(true);
                setTimeout(() => setShowConfetti(false), 3000);
            }

            const reviewCount = (card.reviewCount || 0) + 1;
            const successCount = (card.successCount || 0) + (quality >= 3 ? 1 : 0);

            return {
                ...card,
                boxLevel: newBoxLevel,
                lastReviewed: now.toISOString(),
                nextReviewDate: calculateNextReviewDate(newBoxLevel).toISOString(),
                reviewCount,
                successCount,
                successRate: Math.round((successCount / reviewCount) * 100),
                chapter_id: chapterId,
                subject_id: subjectId,
                isModified: true // Mark as modified so it will be saved
            };
        });

        setCards(updatedCards);

        // Mark cards as modified to trigger auto-save
        setCardsModified(true);

        // Save after a short delay to allow for multiple quick ratings
        setTimeout(() => saveCardsToDatabase(updatedCards), 500);
    };

    const resizeTextarea = (ref) => {
        if (ref.current) {
            ref.current.style.height = 'auto';
            ref.current.style.height = `${ref.current.scrollHeight}px`;
        }
    };

    // AI Generation functions
    const fetchAvailableChapters = async () => {
        if (!subjectId) return;

        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication token not found');

            const response = await axios.get(
                `http://localhost:5000/api/subjects/${subjectId}/chapters`,
                { headers: { Authorization: `Bearer ${token}` } }
            );

            if (response.data.success) {
                setAvailableChapters(response.data.chapters);
                // If current chapter is available, select it by default
                if (chapterId) {
                    const currentChapter = response.data.chapters.find(ch => ch.id === chapterId);
                    if (currentChapter) {
                        setSelectedChapters([chapterId]);
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching chapters:', error);
            setAiGenerationError('Failed to load chapters');
        }
    };

    const handleGenerateFlashcards = async () => {
        if (!subjectId || selectedChapters.length === 0) {
            setAiGenerationError('Please select at least one chapter');
            return;
        }

        setIsGenerating(true);
        setGenerationProgress(0);
        setGenerationMessage('Preparing to generate flashcards...');
        setAiGenerationError(null);

        try {
            const token = localStorage.getItem('token');
            if (!token) throw new Error('Authentication token not found');

            setGenerationMessage('Analyzing document content...');
            setGenerationProgress(25);

            const response = await axios.post(
                'http://localhost:5000/api/generate-flashcards',
                {
                    subject_id: subjectId,
                    chapter_ids: selectedChapters
                },
                { headers: { Authorization: `Bearer ${token}` } }
            );

            setGenerationProgress(75);
            setGenerationMessage('Processing AI response...');

            if (response.data.success) {
                setGenerationProgress(100);
                setGenerationMessage(`Successfully generated ${response.data.flashcards_generated} flashcards!`);

                // Reload the flashcards to show the new ones
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                throw new Error(response.data.error || 'Failed to generate flashcards');
            }
        } catch (error) {
            console.error('Error generating flashcards:', error);
            setAiGenerationError(error.response?.data?.error || error.message || 'Failed to generate flashcards');
        } finally {
            setTimeout(() => {
                setIsGenerating(false);
                setGenerationProgress(0);
                setGenerationMessage('');
            }, 3000);
        }
    };

    const toggleChapterSelection = (chapterId) => {
        setSelectedChapters(prev =>
            prev.includes(chapterId)
                ? prev.filter(id => id !== chapterId)
                : [...prev, chapterId]
        );
    };

    const selectAllChapters = () => {
        setSelectedChapters(availableChapters.map(ch => ch.id));
    };

    const deselectAllChapters = () => {
        setSelectedChapters([]);
    };

    // Render components
    const CardDisplay = () => (
        <div className="flashcard-container">
            {loadingError && (
                <div className="flashcard-error-message">
                    <WarningIcon /> {loadingError}
                </div>
            )}
            <AnimatePresence mode="wait">
                {cards.length > 0 ? (
                    <div className="scene">
                        <motion.div
                            key={cards[currentIndex]?.id || 'empty'}
                            className={`card ${isFlipped ? 'flipped' : ''} ${cards[currentIndex]?.type || 'basic'} ${cardTheme}`}
                            onClick={flipCard}
                            style={{ backgroundColor: cards[currentIndex]?.color || '#ffffff' }}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            transition={{ duration: 0.3 }}
                            ref={cardRef}
                        >
                            <div className="card-face card-front">
                                <div className="card-content">
                                    <div className="card-header">
                                        <h3><HelpOutlineIcon /> Question</h3>
                                        {cards[currentIndex]?.chapter_title && (
                                            <div className="card-chapter-info">
                                                <span className="chapter-badge">
                                                    {cards[currentIndex]?.chapter_number || 'Chapter'}: {cards[currentIndex]?.chapter_title}
                                                </span>
                                            </div>
                                        )}
                                        {cards[currentIndex]?.tags?.length > 0 && (
                                            <div className="card-tags">
                                                {cards[currentIndex].tags.map((tag, idx) => (
                                                    <span key={idx} className="tag">{tag}</span>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    {cards[currentIndex]?.type === 'cloze' ? (
                                        <div className="cloze-content" dangerouslySetInnerHTML={{
                                            __html: cards[currentIndex]?.question.replace(/\{\{(.*?)\}\}/g, '<span class="cloze-blank">_____</span>')
                                        }} />
                                    ) : cards[currentIndex]?.type === 'image' && cards[currentIndex]?.media ? (
                                        <div className="media-content">
                                            <img src={cards[currentIndex]?.media} alt="Card media" className="card-image" />
                                            <div className="question-text">{cards[currentIndex]?.question}</div>
                                        </div>
                                    ) : cards[currentIndex]?.type === 'audio' && cards[currentIndex]?.media ? (
                                        <div className="media-content">
                                            <audio controls src={cards[currentIndex]?.media} className="card-audio" />
                                            <div className="question-text">{cards[currentIndex]?.question}</div>
                                        </div>
                                    ) : (
                                        <textarea
                                            ref={cardFrontRef}
                                            value={cards[currentIndex]?.question || ''}
                                            readOnly
                                            rows={1}
                                        />
                                    )}
                                    {!isFlipped && (
                                        <div className="card-footer">
                                            <div className="hint-container">
                                                <span className="hint">
                                                    <FlipIcon /> Tap to flip
                                                </span>
                                            </div>
                                            <button
                                                className="show-answer-button"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setShowAnswer(true);
                                                }}
                                            >
                                                <VisibilityIcon /> Show Answer
                                            </button>
                                        </div>
                                    )}
                                </div>
                                {cards[currentIndex]?.boxLevel && (
                                    <div className="card-level">
                                        <span className="level-indicator">Level {cards[currentIndex]?.boxLevel}</span>
                                    </div>
                                )}
                                {cards[currentIndex]?.difficult && (
                                    <div className="difficult-marker">
                                        <FlagIcon />
                                    </div>
                                )}
                            </div>
                            <div className="card-face card-back">
                                <div className="card-content">
                                    <h3><CheckCircleIcon /> Answer</h3>
                                    {cards[currentIndex]?.type === 'cloze' ? (
                                        <div className="cloze-content" dangerouslySetInnerHTML={{
                                            __html: cards[currentIndex]?.question.replace(/\{\{(.*?)\}\}/g, '<span class="cloze-answer">$1</span>')
                                        }} />
                                    ) : cards[currentIndex]?.type === 'multi-step' ? (
                                        <div className="multi-step-content">
                                            {cards[currentIndex]?.answer.split('---').map((step, idx) => (
                                                <div key={idx} className="step">
                                                    <div className="step-number">{idx + 1}</div>
                                                    <div className="step-content">{step.trim()}</div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <textarea
                                            ref={cardBackRef}
                                            value={cards[currentIndex]?.answer || ''}
                                            readOnly
                                            rows={1}
                                        />
                                    )}
                                    <div className="card-footer">
                                        <div className="rating-buttons">
                                            {[1, 2, 3, 4, 5].map(rating => (
                                                <button
                                                    key={rating}
                                                    className={`rating-btn rating-${rating}`}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        reviewCard(cards[currentIndex]?.id, rating);
                                                        nextCard();
                                                    }}
                                                    title={['Didn\'t know it', 'Difficult to recall', 'Had to think', 'Recalled with effort', 'Perfect recall'][rating - 1]}
                                                >
                                                    {rating}
                                                </button>
                                            ))}
                                        </div>
                                        <span className="hint">Rate your recall</span>
                                    </div>
                                </div>
                                {cards[currentIndex]?.lastReviewed && (
                                    <div className="review-info">
                                        <TimerIcon /> Last reviewed: {new Date(cards[currentIndex]?.lastReviewed).toLocaleDateString()}
                                    </div>
                                )}
                            </div>
                            {showAnswer && !isFlipped && (
                                <div className="answer-preview" onClick={(e) => e.stopPropagation()}>
                                    <div className="preview-header">
                                        <span>Answer Preview</span>
                                        <button
                                            className="close-preview"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setShowAnswer(false);
                                            }}
                                        >
                                            <CloseIcon />
                                        </button>
                                    </div>
                                    <div className="preview-content">{cards[currentIndex]?.answer}</div>
                                </div>
                            )}
                        </motion.div>
                    </div>
                ) : (
                    <motion.div
                        className="no-cards"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3 }}
                    >
                        <div className="empty-state">
                            <LocalLibraryIcon />
                            <p>No flashcards yet</p>
                            <p>Add your first card below</p>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
            {cards.length > 0 && (
                <div className="card-navigation">
                    <button onClick={prevCard} disabled={cards.length <= 1} className="nav-button prev-button">
                        <NavigateBeforeIcon />
                    </button>
                    <div className="card-counter">{currentIndex + 1} / {cards.length}</div>
                    <button onClick={nextCard} disabled={cards.length <= 1} className="nav-button next-button">
                        <NavigateNextIcon />
                    </button>
                </div>
            )}
        </div>
    );

    const CardEditor = () => (
        <div className="card-editor">
            <h3>
                {isEditing ? <><EditIcon /> Edit Card</> : <><AddIcon /> Add New Card</>}
            </h3>
            <div className="card-type-selector">
                <label>Card Type:</label>
                <div className="type-options">
                    {[
                        { type: 'basic', icon: TextFormatIcon, label: 'Basic' },
                        { type: 'cloze', icon: AutoAwesomeIcon, label: 'Cloze' },
                        { type: 'image', icon: ImageIcon, label: 'Image' },
                        { type: 'audio', icon: AudiotrackIcon, label: 'Audio' },
                        { type: 'multi-step', icon: FormatListNumberedIcon, label: 'Multi-step' }
                    ].map(({ type, icon: Icon, label }) => (
                        <button
                            key={type}
                            className={`type-option ${cardType === type ? 'active' : ''}`}
                            onClick={() => setCardType(type)}
                        >
                            <Icon />
                            <span>{label}</span>
                        </button>
                    ))}
                </div>
            </div>
            {cardType === 'cloze' && (
                <div className="cloze-instructions">
                    <p><LightbulbIcon /> Use double curly braces: <code>This is a {{ cloze }} deletion</code></p>
                </div>
            )}
            {cardType === 'multi-step' && (
                <div className="multi-step-instructions">
                    <p><LightbulbIcon /> Separate steps with three dashes: <code>---</code></p>
                </div>
            )}
            <div className="form-group">
                <label>Question:</label>
                <textarea
                    value={newCard.question}
                    onChange={(e) => setNewCard({ ...newCard, question: e.target.value })}
                    placeholder={cardType === 'cloze' ? "Enter text with {{cloze}} deletions..." : "Enter question..."}
                    rows={3}
                    className="question-input"
                />
            </div>
            {(cardType === 'image' || cardType === 'audio') && (
                <div className="form-group">
                    <label>Media URL:</label>
                    <input
                        type="text"
                        value={newCard.media || ''}
                        onChange={(e) => setNewCard({ ...newCard, media: e.target.value })}
                        placeholder={cardType === 'image' ? "Enter image URL..." : "Enter audio URL..."}
                        className="media-input"
                    />
                </div>
            )}
            {cardType !== 'cloze' && (
                <div className="form-group">
                    <label>Answer:</label>
                    <textarea
                        value={newCard.answer}
                        onChange={(e) => setNewCard({ ...newCard, answer: e.target.value })}
                        placeholder={cardType === 'multi-step' ? "Enter steps separated by ---..." : "Enter answer..."}
                        rows={3}
                        className="answer-input"
                    />
                </div>
            )}
            <div className="form-group">
                <label>Tags (comma separated):</label>
                <input
                    type="text"
                    value={Array.isArray(newCard.tags) ? newCard.tags.join(', ') : ''}
                    onChange={(e) => setNewCard({
                        ...newCard,
                        tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                    })}
                    placeholder="Enter tags..."
                    className="tags-input"
                />
            </div>
            <div className="form-group">
                <label>Card Color:</label>
                <div className="color-options">
                    <input
                        type="color"
                        value={cardColor}
                        onChange={(e) => setCardColor(e.target.value)}
                        className="color-picker-input"
                    />
                    <div className="preset-colors">
                        {['#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6', '#ffe8e8', '#fff3e0', '#fffde7', '#f1f8e9', '#e0f7fa', '#e3f2fd'].map(color => (
                            <button
                                key={color}
                                className={`color-preset ${cardColor === color ? 'active' : ''}`}
                                style={{ backgroundColor: color }}
                                onClick={() => setCardColor(color)}
                            />
                        ))}
                    </div>
                </div>
            </div>
            <div className="form-actions">
                {isEditing ? (
                    <>
                        <button className="cancel-button" onClick={() => {
                            setIsEditing(false);
                            setEditCard(null);
                            setNewCard({ question: '', answer: '', type: 'basic', media: null, tags: [] });
                        }}>
                            <CancelIcon /> Cancel
                        </button>
                        <button className="save-button" onClick={updateCard}>
                            <SaveIcon /> Save Changes
                        </button>
                    </>
                ) : (
                    <button
                        className="add-button"
                        onClick={addCard}
                        disabled={!newCard.question.trim() || (cardType !== 'cloze' && !newCard.answer.trim())}
                    >
                        <AddIcon /> Add Card
                    </button>
                )}
            </div>
        </div>
    );

    return (
        <div className={`flashcards-app ${darkMode ? 'theme-dark' : ''}`}>
            {isExploding && (
                <div className="confetti-container">
                    <ConfettiExplosion force={0.8} duration={2000} particleCount={100} width={1600} />
                </div>
            )}
            {showConfetti && (
                <div className="achievement-notification">
                    <EmojiEventsIcon />
                    <div className="achievement-text">
                        <h3>Card Mastered!</h3>
                        <p>This card has reached maximum proficiency</p>
                    </div>
                </div>
            )}
            <header className="app-header">
                <div className="header-left">
                    <h1>{chapterName} Flashcards</h1>
                    {streak > 0 && (
                        <div className="streak-counter">
                            <LocalFireDepartmentIcon />
                            <span>{streak}</span>
                        </div>
                    )}
                </div>
                <div className="app-controls">
                    <button className="icon-button" onClick={() => window.confirm('Are you sure you want to reset all cards?') && setCards([])} title="Reset all cards">
                        <DeleteIcon />
                    </button>
                    <button className="icon-button" onClick={() => {
                        const data = JSON.stringify(cards, null, 2);
                        const blob = new Blob([data], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `flashcards-${chapterId}-${new Date().toISOString().slice(0, 10)}.json`;
                        link.click();
                        URL.revokeObjectURL(url);
                    }} title="Export cards">
                        <FileDownloadIcon />
                    </button>
                    <label className="icon-button" title="Import cards">
                        <FileUploadIcon />
                        <input
                            type="file"
                            accept=".json"
                            onChange={(e) => {
                                const file = e.target.files[0];
                                if (!file) return;
                                const reader = new FileReader();
                                reader.onload = (event) => {
                                    try {
                                        const importedCards = JSON.parse(event.target.result);
                                        if (!Array.isArray(importedCards)) throw new Error('Invalid file format');
                                        setCards(importedCards.map(card => ({
                                            ...card,
                                            tags: Array.isArray(card.tags) ? card.tags : []
                                        })));
                                        setCurrentIndex(0);
                                        setLoadingError(null);
                                    } catch (error) {
                                        setLoadingError('Failed to import flashcards.');
                                    }
                                    e.target.value = '';
                                };
                                reader.readAsText(file);
                            }}
                            style={{ display: 'none' }}
                        />
                    </label>
                    <button className="leitner-button" onClick={() => setShowLeitnerSystem(true)} title="Open Leitner System">
                        <SchoolIcon /> Leitner System
                    </button>
                    <button
                        className="ai-generate-button"
                        onClick={() => {
                            fetchAvailableChapters();
                            setShowAIModal(true);
                        }}
                        title="Generate Flashcards with AI"
                    >
                        <SmartToyIcon /> AI Generate
                    </button>
                    <div className="control-group">
                        <select value={studyMode} onChange={(e) => setStudyMode(e.target.value)} className="mode-select">
                            <option value="sequential">Sequential</option>
                            <option value="random">Random</option>
                            <option value="spaced">Spaced Repetition</option>
                            <option value="difficult">Difficult Cards</option>
                        </select>
                        <button className="stats-button" onClick={() => setShowStats(!showStats)} title="Show/Hide Statistics">
                            <BarChartIcon />
                        </button>
                    </div>
                </div>
            </header>
            {loading && (
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Loading flashcards...</p>
                </div>
            )}
            {showStats && cards.length > 0 && (
                <div className="stats-panel">
                    <div className="stats-header">
                        <h3>Flashcard Statistics</h3>
                        <button className="close-stats" onClick={() => setShowStats(false)}>
                            <CloseIcon />
                        </button>
                    </div>
                    <div className="stats-grid">
                        <div className="stat-item">
                            <div className="stat-value">{cards.length}</div>
                            <div className="stat-label">Total Cards</div>
                        </div>
                        <div className="stat-item">
                            <div className="stat-value">{cards.filter(card => card.difficult).length}</div>
                            <div className="stat-label">Difficult Cards</div>
                        </div>
                        <div className="stat-item">
                            <div className="stat-value">{cards.filter(card => card.reviewCount > 0).length}</div>
                            <div className="stat-label">Reviewed Cards</div>
                        </div>
                        <div className="stat-item">
                            <div className="stat-value">{cards.filter(card => card.boxLevel >= 4).length}</div>
                            <div className="stat-label">Mastered Cards</div>
                        </div>
                    </div>
                </div>
            )}
            <CardDisplay />
            <CardEditor />
            {cards.length > 0 && (
                <div className="cards-list-container">
                    <div className="cards-header">
                        <h3><LocalLibraryIcon /> Your Flashcards</h3>
                        <div className="cards-filter">
                            <FilterListIcon />
                            <select className="filter-select">
                                <option value="all">All Cards</option>
                                <option value="due">Due for Review</option>
                                <option value="difficult">Difficult Cards</option>
                                <option value="level-1">Level 1</option>
                                <option value="level-2">Level 2</option>
                                <option value="level-3">Level 3+</option>
                                <option value="type-basic">Basic Cards</option>
                                <option value="type-cloze">Cloze Cards</option>
                                <option value="type-media">Media Cards</option>
                            </select>
                        </div>
                    </div>
                    <div className="cards-list">
                        {cards.map((card, index) => (
                            <motion.div
                                key={card.id}
                                className={`card-item ${index === currentIndex ? 'active' : ''} ${card.difficult ? 'difficult' : ''} ${card.type || 'basic'}`}
                                onClick={() => {
                                    setCurrentIndex(index);
                                    setIsFlipped(false);
                                    setShowAnswer(false);
                                }}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.2, delay: index * 0.03 }}
                            >
                                <div className="card-type-indicator">
                                    {card.type === 'cloze' && <AutoAwesomeIcon />}
                                    {card.type === 'image' && <ImageIcon />}
                                    {card.type === 'audio' && <AudiotrackIcon />}
                                    {card.type === 'multi-step' && <FormatListNumberedIcon />}
                                    {(!card.type || card.type === 'basic') && <TextFormatIcon />}
                                </div>
                                <div className={`card-preview ${card.flipped ? 'flipped' : ''}`} style={{ backgroundColor: card.color || '#ffffff' }}>
                                    {card.chapter_title && (
                                        <div className="card-preview-chapter">
                                            <span className="chapter-badge-small">
                                                {card.chapter_number || 'Ch'}: {card.chapter_title.substring(0, 20)}{card.chapter_title.length > 20 ? '...' : ''}
                                            </span>
                                        </div>
                                    )}
                                    <div className="card-preview-front">
                                        {card.type === 'cloze'
                                            ? card.question.replace(/\{\{(.*?)\}\}/g, '____')
                                            : card.question.substring(0, 50) + (card.question.length > 50 ? '...' : '')}
                                    </div>
                                    <div className="card-preview-back">
                                        {card.type === 'cloze'
                                            ? card.question.replace(/\{\{(.*?)\}\}/g, (_match, content) => content)
                                            : card.answer.substring(0, 50) + (card.answer.length > 50 ? '...' : '')}
                                    </div>
                                </div>
                                {card.tags?.length > 0 && (
                                    <div className="card-tags-preview">
                                        {card.tags.slice(0, 2).map((tag, idx) => (
                                            <span key={idx} className="tag-preview">{tag}</span>
                                        ))}
                                        {card.tags.length > 2 && <span className="more-tags">+{card.tags.length - 2}</span>}
                                    </div>
                                )}
                                <div className="card-actions">
                                    <button
                                        className="edit-button"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            setCardType(card.type || 'basic');
                                            setIsEditing(true);
                                            setEditCard(card);
                                            setNewCard({
                                                question: card.question,
                                                answer: card.answer,
                                                type: card.type || 'basic',
                                                media: card.media || null,
                                                tags: card.tags || []
                                            });
                                            setCardColor(card.color || '#ffffff');
                                        }}
                                    >
                                        <EditIcon />
                                    </button>
                                    <button
                                        className="delete-button"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            removeCard(card.id);
                                        }}
                                    >
                                        <DeleteIcon />
                                    </button>
                                </div>
                                <div className="card-meta">
                                    <div className="meta-left">
                                        <span className="card-number">Card {index + 1}</span>
                                        {card.difficult && (
                                            <span className="difficult-tag">
                                                <FlagIcon /> Difficult
                                            </span>
                                        )}
                                    </div>
                                    <div className="meta-right">
                                        {card.boxLevel && (
                                            <span className={`level-badge level-${card.boxLevel}`}>L{card.boxLevel}</span>
                                        )}
                                    </div>
                                </div>
                                {card.reviewCount > 0 && (
                                    <div className="card-progress-bar">
                                        <div
                                            className="progress-fill"
                                            style={{
                                                width: `${card.successRate || 0}%`,
                                                backgroundColor: card.successRate > 80 ? '#28CD41' : card.successRate > 50 ? '#007AFF' : '#FF9500'
                                            }}
                                        ></div>
                                    </div>
                                )}
                            </motion.div>
                        ))}
                    </div>
                </div>
            )}
            {showTutorial && (
                <div className="tutorial-overlay">
                    <div className="tutorial-content">
                        <h3><AutoAwesomeIcon /> Welcome to Flashcards!</h3>
                        <div className="tutorial-steps">
                            {[
                                { title: 'Create Cards', text: 'Add new flashcards with questions and answers' },
                                { title: 'Study & Flip', text: 'Click cards to flip them and test your knowledge' },
                                { title: 'Rate Your Recall', text: 'Rate how well you remembered each card' },
                                { title: 'Track Progress', text: 'Watch your mastery level increase over time' }
                            ].map(({ title, text }, idx) => (
                                <div key={idx} className="tutorial-step">
                                    <div className="step-number">{idx + 1}</div>
                                    <div className="step-content">
                                        <h4>{title}</h4>
                                        <p>{text}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <button className="tutorial-close" onClick={() => setShowTutorial(false)}>
                            Got it!
                        </button>
                    </div>
                </div>
            )}

            {showLeitnerSystem && (
                <div className="leitner-system-overlay">
                    <LeitnerSystem
                        chapterId={chapterId}
                        subjectId={subjectId}
                        onBack={() => setShowLeitnerSystem(false)}
                    />
                </div>
            )}

            {/* AI Generation Modal */}
            {showAIModal && (
                <div className={`ai-modal-overlay ${darkMode ? 'theme-dark' : ''}`}>
                    <div className={`ai-modal-container ${darkMode ? 'theme-dark' : ''}`}>
                        <div className="ai-modal-header">
                            <h2><SmartToyIcon /> Generate Flashcards with AI</h2>
                            <button
                                className="close-button"
                                onClick={() => setShowAIModal(false)}
                                disabled={isGenerating}
                            >
                                <CloseIcon />
                            </button>
                        </div>

                        <div className="ai-modal-content">
                            {!isGenerating ? (
                                <>
                                    <div className="chapter-selection">
                                        <h3>Select Chapters for Flashcard Generation</h3>
                                        <p>Choose which chapters you want to generate flashcards for:</p>

                                        <div className="selection-controls">
                                            <button
                                                className="select-all-btn"
                                                onClick={selectAllChapters}
                                                disabled={availableChapters.length === 0}
                                            >
                                                <SelectAllIcon /> Select All
                                            </button>
                                            <button
                                                className="deselect-all-btn"
                                                onClick={deselectAllChapters}
                                                disabled={selectedChapters.length === 0}
                                            >
                                                <DeselectIcon /> Deselect All
                                            </button>
                                        </div>

                                        <div className="chapters-list">
                                            {availableChapters.length === 0 ? (
                                                <div className="no-chapters">
                                                    <p>No chapters found for this subject.</p>
                                                </div>
                                            ) : (
                                                availableChapters.map(chapter => (
                                                    <div
                                                        key={chapter.id}
                                                        className={`chapter-item ${selectedChapters.includes(chapter.id) ? 'selected' : ''}`}
                                                        onClick={() => toggleChapterSelection(chapter.id)}
                                                    >
                                                        <div className="chapter-checkbox">
                                                            {selectedChapters.includes(chapter.id) ?
                                                                <CheckBoxIcon /> :
                                                                <CheckBoxOutlineBlankIcon />
                                                            }
                                                        </div>
                                                        <div className="chapter-info">
                                                            <div className="chapter-number">{chapter.chapter_number}</div>
                                                            <div className="chapter-title">{chapter.title}</div>
                                                        </div>
                                                    </div>
                                                ))
                                            )}
                                        </div>

                                        {aiGenerationError && (
                                            <div className="error-message">
                                                <WarningIcon /> {aiGenerationError}
                                            </div>
                                        )}
                                    </div>

                                    <div className="ai-modal-actions">
                                        <button
                                            className="cancel-btn"
                                            onClick={() => setShowAIModal(false)}
                                        >
                                            Cancel
                                        </button>
                                        <button
                                            className="generate-btn"
                                            onClick={handleGenerateFlashcards}
                                            disabled={selectedChapters.length === 0}
                                        >
                                            <SmartToyIcon /> Generate Flashcards
                                        </button>
                                    </div>
                                </>
                            ) : (
                                <div className="generation-progress">
                                    <div className="progress-header">
                                        <h3>Generating Flashcards...</h3>
                                        <p>{generationMessage}</p>
                                    </div>

                                    <div className="progress-bar-container">
                                        <div className="progress-bar">
                                            <div
                                                className="progress-fill"
                                                style={{ width: `${generationProgress}%` }}
                                            ></div>
                                        </div>
                                        <div className="progress-text">{generationProgress}%</div>
                                    </div>

                                    {aiGenerationError && (
                                        <div className="error-message">
                                            <WarningIcon /> {aiGenerationError}
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FlashCards;