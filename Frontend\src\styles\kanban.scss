// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;
$system-gold: #fbb034;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Mixins
@mixin macOS-shadow($opacity: 0.1) {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, $opacity),
        0 0.0625rem 0.125rem rgba(0, 0, 0, $opacity * 0.5);
}

@mixin macOS-focus-ring {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.25);
}

@mixin macOS-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
    cursor: pointer;
    border: none;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    &:hover::before {
        opacity: 1;
    }

    &:active {
        transform: scale(0.98);
    }

    &:focus {
        @include macOS-focus-ring;
    }
}

@mixin glass-effect {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba($window-background, 0.8);
    border: 1px solid rgba($border-color, 0.3);

    .theme-dark & {
        background: rgba($dark-window-background, 0.8);
        border: 1px solid rgba($dark-border-color, 0.3);
    }
}

// Global drag and drop styles
body.is-dragging {
    cursor: grabbing !important;
    user-select: none !important;

    * {
        cursor: grabbing !important;
    }
}

// DragOverlay specific styles for better cursor positioning
.dnd-kit-drag-overlay {
    cursor: grabbing !important;
    pointer-events: none !important;
    z-index: 1000 !important;
}

// Advanced Kanban Board Styles
.advanced-kanban-board {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    width: 100%;
    height: 100%;
    padding: 1.5rem;
    background: $window-background;
    color: $primary-text;
    border-radius: 1rem;
    @include macOS-shadow(0.08);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    position: relative;
    overflow: hidden;
    box-sizing: border-box;

    &.theme-dark {
        background: $dark-window-background;
        color: $dark-primary-text;
    }

    // Header Section
    .kanban-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid $separator-color;

        .theme-dark & {
            border-bottom-color: $dark-separator-color;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;

            .board-title {
                font-size: 1.75rem;
                font-weight: 700;
                margin: 0;
                background: linear-gradient(135deg, $system-blue, $system-green);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .task-count {
                padding: 0.375rem 0.75rem;
                background: $secondary-background;
                color: $secondary-text;
                border-radius: 1rem;
                font-size: 0.75rem;
                font-weight: 600;

                .theme-dark & {
                    background: $dark-secondary-background;
                    color: $dark-secondary-text;
                }
            }
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .search-container {
                position: relative;
                display: flex;
                align-items: center;

                .search-input {
                    width: 16rem;
                    padding: 0.75rem 1rem 0.75rem 2.5rem;
                    border: 1px solid $border-color;
                    border-radius: 0.75rem;
                    background: $window-background;
                    color: $primary-text;
                    font-size: 0.875rem;
                    transition: all 0.2s ease;

                    &::placeholder {
                        color: $placeholder-text;
                    }

                    &:focus {
                        @include macOS-focus-ring;
                        border-color: $system-blue;
                    }

                    .theme-dark & {
                        background: $dark-secondary-background;
                        color: $dark-primary-text;
                        border-color: $dark-border-color;

                        &::placeholder {
                            color: $dark-placeholder-text;
                        }
                    }
                }

                .search-icon {
                    position: absolute;
                    left: 0.75rem;
                    color: $tertiary-text;
                    font-size: 1rem;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }
            }

            .add-task-btn {
                @include macOS-button;
                background: $system-blue;
                color: white;

                &:hover {
                    background: darken($system-blue, 5%);
                }
            }

            .filter-btn {
                @include macOS-button;
                background: $secondary-background;
                color: $primary-text;

                &:hover {
                    background: darken($secondary-background, 5%);
                }

                .theme-dark & {
                    background: $dark-secondary-background;
                    color: $dark-primary-text;

                    &:hover {
                        background: lighten($dark-secondary-background, 5%);
                    }
                }
            }
        }
    }

    // Board Container
    .kanban-board-container {
        display: flex;
        gap: 1.5rem;
        height: calc(100vh - 12rem);
        overflow-x: auto;
        overflow-y: hidden;
        padding-bottom: 1rem;
        box-sizing: border-box;

        // Custom scrollbar
        &::-webkit-scrollbar {
            height: 0.5rem;
        }

        &::-webkit-scrollbar-track {
            background: $secondary-background;
            border-radius: 0.25rem;

            .theme-dark & {
                background: $dark-secondary-background;
            }
        }

        &::-webkit-scrollbar-thumb {
            background: $system-gray;
            border-radius: 0.25rem;

            &:hover {
                background: darken($system-gray, 10%);
            }
        }
    }

    // Column Styles
    .kanban-column {
        min-width: 20rem;
        max-width: 20rem;
        display: flex;
        flex-direction: column;
        background: $secondary-background;
        border-radius: 1rem;
        padding: 1rem;
        @include macOS-shadow(0.05);
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        box-sizing: border-box;

        &.theme-dark {
            background: $dark-secondary-background;
        }

        body.is-dragging & {
            border: 2px dashed rgba($system-blue, 0.4);
            background: rgba($system-blue, 0.05);
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

            .theme-dark & {
                border-color: rgba($system-blue, 0.5);
                background: rgba($system-blue, 0.1);
            }
        }

        &.is-dragging-over {
            background: rgba($system-blue, 0.15) !important;
            border: 2px dashed $system-blue !important;
            transform: scale(1.01);
            box-shadow: 0 0 0 4px rgba($system-blue, 0.1);

            .theme-dark & {
                background: rgba($system-blue, 0.25) !important;
                box-shadow: 0 0 0 4px rgba($system-blue, 0.2);
            }
        }

        .column-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid $separator-color;

            .theme-dark & {
                border-bottom-color: $dark-separator-color;
            }

            .column-title {
                font-size: 1.125rem;
                font-weight: 700;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .status-indicator {
                    width: 0.75rem;
                    height: 0.75rem;
                    border-radius: 50%;

                    &.todo {
                        background: $system-gray;
                    }

                    &.in-progress {
                        background: $system-orange;
                    }

                    &.done {
                        background: $system-green;
                    }
                }
            }

            .task-count-badge {
                background: $window-background;
                color: $secondary-text;
                padding: 0.25rem 0.5rem;
                border-radius: 0.5rem;
                font-size: 0.75rem;
                font-weight: 600;
                min-width: 1.5rem;
                text-align: center;

                .theme-dark & {
                    background: $dark-window-background;
                    color: $dark-secondary-text;
                }
            }
        }

        .tasks-container {
            flex: 1;
            overflow-y: auto;
            padding-right: 0.25rem;
            min-height: 8rem;
            border-radius: 0.5rem;
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

            body.is-dragging & {
                background: rgba($system-blue, 0.02);

                .theme-dark & {
                    background: rgba($system-blue, 0.05);
                }
            }

            &::-webkit-scrollbar {
                width: 0.375rem;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba($system-gray, 0.3);
                border-radius: 0.25rem;

                &:hover {
                    background: rgba($system-gray, 0.5);
                }
            }
        }

        .empty-drop-zone {
            min-height: 6rem;
            border-radius: 0.75rem;
            border: 2px dashed transparent;
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 0.5rem 0;

            &:not(.is-dragging-over) {
                border-color: transparent;
                background: rgba($secondary-background, 0.5);

                .theme-dark & {
                    background: rgba($dark-secondary-background, 0.3);
                }

                body.is-dragging & {
                    border-color: rgba($system-blue, 0.4) !important;
                    background: rgba($system-blue, 0.05) !important;

                    .theme-dark & {
                        border-color: rgba($system-blue, 0.5) !important;
                        background: rgba($system-blue, 0.1) !important;
                    }
                }
            }

            &.is-dragging-over {
                border-color: $system-blue !important;
                background: rgba($system-blue, 0.1) !important;
                transform: scale(1.02);
                box-shadow: 0 0 0 4px rgba($system-blue, 0.1);

                .theme-dark & {
                    background: rgba($system-blue, 0.2) !important;
                    box-shadow: 0 0 0 4px rgba($system-blue, 0.2);
                }

                .empty-state {
                    .empty-icon {
                        transform: scale(1.2);
                    }

                    .empty-title {
                        color: $system-blue;
                    }

                    .empty-description {
                        color: $system-blue;
                        font-weight: 600;
                    }
                }
            }

            .empty-state {
                text-align: center;
                padding: 1rem;
                transition: all 0.3s ease;

                .empty-icon {
                    font-size: 2rem;
                    margin-bottom: 0.5rem;
                    display: block;
                    transition: transform 0.3s ease;
                }

                .empty-title {
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: $secondary-text;
                    margin-bottom: 0.25rem;
                    transition: color 0.3s ease;

                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }

                .empty-description {
                    font-size: 0.75rem;
                    color: $tertiary-text;
                    line-height: 1.4;
                    transition: all 0.3s ease;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }
            }
        }
    }

    // Task Card Styles
    .kanban-task {
        background: $window-background;
        border-radius: 0.75rem;
        padding: 1rem;
        margin: 0 0 0.75rem 0; // Removed margin-left to prevent offset
        @include macOS-shadow(0.08);
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        cursor: grab;
        border: 1px solid transparent;
        position: relative;
        overflow: hidden;
        user-select: none;
        box-sizing: border-box;

        &.theme-dark {
            background: $dark-window-background;
        }

        &:hover:not(.is-dragging) {
            @include macOS-shadow(0.15);
            border-color: rgba($system-blue, 0.3);
        }

        body.is-dragging &:not(.is-dragging) {
            opacity: 0.3 !important;
            border-color: rgba($system-blue, 0.2);
            background: rgba($system-blue, 0.02);
            transition: opacity 0.2s ease;

            .theme-dark & {
                border-color: rgba($system-blue, 0.3);
                background: rgba($system-blue, 0.05);
            }
        }

        &.is-dragging {
            cursor: grabbing !important;
            opacity: 0.5 !important; // Match JSX opacity for original task
            border-color: $system-blue !important;
            transition: none !important;
            pointer-events: none !important;
            position: relative !important;
            transform: none !important;

            .theme-dark & {
                border-color: $system-blue !important;
            }
        }

        .task-title,
        .task-description {
            user-select: none;
        }

        .task-actions {
            .action-btn {
                pointer-events: auto;
                position: relative;
                z-index: 10;
            }
        }

        &.is-dragging .task-actions {
            pointer-events: none;
        }

        &:not(.is-dragging) {
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        }

        .task-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 0.75rem;

            .task-title {
                font-size: 1rem;
                font-weight: 600;
                margin: 0;
                line-height: 1.4;
                color: $primary-text;
                flex: 1;
                text-align: left;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .task-actions {
                display: flex;
                gap: 0.25rem;
                opacity: 0;
                transition: opacity 0.2s ease;

                .action-btn {
                    width: 1.5rem;
                    height: 1.5rem;
                    border: none;
                    background: none;
                    border-radius: 0.375rem;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.75rem;
                    transition: all 0.2s ease;

                    &:hover {
                        background: $secondary-background;

                        .theme-dark & {
                            background: $dark-secondary-background;
                        }
                    }

                    &.edit-btn {
                        color: $system-blue;
                    }

                    &.delete-btn {
                        color: $system-red;
                    }
                }
            }

            &:hover .task-actions {
                opacity: 1;
            }
        }

        .task-description {
            font-size: 0.875rem;
            color: $secondary-text;
            line-height: 1.5;
            margin-bottom: 0.75rem;
            text-align: left;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .task-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 0.5rem;

            .task-meta {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .priority-indicator {
                    width: 0.5rem;
                    height: 0.5rem;
                    border-radius: 50%;

                    &.low {
                        background: $system-green;
                    }

                    &.medium {
                        background: $system-orange;
                    }

                    &.high {
                        background: $system-red;
                    }
                }

                .due-date {
                    font-size: 0.75rem;
                    color: $tertiary-text;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }

                    &.overdue {
                        color: $system-red;
                    }

                    &.due-soon {
                        color: $system-orange;
                    }
                }
            }

            .task-tags {
                display: flex;
                gap: 0.25rem;
                flex-wrap: wrap;

                .tag {
                    padding: 0.125rem 0.5rem;
                    background: rgba($system-blue, 0.1);
                    color: $system-blue;
                    border-radius: 0.75rem;
                    font-size: 0.625rem;
                    font-weight: 600;

                    .theme-dark & {
                        background: rgba($system-blue, 0.2);
                    }
                }
            }
        }

        &.priority-high {
            border-left: 0.25rem solid $system-red;
        }

        &.priority-medium {
            border-left: 0.25rem solid $system-orange;
        }

        &.priority-low {
            border-left: 0.25rem solid $system-green;
        }
    }

    // Task Form Modal
    .kanban-task-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999999;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        cursor: pointer;
        animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        transition: background-color 0.2s ease;

        &:hover {
            background: rgba(0, 0, 0, 0.6);
        }

        .theme-dark & {
            background: rgba(0, 0, 0, 0.7);

            &:hover {
                background: rgba(0, 0, 0, 0.8);
            }
        }

        .kanban-task-form {
            background: $window-background;
            border-radius: 1rem;
            padding: 2rem;
            width: 90%;
            max-width: 32rem;
            @include macOS-shadow(0.25);
            animation: slideUp 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
            cursor: default;
            position: relative;
            box-sizing: border-box;

            .theme-dark & {
                background: $dark-window-background;
            }

            .form-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 1.5rem;
                position: relative;
                z-index: 10001;

                h2 {
                    font-size: 1.25rem;
                    font-weight: 700;
                    margin: 0;
                    color: $primary-text;

                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }

                button {
                    width: 2rem;
                    height: 2rem;
                    border: 2px solid $system-red;
                    background: $secondary-background;
                    border-radius: 50%;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: $system-red;
                    transition: all 0.2s ease;
                    font-size: 1.25rem;
                    font-weight: 700;
                    line-height: 1;

                    &:hover {
                        background: $system-red;
                        color: white;
                        transform: scale(1.1);
                    }

                    &:active {
                        transform: scale(0.95);
                    }

                    &:focus {
                        @include macOS-focus-ring;
                    }

                    .theme-dark & {
                        background: $dark-secondary-background;
                        color: $dark-tertiary-text;

                        &:hover {
                            background: $system-red;
                            color: white;
                        }
                    }
                }
            }

            .form-group {
                margin-bottom: 1rem;

                label {
                    display: block;
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: $secondary-text;
                    margin-bottom: 0.5rem;

                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }

                input,
                textarea,
                select {
                    width: 100%;
                    padding: 0.75rem;
                    border: 1px solid $border-color;
                    border-radius: 0.5rem;
                    background: $window-background;
                    color: $primary-text;
                    font-size: 0.875rem;
                    transition: all 0.2s ease;
                    box-sizing: border-box;

                    &::placeholder {
                        color: $placeholder-text;
                    }

                    &:focus {
                        @include macOS-focus-ring;
                        border-color: $system-blue;
                    }

                    .theme-dark & {
                        background: $dark-secondary-background;
                        color: $dark-primary-text;
                        border-color: $dark-border-color;

                        &::placeholder {
                            color: $dark-placeholder-text;
                        }
                    }
                }

                textarea {
                    resize: vertical;
                    min-height: 4rem;
                }
            }
        }
    }

    // Loading and Empty States
    .loading-state {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 20rem;
        color: $tertiary-text;

        .theme-dark & {
            color: $dark-tertiary-text;
        }

        .loading-spinner {
            width: 2rem;
            height: 2rem;
            border: 2px solid $secondary-background;
            border-top: 2px solid $system-blue;
            border-radius: 50%;
            animation: spin 1s linear infinite;

            .theme-dark & {
                border-color: $dark-secondary-background;
                border-top-color: $system-blue;
            }
        }
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 20rem;
        color: $tertiary-text;
        text-align: center;

        .theme-dark & {
            color: $dark-tertiary-text;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .empty-description {
            font-size: 0.875rem;
            opacity: 0.7;
        }
    }

    // Drag and Drop Portal Styles
    .kanban-drag-portal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        pointer-events: none;
    }

    // Improve drag placeholder
    .kanban-task-placeholder {
        background: rgba($system-blue, 0.1);
        border: 2px dashed $system-blue;
        border-radius: 0.75rem;
        margin-bottom: 0.75rem;
        min-height: 4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $system-blue;
        font-size: 0.875rem;
        font-weight: 600;

        .theme-dark & {
            background: rgba($system-blue, 0.2);
        }

        &::after {
            content: 'Drop task here';
        }
    }
}

// Animations
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(2rem);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@keyframes dragPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.02);
    }
}

@keyframes columnPulse {

    0%,
    100% {
        transform: scale(1.03);
        box-shadow: 0 0 30px rgba(0, 122, 255, 0.3);
    }

    50% {
        transform: scale(1.04);
        box-shadow: 0 0 40px rgba(0, 122, 255, 0.5);
    }
}

@keyframes indicatorBounce {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.3);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

@keyframes bounceBack {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.95) rotate(-2deg);
    }

    100% {
        transform: scale(1) rotate(0deg);
    }
}

@keyframes dropSuccess {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes errorShake {

    0%,
    100% {
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-5px);
    }

    20%,
    40%,
    60%,
    80% {
        transform: translateX(5px);
    }
}

@keyframes successPop {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }

    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 1;
    }

    100% {
        transform: scale(1) rotate(360deg);
        opacity: 0;
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 0.8;
        transform: scaleY(1);
    }

    50% {
        opacity: 1;
        transform: scaleY(1.2);
    }
}

// Kanban Modal Close Button Override
.kanban-modal-close-button {
    width: 2.5rem !important;
    height: 2.5rem !important;
    border: 3px solid #FF3B30 !important;
    background: #F2F2F7 !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #FF3B30 !important;
    font-size: 1.5rem !important;
    font-weight: 900 !important;
    z-index: 1000001 !important;
    position: absolute !important;
    top: -0.5rem !important;
    right: -0.5rem !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
    min-width: 2.5rem !important;
    min-height: 2.5rem !important;
    max-width: 2.5rem !important;
    max-height: 2.5rem !important;
    box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3) !important;
    outline: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif !important;
    line-height: 1 !important;
    text-align: center !important;
    vertical-align: middle !important;

    .theme-dark & {
        background: #2C2C2E !important;
    }

    &:hover {
        background: #FF3B30 !important;
        color: white !important;
        transform: scale(1.1) !important;
        box-shadow: 0 6px 20px rgba(255, 59, 48, 0.5) !important;
    }

    &:active {
        transform: scale(0.95) !important;
    }

    &:focus {
        outline: 2px solid #007AFF !important;
        outline-offset: 2px !important;
    }
}

// Responsive Design
@media (max-width: 768px) {
    .advanced-kanban-board {
        padding: 1rem;

        .kanban-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;

            .header-actions {
                justify-content: space-between;

                .search-container .search-input {
                    width: 100%;
                }
            }
        }

        .kanban-board-container {
            height: calc(100vh - 14rem);
        }

        .kanban-column {
            min-width: 16rem;
            max-width: 16rem;
        }

        .kanban-task-modal-overlay .kanban-task-form {
            margin: 1rem;
            width: calc(100% - 2rem);

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    }
}

// Modal Animations
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Delete Modal Styles
.kanban-delete-modal-overlay {
    .kanban-delete-modal {

        .delete-success-content,
        .delete-confirm-content {
            h3 {
                .theme-dark & {
                    color: $dark-primary-text !important;
                }
            }

            p {
                .theme-dark & {
                    color: $dark-secondary-text !important;
                }
            }
        }

        button {
            transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}