import React, { useState, useEffect } from 'react';
import {
    BrowserRouter as Router,
    Routes,
    Route,
    Navigate,
    Outlet,
    useNavigate,
    useLocation
} from 'react-router-dom';

import LeftSidebar from './components/LeftSidebar.jsx';
import RightSidebar from './components/RightSidebar.jsx';
import Header from './components/Header.jsx';
import MainContent from './components/MainContent.jsx';

import StepsPage from './pages/Welcome.jsx';
import Login from './pages/Login.jsx';
import Dashboard from './pages/Dashboard.jsx';
import Settings from './pages/Settings.jsx';
import Profile from './pages/Profile.jsx';

import Subjects from './pages/Subjects.jsx';
import StudyTools from './pages/StudyTools.jsx';
import Journal from './pages/Journal.jsx';
import Achievements from './pages/Achievements.jsx';
import StudyPlanList from './components/StudyPlanList.jsx';
import StudyPlanDetail from './components/StudyPlanDetail.jsx';

import { AuthProvider, useAuth } from '../authContext.jsx';
import { ObjectivesProvider } from './contexts/ObjectivesContext.jsx';
import { PomodoroProvider } from './contexts/pomodoroContext.jsx';
import { ThemeProvider, useTheme } from './contexts/ThemeContext.jsx';
import MiniTimer from './components/MiniTimer.jsx';
import PomodoroPopup from './components/PomodoroPopup.jsx';
import './App.scss';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

const AppContent = ({
    leftSidebarCollapsed,
    rightSidebarCollapsed,
    toggleLeftSidebar,
    toggleRightSidebar
}) => {
    const { darkMode, toggleTheme } = useTheme();
    const { user, loading, login, logout } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();
    const [authChecked, setAuthChecked] = useState(false);
    const [hasPreferences, setHasPreferences] = useState(null);

    // Log user state for debugging
    useEffect(() => {
        console.log('App.jsx: User state:', user);
    }, [user]);

    // Check auth status and preferences
    useEffect(() => {
        let isMounted = true;

        const checkAuthAndPreferences = async () => {
            console.log('App.jsx: Checking auth and preferences...');

            if (!user || !user.token) {
                console.log('App.jsx: No user or token. Setting authChecked to true.');
                if (isMounted) {
                    setHasPreferences(null);
                    setAuthChecked(true);
                }
                return;
            }

            try {
                console.log('App.jsx: Fetching preferences for user:', user.id);
                const response = await axios.get(`${API_URL}/api/check-preferences/${user.id}`, {
                    headers: { Authorization: `Bearer ${user.token}` }
                });

                if (isMounted) {
                    const hasPrefs = response.data.preferencesCompleted || false;
                    console.log('App.jsx: User preferences completed:', hasPrefs);
                    setHasPreferences(hasPrefs);
                    setAuthChecked(true);
                }
            } catch (error) {
                console.error('App.jsx: Error checking preferences:', error);
                if (error.response?.status === 403) {
                    console.error('App.jsx: Invalid or expired token.');
                    logout();
                    if (isMounted) {
                        setHasPreferences(null);
                        setAuthChecked(true);
                    }
                } else {
                    if (isMounted) {
                        setHasPreferences(false);
                        setAuthChecked(true);
                    }
                }
            }
        };

        // Always check auth status when user changes or on initial load
        if (user !== null || !authChecked) {
            checkAuthAndPreferences();
        }

        return () => { isMounted = false; };
    }, [user, logout]);

    // Store last visited path in session storage
    useEffect(() => {
        if (user && hasPreferences === true && location.pathname !== '/login' && location.pathname !== '/steps') {
            sessionStorage.setItem('lastVisitedPath', location.pathname);
        }
    }, [location.pathname, user, hasPreferences]);

    // Handle redirects
    useEffect(() => {
        if (loading || !authChecked) {
            console.log('App.jsx: Still loading or auth not checked yet, skipping redirect logic');
            return;
        }

        console.log('App.jsx: Handling redirects. User:', !!user, 'hasPreferences:', hasPreferences, 'Path:', location.pathname);

        const currentPath = location.pathname;
        const protectedRoutes = ['/dashboard', '/subjects', '/study-tools', '/journal', '/achievements', '/settings', '/profile'];
        const lastVisitedPath = sessionStorage.getItem('lastVisitedPath') || '/dashboard';

        // If not logged in, only allow access to login page
        if (!user) {
            if (currentPath !== '/login') {
                console.log('App.jsx: Not logged in, redirecting to login');
                navigate('/login', { replace: true });
            }
            return;
        }

        // From this point on, user is logged in

        // Special case for root path
        if (currentPath === '/') {
            if (hasPreferences === true) {
                console.log('App.jsx: Root path with preferences, redirecting to dashboard');
                navigate('/dashboard', { replace: true });
            } else if (hasPreferences === false) {
                console.log('App.jsx: Root path without preferences, redirecting to steps');
                navigate('/steps', { replace: true });
            }
            return;
        }

        // If on login page but already authenticated
        if (currentPath === '/login') {
            if (hasPreferences === true) {
                console.log('App.jsx: Already logged in with preferences, redirecting to', lastVisitedPath);
                navigate(lastVisitedPath, { replace: true });
            } else if (hasPreferences === false) {
                console.log('App.jsx: Already logged in without preferences, redirecting to steps');
                navigate('/steps', { replace: true });
            }
            return;
        }

        // If preferences completed but on steps page
        if (hasPreferences === true && currentPath === '/steps') {
            console.log('App.jsx: Has preferences but on steps page, redirecting to', lastVisitedPath);
            navigate(lastVisitedPath, { replace: true });
            return;
        }

        // If preferences not completed and trying to access protected route
        if (hasPreferences === false && protectedRoutes.includes(currentPath)) {
            console.log('App.jsx: No preferences but trying to access protected route, redirecting to steps');
            navigate('/steps', { replace: true });
            return;
        }

        console.log('App.jsx: No redirection needed');
    }, [authChecked, hasPreferences, user, loading, navigate, location.pathname]);

    const handleStepsComplete = async (preferences) => {
        console.log('App.jsx: handleStepsComplete called with user:', user, 'preferences:', preferences);
        if (!user || !user.token) {
            console.error('App.jsx: No user or token available. Redirecting to login.');
            navigate('/login', { replace: true });
            return;
        }

        try {
            const response = await axios.post(`${API_URL}/api/preferences`, {
                user_id: user.id,
                ...preferences
            }, {
                headers: { Authorization: `Bearer ${user.token}` }
            });

            if (response.data.success) {
                setHasPreferences(true);
                navigate('/dashboard');
            }
        } catch (error) {
            console.error('App.jsx: Error saving preferences:', error);
            if (error.response?.status === 403) {
                console.error('App.jsx: Invalid or expired token. Redirecting to login.');
                logout();
                navigate('/login', { replace: true });
            }
        }
    };

    const handleLogin = (userData, authToken) => {
        console.log('App.jsx: handleLogin called with userData:', userData, 'authToken:', authToken);
        login(userData, authToken);
        // We'll let Login.jsx handle the navigation directly after checking preferences
        // This prevents the flash of the steps page
    };

    const handleLogout = () => {
        logout();
        navigate('/login');
    };

    const renderMainContent = (children) => (
        <MainContent
            toggleRightSidebar={toggleRightSidebar}
            rightSidebarCollapsed={rightSidebarCollapsed}
        >
            {children}
        </MainContent>
    );

    if (loading) {
        return <div className="loading-screen">Loading...</div>;
    }

    return (
        <Routes>
            <Route path="/login" element={<Login onLogin={handleLogin} />} />
            <Route path="/steps" element={<StepsPage onComplete={handleStepsComplete} />} />

            <Route
                path="*"
                element={
                    user ? (
                        hasPreferences ? (
                            <div className={`main-dashboard ${darkMode ? 'theme-dark' : ''}`}>
                                <LeftSidebar
                                    collapsed={leftSidebarCollapsed}
                                    toggleCollapse={toggleLeftSidebar}
                                    onLogout={handleLogout}
                                />
                                <div className="main-content-wrapper">
                                    <Header
                                        darkMode={darkMode}
                                        toggleTheme={toggleTheme}
                                        user={user}
                                    />
                                    <ObjectivesProvider>
                                        <Routes>
                                            <Route element={<Outlet />}>
                                                <Route path="/dashboard" element={renderMainContent(<Dashboard />)} />
                                                <Route path="/subjects" element={renderMainContent(<Subjects />)} />
                                                <Route path="/study-tools" element={renderMainContent(<StudyTools />)} />
                                                <Route path="/journal" element={renderMainContent(<Journal />)} />
                                                <Route path="/achievements" element={renderMainContent(<Achievements />)} />
                                                <Route path="/settings" element={renderMainContent(<Settings />)} />
                                                <Route path="/profile" element={renderMainContent(<Profile />)} />
                                                <Route path="/study-plans" element={renderMainContent(<StudyPlanList />)} />
                                                <Route path="/study-plan/:planId" element={renderMainContent(<StudyPlanDetail />)} />
                                                <Route path="*" element={<Navigate to="/dashboard" replace />} />
                                            </Route>
                                        </Routes>
                                    </ObjectivesProvider>
                                </div>
                                <RightSidebar
                                    collapsed={rightSidebarCollapsed}
                                    toggleCollapse={toggleRightSidebar}
                                />
                            </div>
                        ) : (
                            <Navigate to="/steps" replace />
                        )
                    ) : (
                        <Navigate to="/login" replace />
                    )
                }
            />
        </Routes>
    );
};

const App = () => {
    const [leftSidebarCollapsed, setLeftSidebarCollapsed] = useState(false);
    const [rightSidebarCollapsed, setRightSidebarCollapsed] = useState(true);

    const toggleLeftSidebar = () => setLeftSidebarCollapsed(!leftSidebarCollapsed);
    const toggleRightSidebar = () => setRightSidebarCollapsed(!rightSidebarCollapsed);

    return (
        <Router
            future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true
            }}
        >
            <AuthProvider>
                <ThemeProvider>
                    <PomodoroProvider>
                        <AppContent
                            leftSidebarCollapsed={leftSidebarCollapsed}
                            rightSidebarCollapsed={rightSidebarCollapsed}
                            toggleLeftSidebar={toggleLeftSidebar}
                            toggleRightSidebar={toggleRightSidebar}
                        />
                        <MiniTimer />
                        <PomodoroPopup />
                        {/* Audio elements for Pomodoro */}
                        <audio id="pomodoro-complete" src="https://www.soundjay.com/buttons/beep-07.mp3" />
                        <audio id="pomodoro-tick" src="https://www.soundjay.com/clock/sounds/clock-ticking-2.mp3" />
                    </PomodoroProvider>
                </ThemeProvider>
            </AuthProvider>
        </Router>
    );
};

export default App;