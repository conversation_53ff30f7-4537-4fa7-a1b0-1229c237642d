@use "variables" as *;
@use "sass:color";

.contact-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000 !important;
  animation: fadeIn 0.2s ease-out;

  &.theme-dark {
    background-color: rgba(0, 0, 0, 0.7);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
}

.contact-modal {
  width: 90%;
  max-width: 650px;
  max-height: 90vh;
  background-color: $window-background;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid $border-color;
  z-index: 10000;

  .theme-dark & {
    background-color: $dark-window-background;
    border-color: $dark-border-color;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid $border-color;

    .theme-dark & {
      border-color: $dark-border-color;
    }

    h2 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: $primary-text;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .close-button {
      background: transparent;
      border: none;
      color: $tertiary-text;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba($system-gray, 0.1);
        color: $secondary-text;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .theme-dark & {
        color: $dark-tertiary-text;

        &:hover {
          background-color: rgba($dark-tertiary-text, 0.2);
          color: $dark-secondary-text;
        }
      }
    }
  }

  .tabs-container {
    display: flex;
    border-bottom: 1px solid $border-color;
    background-color: $secondary-background;

    .theme-dark & {
      border-color: $dark-border-color;
      background-color: $dark-secondary-background;
    }

    .tab {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0.75rem 0.5rem;
      cursor: pointer;
      transition: all 0.2s ease;
      color: $tertiary-text;
      position: relative;
      font-size: 0.9rem;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -1px;
        width: 100%;
        height: 2px;
        background-color: transparent;
        transition: all 0.2s ease;
      }

      &:hover {
        color: $secondary-text;
      }

      &.active {
        color: $system-blue;

        &::after {
          background-color: $system-blue;
        }
      }

      .theme-dark & {
        color: $dark-tertiary-text;

        &:hover {
          color: $dark-secondary-text;
        }

        &.active {
          color: $system-blue;
        }
      }

      .tab-icon {
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .contact-form {
    padding: 1.5rem;
    overflow-y: auto;

    .form-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0;
      }
    }

    .form-group {
      margin-bottom: 1rem;
      flex: 1;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        color: $secondary-text;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }

      input,
      textarea {
        width: 100%;
        padding: 0.75rem;
        border-radius: 8px;
        border: 1px solid $border-color;
        background-color: $window-background;
        font-size: 0.95rem;
        color: $primary-text;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        &:focus {
          outline: none;
          border-color: $system-blue;
          box-shadow: 0 0 0 2px rgba($system-blue, 0.2);
        }

        &.error {
          border-color: $system-red;

          &:focus {
            box-shadow: 0 0 0 2px rgba($system-red, 0.2);
          }
        }

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .theme-dark & {
          background-color: $dark-window-background;
          border-color: $dark-border-color;
          color: $dark-primary-text;

          &:focus {
            border-color: $system-blue;
            box-shadow: 0 0 0 2px rgba($system-blue, 0.3);
          }
        }
      }

      textarea {
        resize: vertical;
        min-height: 100px;
      }

      .error-message {
        display: flex;
        align-items: center;
        color: $system-red;
        font-size: 0.8rem;
        margin-top: 0.5rem;

        svg {
          margin-right: 0.25rem;
          font-size: 1rem;
        }
      }

      .character-count {
        text-align: right;
        font-size: 0.8rem;
        color: $tertiary-text;
        margin-top: 0.25rem;

        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }
    }

    .priority-group {
      .priority-options {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;

        .priority-option {
          display: flex;
          align-items: center;
          padding: 0.5rem 0.75rem;
          border-radius: 6px;
          border: 1px solid $border-color;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: var(--priority-color);
          }

          &.active {
            background-color: rgba(var(--priority-color), 0.1);
            border-color: var(--priority-color);
          }

          .theme-dark & {
            border-color: $dark-border-color;

            &:hover {
              border-color: var(--priority-color);
            }
          }

          .priority-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--priority-color);
            margin-right: 0.5rem;
          }

          .priority-label {
            font-size: 0.9rem;
            color: $secondary-text;

            .theme-dark & {
              color: $dark-secondary-text;
            }
          }
        }
      }
    }

    .attachment-group {
      .attachment-container {
        display: flex;
        align-items: center;

        .attachment-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 0.75rem;
          border-radius: 6px;
          border: 1px solid $border-color;
          background-color: $window-background;
          color: $system-blue;
          font-size: 0.9rem;
          cursor: pointer;
          transition: all 0.2s ease;

          svg {
            margin-right: 0.5rem;
          }

          &:hover {
            background-color: rgba($system-blue, 0.05);
            border-color: $system-blue;
          }

          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
          }

          .theme-dark & {
            background-color: $dark-window-background;
            border-color: $dark-border-color;

            &:hover {
              background-color: rgba($system-blue, 0.1);
              border-color: $system-blue;
            }
          }
        }
      }
    }

    .attachments-list {
      margin-bottom: 1.5rem;

      .attachment-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem 0.75rem;
        background-color: $secondary-background;
        border-radius: 6px;
        margin-bottom: 0.5rem;

        .theme-dark & {
          background-color: $dark-secondary-background;
        }

        .attachment-name {
          font-size: 0.9rem;
          color: $secondary-text;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 80%;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }

        .remove-attachment {
          background: transparent;
          border: none;
          color: $tertiary-text;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0.25rem;
          border-radius: 50%;
          transition: all 0.2s ease;

          &:hover {
            color: $system-red;
            background-color: rgba($system-red, 0.1);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .theme-dark & {
            color: $dark-tertiary-text;

            &:hover {
              color: $system-red;
              background-color: rgba($system-red, 0.2);
            }
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-top: 1rem;

      button {
        padding: 0.75rem 1.25rem;
        border-radius: 8px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        svg {
          margin-right: 0.5rem;
        }
      }

      .cancel-button {
        background-color: $secondary-background;
        color: $secondary-text;
        border: 1px solid $border-color;

        &:hover:not(:disabled) {
          background-color: $system-light-gray;
        }

        .theme-dark & {
          background-color: $dark-secondary-background;
          color: $dark-secondary-text;
          border-color: $dark-border-color;

          &:hover:not(:disabled) {
            background-color: $dark-border-color;
          }
        }
      }

      .submit-button {
        background-color: $system-blue;
        color: white;
        border: none;

        &:hover:not(:disabled) {
          background-color: color.adjust($system-blue, $lightness: -5%);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba($system-blue, 0.3);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
          background-color: color.adjust($system-blue, $lightness: -10%);
          box-shadow: 0 1px 3px rgba($system-blue, 0.2);
        }

        .spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top-color: white;
          animation: spin 1s linear infinite;
          margin-right: 0.5rem;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
      }
    }
  }

  .success-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1.5rem;
    text-align: center;
    animation: fadeIn 0.5s ease-out;

    .success-icon {
      font-size: 4rem;
      color: $system-green;
      margin-bottom: 1.5rem;
    }

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: $primary-text;

      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    p {
      margin: 0;
      font-size: 1rem;
      color: $secondary-text;
      max-width: 80%;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }
}