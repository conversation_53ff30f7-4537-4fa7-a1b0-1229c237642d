-- Complete migration script to add chapter_id to quizzes table
-- Run this script on your existing database

-- Step 1: Add chapter_id column to quizzes table
ALTER TABLE quizzes 
ADD COLUMN IF NOT EXISTS chapter_id INTEGER;

-- Step 2: Add foreign key constraint
ALTER TABLE quizzes 
ADD CONSTRAINT fk_quizzes_chapter_id 
FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL;

-- Step 3: Add index for better performance
CREATE INDEX IF NOT EXISTS idx_quizzes_chapter_id ON quizzes(chapter_id);

-- Step 4: Add comment to document the purpose
COMMENT ON COLUMN quizzes.chapter_id IS 'Optional reference to the chapter this quiz is focused on';

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'quizzes' AND column_name = 'chapter_id';
