import React from 'react';
import SpeedIcon from '@mui/icons-material/Speed';
import '../../styles/SettingsPanels.scss';

const ResponsePriorityPanel = ({ settings }) => {
  const { responseTime, setResponseTime, isDarkMode } = settings;
  
  const priorityLevels = [
    { id: 'standard', label: 'Standard', time: '24-48 hours', description: 'Regular response time for non-urgent inquiries' },
    { id: 'normal', label: 'Priority', time: '12-24 hours', description: 'Faster response for important questions' },
    { id: 'urgent', label: 'Urgent', time: '1-6 hours', description: 'Rapid response for time-sensitive issues' }
  ];
  
  return (
    <div className={`detail-panel priority-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Response Priority</h2>
        <p className="panel-description">
          Set the priority level for responses from our support team.
        </p>
      </div>
      
      <div className="settings-group priority-matrix">
        {priorityLevels.map(level => (
          <div 
            key={level.id}
            className={`priority-level ${responseTime === level.id ? 'active' : ''}`}
            onClick={() => setResponseTime(level.id)}
          >
            <div className="priority-header">
              <div className="priority-icon">
                <SpeedIcon />
              </div>
              <div className="priority-label">
                <h3>{level.label}</h3>
                <span className="response-time">{level.time}</span>
              </div>
              <div className="priority-selector"></div>
            </div>
            <p className="priority-description">{level.description}</p>
          </div>
        ))}
      </div>
      
      <div className="priority-visualization">
        <div className="speedometer">
          <div className="speedometer-scale">
            <div className="scale-segment standard"></div>
            <div className="scale-segment normal"></div>
            <div className="scale-segment urgent"></div>
          </div>
          <div 
            className="speedometer-needle"
            style={{
              transform: `rotate(${
                responseTime === 'standard' ? -30 :
                responseTime === 'normal' ? 0 : 30
              }deg)`
            }}
          ></div>
          <div className="speedometer-center">
            <span>{
              responseTime === 'standard' ? 'Standard' :
              responseTime === 'normal' ? 'Priority' : 'Urgent'
            }</span>
          </div>
        </div>
      </div>
      
      <div className="info-box">
        <p>
          Response priority affects how quickly our support team will respond to your inquiries. 
          Higher priority levels are intended for time-sensitive or critical issues.
        </p>
      </div>
    </div>
  );
};

export default ResponsePriorityPanel;
