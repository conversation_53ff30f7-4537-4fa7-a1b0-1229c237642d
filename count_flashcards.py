
import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '5432')
DB_NAME = os.environ.get('DB_NAME', 'blueprint')
DB_USER = os.environ.get('DB_USER', 'postgres')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Lukind@1956')

# Connect to the database
conn = psycopg2.connect(
    host=DB_HOST,
    port=DB_PORT,
    dbname=DB_NAME,
    user=DB_USER,
    password=DB_PASSWORD
)

cursor = conn.cursor()

# Count total flashcards
cursor.execute("SELECT COUNT(*) FROM flashcards")
total_count = cursor.fetchone()[0]
print(f"Total flashcards: {total_count}")

# Count flashcards by user
cursor.execute("SELECT user_id, COUNT(*) FROM flashcards GROUP BY user_id")
user_counts = cursor.fetchall()
for user_id, count in user_counts:
    print(f"User {user_id}: {count} flashcards")

# Count AI-generated flashcards
cursor.execute("SELECT COUNT(*) FROM flashcards WHERE ai_generated = TRUE")
ai_generated_count = cursor.fetchone()[0]
print(f"AI-generated flashcards: {ai_generated_count}")

# Close the connection
cursor.close()
conn.close()
