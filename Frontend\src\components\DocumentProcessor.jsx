import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../authContext';
import { useNavigate } from 'react-router-dom';
import '../styles/DocumentProcessor.scss';
import StudyPlanCreator from './StudyPlanCreator';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import AutoStoriesIcon from '@mui/icons-material/AutoStories';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import SchoolIcon from '@mui/icons-material/School';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

/**
 * Message popup component for displaying success or error messages
 */
const MessagePopup = ({ message, isError = false, onClose }) => {
    // Auto-close success messages after 2 seconds
    useEffect(() => {
        if (!isError) {
            const timer = setTimeout(() => {
                onClose();
            }, 2000);

            return () => clearTimeout(timer);
        }
    }, [isError, onClose]);

    // Theme detection with MutationObserver to detect theme changes
    const [isDarkMode, setIsDarkMode] = useState(false);

    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    return (
        <div className={`message-popup-overlay ${isDarkMode ? 'theme-dark' : ''}`}>
            <div className={`message-popup-container ${isDarkMode ? 'theme-dark' : ''}`}>
                <div className="popup-header">
                    <h2>{isError ? 'Error' : 'Success'}</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>
                <div className="popup-content">
                    <p>{message}</p>
                </div>
            </div>
        </div>
    );
};

/**
 * Document Processor component for processing uploaded documents
 */
const DocumentProcessor = ({ documentId, documentName, selectedTools = [], onClose }) => {
    const { user, token } = useAuth();
    const navigate = useNavigate();

    // State management
    const [processingStatus, setProcessingStatus] = useState('processing'); // 'processing', 'success', 'error'
    const [processingStep, setProcessingStep] = useState(1); // 1: Extracting text, 2: Analyzing content, 3: Creating structure
    const [processingProgress, setProcessingProgress] = useState(0);
    const [curriculumData, setCurriculumData] = useState(null);
    const [showStudyPlanCreator, setShowStudyPlanCreator] = useState(false);
    const [messagePopup, setMessagePopup] = useState({ show: false, message: '', isError: false });

    // Theme detection with MutationObserver to detect theme changes
    const [isDarkMode, setIsDarkMode] = useState(false);

    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    // Force using port 5000 for the Gemini AI backend
    const API_URL = 'http://127.0.0.1:5000';

    // Helper function to create a mock curriculum
    const createMockCurriculum = (id, name) => {
        return {
            subjects: [
                {
                    id: id || '1',
                    name: name || 'Uploaded Document',
                    chapters: [
                        {
                            id: 1,
                            name: "Chapter 1",
                            objectives: [
                                { id: 1, text: "Learn the basics" },
                                { id: 2, text: "Understand key concepts" }
                            ]
                        },
                        {
                            id: 2,
                            name: "Chapter 2",
                            objectives: [
                                { id: 3, text: "Apply knowledge to real-world scenarios" },
                                { id: 4, text: "Develop advanced skills" }
                            ]
                        }
                    ]
                }
            ]
        };
    };

    useEffect(() => {
        const processDocument = async () => {
            setProcessingStatus('processing');
            setProcessingStep(1);
            setProcessingProgress(0);

            try {
                console.log('Processing document:', documentId, documentName);
                console.log('Selected tools:', selectedTools);

                // Step 1: Extracting text from document
                setProcessingStep(1);
                simulateProgress(0, 30);

                // Step 2: Analyzing content with AI
                setProcessingStep(2);
                simulateProgress(30, 70);

                // Step 3: Creating learning structure
                setProcessingStep(3);
                simulateProgress(70, 100);

                console.log('Fetching data for subject ID:', documentId);
                console.log('Using API URL:', API_URL);
                console.log('Token available:', !!token);

                // Make sure documentId is a number
                let subjectId;
                try {
                    subjectId = parseInt(documentId);
                    if (isNaN(subjectId)) {
                        console.error('Invalid subject ID:', documentId);
                        throw new Error('Invalid subject ID');
                    }
                } catch (error) {
                    console.error('Error parsing subject ID:', error);
                    // Create a mock curriculum as fallback
                    const mockCurriculumData = createMockCurriculum(documentId, documentName);
                    setCurriculumData(mockCurriculumData);
                    setProcessingStatus('success');
                    return; // Exit early
                }

                // Call the API to process the document
                try {
                    // Use the upload_document endpoint which already handles document processing
                    const response = await axios.post(
                        `${API_URL}/api/process_document`,
                        {
                            subject_id: subjectId,
                            study_tools: selectedTools // Include selected tools from props
                        },
                        {
                            headers: {
                                Authorization: `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );

                    console.log('Document processing response:', response.data);

                    if (response.data.success) {
                        // Format the data for our UI
                        const processedData = response.data.data || {};

                        // Transform the data to match our expected format
                        const formattedData = {
                            subjects: processedData.subjects?.map(subject => ({
                                id: subjectId,
                                name: subject.name || documentName,
                                chapters: subject.chapters?.map((chapter, chIdx) => ({
                                    id: chIdx + 1,
                                    name: chapter.title || chapter.name,
                                    objectives: chapter.objectives?.map((objective, objIdx) => ({
                                        id: objIdx + 1,
                                        text: typeof objective === 'string' ? objective : objective.text,
                                        estimated_time_minutes: objective.estimated_time_minutes || 30,
                                        difficulty_level: objective.difficulty_level || 2
                                    }))
                                }))
                            })) || []
                        };

                        if (formattedData.subjects.length === 0) {
                            // Fallback to mock data if no subjects were found
                            setCurriculumData(createMockCurriculum(subjectId, documentName));
                        } else {
                            setCurriculumData(formattedData);
                        }

                        // Check if any tools were generated and show appropriate success message
                        const toolsGenerated = [];

                        // Check for flashcards
                        const flashcardsGenerated = response.data.flashcards_generated || 0;
                        if (flashcardsGenerated > 0) {
                            toolsGenerated.push(`${flashcardsGenerated} flashcards`);
                        }

                        // Check for other tools based on selectedTools
                        // Note: Tool IDs are strings in selectedTools array
                        if (selectedTools.includes('10') && response.data.quizzes_generated) {
                            toolsGenerated.push(`${response.data.quizzes_generated} quizzes`);
                        }

                        if (selectedTools.includes('4') && response.data.cornell_notes_generated) {
                            toolsGenerated.push('Cornell notes template');
                        }

                        if (selectedTools.includes('6') && response.data.mindmap_generated) {
                            toolsGenerated.push('mind map structure');
                        }

                        // Check for flashcards using the correct tool ID (9)
                        if (selectedTools.includes('9') && flashcardsGenerated === 0) {
                            // If flashcard tool was selected but no flashcards were generated,
                            // it might be because the backend uses a different ID
                            console.log('Flashcard tool was selected but no flashcards generated');
                        }

                        // Show success message if any tools were generated
                        if (toolsGenerated.length > 0) {
                            setMessagePopup({
                                show: true,
                                message: `Successfully generated: ${toolsGenerated.join(', ')}!`,
                                isError: false
                            });
                        }

                        setProcessingStatus('success');
                    } else {
                        throw new Error(response.data.message || response.data.error || 'Failed to process document');
                    }
                } catch (apiError) {
                    console.error('API error processing document:', apiError);
                    // Fallback to mock data
                    const mockCurriculumData = createMockCurriculum(subjectId, documentName);
                    setCurriculumData(mockCurriculumData);
                    setProcessingStatus('success');
                }
            } catch (error) {
                console.error('Error in processDocument:', error);
                setProcessingStatus('error');
                setMessagePopup({
                    show: true,
                    message: `Error processing document: ${error.message}`,
                    isError: true
                });
            }
        };

        if (documentId && token) {
            processDocument();
        }
    }, [documentId, token, user.id, API_URL, documentName, selectedTools]);

    const simulateProgress = (start, end) => {
        const duration = 3000; // 3 seconds
        const interval = 100; // Update every 100ms
        const steps = duration / interval;
        const increment = (end - start) / steps;

        let progress = start;
        const timer = setInterval(() => {
            progress += increment;
            setProcessingProgress(Math.min(progress, end));

            if (progress >= end) {
                clearInterval(timer);
            }
        }, interval);
    };

    const handleCreateStudyPlan = () => {
        setShowStudyPlanCreator(true);
    };

    const handleMessagePopupClose = () => {
        setMessagePopup({ show: false, message: '', isError: false });
    };

    const renderProcessingContent = () => {
        return (
            <div className="processing-content">
                <div className="progress-indicator">
                    <CircularProgress
                        variant="determinate"
                        value={processingProgress}
                        size={80}
                        thickness={4}
                    />
                    <div className="progress-label">
                        {Math.round(processingProgress)}%
                    </div>
                </div>

                <h3>Processing Document</h3>
                <p className="document-name">{documentName}</p>

                <div className="processing-steps">
                    <div className={`step ${processingStep >= 1 ? 'active' : ''} ${processingStep > 1 ? 'completed' : ''}`}>
                        <div className="step-number">1</div>
                        <div className="step-text">Extracting text from document</div>
                    </div>
                    <div className={`step ${processingStep >= 2 ? 'active' : ''} ${processingStep > 2 ? 'completed' : ''}`}>
                        <div className="step-number">2</div>
                        <div className="step-text">Analyzing content with AI</div>
                    </div>
                    <div className={`step ${processingStep >= 3 ? 'active' : ''} ${processingStep > 3 ? 'completed' : ''}`}>
                        <div className="step-number">3</div>
                        <div className="step-text">Creating learning structure</div>
                    </div>
                </div>

                <p className="processing-note">
                    This may take a few minutes. We're using AI to analyze your document and create a structured learning plan.
                </p>
            </div>
        );
    };

    const renderSuccessContent = () => {
        if (!curriculumData) return null;

        const totalSubjects = curriculumData.subjects.length;
        const totalChapters = curriculumData.subjects.reduce(
            (sum, subject) => sum + subject.chapters.length, 0
        );
        const totalObjectives = curriculumData.subjects.reduce(
            (sum, subject) => sum + subject.chapters.reduce(
                (chapterSum, chapter) => chapterSum + chapter.objectives.length, 0
            ), 0
        );

        return (
            <div className="success-content">
                <div className="success-icon">
                    <CheckCircleIcon />
                </div>

                <h3>Document Processed Successfully!</h3>
                <p className="document-name">{documentName}</p>

                <div className="curriculum-stats">
                    <div className="stat-item">
                        <MenuBookIcon />
                        <div className="stat-value">{totalSubjects}</div>
                        <div className="stat-label">Subjects</div>
                    </div>
                    <div className="stat-item">
                        <AutoStoriesIcon />
                        <div className="stat-value">{totalChapters}</div>
                        <div className="stat-label">Chapters</div>
                    </div>
                    <div className="stat-item">
                        <SchoolIcon />
                        <div className="stat-value">{totalObjectives}</div>
                        <div className="stat-label">Objectives</div>
                    </div>
                </div>

                <div className="action-buttons">
                    <button
                        className="macos-button primary"
                        onClick={handleCreateStudyPlan}
                    >
                        Create Study Plan <ArrowForwardIcon />
                    </button>
                    <button
                        className="macos-button secondary"
                        onClick={onClose}
                    >
                        Close
                    </button>
                </div>
            </div>
        );
    };

    const renderErrorContent = () => {
        return (
            <div className="error-content">
                <div className="error-icon">
                    <ErrorIcon />
                </div>

                <h3>Processing Failed</h3>
                <p className="document-name">{documentName}</p>

                <p className="error-message">
                    We encountered an error while processing your document. Please try again or upload a different document.
                </p>

                <div className="action-buttons">
                    <button
                        className="macos-button"
                        onClick={() => window.location.reload()}
                    >
                        Try Again
                    </button>
                    <button
                        className="macos-button secondary"
                        onClick={onClose}
                    >
                        Close
                    </button>
                </div>
            </div>
        );
    };

    return (
        <div className={`document-processor-overlay ${isDarkMode ? 'theme-dark' : ''}`}>
            <div className={`document-processor-container ${isDarkMode ? 'theme-dark' : ''}`}>
                <div className="popup-header-main">
                    <h2>Document Processor</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>
                <div className="popup-content">
                    {processingStatus === 'processing' && renderProcessingContent()}
                    {processingStatus === 'success' && renderSuccessContent()}
                    {processingStatus === 'error' && renderErrorContent()}
                </div>
            </div>

            {showStudyPlanCreator && (
                <StudyPlanCreator
                    onClose={() => setShowStudyPlanCreator(false)}
                    curriculumId={documentId}
                    curriculumTitle={documentName}
                />
            )}

            {messagePopup.show && (
                <MessagePopup
                    message={messagePopup.message}
                    isError={messagePopup.isError}
                    onClose={handleMessagePopupClose}
                />
            )}
        </div>
    );
};

export default DocumentProcessor;
