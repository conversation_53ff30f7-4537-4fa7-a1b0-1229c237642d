// Import variables from the main variables file
@import '../../../styles/variables';
@import './QuizModals.scss';

// Advanced Quizzes App - macOS Inspired Design
.advanced-quizzes-app {
  max-width: 65rem !important;
  width: 100%;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  min-height: 100vh;
  background: $window-background;
  color: $primary-text;
  display: flex;
  flex-direction: column;

  &.theme-dark {
    background: $dark-window-background;
    color: $dark-primary-text;
  }
}

// Fixed Header Section
.quizzes-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: $window-background;
  padding: 2rem 1rem 1rem 1rem;
  margin-bottom: 0;
  border-bottom: 1px solid $border-color;
  backdrop-filter: blur(10px);

  .theme-dark & {
    background: $dark-window-background;
    border-bottom-color: $dark-border-color;
  }

  &.fixed-header {
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);

    .theme-dark & {
      box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    }
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    .header-left {
      display: flex;
      flex-direction: column;
      text-align: left;
      gap: 0;
      justify-content: left;

      .app-title {
        margin: 0 0 0.5rem 0;
        padding: 0;
        font-size: 2rem;
        font-weight: 700;
        color: $primary-text;
        text-align: left;
        width: 100%;

        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .app-subtitle {
        margin: 0;
        font-size: 1rem;
        color: $secondary-text;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    .header-controls {
      display: flex;
      gap: 1rem;
      align-items: center;

      .control-button {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        &.primary {
          background: $system-blue;
          color: white;

          &:hover {
            background: darken($system-blue, 10%);
            transform: translateY(-1px);
          }
        }

        &.secondary {
          background: $secondary-background;
          color: $secondary-text;
          border: 1px solid $border-color;

          .theme-dark & {
            background: $dark-secondary-background;
            color: $dark-secondary-text;
            border-color: $dark-border-color;
          }

          &:hover {
            background: $system-light-gray;

            .theme-dark & {
              background: $dark-border-color;
            }
          }
        }

        svg {
          width: 1rem;
          height: 1rem;
        }

        .material-icons {
          font-size: 1rem;
          line-height: 1;
        }
      }

      .close-modal-btn {
        background: rgba($system-red, 0.1);
        color: $system-red;
        border: 1px solid rgba($system-red, 0.2);

        .theme-dark & {
          background: rgba($system-red, 0.15);
          border-color: rgba($system-red, 0.3);
        }

        &:hover {
          background: rgba($system-red, 0.2);
          color: white;
          background: $system-red;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba($system-red, 0.3);

          .theme-dark & {
            background: $system-red;
          }
        }

        .material-icons {
          font-size: 1rem;
          line-height: 1;
        }
      }
    }
  }
}

// Scrollable Content Area
.quizzes-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 1rem 2rem 1rem;
  margin-top: 1rem;

  // Custom scrollbar for webkit browsers
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba($system-gray, 0.3);
    border-radius: 4px;

    .theme-dark & {
      background: rgba($dark-border-color, 0.5);
    }

    &:hover {
      background: rgba($system-gray, 0.5);

      .theme-dark & {
        background: rgba($dark-border-color, 0.7);
      }
    }
  }
}

// Quiz Form Container - AI Form Styling
.quiz-form-container {
  background: $window-background;
  border: 1px solid $border-color;
  border-radius: 1rem;
  margin: 2rem 0;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;

  .theme-dark & {
    background: $dark-window-background;
    border-color: $dark-border-color;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  }

  &.ai-form {
    background: $secondary-background;
    border: 2px solid $system-blue;
    box-shadow: 0 8px 32px rgba($system-blue, 0.2);

    .theme-dark & {
      background: $dark-secondary-background;
      border-color: $system-blue;
      box-shadow: 0 8px 32px rgba($system-blue, 0.3);
    }
  }

  .form-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    background: rgba($system-blue, 0.05);
    border-bottom: 1px solid $border-color;

    .theme-dark & {
      background: rgba($system-blue, 0.1);
      border-bottom-color: $dark-border-color;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 1rem;

      .ai-icon {
        width: 3rem;
        height: 3rem;
        background: $system-blue;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 16px rgba($system-blue, 0.3);

        svg {
          width: 1.5rem;
          height: 1.5rem;
          color: white;
        }
      }

      .header-text {
        h2 {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
          color: $system-blue;

          .theme-dark & {
            color: $system-blue;
          }
        }

        p {
          margin: 0.25rem 0 0 0;
          font-size: 0.875rem;
          color: $secondary-text;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .close-form-btn {
      width: 2.5rem;
      height: 2.5rem;
      border: none;
      background: rgba($system-light-gray, 0.5);
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      color: $secondary-text;

      .theme-dark & {
        background: rgba($dark-border-color, 0.5);
        color: $dark-secondary-text;
      }

      &:hover {
        background: rgba($system-red, 0.1);
        color: $system-red;
        transform: scale(1.05);
      }

      svg {
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }

  .ai-generation-form {
    padding: 2rem;

    .form-group {
      margin-bottom: 1.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: $primary-text;
        margin-bottom: 0.75rem;

        .theme-dark & {
          color: $dark-primary-text;
        }

        &::after {
          content: '*';
          color: $system-red;
          margin-left: 0.25rem;
        }
      }

      .checkbox-group {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        .checkbox-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1rem;
          background: $window-background;
          border: 2px solid $border-color;
          border-radius: 0.75rem;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 0.875rem;
          font-weight: 500;
          color: $secondary-text;
          min-width: 8rem;

          .theme-dark & {
            background: $dark-window-background;
            border-color: $dark-border-color;
            color: $dark-secondary-text;
          }

          &:hover {
            border-color: $system-blue;
            background: rgba($system-blue, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba($system-blue, 0.15);

            .theme-dark & {
              background: rgba($system-blue, 0.1);
            }
          }

          input[type="checkbox"] {
            width: 1.125rem;
            height: 1.125rem;
            border: 2px solid $border-color;
            border-radius: 0.25rem;
            background: $window-background;
            cursor: pointer;
            position: relative;
            margin: 0;
            appearance: none;
            transition: all 0.2s ease;

            .theme-dark & {
              border-color: $dark-border-color;
              background: $dark-window-background;
            }

            &:checked {
              background: $system-blue;
              border-color: $system-blue;

              &::after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-size: 0.75rem;
                font-weight: 600;
              }
            }

            &:focus {
              outline: none;
              box-shadow: 0 0 0 3px rgba($system-blue, 0.2);
            }
          }

          &:has(input:checked) {
            border-color: $system-blue;
            background: rgba($system-blue, 0.1);
            color: $system-blue;

            .theme-dark & {
              background: rgba($system-blue, 0.15);
            }
          }

          span {
            user-select: none;
          }
        }
      }

      .form-select {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid $border-color;
        border-radius: 0.75rem;
        background: $window-background;
        color: $primary-text;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.75rem center;
        background-repeat: no-repeat;
        background-size: 1rem;
        padding-right: 2.5rem;

        .theme-dark & {
          border-color: $dark-border-color;
          background: $dark-window-background;
          color: $dark-primary-text;
        }

        &:focus {
          outline: none;
          border-color: $system-blue;
          box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
        }

        &:hover {
          border-color: $system-blue;
        }
      }

      // Chapter Selection Container - Enhanced specificity
      .form-group .chapter-selection-container {
        border: 2px solid $border-color !important;
        border-radius: 0.75rem !important;
        background: $window-background !important;
        overflow: hidden !important;
        max-height: 280px !important;
        display: flex !important;
        flex-direction: column !important;

        .theme-dark & {
          border-color: $dark-border-color;
          background: $dark-window-background;
        }

        .chapter-selection-header {
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
          padding: 0.5rem 0.75rem !important;
          background: rgba($system-blue, 0.05) !important;
          border-bottom: 1px solid $border-color !important;
          flex-shrink: 0 !important;

          .theme-dark & {
            background: rgba($system-blue, 0.1);
            border-bottom-color: $dark-border-color;
          }

          .selection-controls {
            display: flex;
            gap: 0.375rem;

            .select-all-btn,
            .deselect-all-btn {
              padding: 0.25rem 0.5rem;
              border: 1px solid $border-color;
              border-radius: 0.25rem;
              background: $window-background;
              color: $secondary-text;
              font-size: 0.6875rem;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s ease;
              white-space: nowrap;

              .theme-dark & {
                border-color: $dark-border-color;
                background: $dark-window-background;
                color: $dark-secondary-text;
              }

              &:hover:not(:disabled) {
                border-color: $system-blue;
                color: $system-blue;
                background: rgba($system-blue, 0.05);
                transform: translateY(-1px);

                .theme-dark & {
                  background: rgba($system-blue, 0.1);
                }
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }
            }
          }

          .selection-count {
            font-size: 0.6875rem;
            font-weight: 600;
            color: $system-blue;
            background: rgba($system-blue, 0.1);
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;

            .theme-dark & {
              color: lighten($system-blue, 10%);
              background: rgba($system-blue, 0.2);
            }
          }
        }

        .chapters-list {
          flex: 1 !important;
          overflow-y: auto !important;
          padding: 0.25rem !important;
          min-height: 0 !important;

          // Custom iOS-like scrollbar
          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba($system-gray, 0.4);
            border-radius: 2px;
            transition: background 0.2s ease;

            .theme-dark & {
              background: rgba($dark-border-color, 0.6);
            }

            &:hover {
              background: rgba($system-gray, 0.6);

              .theme-dark & {
                background: rgba($dark-border-color, 0.8);
              }
            }
          }

          .chapter-checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.125rem;
            min-height: 2.5rem;

            &:last-child {
              margin-bottom: 0;
            }

            &:hover {
              background: rgba($system-blue, 0.08);
              transform: translateX(2px);

              .theme-dark & {
                background: rgba($system-blue, 0.12);
              }
            }

            input[type="checkbox"] {
              width: 0.875rem;
              height: 0.875rem;
              border: 1.5px solid $border-color;
              border-radius: 0.1875rem;
              background: $window-background;
              cursor: pointer;
              position: relative;
              margin: 0;
              appearance: none;
              transition: all 0.2s ease;
              flex-shrink: 0;

              .theme-dark & {
                border-color: $dark-border-color;
                background: $dark-window-background;
              }

              &:checked {
                background: $system-blue;
                border-color: $system-blue;
                transform: scale(1.1);

                &::after {
                  content: '✓';
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  color: white;
                  font-size: 0.5625rem;
                  font-weight: 700;
                  line-height: 1;
                }
              }

              &:focus {
                outline: none;
                box-shadow: 0 0 0 2px rgba($system-blue, 0.3);
              }
            }

            .chapter-info {
              flex: 1;
              min-width: 0;

              .chapter-title {
                font-size: 0.8125rem;
                font-weight: 500;
                color: $primary-text;
                line-height: 1.3;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                .theme-dark & {
                  color: $dark-primary-text;
                }
              }
            }
          }
        }

        .no-chapters-message {
          padding: 1.5rem;
          text-align: center;
          color: $secondary-text;
          font-size: 0.8125rem;
          font-style: italic;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .form-row {
      display: flex !important;
      flex-direction: column;
      gap: 1.5rem;

      @media (max-width: 768px) {
        grid-template-rows: 1fr;
      }
    }

    .info-section {
      background: rgba($system-blue, 0.05);
      border: 1px solid rgba($system-blue, 0.1);
      border-radius: 0.75rem;
      padding: 1rem;
      margin-top: 1rem;

      .theme-dark & {
        background: rgba($system-blue, 0.1);
        border-color: rgba($system-blue, 0.2);
      }

      .info-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 0.75rem;

        &:last-child {
          margin-bottom: 0;
        }

        svg {
          width: 1.125rem;
          height: 1.125rem;
          color: $system-blue;
          flex-shrink: 0;
          margin-top: 0.125rem;
        }

        span {
          font-size: 0.875rem;
          color: $secondary-text;
          line-height: 1.4;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid $border-color;

      .theme-dark & {
        border-top-color: $dark-border-color;
      }

      .cancel-button {
        padding: 0.75rem 1.5rem;
        border: 2px solid $border-color;
        background: $window-background;
        color: $secondary-text;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;

        .theme-dark & {
          border-color: $dark-border-color;
          background: $dark-window-background;
          color: $dark-secondary-text;
        }

        &:hover {
          border-color: $system-red;
          color: $system-red;
          background: rgba($system-red, 0.05);

          .theme-dark & {
            background: rgba($system-red, 0.1);
          }
        }
      }

      .generate-button {
        padding: 0.75rem 1.5rem;
        border: none;
        background: $system-blue;
        color: white;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 16px rgba($system-blue, 0.3);

        &:hover:not(:disabled) {
          background: darken($system-blue, 10%);
          transform: translateY(-1px);
          box-shadow: 0 6px 20px rgba($system-blue, 0.4);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        svg {
          width: 1rem;
          height: 1rem;
        }

        .loading-spinner {
          width: 1rem;
          height: 1rem;
          border: 2px solid rgba(white, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }
    }
  }

  // AI Quiz Preview Styling
  .ai-quiz-preview {
    padding: 2rem;
    background: rgba($system-green, 0.05);
    border-top: 1px solid $border-color;

    .theme-dark & {
      background: rgba($system-green, 0.1);
      border-top-color: $dark-border-color;
    }

    .preview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.5rem;

      h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: $primary-text;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .theme-dark & {
          color: $dark-primary-text;
        }

        &::before {
          content: '✨';
          font-size: 1rem;
        }
      }

      .quiz-meta {
        display: flex;
        gap: 0.75rem;
        align-items: center;

        .difficulty-badge {
          padding: 0.375rem 0.75rem;
          border-radius: 0.5rem;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;

          &.easy {
            background: rgba($system-green, 0.1);
            color: $system-green;
            border: 1px solid rgba($system-green, 0.2);
          }

          &.medium {
            background: rgba($system-orange, 0.1);
            color: $system-orange;
            border: 1px solid rgba($system-orange, 0.2);
          }

          &.hard {
            background: rgba($system-red, 0.1);
            color: $system-red;
            border: 1px solid rgba($system-red, 0.2);
          }
        }

        .question-count {
          padding: 0.375rem 0.75rem;
          background: rgba($system-blue, 0.1);
          color: $system-blue;
          border: 1px solid rgba($system-blue, 0.2);
          border-radius: 0.5rem;
          font-size: 0.75rem;
          font-weight: 600;
        }
      }
    }

    .preview-content {
      h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: $primary-text;

        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      p {
        margin: 0 0 1.5rem 0;
        color: $secondary-text;
        line-height: 1.5;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }

      .questions-preview {
        .question-preview {
          margin-bottom: 1.5rem;
          padding: 1rem;
          border: 1px solid $border-color;
          border-radius: 0.5rem;
          background-color: $window-background;

          .theme-dark & {
            border-color: $dark-border-color;
            background-color: $dark-window-background;
          }

          .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;

            strong {
              color: $primary-text;
              font-weight: 600;

              .theme-dark & {
                color: $dark-primary-text;
              }
            }

            .question-type {
              padding: 0.25rem 0.5rem;
              border-radius: 0.25rem;
              font-size: 0.75rem;
              font-weight: 500;
              text-transform: uppercase;
              letter-spacing: 0.025em;

              &.multiple_choice {
                background-color: rgba($system-blue, 0.1);
                color: $system-blue;
                border: 1px solid rgba($system-blue, 0.2);
              }

              &.short_answer {
                background-color: rgba($system-green, 0.1);
                color: $system-green;
                border: 1px solid rgba($system-green, 0.2);
              }
            }
          }

          .question-text {
            color: $secondary-text;
            margin-bottom: 0.75rem;
            line-height: 1.4;

            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .options-preview {
            .option-preview {
              padding: 0.375rem 0.75rem;
              margin-bottom: 0.25rem;
              background-color: rgba($system-light-gray, 0.3);
              border-radius: 0.375rem;
              font-size: 0.875rem;
              color: $secondary-text;

              .theme-dark & {
                background-color: rgba($dark-border-color, 0.3);
                color: $dark-secondary-text;
              }

              &:last-child {
                margin-bottom: 0;
              }
            }

            .more-options {
              padding: 0.375rem 0.75rem;
              font-size: 0.8125rem;
              color: $secondary-text;
              font-style: italic;
              opacity: 0.7;

              .theme-dark & {
                color: $dark-secondary-text;
              }
            }
          }
        }

        .more-questions {
          text-align: center;
          padding: 1rem;
          color: $secondary-text;
          font-style: italic;
          opacity: 0.7;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .preview-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid $border-color;

      .theme-dark & {
        border-top-color: $dark-border-color;
      }

      .cancel-button {
        padding: 0.75rem 1.5rem;
        border: 2px solid $border-color;
        background: $window-background;
        color: $secondary-text;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;

        .theme-dark & {
          border-color: $dark-border-color;
          background: $dark-window-background;
          color: $dark-secondary-text;
        }

        &:hover {
          border-color: $system-orange;
          color: $system-orange;
          background: rgba($system-orange, 0.05);

          .theme-dark & {
            background: rgba($system-orange, 0.1);
          }
        }
      }

      .generate-new-button {
        padding: 0.875rem 2rem;
        border: none;
        background: linear-gradient(135deg, $system-blue 0%, lighten($system-blue, 10%) 100%);
        color: white;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 16px rgba($system-blue, 0.3);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(white, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover {
          background: linear-gradient(135deg, lighten($system-blue, 5%) 0%, lighten($system-blue, 15%) 100%);
          transform: translateY(-1px);
          box-shadow: 0 6px 20px rgba($system-blue, 0.4);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(0);
        }

        svg {
          width: 1rem;
          height: 1rem;
          flex-shrink: 0;
        }
      }

      .edit-button {
        padding: 0.75rem 1.5rem;
        border: 2px solid $system-blue;
        background: rgba($system-blue, 0.05);
        color: $system-blue;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;

        .theme-dark & {
          background: rgba($system-blue, 0.1);
        }

        &:hover {
          background: rgba($system-blue, 0.1);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba($system-blue, 0.2);

          .theme-dark & {
            background: rgba($system-blue, 0.15);
          }
        }
      }

      .save-button {
        padding: 0.75rem 1.5rem;
        border: none;
        background: $system-green;
        color: white;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 4px 16px rgba($system-green, 0.3);

        &:hover {
          background: darken($system-green, 10%);
          transform: translateY(-1px);
          box-shadow: 0 6px 20px rgba($system-green, 0.4);
        }
      }
    }
  }
}

// Filter Bar
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
  padding: .5rem;
  border-radius: 1rem;
  margin-bottom: 2rem;
  background: $secondary-background;
  border: 1px solid $border-color;

  .theme-dark & {
    background: $dark-secondary-background;
    border-color: $dark-border-color;
  }

  .search-section {
    flex: 1;
    position: relative;

    .search-icon {
      position: absolute;
      left: 1rem;
      top: 50%;
      transform: translateY(-50%);
      width: 1rem;
      height: 1rem;
      color: $tertiary-text;

      .theme-dark & {
        color: $dark-tertiary-text;
      }
    }

    .search-input {
      width: 100%;
      padding: 0.875rem 1rem 0.875rem 2.75rem;
      border: 1px solid $border-color;
      border-radius: 0.75rem;
      font-size: 0.875rem;
      transition: all 0.2s ease;
      background: $window-background;
      color: $primary-text;

      .theme-dark & {
        background: $dark-window-background;
        color: $dark-primary-text;
        border-color: $dark-border-color;
      }

      &::placeholder {
        color: $placeholder-text;

        .theme-dark & {
          color: $dark-placeholder-text;
        }
      }

      &:focus {
        outline: none;
        border-color: $system-blue;
        box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
      }
    }
  }

  .filter-controls {
    display: flex;
    gap: 1rem;
    align-items: center;

    .filter-select {
      padding: 0.75rem 1rem;
      border: 1px solid $border-color;
      border-radius: 0.75rem;
      background: $window-background;
      color: $primary-text;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
      appearance: none;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 0.75rem center;
      background-repeat: no-repeat;
      background-size: 1rem;
      padding-right: 2.5rem;

      .theme-dark & {
        border-color: $dark-border-color;
        background: $dark-window-background;
        color: $dark-primary-text;
      }

      &:focus {
        outline: none;
        border-color: $system-blue;
        box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
      }
    }
  }
}

// Quiz Grid - Enhanced Layout
.quiz-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(22rem, 1fr));
  gap: .5rem;
  //margin-bottom: 1rem;
  position: relative;

  // Staggered animation for cards
  .quiz-card {
    animation: slideInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);

    @for $i from 1 through 12 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }

  // Grid layout adjustments for different screen sizes
  @media (min-width: 1400px) {
    grid-template-columns: repeat(auto-fit, minmax(24rem, 1fr));
    gap: 1rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
    gap: 1rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  // Empty grid state
  &:empty::after {
    content: 'No quizzes to display';
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: $tertiary-text;
    font-style: italic;

    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }
}

// Slide in animation
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Quiz Card - Premium Design
.quiz-card {
  background: $window-background;
  border: 1px solid $border-color;
  border-radius: 1.25rem;
  padding: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);

  .theme-dark & {
    background: $dark-window-background;
    border-color: $dark-border-color;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  }

  // Subtle gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba($system-blue, 0.02) 0%, rgba($system-green, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;

    .theme-dark & {
      background: linear-gradient(135deg, rgba($system-blue, 0.05) 0%, rgba($system-green, 0.05) 100%);
    }
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: $system-blue;

    .theme-dark & {
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    &::before {
      opacity: 1;
    }

    .quiz-card-header .quiz-meta .difficulty-badge {
      transform: scale(1.05);
    }

    .start-quiz-btn {
      background: linear-gradient(135deg, $system-blue 0%, lighten($system-blue, 10%) 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba($system-blue, 0.4);
    }
  }

  // Active/Focus states
  &:active {
    transform: translateY(-4px) scale(1.01);
  }

  &:focus-within {
    outline: none;
    border-color: $system-blue;
    box-shadow: 0 0 0 3px rgba($system-blue, 0.2);
  }
}

.quiz-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;

  .quiz-meta {
    display: flex;
    gap: 0.5rem;

    .difficulty-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-size: 0.6875rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.05em;

      &.easy {
        background: rgba($system-green, 0.1);
        color: $system-green;
      }

      &.medium {
        background: rgba($system-orange, 0.1);
        color: $system-orange;
      }

      &.hard {
        background: rgba($system-red, 0.1);
        color: $system-red;
      }
    }

    .time-badge {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-size: 0.6875rem;
      font-weight: 600;
      background: rgba($system-blue, 0.1);
      color: $system-blue;

      svg {
        width: 0.75rem;
        height: 0.75rem;
      }
    }

    .status-badge {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-size: 0.6875rem;
      font-weight: 600;
      transition: all 0.2s ease;

      &.completed {
        background: rgba($system-green, 0.1);
        color: $system-green;
        border: 1px solid rgba($system-green, 0.2);

        .theme-dark & {
          background: rgba($system-green, 0.15);
          border-color: rgba($system-green, 0.3);
        }

        .material-icons {
          font-size: 0.875rem;
          line-height: 1;
        }

        &:hover {
          background: rgba($system-green, 0.15);
          transform: scale(1.05);

          .theme-dark & {
            background: rgba($system-green, 0.2);
          }
        }
      }
    }
  }

  .quiz-actions,
  .quiz-actions-menu {
    display: flex;
    gap: 0.5rem;

    .action-btn,
    .menu-button {
      width: 2rem;
      height: 2rem;
      border: none;
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      background: rgba($system-light-gray, 0.3);
      color: $secondary-text;

      .theme-dark & {
        background: rgba($dark-border-color, 0.3);
        color: $dark-secondary-text;
      }

      &:hover {
        background: rgba($system-blue, 0.1);
        color: $system-blue;
        transform: scale(1.05);
      }

      &.delete:hover,
      &.delete-btn:hover {
        background: rgba($system-red, 0.1);
        color: $system-red;
      }

      svg {
        width: 1rem;
        height: 1rem;
      }

      .material-icons {
        font-size: 1rem;
        line-height: 1;
      }
    }
  }
}

.quiz-content {


  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: $primary-text;
    line-height: 1.3;

    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  p {
    margin: 0 0 1rem 0;
    color: $secondary-text;
    line-height: 1.4;
    font-size: 0.875rem;

    .theme-dark & {
      color: $dark-secondary-text;
    }
  }

  .quiz-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      font-size: 0.8125rem;
      color: $tertiary-text;

      .theme-dark & {
        color: $dark-tertiary-text;
      }

      svg {
        width: 0.875rem;
        height: 0.875rem;
      }
    }
  }
}

.quiz-card-footer {
  .start-quiz-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0.75rem;
    background: $system-blue;
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:hover {
      background: darken($system-blue, 10%);
      transform: translateY(-1px);
    }

    svg {
      width: 1rem;
      height: 1rem;
    }
  }
}

// Empty State - Enhanced Design
.empty-state {
  text-align: center;
  padding: .5rem !important;
  color: $secondary-text;
  position: relative;
  background: rgba($secondary-background, 0.5);
  border-radius: 1.5rem;
  border: 2px dashed $border-color;
  margin: 1rem 0;
  display: flex;
  justify-content: center;
  flex-direction: column;

  .theme-dark & {
    color: $dark-secondary-text;
    background: rgba($dark-secondary-background, 0.5);
    border-color: $dark-border-color;
  }

  // Animated background pattern
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 50%, rgba($system-blue, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($system-green, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba($system-orange, 0.05) 0%, transparent 50%);
    border-radius: inherit;
    animation: float 6s ease-in-out infinite;
    pointer-events: none;

    .theme-dark & {
      background-image: radial-gradient(circle at 20% 50%, rgba($system-blue, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba($system-green, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba($system-orange, 0.1) 0%, transparent 50%);
    }
  }

  .empty-icon {
    width: 5rem;
    height: 5rem;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, rgba($system-blue, 0.1) 0%, rgba($system-green, 0.1) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
    animation: pulse 2s ease-in-out infinite;

    .theme-dark & {
      background: linear-gradient(135deg, rgba($system-blue, 0.2) 0%, rgba($system-green, 0.2) 100%);
    }

    svg {
      width: 2.5rem;
      height: 2.5rem;
      color: $system-blue;
    }

    // Floating particles effect
    &::after {
      content: '✨';
      position: absolute;
      top: -0.5rem;
      right: -0.5rem;
      font-size: 1.5rem;
      animation: sparkle 3s ease-in-out infinite;
    }
  }

  h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: $primary-text;
    position: relative;
    z-index: 1;

    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  p {
    margin: 0 auto !important;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    line-height: 1.6;
    //max-width: 28rem;
    width: 100%;
    position: relative;
    z-index: 1;

  }

  // Enhanced CTA button
  .control-button.primary {
    background: linear-gradient(135deg, $system-blue 0%, lighten($system-blue, 10%) 100%);
    border: none;
    width: fit-content;
    padding: 1rem;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    color: white;
    box-shadow: 0 8px 20px rgba($system-blue, 0.3);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 1rem auto 0 auto;

    svg {
      width: 5rem;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 30px rgba($system-blue, 0.4);
      background: linear-gradient(135deg, lighten($system-blue, 5%) 0%, lighten($system-blue, 15%) 100%);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// Animations for empty state
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes sparkle {

  0%,
  100% {
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }

  50% {
    opacity: 1;
    transform: rotate(180deg) scale(1.2);
  }
}

// Quiz Player
.quiz-player {
  max-width: 50rem;
  margin: 0 auto;

  .quiz-container {
    padding: 2rem;
    border-radius: 1rem;
    background: $window-background;
    border: 1px solid $border-color;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .theme-dark & {
      background: $dark-window-background;
      border-color: $dark-border-color;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
  }

  .quiz-header {
    margin-bottom: 2rem !important;

    .quiz-title-section {
      margin-bottom: 1.5rem;

      h2 {
        margin: 0 0 0.5rem 0;
        font-size: 1.75rem;
        font-weight: 600;
        color: $primary-text;

        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .quiz-description {
        margin: 0;
        color: $secondary-text;
        font-size: 1rem;
        line-height: 1.5;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    .quiz-progress-section {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .progress-info {
        flex: 1;

        .question-counter {
          font-size: 0.875rem;
          font-weight: 600;
          color: $secondary-text;
          margin-bottom: 0.5rem;
          display: block;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }

        .progress-bar {
          width: 100%;
          height: 0.5rem;
          border-radius: 0.25rem;
          overflow: hidden;
          background: $system-light-gray;

          .theme-dark & {
            background: $dark-border-color;
          }

          .progress-fill {
            height: 100%;
            background: $system-blue;
            transition: width 0.3s ease;
          }
        }
      }

      .timer {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        margin-left: 1rem;
        background: rgba($system-blue, 0.1);
        color: $system-blue;

        &.warning {
          background: rgba($system-orange, 0.1);
          color: $system-orange;
        }

        &.danger {
          background: rgba($system-red, 0.1);
          color: $system-red;
          animation: pulse 1s infinite;
        }

        svg {
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }

  .question-container {
    margin-bottom: 2rem;

    .question-text {
      font-size: 1.25rem;
      font-weight: 500;
      line-height: 1.5;
      margin-bottom: 2rem;
      color: $primary-text;

      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .options-container {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .option {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: .5rem;
        border: 2px solid $border-color;
        border-radius: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease;
        background: $window-background;

        .theme-dark & {
          border-color: $dark-border-color;
          background: $dark-window-background;
        }

        &:hover {
          border-color: $system-blue;
          background: rgba($system-blue, 0.05);

          .theme-dark & {
            background: rgba($system-blue, 0.1);
          }
        }

        &.selected {
          border-color: $system-blue;
          background: rgba($system-blue, 0.1);

          .theme-dark & {
            background: rgba($system-blue, 0.15);
          }

          .option-letter {
            background: $system-blue;
            color: white;
          }
        }

        .option-letter {
          width: 2.5rem;
          height: 2.5rem;
          border-radius: 50%;
          background: $secondary-background;
          color: $secondary-text;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1rem;
          flex-shrink: 0;

          .theme-dark & {
            background: $dark-secondary-background;
            color: $dark-secondary-text;
          }
        }

        .option-text {
          flex: 1;
          font-size: 1rem;
          line-height: 1.4;
          color: $primary-text;

          .theme-dark & {
            color: $dark-primary-text;
          }
        }
      }
    }

    .short-answer-container {
      margin-top: 1.5rem;

      .short-answer-input {
        width: 100%;
        min-height: 6rem;
        padding: 1rem;
        border: 2px solid $border-color;
        border-radius: 0.75rem;
        font-size: 1rem;
        font-family: inherit;
        line-height: 1.5;
        resize: vertical;
        transition: all 0.2s ease;
        background-color: $window-background;
        color: $primary-text;

        .theme-dark & {
          border-color: $dark-border-color;
          background-color: $dark-window-background;
          color: $dark-primary-text;
        }

        &:focus {
          outline: none;
          border-color: $system-blue;
          box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
        }

        &::placeholder {
          color: $placeholder-text;

          .theme-dark & {
            color: $dark-placeholder-text;
          }
        }
      }

      .answer-hint {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        margin-top: 0.75rem;
        padding: 0.75rem;
        background-color: rgba($system-blue, 0.05);
        border: 1px solid rgba($system-blue, 0.2);
        border-radius: 0.5rem;

        .theme-dark & {
          background-color: rgba($system-blue, 0.1);
          border-color: rgba($system-blue, 0.3);
        }

        svg {
          width: 1rem;
          height: 1rem;
          color: $system-blue;
          flex-shrink: 0;
          margin-top: 0.125rem;
        }

        span {
          font-size: 0.875rem;
          color: $secondary-text;
          line-height: 1.4;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }
  }

  .navigation {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid $border-color;

    .theme-dark & {
      border-top-color: $dark-border-color;
    }

    .nav-button {
      padding: 0.875rem 1.75rem;
      border: 2px solid $border-color;
      border-radius: 0.5rem;
      background: $window-background;
      color: $secondary-text;
      font-size: 0.9375rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      position: relative;
      overflow: hidden;
      min-width: 7rem;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

      .theme-dark & {
        border-color: $dark-border-color;
        background: $dark-window-background;
        color: $dark-secondary-text;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }

      // Subtle gradient overlay
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba($system-blue, 0.02) 0%, rgba($system-green, 0.02) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;

        .theme-dark & {
          background: linear-gradient(135deg, rgba($system-blue, 0.05) 0%, rgba($system-green, 0.05) 100%);
        }
      }

      &:hover:not(:disabled) {
        border-color: $system-blue;
        //color: $system-blue;
        background: rgba($system-blue, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba($system-blue, 0.15);

        .theme-dark & {
          background: rgba($system-blue, 0.1);
          box-shadow: 0 8px 20px rgba($system-blue, 0.25);
        }

        &::before {
          opacity: 1;
        }

        .material-icons {
          transform: scale(1.1);
        }
      }

      &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 4px 12px rgba($system-blue, 0.2);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .theme-dark & {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .material-icons {
        font-size: 1.125rem;
        line-height: 1;
        transition: transform 0.2s ease;
      }

      &.prev-button {
        .material-icons {
          margin-right: 0.25rem;
        }
      }

      &.next-button {
        background: linear-gradient(135deg, $system-blue 0%, lighten($system-blue, 5%) 100%);
        color: white;
        border-color: $system-blue;
        box-shadow: 0 4px 16px rgba($system-blue, 0.3);

        &::before {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
          opacity: 1;
        }

        .material-icons {
          margin-left: 0.25rem;
        }

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, darken($system-blue, 5%) 0%, $system-blue 100%);
          border-color: darken($system-blue, 5%);
          transform: translateY(-3px);
          box-shadow: 0 12px 24px rgba($system-blue, 0.4);

          &::before {
            opacity: 1;
          }
        }

        &:active:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 8px 16px rgba($system-blue, 0.3);
        }

        &.disabled {
          background: $system-light-gray;
          color: $secondary-text;
          border-color: $border-color;
          cursor: not-allowed;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

          .theme-dark & {
            background: $dark-border-color;
            color: $dark-secondary-text;
            border-color: $dark-border-color;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &::before {
            opacity: 0;
          }

          &:hover {
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

            .theme-dark & {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }
}

// Quiz Summary Section
.quiz-summary {
  background: $window-background;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.5s ease-out;
  max-width: 50rem;
  margin: 0 auto;
  border: 1px solid $border-color;

  .theme-dark & {
    background: $dark-window-background;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    border-color: $dark-border-color;
  }

  .summary-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid $border-color;

    .theme-dark & {
      border-bottom-color: $dark-border-color;
    }

    h2 {
      margin: 0 0 0.5rem 0;
      font-size: 1.75rem;
      font-weight: 700;
      color: $primary-text;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;

      .theme-dark & {
        color: $dark-primary-text;
      }

      &::before {
        content: '📋';
        font-size: 1.5rem;
      }
    }

    .quiz-name {
      margin: 0;
      font-size: 1rem;
      color: $secondary-text;
      font-weight: 500;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  .summary-content {
    .summary-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
      margin-bottom: 2rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .stat-item {
        background: $secondary-background;
        border: 1px solid $border-color;
        border-radius: 0.75rem;
        padding: 1.25rem;
        text-align: center;
        transition: all 0.2s ease;

        .theme-dark & {
          background: $dark-secondary-background;
          border-color: $dark-border-color;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

          .theme-dark & {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
          }
        }

        .stat-value {
          font-size: 2rem;
          font-weight: 700;
          color: $system-blue;
          margin-bottom: 0.25rem;
          line-height: 1;
        }

        .stat-label {
          font-size: 0.875rem;
          color: $secondary-text;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .questions-overview {
      h3 {
        margin: 0 0 1rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: $primary-text;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .theme-dark & {
          color: $dark-primary-text;
        }

        &::before {
          content: '📝';
          font-size: 1rem;
        }
      }

      .questions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(4rem, 1fr));
        gap: 0.75rem;
        margin-bottom: 1.5rem;

        @media (max-width: 768px) {
          grid-template-columns: repeat(auto-fill, minmax(3.5rem, 1fr));
          gap: 0.5rem;
        }

        .question-overview-item {
          aspect-ratio: 1;
          background: $window-background;
          border: 2px solid $border-color;
          border-radius: 0.75rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;

          .theme-dark & {
            background: $dark-window-background;
            border-color: $dark-border-color;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

            .theme-dark & {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
          }

          &.answered {
            border-color: $system-green;
            background: rgba($system-green, 0.05);

            .theme-dark & {
              background: rgba($system-green, 0.1);
            }

            .question-number {
              color: $system-green;
            }

            .question-status svg {
              color: $system-green;
            }

            &:hover {
              border-color: darken($system-green, 10%);
              background: rgba($system-green, 0.1);

              .theme-dark & {
                background: rgba($system-green, 0.15);
              }
            }
          }

          &.unanswered {
            border-color: $system-orange;
            background: rgba($system-orange, 0.05);

            .theme-dark & {
              background: rgba($system-orange, 0.1);
            }

            .question-number {
              color: $system-orange;
            }

            .question-status svg {
              color: $system-orange;
            }

            &:hover {
              border-color: darken($system-orange, 10%);
              background: rgba($system-orange, 0.1);

              .theme-dark & {
                background: rgba($system-orange, 0.15);
              }
            }
          }

          .question-number {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            line-height: 1;
          }

          .question-status {
            svg {
              width: 1rem;
              height: 1rem;
            }
          }
        }
      }
    }

    .warning-message {
      background: rgba($system-orange, 0.1);
      border: 1px solid rgba($system-orange, 0.3);
      border-radius: 0.75rem;
      padding: 1rem;
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      margin-bottom: 1.5rem;

      .theme-dark & {
        background: rgba($system-orange, 0.15);
        border-color: rgba($system-orange, 0.4);
      }

      svg {
        width: 1.25rem;
        height: 1.25rem;
        color: $system-orange;
        flex-shrink: 0;
        margin-top: 0.125rem;
      }

      span {
        font-size: 0.875rem;
        color: $primary-text;
        line-height: 1.4;

        .theme-dark & {
          color: $dark-primary-text;
        }
      }
    }
  }

  .summary-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 1.5rem;
    border-top: 1px solid $border-color;

    .theme-dark & {
      border-top-color: $dark-border-color;
    }

    @media (max-width: 768px) {
      flex-direction: column;
    }

    .review-button {
      padding: 0.875rem 1.75rem;
      border: 2px solid $system-blue;
      background: $window-background;
      color: $system-blue;
      border-radius: 0.75rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .theme-dark & {
        background: $dark-window-background;
      }

      &:hover {
        background: rgba($system-blue, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba($system-blue, 0.2);

        .theme-dark & {
          background: rgba($system-blue, 0.1);
        }
      }

      &::before {
        content: '👁️';
        font-size: 1rem;
      }
    }

    .submit-button {
      padding: 0.875rem 2rem;
      border: none;
      background: $system-green;
      color: white;
      border-radius: 0.75rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      box-shadow: 0 4px 16px rgba($system-green, 0.3);

      &:hover {
        background: darken($system-green, 10%);
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba($system-green, 0.4);
      }

      &::before {
        content: '✅';
        font-size: 1rem;
      }
    }
  }
}

// Quiz Results
.quiz-results {
  max-width: 50rem;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 1rem;
  background: $window-background;
  border: 1px solid $border-color;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  .theme-dark & {
    background: $dark-window-background;
    border-color: $dark-border-color;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid $border-color;

    .theme-dark & {
      border-bottom-color: $dark-border-color;
    }

    .results-title {
      h2 {
        margin: 0 0 0.5rem 0;
        font-size: 1.75rem;
        font-weight: 600;
        color: $primary-text;

        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .quiz-name {
        margin: 0;
        font-size: 1rem;
        color: $secondary-text;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    .score-display {
      text-align: center;
      padding: 1.5rem;
      border-radius: 1rem;
      min-width: 8rem;

      &.good {
        background: rgba($system-green, 0.1);
        border: 2px solid rgba($system-green, 0.2);

        .score-value {
          color: $system-green;
        }
      }

      &.bad {
        background: rgba($system-red, 0.1);
        border: 2px solid rgba($system-red, 0.2);

        .score-value {
          color: $system-red;
        }
      }

      .score-value {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        line-height: 1;
      }

      .score-label {
        font-size: 0.875rem;
        color: $secondary-text;
        margin-top: 0.5rem;
        font-weight: 500;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }

  .results-details {
    .summary {
      margin-bottom: 2rem;
      padding: 1.5rem;
      background: $secondary-background;
      border-radius: 0.75rem;
      border: 1px solid $border-color;

      .theme-dark & {
        background: $dark-secondary-background;
        border-color: $dark-border-color;
      }

      p {
        margin: 0;
        font-size: 1rem;
        color: $primary-text;
        line-height: 1.5;

        .theme-dark & {
          color: $dark-primary-text;
        }
      }
    }

    .answers-review {
      .question-review {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        border-radius: 0.75rem;
        border: 1px solid $border-color;
        background: $window-background;

        .theme-dark & {
          border-color: $dark-border-color;
          background: $dark-window-background;
        }

        &.correct {
          border-color: rgba($system-green, 0.3);
          background: rgba($system-green, 0.05);

          .theme-dark & {
            background: rgba($system-green, 0.1);
          }
        }

        &.incorrect {
          border-color: rgba($system-red, 0.3);
          background: rgba($system-red, 0.05);

          .theme-dark & {
            background: rgba($system-red, 0.1);
          }
        }

        .question-text {
          margin-bottom: 1rem;
          font-size: 1rem;
          color: $primary-text;
          line-height: 1.5;

          .theme-dark & {
            color: $dark-primary-text;
          }

          strong {
            color: $system-blue;
          }
        }

        .user-answer {
          margin-bottom: 0.75rem;
          font-size: 0.875rem;
          color: $secondary-text;

          .theme-dark & {
            color: $dark-secondary-text;
          }

          strong {
            color: $primary-text;

            .theme-dark & {
              color: $dark-primary-text;
            }
          }
        }

        .correct-answer {
          margin-bottom: 0.75rem;
          font-size: 0.875rem;
          color: $secondary-text;

          .theme-dark & {
            color: $dark-secondary-text;
          }

          strong {
            color: $system-green;
          }
        }

        .explanation {
          padding: 1rem;
          background: rgba($system-blue, 0.05);
          border: 1px solid rgba($system-blue, 0.1);
          border-radius: 0.5rem;
          font-size: 0.875rem;
          color: $secondary-text;
          line-height: 1.4;

          .theme-dark & {
            background: rgba($system-blue, 0.1);
            border-color: rgba($system-blue, 0.2);
            color: $dark-secondary-text;
          }

          strong {
            color: $system-blue;
          }
        }
      }
    }
  }

  .results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid $border-color;

    .theme-dark & {
      border-top-color: $dark-border-color;
    }

    .restart-button {
      padding: 0.75rem 1.5rem;
      border: 2px solid $system-blue;
      background: rgba($system-blue, 0.05);
      color: $system-blue;
      border-radius: 0.75rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      .theme-dark & {
        background: rgba($system-blue, 0.1);
      }

      &:hover {
        background: rgba($system-blue, 0.1);
        transform: translateY(-1px);

        .theme-dark & {
          background: rgba($system-blue, 0.15);
        }
      }
    }

    .back-button {
      padding: 0.75rem 1.5rem;
      border: 2px solid $border-color;
      background: $window-background;
      color: $secondary-text;
      border-radius: 0.75rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      .theme-dark & {
        border-color: $dark-border-color;
        background: $dark-window-background;
        color: $dark-secondary-text;
      }

      &:hover {
        border-color: $system-gray;
        color: $system-gray;
        background: rgba($system-gray, 0.05);

        .theme-dark & {
          background: rgba($system-gray, 0.1);
        }
      }
    }
  }
}

// Animations
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Message Banners
.message-banner {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  min-width: 20rem;
  max-width: 90vw;

  .theme-dark & {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  &.error {
    background: rgba($system-red, 0.1);
    border: 1px solid rgba($system-red, 0.3);
    color: $system-red;

    .theme-dark & {
      background: rgba($system-red, 0.15);
      border-color: rgba($system-red, 0.4);
    }
  }

  &.success {
    background: rgba($system-green, 0.1);
    border: 1px solid rgba($system-green, 0.3);
    color: $system-green;

    .theme-dark & {
      background: rgba($system-green, 0.15);
      border-color: rgba($system-green, 0.4);
    }
  }

  .message-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;

    svg {
      width: 1.25rem;
      height: 1.25rem;
      flex-shrink: 0;
    }

    span {
      font-size: 0.875rem;
      font-weight: 500;
      line-height: 1.4;
    }
  }

  .close-message {
    width: 2rem;
    height: 2rem;
    border: none;
    background: transparent;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 1rem;
    color: currentColor;
    opacity: 0.7;

    &:hover {
      opacity: 1;
      background: rgba($system-gray, 0.1);

      .theme-dark & {
        background: rgba($dark-tertiary-text, 0.1);
      }
    }

    svg {
      width: 1rem;
      height: 1rem;
    }
  }
}

// Search Input Container
.search-input-container {
  position: relative;
  flex: 1;

  .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    color: $tertiary-text;
    z-index: 1;

    .theme-dark & {
      color: $dark-tertiary-text;
    }
  }

  .search-input {
    width: 100%;
    padding: 0.875rem 3rem 0.875rem 2.75rem;
    border: 1px solid $border-color;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: $window-background;
    color: $primary-text;

    .theme-dark & {
      background: $dark-window-background;
      color: $dark-primary-text;
      border-color: $dark-border-color;
    }

    &::placeholder {
      color: $placeholder-text;

      .theme-dark & {
        color: $dark-placeholder-text;
      }
    }

    &:focus {
      outline: none;
      border-color: $system-blue;
      box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
    }
  }

  .clear-search {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.5rem;
    height: 1.5rem;
    border: none;
    background: transparent;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: $tertiary-text;

    .theme-dark & {
      color: $dark-tertiary-text;
    }

    &:hover {
      background: rgba($system-gray, 0.1);
      color: $system-gray;

      .theme-dark & {
        background: rgba($dark-tertiary-text, 0.1);
      }
    }

    svg {
      width: 0.875rem;
      height: 0.875rem;
    }
  }
}

// Create Quiz Section
.create-quiz-section {
  .create-quiz-btn {
    white-space: nowrap;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    svg {
      width: 1rem;
      height: 1rem;
    }
  }
}

// Quiz List Container - Enhanced Styling
.quiz-list-container {
  position: relative;
  padding: 1rem;
  background: $window-background;
  border-radius: 1rem;
  border: 1px solid $border-color;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;

  .theme-dark & {
    background: $dark-window-background;
    border-color: $dark-border-color;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  // Enhanced header with gradient background


  h2 {
    margin: 0 0 0 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: $primary-text;
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;

    .theme-dark & {
      color: $dark-primary-text;
    }

    // Add icon before title
    &::before {
      content: '📚';
      font-size: 1.5rem;
      display: inline-block;
    }

    // Quiz count styling
    .quiz-count {
      background: rgba($system-blue, 0.1);
      color: $system-blue;
      padding: 0.25rem 0.75rem;
      border-radius: 1rem;
      font-size: 0.875rem;
      font-weight: 600;
      margin-left: auto;

      .theme-dark & {
        background: rgba($system-blue, 0.2);
      }
    }
  }

  // Loading state
  &.loading {
    .quiz-grid {
      opacity: 0.5;
      pointer-events: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 2rem;
      height: 2rem;
      border: 3px solid rgba($system-blue, 0.3);
      border-top: 3px solid $system-blue;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

// Quiz Card Header
.quiz-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;

  .quiz-actions-menu {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: all 0.2s ease;

    .quiz-card:hover & {
      opacity: 1;
    }

    .menu-button {
      width: 2rem;
      height: 2rem;
      border: none;
      border-radius: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      background: rgba($system-light-gray, 0.3);
      color: $secondary-text;

      .theme-dark & {
        background: rgba($dark-border-color, 0.3);
        color: $dark-secondary-text;
      }

      &:hover {
        background: rgba($system-blue, 0.1);
        color: $system-blue;
        transform: scale(1.05);
      }

      &.delete:hover {
        background: rgba($system-red, 0.1);
        color: $system-red;
      }

      svg {
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// Quiz Content
.quiz-content {


  .quiz-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: $primary-text;
    line-height: 1.3;
    text-align: left;

    .theme-dark & {
      color: $dark-primary-text;
    }
  }

  .quiz-description {
    margin: 0 0 1rem 0;
    color: $secondary-text;
    line-height: 1.4;
    font-size: 0.875rem;
    text-align: left;

    .theme-dark & {
      color: $dark-secondary-text;
    }
  }
}



// Additional responsive styles
@media (max-width: 768px) {
  .advanced-quizzes-app {
    padding: 1rem;
  }

  .quizzes-header {
    .header-content {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;

      .header-controls {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }

  .filter-bar {
    flex-direction: column;
    gap: 1rem;

    .filter-controls {
      flex-direction: column;
      width: 100%;

      .filter-select {
        width: 100%;
      }
    }

    .create-quiz-section {
      width: 100%;

      .create-quiz-btn {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .quiz-grid {
    grid-template-columns: 1fr;
    gap: .5rem;
  }

  .quiz-form-container {
    margin: 1rem 0;

    .form-header {
      padding: 1rem;

      .header-content {
        .ai-icon {
          width: 2.5rem;
          height: 2.5rem;

          svg {
            width: 1.25rem;
            height: 1.25rem;
          }
        }

        .header-text {
          h2 {
            font-size: 1.25rem;
          }

          p {
            font-size: 0.8125rem;
          }
        }
      }
    }

    .ai-generation-form {
      padding: 1rem;

      .form-row {
        grid-template-rows: 1fr;
      }

      .checkbox-group {
        flex-direction: column;

        .checkbox-item {
          min-width: auto;
          width: 100%;
        }
      }

      .form-actions {
        flex-direction: column;

        .cancel-button,
        .generate-button {
          width: 100%;
        }
      }
    }
  }

  .quiz-player {
    .quiz-container {
      padding: 1rem;
    }

    .quiz-header {
      .quiz-progress-section {
        flex-direction: column;
        gap: 1rem;

        .timer {
          margin-left: 0;
          align-self: flex-start;
        }
      }
    }

    .question-container {
      .question-text {
        font-size: 1.125rem;
      }

      .options-container {
        .option {
          padding: 0.5rem !important;
          margin: 0 !important;

          .option-letter {
            width: 2rem;
            height: 2rem;
            font-size: 0.875rem;
          }

          .option-text {
            font-size: 0.875rem;
          }
        }
      }
    }

    .navigation {
      flex-direction: column;
      gap: 0.75rem;

      .nav-button {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .quiz-results {
    padding: 1rem;

    .results-header {
      flex-direction: column;
      gap: 1rem;
      text-align: center;

      .score-display {
        min-width: auto;
        padding: 1rem;
      }
    }

    .results-actions {
      flex-direction: column;

      .restart-button,
      .back-button {
        width: 100%;
      }
    }
  }

  .message-banner {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    transform: none;
    min-width: auto;
    max-width: none;
  }
}

// Additional small screen optimizations
@media (max-width: 480px) {
  .advanced-quizzes-app {
    padding: 0.5rem;
  }

  .quiz-card {
    padding: 1rem;

    .quiz-card-header {
      .quiz-meta {
        flex-direction: column;
        gap: 0.25rem;
        align-items: flex-start;
      }
    }
  }

  .ai-quiz-preview {
    .preview-actions {
      flex-direction: column;

      .cancel-button,
      .edit-button,
      .save-button {
        width: 100%;
      }
    }
  }
}