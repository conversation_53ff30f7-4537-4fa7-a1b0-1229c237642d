const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'blueprint',
    password: 'Lukind@1956',
    port: 5432,
});

async function fixKanbanSchema() {
    try {
        console.log('Connecting to database...');
        
        // Check current schema
        const schema = await pool.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = 'public' 
            AND table_name = 'kanban_board'
            ORDER BY ordinal_position;
        `);
        
        console.log('\nCurrent Kanban Board Table Schema:');
        console.log('==================================');
        schema.rows.forEach(row => {
            console.log(`${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
        });
        
        const existingColumns = schema.rows.map(row => row.column_name);
        console.log('\nExisting columns:', existingColumns);
        
        // Check if we need to migrate
        const needsMigration = existingColumns.includes('task') && !existingColumns.includes('title');
        
        if (needsMigration) {
            console.log('\n🔧 Database needs migration. Starting migration...');
            
            // Add missing columns
            if (!existingColumns.includes('chapter_id')) {
                console.log('Adding chapter_id column...');
                await pool.query('ALTER TABLE kanban_board ADD COLUMN chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE');
            }
            
            if (!existingColumns.includes('title')) {
                console.log('Adding title column...');
                await pool.query('ALTER TABLE kanban_board ADD COLUMN title TEXT');
                // Migrate existing data
                await pool.query('UPDATE kanban_board SET title = task WHERE title IS NULL');
                await pool.query('ALTER TABLE kanban_board ALTER COLUMN title SET NOT NULL');
            }
            
            if (!existingColumns.includes('description')) {
                console.log('Adding description column...');
                await pool.query('ALTER TABLE kanban_board ADD COLUMN description TEXT');
            }
            
            if (!existingColumns.includes('priority')) {
                console.log('Adding priority column...');
                await pool.query(`ALTER TABLE kanban_board ADD COLUMN priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high'))`);
            }
            
            if (!existingColumns.includes('due_date')) {
                console.log('Adding due_date column...');
                await pool.query('ALTER TABLE kanban_board ADD COLUMN due_date DATE');
            }
            
            if (!existingColumns.includes('tags')) {
                console.log('Adding tags column...');
                await pool.query('ALTER TABLE kanban_board ADD COLUMN tags TEXT');
            }
            
            if (!existingColumns.includes('position')) {
                console.log('Adding position column...');
                await pool.query('ALTER TABLE kanban_board ADD COLUMN position INTEGER DEFAULT 0');
            }
            
            if (!existingColumns.includes('updated_at')) {
                console.log('Adding updated_at column...');
                await pool.query('ALTER TABLE kanban_board ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP');
            }
            
            // Update status column to use new values
            console.log('Updating status column...');
            await pool.query(`
                UPDATE kanban_board 
                SET status = CASE 
                    WHEN status = 'todo' THEN 'To Do'
                    WHEN status = 'in_progress' THEN 'In Progress'
                    WHEN status = 'done' THEN 'Done'
                    ELSE status
                END
            `);
            
            // Drop the old task column constraint and make it nullable
            console.log('Making task column nullable...');
            await pool.query('ALTER TABLE kanban_board ALTER COLUMN task DROP NOT NULL');
            
            console.log('\n✅ Migration completed successfully!');
            
        } else if (existingColumns.includes('title')) {
            console.log('\n✅ Database schema is already up to date!');
        } else {
            console.log('\n❌ Unexpected database state. Manual intervention may be required.');
        }
        
        // Show final schema
        const finalSchema = await pool.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = 'public' 
            AND table_name = 'kanban_board'
            ORDER BY ordinal_position;
        `);
        
        console.log('\nFinal Kanban Board Table Schema:');
        console.log('================================');
        finalSchema.rows.forEach(row => {
            console.log(`${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
        });
        
    } catch (error) {
        console.error('Error fixing schema:', error);
    } finally {
        await pool.end();
    }
}

fixKanbanSchema();
