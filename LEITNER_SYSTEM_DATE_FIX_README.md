# Leitner System Date Format Fix

This document explains how to fix the 500 Internal Server Error in the Leitner System component when saving flashcards.

## Problem

The Leitner System component is experiencing a 500 Internal Server Error when saving flashcards. The error occurs when:

1. A user clicks on the "Correct" or "Incorrect" button
2. The component tries to save the card to the database
3. The server returns a 500 Internal Server Error

## Root Cause

After investigation, we identified two main issues:

1. **Date Objects vs. ISO Strings**: The component is sending Date objects to the server, but the server expects ISO strings.
2. **Cards Not Marked as Modified**: The cards are not being properly marked as modified, which causes issues with the save logic.

## Solution

The solution is to:

1. Convert all Date objects to ISO strings before sending them to the server
2. Mark cards as modified when they are updated

## Implementation

We need to modify the following functions in the LeitnerSystem.jsx file:

### 1. handleCardReview Function

```jsx
// Update card review data
const now = new Date();
card.last_reviewed = now.toISOString(); // Convert to ISO string for server
card.review_count = (card.review_count || 0) + 1;
card.isModified = true; // Mark as modified so it will be saved
```

### 2. Date Objects in handleCardReview

For correct answers:
```jsx
// Calculate next review date based on box level
const nextReviewDays = fromBox === 1 ? 1 : (fromBox === 2 ? 3 : 7);
const nextReviewDate = new Date();
nextReviewDate.setDate(nextReviewDate.getDate() + nextReviewDays);
card.next_review_date = nextReviewDate.toISOString(); // Convert to ISO string for server
```

For mastered cards:
```jsx
// Set next review date for mastered cards (14 days)
const nextReviewDate = new Date();
nextReviewDate.setDate(nextReviewDate.getDate() + 14);
card.next_review_date = nextReviewDate.toISOString(); // Convert to ISO string for server
```

For incorrect answers:
```jsx
// Set next review date for tomorrow
const nextReviewDate = new Date();
nextReviewDate.setDate(nextReviewDate.getDate() + 1);
card.next_review_date = nextReviewDate.toISOString(); // Convert to ISO string for server
```

### 3. moveCard Function

```jsx
// Create a copy of the card and update its box_level
const card = {
  ...prevBoxes[fromBox][cardIndex],
  box_level: toBox,
  // Update the last_reviewed timestamp
  last_reviewed: prevBoxes[fromBox][cardIndex].last_reviewed || new Date().toISOString(),
  // Mark as modified so it will be saved
  isModified: true
};
```

## Expected Behavior After Fix

After applying these fixes:

1. When a user clicks on the "Correct" or "Incorrect" button, the card will be properly saved to the database
2. No more 500 Internal Server Error
3. The streak counter will update properly
4. Cards will move between boxes as expected

## Technical Details

The fix addresses two key issues:

1. **Date Format**: JavaScript Date objects need to be converted to ISO strings (e.g., "2023-06-15T12:30:45.678Z") before being sent to the server. This is done using the `toISOString()` method.

2. **Modification Tracking**: Cards need to be explicitly marked as modified by setting `isModified: true` when they are updated. This ensures that the save logic correctly identifies which cards need to be saved.

These changes ensure that the data sent to the server is in the correct format and that only modified cards are saved, which prevents unique constraint violations and other database errors.
