const { Pool } = require('pg');
require('dotenv').config();

// Create a PostgreSQL connection pool
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'blueprint',
    password: process.env.DB_PASSWORD || 'Lukind@1956',
    port: process.env.DB_PORT || 5432,
});

async function createLeitnerSystemTable() {
    try {
        // Check if the leitner_system table exists
        const tableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'leitner_system'
            );
        `);

        if (!tableCheck.rows[0].exists) {
            console.log('Leitner system table does not exist. Creating it...');
            
            // Create the leitner_system table
            await pool.query(`
                CREATE TABLE IF NOT EXISTS leitner_system (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    flashcard_id INTEGER REFERENCES flashcards(id) ON DELETE CASCADE,
                    box_level INTEGER NOT NULL DEFAULT 1,
                    last_reviewed TIMESTAMP,
                    next_review_date TIMESTAMP,
                    review_count INTEGER DEFAULT 0,
                    success_rate FLOAT DEFAULT 0,
                    streak INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT unique_user_flashcard UNIQUE (user_id, flashcard_id)
                );
            `);
            
            console.log('Leitner system table created successfully');
        } else {
            console.log('Leitner system table already exists');
        }

        // Create an index for faster queries
        await pool.query(`
            CREATE INDEX IF NOT EXISTS idx_leitner_user_chapter 
            ON leitner_system (user_id, chapter_id);
        `);
        
        console.log('Leitner system index created successfully');
        
        // Add a trigger to update the updated_at timestamp
        await pool.query(`
            CREATE OR REPLACE FUNCTION update_leitner_timestamp()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            
            DROP TRIGGER IF EXISTS update_leitner_timestamp ON leitner_system;
            
            CREATE TRIGGER update_leitner_timestamp
            BEFORE UPDATE ON leitner_system
            FOR EACH ROW
            EXECUTE FUNCTION update_leitner_timestamp();
        `);
        
        console.log('Leitner system timestamp trigger created successfully');
        
        console.log('All Leitner system database operations completed successfully');
    } catch (error) {
        console.error('Error creating Leitner system table:', error);
    } finally {
        // Close the pool
        await pool.end();
    }
}

// Run the function
createLeitnerSystemTable();
