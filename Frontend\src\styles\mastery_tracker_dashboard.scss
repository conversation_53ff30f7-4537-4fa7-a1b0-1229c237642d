@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// Mastery Tracker Card - Minimalistic Design
.mastery-card {
    width: 100%;
    height: 220px;
    padding: 1rem;
    cursor: pointer;
    @include transition(all);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    // Light theme
    background: $window-background;
    border: 1px solid rgba($border-color, 0.3);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    // Dark theme
    &.theme-dark {
        background: $dark-window-background;
        border: 1px solid rgba($dark-border-color, 0.3);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    }

    // Widget Header
    .widget-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;

        .widget-title {
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0;
            color: $secondary-text;
            letter-spacing: -0.1px;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .mastery-percentage {
            font-size: 1.25rem;
            font-weight: 700;
            color: $primary-text;
            background: rgba($system-blue, 0.08);
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            min-width: 2rem;
            text-align: center;

            .theme-dark & {
                color: $dark-primary-text;
                background: rgba($system-blue, 0.15);
            }
        }
    }

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

        &.theme-dark {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .mastery-gauge-mini {
            transform: scale(1.02);
        }
    }

    &:focus {
        outline: 2px solid $system-blue;
        outline-offset: 2px;
    }

    // Mastery Preview
    .mastery-preview {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;

        .mastery-gauge-mini {
            flex-shrink: 0;
            @include transition(all);

            .mini-gauge {
                width: 80px;
                height: 80px;

                .progress-circle {
                    @include transition(all);
                }

                .gauge-text {
                    font-size: 0.8rem;
                    font-weight: 700;
                    fill: $primary-text;

                    .theme-dark & {
                        fill: $dark-primary-text;
                    }
                }
            }
        }

        .stage-indicators {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex: 1;

            .stage-indicator {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .stage-dot {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    position: relative;

                    .stage-count {
                        font-size: 0.7rem;
                        font-weight: 700;
                        color: white;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    }
                }

                .stage-label {
                    font-size: 0.7rem;
                    font-weight: 600;
                    color: $tertiary-text;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }
            }
        }
    }

    // Mastery Summary
    .mastery-summary {
        display: flex;
        justify-content: space-between;
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid rgba($separator-color, 0.5);

        .theme-dark & {
            border-top-color: rgba($dark-separator-color, 0.5);
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.125rem;

            .summary-label {
                font-size: 0.7rem;
                font-weight: 500;
                color: $tertiary-text;
                text-transform: uppercase;
                letter-spacing: 0.5px;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }

            .summary-value {
                font-size: 0.9rem;
                font-weight: 600;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }
        }
    }

    // Responsive Design
    @media (max-width: 768px) {
        height: 200px;
        padding: 0.75rem;

        .widget-header {
            margin-bottom: 0.75rem;

            .widget-title {
                font-size: 0.85rem;
            }

            .mastery-percentage {
                font-size: 1.1rem;
                padding: 0.2rem 0.4rem;
            }
        }

        .mastery-preview {
            gap: 0.75rem;

            .mastery-gauge-mini {
                .mini-gauge {
                    width: 70px;
                    height: 70px;

                    .gauge-text {
                        font-size: 0.75rem;
                    }
                }
            }

            .stage-indicators {
                gap: 0.375rem;

                .stage-indicator {
                    gap: 0.375rem;

                    .stage-dot {
                        width: 18px;
                        height: 18px;

                        .stage-count {
                            font-size: 0.65rem;
                        }
                    }

                    .stage-label {
                        font-size: 0.65rem;
                    }
                }
            }
        }

        .mastery-summary {
            margin-top: 0.5rem;
            padding-top: 0.5rem;

            .summary-item {
                .summary-label {
                    font-size: 0.65rem;
                }

                .summary-value {
                    font-size: 0.8rem;
                }
            }
        }
    }
}

// Modal Overlay - macOS style
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: rgba(0, 0, 0, 0.5);

    // Dark theme
    .theme-dark & {
        background-color: rgba(0, 0, 0, 0.7);
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }
}

// Modal Window - macOS style
.modal-window {
    width: 1000px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    border-radius: 16px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: $window-background;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid $border-color;

    // Dark theme
    .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        border: 1px solid $dark-border-color;
    }

    @keyframes slideIn {
        from {
            transform: translateY(20px);
            opacity: 0;
        }

        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    // Enhanced Modal Header - macOS style
    .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        border-bottom: 1px solid $separator-color;
        background-color: rgba($window-background, 0.8);

        // Dark theme
        &.theme-dark {
            border-bottom: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
            justify-content: center;

            .header-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 8px;
                background: rgba($system-blue, 0.1);

                &.theme-dark {
                    background: rgba($system-blue, 0.2);
                }

                svg {
                    color: $system-blue;

                    .theme-dark & {
                        color: color.adjust($system-blue, $lightness: 10%);
                    }
                }
            }
        }

        .header-stats {
            display: flex;
            gap: 1rem;

            .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.125rem;

                .stat-number {
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: $system-blue;
                    line-height: 1;

                    .theme-dark & {
                        color: color.adjust($system-blue, $lightness: 10%);
                    }
                }

                .stat-label {
                    font-size: 0.7rem;
                    font-weight: 500;
                    color: $tertiary-text;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }
            }
        }

        .traffic-lights {
            display: flex;
            gap: 8px;
            margin-right: 16px;

            .traffic-light {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: none;
                cursor: pointer;
                @include transition(all);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                &.red {
                    background: $system-red;
                }

                &.yellow {
                    background: $system-orange;
                }

                &.green {
                    background: $system-green;
                }

                &:hover {
                    transform: scale(1.15);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
                }

                &:active {
                    transform: scale(0.95);
                }

                &:disabled {
                    opacity: 0.4;
                    cursor: default;
                }
            }
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
            letter-spacing: -0.2px;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }

    // Enhanced Modal Content
    .modal-content {
        padding: 24px;
        @include ios-autohide-scrollbar;

        // Dark mode specific styles handled by individual components

        // PCLHdM Explanation Section
        .pclhdm-explanation {
            margin-bottom: 2rem;

            .explanation-header {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1.5rem;
                padding: 1rem;
                border-radius: 12px;
                background: rgba($system-blue, 0.05);
                border: 1px solid rgba($system-blue, 0.1);

                &.theme-dark {
                    background: rgba($system-blue, 0.1);
                    border: 1px solid rgba($system-blue, 0.2);
                }

                .explanation-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 40px;
                    height: 40px;
                    border-radius: 10px;
                    background: rgba($system-blue, 0.1);

                    &.theme-dark {
                        background: rgba($system-blue, 0.2);
                    }

                    svg {
                        color: $system-blue;

                        &.theme-dark {
                            color: color.adjust($system-blue, $lightness: 10%);
                        }
                    }
                }

                .explanation-text {
                    flex: 1;

                    h3 {
                        font-size: 1.25rem;
                        font-weight: 600;
                        margin: 0 0 0.25rem 0;
                        color: $primary-text;

                        &.theme-dark {
                            color: $dark-primary-text;
                        }
                    }

                    p {
                        font-size: 0.9rem;
                        color: $secondary-text;
                        margin: 0;

                        &.theme-dark {
                            color: $dark-secondary-text;
                        }
                    }
                }
            }

            .stages-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 1rem;

                .stage-card {
                    background: $window-background;
                    border: 1px solid $border-color;
                    border-radius: 12px;
                    padding: 1rem;
                    @include transition(all);

                    &.theme-dark {
                        background: $dark-window-background;
                        border: 1px solid $dark-border-color;
                    }

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

                        &.theme-dark {
                            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
                        }
                    }

                    .stage-header {
                        display: flex;
                        align-items: center;
                        gap: 0.75rem;
                        margin-bottom: 0.75rem;

                        .stage-number {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 24px;
                            height: 24px;
                            border-radius: 50%;
                            background: $secondary-background;
                            color: $primary-text;
                            font-size: 0.8rem;
                            font-weight: 700;

                            &.theme-dark {
                                background: $dark-secondary-background;
                                color: $dark-primary-text;
                            }
                        }

                        .stage-color-indicator {
                            width: 12px;
                            height: 12px;
                            border-radius: 50%;
                            flex-shrink: 0;
                        }
                    }

                    .stage-content {
                        .stage-name {
                            font-size: 1rem;
                            font-weight: 600;
                            margin: 0 0 0.25rem 0;
                            color: $primary-text;

                            &.theme-dark {
                                color: $dark-primary-text;
                            }
                        }

                        .stage-code {
                            font-size: 0.8rem;
                            color: $tertiary-text;
                            font-weight: 500;
                            margin-bottom: 0.5rem;
                            display: block;

                            &.theme-dark {
                                color: $dark-tertiary-text;
                            }
                        }

                        .stage-description {
                            font-size: 0.85rem;
                            color: $secondary-text;
                            line-height: 1.4;
                            margin: 0;

                            &.theme-dark {
                                color: $dark-secondary-text;
                            }
                        }
                    }
                }
            }
        }

        // Subjects Progress Section
        .subjects-progress {
            .subjects-header {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                margin-bottom: 1.5rem;
                padding-bottom: 0.75rem;
                border-bottom: 1px solid $separator-color;

                &.theme-dark {
                    border-bottom-color: $dark-separator-color;
                }

                .subjects-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 32px;
                    height: 32px;
                    border-radius: 8px;
                    background: rgba($system-green, 0.1);

                    &.theme-dark {
                        background: rgba($system-green, 0.2);
                    }

                    svg {
                        color: $system-green;

                        &.theme-dark {
                            color: color.adjust($system-green, $lightness: 10%);
                        }
                    }
                }

                h3 {
                    font-size: 1.2rem;
                    font-weight: 600;
                    margin: 0;
                    color: $primary-text;

                    &.theme-dark {
                        color: $dark-primary-text;
                    }
                }
            }

            .subjects-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                gap: 1.5rem;

                .subject-card {
                    background: $window-background;
                    border: 1px solid $border-color;
                    border-radius: 16px;
                    padding: 1.5rem;
                    @include transition(all);

                    &.theme-dark {
                        background: $dark-window-background;
                        border: 1px solid $dark-border-color;
                    }

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

                        &.theme-dark {
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
                        }
                    }

                    .subject-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        margin-bottom: 1rem;

                        .subject-info {
                            flex: 1;
                            margin-right: 1rem;

                            .subject-name {
                                font-size: 1.1rem;
                                font-weight: 600;
                                margin: 0 0 0.25rem 0;
                                color: $primary-text;

                                &.theme-dark {
                                    color: $dark-primary-text;
                                }
                            }

                            .subject-description {
                                font-size: 0.85rem;
                                color: $secondary-text;
                                margin: 0;
                                line-height: 1.4;

                                &.theme-dark {
                                    color: $dark-secondary-text;
                                }
                            }
                        }

                        .subject-stage {
                            display: flex;
                            flex-direction: column;
                            align-items: flex-end;
                            gap: 0.25rem;

                            .current-stage-indicator {
                                padding: 0.25rem 0.5rem;
                                border-radius: 6px;
                                font-size: 0.7rem;
                                font-weight: 700;
                                color: white;
                                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                            }

                            .stage-progress {
                                font-size: 0.8rem;
                                font-weight: 600;
                                color: $tertiary-text;

                                &.theme-dark {
                                    color: $dark-tertiary-text;
                                }
                            }
                        }
                    }

                    .subject-gauge {
                        margin: 1rem 0;
                        display: flex;
                        justify-content: center;
                    }

                    .stage-timeline {
                        .timeline-title {
                            font-size: 0.9rem;
                            font-weight: 600;
                            margin: 0 0 0.75rem 0;
                            color: $secondary-text;

                            &.theme-dark {
                                color: $dark-secondary-text;
                            }
                        }

                        .timeline {
                            display: flex;
                            flex-direction: column;
                            gap: 0.5rem;

                            .timeline-item {
                                display: flex;
                                align-items: center;
                                gap: 0.75rem;

                                .timeline-dot {
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 20px;
                                    height: 20px;
                                    border-radius: 50%;
                                    flex-shrink: 0;
                                    position: relative;

                                    .progress-ring {
                                        width: 8px;
                                        height: 8px;
                                        border-radius: 50%;
                                        background: rgba(255, 255, 255, 0.3);
                                        animation: pulse 2s infinite;
                                    }

                                    @keyframes pulse {

                                        0%,
                                        100% {
                                            opacity: 1;
                                        }

                                        50% {
                                            opacity: 0.5;
                                        }
                                    }
                                }

                                .timeline-content {
                                    flex: 1;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;

                                    .timeline-stage {
                                        font-size: 0.85rem;
                                        font-weight: 500;
                                        color: $primary-text;

                                        &.theme-dark {
                                            color: $dark-primary-text;
                                        }
                                    }

                                    .timeline-date {
                                        font-size: 0.75rem;
                                        color: $tertiary-text;

                                        &.theme-dark {
                                            color: $dark-tertiary-text;
                                        }
                                    }
                                }

                                &.completed {
                                    .timeline-content {
                                        .timeline-stage {
                                            text-decoration: line-through;
                                            opacity: 0.7;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Modal Footer
    .modal-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 16px 20px;
        border-top: 1px solid $separator-color;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        background-color: rgba($window-background, 0.8);

        // Dark theme
        &.theme-dark {
            border-top: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        .close-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            @include transition(all);

            // Light theme
            background-color: $system-blue;
            color: white;
            border: none;
            box-shadow: 0 1px 3px rgba($system-blue, 0.3);

            // Dark theme
            &.theme-dark {
                background-color: $system-blue;
                color: white;
                box-shadow: 0 1px 3px rgba($system-blue, 0.5);
            }

            &:hover {
                background-color: darken($system-blue, 5%);
                transform: translateY(-1px);
                box-shadow: 0 2px 5px rgba($system-blue, 0.4);

                &.theme-dark {
                    background-color: darken($system-blue, 5%);
                    box-shadow: 0 2px 5px rgba($system-blue, 0.6);
                }
            }

            &:active {
                background-color: darken($system-blue, 10%);
                transform: translateY(0);
                box-shadow: 0 1px 2px rgba($system-blue, 0.3);

                &.theme-dark {
                    background-color: darken($system-blue, 10%);
                    box-shadow: 0 1px 2px rgba($system-blue, 0.5);
                }
            }

            &:focus {
                outline: 2px solid $system-blue;
                outline-offset: 2px;
            }
        }
    }
}