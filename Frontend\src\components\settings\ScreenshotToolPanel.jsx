import React, { useState } from 'react';
import ScreenshotMonitorIcon from '@mui/icons-material/ScreenshotMonitor';
import ImageIcon from '@mui/icons-material/Image';
import EditIcon from '@mui/icons-material/Edit';
import SendIcon from '@mui/icons-material/Send';
import '../../styles/SettingsPanels.scss';

const ScreenshotToolPanel = ({ settings }) => {
  const { isDarkMode } = settings;
  const [activeTab, setActiveTab] = useState('capture');
  
  const tabs = [
    { id: 'capture', label: 'Capture', icon: <ScreenshotMonitorIcon /> },
    { id: 'annotate', label: 'Annotate', icon: <EditIcon /> },
    { id: 'gallery', label: 'Gallery', icon: <ImageIcon /> }
  ];
  
  return (
    <div className={`detail-panel screenshot-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Screenshot Annotation Tool</h2>
        <p className="panel-description">
          Capture, annotate, and share screenshots to help explain issues or ask questions.
        </p>
      </div>
      
      <div className="screenshot-tabs">
        {tabs.map(tab => (
          <div 
            key={tab.id}
            className={`tab-item ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <div className="tab-icon">{tab.icon}</div>
            <span className="tab-label">{tab.label}</span>
          </div>
        ))}
      </div>
      
      <div className="screenshot-content">
        {activeTab === 'capture' && (
          <div className="capture-section">
            <div className="capture-preview">
              <div className="preview-placeholder">
                <ScreenshotMonitorIcon />
                <p>Click the button below to capture your screen</p>
              </div>
            </div>
            
            <div className="capture-controls">
              <button className="capture-button">
                <ScreenshotMonitorIcon />
                <span>Capture Screenshot</span>
              </button>
              
              <div className="capture-options">
                <label className="option-item">
                  <input type="checkbox" />
                  <span>Include cursor</span>
                </label>
                
                <label className="option-item">
                  <input type="checkbox" checked />
                  <span>Capture active window only</span>
                </label>
                
                <label className="option-item">
                  <input type="checkbox" />
                  <span>Delay 3 seconds</span>
                </label>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'annotate' && (
          <div className="annotate-section">
            <div className="annotation-canvas">
              <div className="canvas-placeholder">
                <EditIcon />
                <p>Capture a screenshot first or upload an image to annotate</p>
              </div>
            </div>
            
            <div className="annotation-tools">
              <div className="tool-group">
                <button className="tool-button">
                  <span className="tool-icon">✏️</span>
                  <span>Draw</span>
                </button>
                
                <button className="tool-button">
                  <span className="tool-icon">📝</span>
                  <span>Text</span>
                </button>
                
                <button className="tool-button">
                  <span className="tool-icon">⬜</span>
                  <span>Shape</span>
                </button>
                
                <button className="tool-button">
                  <span className="tool-icon">🔍</span>
                  <span>Highlight</span>
                </button>
              </div>
              
              <div className="color-picker">
                <span className="color-dot" style={{ backgroundColor: '#FF3B30' }}></span>
                <span className="color-dot" style={{ backgroundColor: '#FF9500' }}></span>
                <span className="color-dot" style={{ backgroundColor: '#FFCC00' }}></span>
                <span className="color-dot" style={{ backgroundColor: '#28CD41' }}></span>
                <span className="color-dot active" style={{ backgroundColor: '#007AFF' }}></span>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'gallery' && (
          <div className="gallery-section">
            <div className="gallery-grid">
              <div className="empty-gallery">
                <ImageIcon />
                <p>No screenshots saved yet</p>
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="screenshot-actions">
        <button className="action-button secondary">
          <span>Cancel</span>
        </button>
        
        <button className="action-button primary">
          <SendIcon />
          <span>Send with Report</span>
        </button>
      </div>
      
      <div className="info-box">
        <p>
          The screenshot tool allows you to capture your screen, add annotations, 
          and include the image with your support requests or questions.
        </p>
      </div>
    </div>
  );
};

export default ScreenshotToolPanel;
