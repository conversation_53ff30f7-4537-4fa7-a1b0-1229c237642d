import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import axios from "axios";
import "../styles/Login.scss";
import logo from "../assets/logo.png";

const Login = ({ onLogin }) => {
    const [isSignUp, setIsSignUp] = useState(false);
    const [fullName, setFullName] = useState("");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState("");
    const [passwordStrength, setPasswordStrength] = useState(0);
    const [passwordFeedback, setPasswordFeedback] = useState("");
    const [showTerms, setShowTerms] = useState(false);
    const [termsAccepted, setTermsAccepted] = useState(false);
    const navigate = useNavigate();
    const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

    const flipCard = () => {
        setIsSignUp((prev) => !prev);
        setError("");
        setSuccess(false);
    };

    const togglePasswordVisibility = () => {
        setShowPassword((prev) => !prev);
    };

    // Password strength checker
    const checkPasswordStrength = (password) => {
        // Initialize strength score
        let strength = 0;
        let feedback = [];

        // Check password length
        if (password.length >= 8) {
            strength += 1;
        } else {
            feedback.push("At least 8 characters");
        }

        // Check for uppercase letters
        if (/[A-Z]/.test(password)) {
            strength += 1;
        } else {
            feedback.push("At least 1 uppercase letter");
        }

        // Check for lowercase letters
        if (/[a-z]/.test(password)) {
            strength += 1;
        } else {
            feedback.push("At least 1 lowercase letter");
        }

        // Check for numbers
        if (/[0-9]/.test(password)) {
            strength += 1;
        } else {
            feedback.push("At least 1 number");
        }

        // Check for special characters
        if (/[^A-Za-z0-9]/.test(password)) {
            strength += 1;
        } else {
            feedback.push("At least 1 special character");
        }

        setPasswordStrength(strength);
        setPasswordFeedback(feedback.join(" • "));
    };

    // Toggle terms and conditions modal
    const toggleTerms = () => {
        setShowTerms(prev => !prev);
    };

    // Check password strength whenever password changes
    useEffect(() => {
        if (password) {
            checkPasswordStrength(password);
        } else {
            setPasswordStrength(0);
            setPasswordFeedback("");
        }
    }, [password]);

    const handleSignIn = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError("");
        setSuccess(false);
        try {
            const response = await axios.post(`${API_URL}/api/auth/login`, {
                email,
                password,
            });

            if (response.data.success) {
                const { token, user } = response.data;
                onLogin(user, token);
                setSuccess(true);

                // Let App.jsx handle the redirection
                // This prevents race conditions between multiple redirects
                console.log("Login.jsx: Login successful, letting App.jsx handle redirection");
            }
        } catch (err) {
            setError(err.response?.data?.message || "Login failed");
        } finally {
            setLoading(false);
        }
    };

    const handleSignUp = async (e) => {
        e.preventDefault();
        setError("");
        setSuccess(false);

        if (password !== confirmPassword) {
            setError("Passwords do not match.");
            return;
        }

        if (passwordStrength < 3) {
            setError("Password is not strong enough. Please improve your password.");
            return;
        }

        if (!termsAccepted) {
            setError("You must accept the Terms & Conditions to create an account.");
            return;
        }

        setLoading(true);
        try {
            const response = await axios.post(`${API_URL}/api/auth/register`, {
                full_name: fullName,
                email,
                password,
            });

            if (response.status === 201) {
                const loginResponse = await axios.post(`${API_URL}/api/auth/login`, {
                    email,
                    password,
                });

                if (loginResponse.data.success) {
                    const { token, user } = loginResponse.data;
                    onLogin(user, token);
                    setSuccess(true);
                    // Let App.jsx handle the redirection
                    // For new users, this will be to the steps page
                    console.log("Login.jsx: Registration successful, letting App.jsx handle redirection");
                }
            }
        } catch (err) {
            setError(err.response?.data?.message || "Registration failed");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="login">
            {success && (
                <div className="redirect-loader">
                    <div className="spinner"></div>
                    <p>Redirecting...</p>
                </div>
            )}

            {/* Terms and Conditions Modal */}
            <TermsAndConditionsModal
                show={showTerms}
                onClose={() => setShowTerms(false)}
                onAccept={() => setTermsAccepted(true)}
            />
            <div className="login_hero">
                <div className="img"></div>
                <div className="login-container">
                    <div className="logo">
                        <img src={logo} alt="" />
                        <h2>Welcome to <span>Blueprint AI</span></h2>
                    </div>
                    <AnimatePresence mode="wait">
                        {isSignUp ? (
                            <motion.div
                                key="signup"
                                initial={{ rotateY: 90 }}
                                animate={{ rotateY: 0 }}
                                exit={{ rotateY: -90 }}
                                transition={{ duration: 0.6 }}
                                className="login-card"
                            >
                                <h2>Create an Account</h2>
                                <p className="question">
                                    Already have an account?{" "}
                                    <button className="signup" onClick={flipCard}>
                                        Sign In
                                    </button>
                                </p>
                                <form onSubmit={handleSignUp}>
                                    <input
                                        type="text"
                                        placeholder="Full Name"
                                        value={fullName}
                                        onChange={(e) => setFullName(e.target.value)}
                                        required
                                    />
                                    <input
                                        type="email"
                                        placeholder="Email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                    />
                                    <div className="password">
                                        <div className="password-input">
                                            <input
                                                type={showPassword ? "text" : "password"}
                                                placeholder="Password"
                                                value={password}
                                                onChange={(e) => setPassword(e.target.value)}
                                                required
                                                minLength="8"
                                            />
                                            <IconButton
                                                onClick={togglePasswordVisibility}
                                                edge="end"
                                                className="visibility-toggle"
                                            >
                                                {showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </div>

                                        {/* Password strength indicator */}
                                        {password && (
                                            <div className="password-strength">
                                                <div className="strength-bars">
                                                    {[1, 2, 3, 4, 5].map((level) => (
                                                        <div
                                                            key={level}
                                                            className={`strength-bar ${passwordStrength >= level ?
                                                                level <= 2 ? 'weak' :
                                                                    level <= 3 ? 'medium' :
                                                                        'strong'
                                                                : ''
                                                                }`}
                                                        ></div>
                                                    ))}
                                                </div>
                                                <div className="strength-text">
                                                    {passwordStrength === 0 && "Very weak"}
                                                    {passwordStrength === 1 && "Very weak"}
                                                    {passwordStrength === 2 && "Weak"}
                                                    {passwordStrength === 3 && "Medium"}
                                                    {passwordStrength === 4 && "Strong"}
                                                    {passwordStrength === 5 && "Very strong"}
                                                </div>
                                                {passwordFeedback && (
                                                    <div className="strength-feedback">
                                                        {passwordFeedback}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        <input
                                            type={showPassword ? "text" : "password"}
                                            placeholder="Confirm Password"
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            required
                                            minLength="8"
                                        />
                                    </div>
                                    {error && <div className="error-message">{error}</div>}
                                    <label className={`terms-checkbox ${termsAccepted ? 'terms-accepted' : ''}`}>
                                        <input
                                            type="checkbox"
                                            checked={termsAccepted}
                                            onChange={() => setTermsAccepted(prev => !prev)}
                                        />
                                        I agree to the{" "}
                                        <button
                                            type="button"
                                            className="terms-link"
                                            onClick={toggleTerms}
                                        >
                                            Terms & Conditions
                                        </button>
                                        {termsAccepted && (
                                            <span className="terms-accepted-indicator">✓</span>
                                        )}
                                    </label>
                                    <button type="submit" disabled={loading}>
                                        {loading
                                            ? "Creating Account..."
                                            : success
                                                ? "Account Created!"
                                                : "Create Account"}
                                    </button>
                                </form>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="signin"
                                initial={{ rotateY: -90 }}
                                animate={{ rotateY: 0 }}
                                exit={{ rotateY: 90 }}
                                transition={{ duration: 0.6 }}
                                className="login-card"
                            >
                                <h2>Sign In</h2>
                                <form onSubmit={handleSignIn}>
                                    <input
                                        type="email"
                                        placeholder="Email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                    />
                                    <div className="password-input">
                                        <input
                                            type={showPassword ? "text" : "password"}
                                            placeholder="Password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            required
                                        />
                                        <IconButton
                                            onClick={togglePasswordVisibility}
                                            edge="end"
                                            className="visibility-toggle"
                                        >
                                            {showPassword ? <VisibilityOff /> : <Visibility />}
                                        </IconButton>
                                    </div>
                                    {error && <div className="error-message">{error}</div>}
                                    <button type="submit" disabled={loading}>
                                        {loading ? "Signing In..." : success ? "Signed In!" : "Sign In"}
                                    </button>
                                </form>
                                <p className="question">
                                    Don't have an account?{" "}
                                    <button onClick={flipCard}>Sign Up</button>
                                </p>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </div>
        </div>
    );
};

// Terms and Conditions Modal Component
const TermsAndConditionsModal = ({ show, onClose, onAccept }) => {
    if (!show) return null;

    const handleAccept = () => {
        onAccept(); // Set the checkbox to checked
        onClose(); // Close the modal
    };

    return (
        <div className="terms-modal-overlay" onClick={onClose}>
            <div className="terms-modal" onClick={(e) => e.stopPropagation()}>
                <div className="terms-modal-header">
                    <h3>Terms and Conditions</h3>
                    <button className="close-button" onClick={onClose}>×</button>
                </div>
                <div className="terms-modal-content">
                    <h4>1. Acceptance of Terms</h4>
                    <p>
                        By accessing and using Blueprint AI, you agree to be bound by these Terms and Conditions,
                        all applicable laws and regulations, and agree that you are responsible for compliance with any
                        applicable local laws.
                    </p>

                    <h4>2. Use License</h4>
                    <p>
                        Permission is granted to temporarily use Blueprint AI for personal, non-commercial purposes only.
                        This is the grant of a license, not a transfer of title, and under this license you may not:
                    </p>
                    <ul>
                        <li>Modify or copy the materials</li>
                        <li>Use the materials for any commercial purpose</li>
                        <li>Attempt to decompile or reverse engineer any software contained in Blueprint AI</li>
                        <li>Remove any copyright or other proprietary notations from the materials</li>
                        <li>Transfer the materials to another person or "mirror" the materials on any other server</li>
                    </ul>

                    <h4>3. Privacy Policy</h4>
                    <p>
                        Your use of Blueprint AI is also governed by our Privacy Policy, which is incorporated by reference
                        into these Terms and Conditions. Please review our Privacy Policy to understand our practices.
                    </p>

                    <h4>4. User Accounts</h4>
                    <p>
                        When you create an account with us, you must provide accurate, complete, and current information.
                        Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account.
                    </p>

                    <h4>5. Limitation of Liability</h4>
                    <p>
                        In no event shall Blueprint AI or its suppliers be liable for any damages arising out of the use
                        or inability to use the materials on Blueprint AI, even if Blueprint AI or a Blueprint AI authorized
                        representative has been notified orally or in writing of the possibility of such damage.
                    </p>
                </div>
                <div className="terms-modal-footer">
                    <button className="accept-button" onClick={handleAccept}>Accept & Close</button>
                </div>
            </div>
        </div>
    );
};

export default Login;