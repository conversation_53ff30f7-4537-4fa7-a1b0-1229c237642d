import React from 'react';
import MemoryIcon from '@mui/icons-material/Memory';
import '../../styles/SettingsPanels.scss';

const NeuralProcessingPanel = ({ settings }) => {
  const { neuralProcessing, setNeuralProcessing, isDarkMode } = settings;
  
  return (
    <div className={`detail-panel neural-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Neural Processing Intensity</h2>
        <p className="panel-description">
          Control how intensively the AI processes your study materials and questions.
        </p>
      </div>
      
      <div className="settings-group">
        <div className="setting-item toggle-setting">
          <div className="toggle-container">
            <div className="toggle-label">
              <MemoryIcon />
              <span>Enhanced Neural Processing</span>
            </div>
            <div className="toggle-description">
              <p>
                When enabled, the AI uses more intensive processing to provide deeper insights 
                and more nuanced explanations. This may slightly increase response time.
              </p>
            </div>
            <label className="toggle-switch">
              <input 
                type="checkbox" 
                checked={neuralProcessing} 
                onChange={() => setNeuralProcessing(!neuralProcessing)}
              />
              <span className="toggle-slider"></span>
              <span className="toggle-status">{neuralProcessing ? 'Enabled' : 'Disabled'}</span>
            </label>
          </div>
        </div>
      </div>
      
      <div className="neural-visualization">
        <div className="neural-network">
          {[...Array(3)].map((_, layerIndex) => (
            <div key={layerIndex} className="neural-layer">
              {[...Array(layerIndex === 1 ? 5 : 4)].map((_, nodeIndex) => (
                <div 
                  key={nodeIndex} 
                  className={`neural-node ${neuralProcessing ? 'active' : ''}`}
                  style={{
                    animationDelay: `${(layerIndex * 0.2) + (nodeIndex * 0.1)}s`
                  }}
                >
                  <div className="node-pulse"></div>
                </div>
              ))}
            </div>
          ))}
          
          {neuralProcessing && (
            <div className="neural-connections">
              {[...Array(12)].map((_, i) => (
                <div 
                  key={i} 
                  className="connection-line"
                  style={{
                    animationDelay: `${i * 0.1}s`
                  }}
                ></div>
              ))}
            </div>
          )}
        </div>
      </div>
      
      <div className="info-box">
        <p>
          Enhanced neural processing uses advanced AI capabilities to provide more 
          insightful and contextually aware responses. This setting affects all 
          interactions with the AI assistant.
        </p>
      </div>
    </div>
  );
};

export default NeuralProcessingPanel;
