import { useState, useEffect } from 'react';
import {
    format,
    addMonths,
    subMonths,
    startOfMonth,
    endOfMonth,
    eachDayOfInterval,
    isSameMonth,
    isSameDay,
    isToday,
    isSameWeek,
    parseISO
} from 'date-fns';


const Calendar = ({ tool, subject }) => {
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [events, setEvents] = useState([]);
    const [newEvent, setNewEvent] = useState({
        title: '',
        date: format(new Date(), 'yyyy-MM-dd'),
        description: '',
        completed: false
    });
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [viewMode, setViewMode] = useState('day'); // 'day' or 'month'
    const [activeTab, setActiveTab] = useState('events'); // 'events' or 'objectives'

    // Load events from localStorage on component mount
    useEffect(() => {
        const savedEvents = JSON.parse(localStorage.getItem('calendarEvents') || '[]');
        setEvents(savedEvents);
    }, []);

    // Save events to localStorage when they change
    useEffect(() => {
        localStorage.setItem('calendarEvents', JSON.stringify(events));
    }, [events]);

    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

    const nextMonth = () => setCurrentMonth(addMonths(currentMonth, 1));
    const prevMonth = () => setCurrentMonth(subMonths(currentMonth, 1));
    const goToToday = () => {
        setCurrentMonth(new Date());
        setSelectedDate(new Date());
    };

    const addEvent = () => {
        if (newEvent.title.trim()) {
            const updatedEvents = [...events, {
                ...newEvent,
                id: Date.now(),
                createdAt: new Date().toISOString()
            }];
            setEvents(updatedEvents);
            setNewEvent({
                title: '',
                date: format(new Date(), 'yyyy-MM-dd'),
                description: '',
                completed: false
            });
        }
    };

    const toggleEventComplete = (id) => {
        setEvents(prev => prev.map(event =>
            event.id === id ? { ...event, completed: !event.completed } : event
        ));
    };

    const removeEvent = (id) => {
        setEvents(prev => prev.filter(event => event.id !== id));
    };

    const getEventsForDate = (date) => {
        return events.filter(event => isSameDay(parseISO(event.date), date));
    };

    const getDayClassNames = (day) => {
        let classNames = 'day-cell';
        if (!isSameMonth(day, currentMonth)) classNames += ' other-month';
        if (isToday(day)) classNames += ' today';
        if (isSameDay(day, selectedDate)) classNames += ' selected';
        if (isSameWeek(day, selectedDate, { weekStartsOn: 0 })) classNames += ' same-week';
        return classNames;
    };

    const handleDateClick = (day) => {
        setSelectedDate(day);
        setViewMode('day');
    };

    return (
        <div className="calendar-app">
            <div className="calendar-header">
                <div className="header-left">
                    <h2>{subject} Calendar</h2>
                    <div className="view-controls">
                        <button
                            className={`view-button ${viewMode === 'month' ? 'active' : ''}`}
                            onClick={() => setViewMode('month')}
                        >
                            Month
                        </button>
                        <button
                            className={`view-button ${viewMode === 'day' ? 'active' : ''}`}
                            onClick={() => setViewMode('day')}
                        >
                            Day
                        </button>
                    </div>
                </div>

                <div className="month-navigation">
                    <button className="nav-button" onClick={prevMonth}>
                        &lt;
                    </button>
                    <h3 className="month-title">
                        {format(currentMonth, 'MMMM yyyy')}
                    </h3>
                    <button className="nav-button" onClick={nextMonth}>
                        &gt;
                    </button>
                    <button className="today-button" onClick={goToToday}>
                        Today
                    </button>
                </div>
            </div>

            {viewMode === 'month' && (
                <div className="calendar-grid">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                        <div key={day} className="day-header">
                            {day.substring(0, 3)}
                        </div>
                    ))}

                    {monthDays.map(day => {
                        const dayEvents = getEventsForDate(day);
                        const hasEvents = dayEvents.length > 0;
                        const hasCompleted = dayEvents.some(e => e.completed);

                        return (
                            <div
                                key={day.toString()}
                                className={getDayClassNames(day)}
                                onClick={() => handleDateClick(day)}
                            >
                                <div className="day-number">{format(day, 'd')}</div>
                                {hasEvents && (
                                    <div className={`event-indicator ${hasCompleted ? 'has-completed' : ''}`}>
                                        {dayEvents.filter(e => !e.completed).length > 0 && (
                                            <span className="pending-count">
                        {dayEvents.filter(e => !e.completed).length}
                      </span>
                                        )}
                                        {hasCompleted && (
                                            <span className="completed-count">
                        {dayEvents.filter(e => e.completed).length}
                      </span>
                                        )}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            )}

            {viewMode === 'day' && (
                <div className="day-view">
                    <div className="day-header">
                        <h3>{format(selectedDate, 'EEEE, MMMM d, yyyy')}</h3>
                        <div className="day-tabs">
                            <button
                                className={`tab-button ${activeTab === 'events' ? 'active' : ''}`}
                                onClick={() => setActiveTab('events')}
                            >
                                Events
                            </button>
                            <button
                                className={`tab-button ${activeTab === 'objectives' ? 'active' : ''}`}
                                onClick={() => setActiveTab('objectives')}
                            >
                                Objectives
                            </button>
                        </div>
                    </div>

                    <div className="day-content">
                        {activeTab === 'events' ? (
                            <div className="events-section">
                                {getEventsForDate(selectedDate).length > 0 ? (
                                    <ul className="events-list">
                                        {getEventsForDate(selectedDate).map(event => (
                                            <EventItem
                                                key={event.id}
                                                event={event}
                                                onToggleComplete={toggleEventComplete}
                                                onRemove={removeEvent}
                                            />
                                        ))}
                                    </ul>
                                ) : (
                                    <div className="empty-state">
                                        <p>No events scheduled for this day</p>
                                    </div>
                                )}

                                <div className="add-event-form">
                                    <h4>Add New Event</h4>
                                    <div className="form-group">
                                        <input
                                            type="text"
                                            value={newEvent.title}
                                            onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                                            placeholder="Event title..."
                                            className="form-input"
                                        />
                                    </div>
                                    <div className="form-group">
                    <textarea
                        value={newEvent.description}
                        onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                        placeholder="Description..."
                        className="form-textarea"
                        rows={3}
                    />
                                    </div>
                                    <div className="form-row">
                                        <input
                                            type="date"
                                            value={newEvent.date}
                                            onChange={(e) => setNewEvent({ ...newEvent, date: e.target.value })}
                                            className="date-input"
                                        />
                                        <button
                                            className="add-button"
                                            onClick={addEvent}
                                            disabled={!newEvent.title.trim()}
                                        >
                                            Add Event
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div className="objectives-section">
                                <div className="objectives-list">
                                    {getEventsForDate(selectedDate).filter(e => !e.completed).length > 0 ? (
                                        <>
                                            <h4>Pending Objectives</h4>
                                            <ul>
                                                {getEventsForDate(selectedDate)
                                                    .filter(e => !e.completed)
                                                    .map(event => (
                                                        <ObjectiveItem
                                                            key={event.id}
                                                            event={event}
                                                            onToggleComplete={toggleEventComplete}
                                                            onRemove={removeEvent}
                                                        />
                                                    ))}
                                            </ul>
                                        </>
                                    ) : (
                                        <div className="empty-state">
                                            <p>No pending objectives</p>
                                        </div>
                                    )}

                                    {getEventsForDate(selectedDate).filter(e => e.completed).length > 0 && (
                                        <>
                                            <h4>Completed Objectives</h4>
                                            <ul>
                                                {getEventsForDate(selectedDate)
                                                    .filter(e => e.completed)
                                                    .map(event => (
                                                        <ObjectiveItem
                                                            key={event.id}
                                                            event={event}
                                                            onToggleComplete={toggleEventComplete}
                                                            onRemove={removeEvent}
                                                        />
                                                    ))}
                                            </ul>
                                        </>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            <div className="upcoming-events">
                <h3>Upcoming Events</h3>
                {events.filter(e => !e.completed && new Date(e.date) >= new Date()).length > 0 ? (
                    <ul>
                        {events
                            .filter(e => !e.completed && new Date(e.date) >= new Date())
                            .sort((a, b) => new Date(a.date) - new Date(b.date))
                            .slice(0, 5)
                            .map(event => (
                                <li key={event.id} onClick={() => {
                                    setSelectedDate(parseISO(event.date));
                                    setViewMode('day');
                                }}>
                                    <span className="event-date">{format(parseISO(event.date), 'MMM d')}</span>
                                    <span className="event-title">{event.title}</span>
                                </li>
                            ))}
                    </ul>
                ) : (
                    <div className="empty-state">
                        <p>No upcoming events</p>
                    </div>
                )}
            </div>
        </div>
    );
};

const EventItem = ({ event, onToggleComplete, onRemove }) => {
    return (
        <li className={`event-item ${event.completed ? 'completed' : ''}`}>
            <div className="event-content">
                <button
                    className={`toggle-button ${event.completed ? 'completed' : ''}`}
                    onClick={() => onToggleComplete(event.id)}
                >
                    {event.completed ? '✓' : ''}
                </button>
                <div className="event-details">
                    <h4>{event.title}</h4>
                    {event.description && <p>{event.description}</p>}
                    <span className="event-time">
            {format(parseISO(event.date), 'MMM d, yyyy')}
          </span>
                </div>
            </div>
            <button
                className="delete-button"
                onClick={() => onRemove(event.id)}
            >
                ×
            </button>
        </li>
    );
};

const ObjectiveItem = ({ event, onToggleComplete, onRemove }) => {
    return (
        <li className={`objective-item ${event.completed ? 'completed' : ''}`}>
            <button
                className={`objective-toggle ${event.completed ? 'completed' : ''}`}
                onClick={() => onToggleComplete(event.id)}
            >
                <div className="toggle-circle"></div>
            </button>
            <div className="objective-content">
                <span className="objective-title">{event.title}</span>
                {event.description && (
                    <p className="objective-description">{event.description}</p>
                )}
            </div>
            <button
                className="objective-delete"
                onClick={() => onRemove(event.id)}
            >
                ×
            </button>
        </li>
    );
};

export default Calendar;