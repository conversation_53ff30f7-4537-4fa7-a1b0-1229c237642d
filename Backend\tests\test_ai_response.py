from ai_assistant import chat_with_ai, get_today_objectives, get_upcoming_objectives
import json
from app import app, db  # Import the Flask app and db
import logging
import psycopg2
from datetime import date, timedelta
from models import User, Subject, Chapter, Objective

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Connect to the database directly to check objectives
conn = psycopg2.connect(
    dbname='blueprint',
    user='postgres',
    password='Lukind@1956',
    host='localhost'
)
cursor = conn.cursor()

# Check which users have objectives for today
cursor.execute("""
    SELECT DISTINCT s.user_id
    FROM objectives o
    JOIN chapters c ON o.chapter_id = c.id
    JOIN subjects s ON c.subject_id = s.id
    WHERE o.display_date = %s
""", (date.today(),))
users_with_objectives = cursor.fetchall()
print(f"\n--- Users with objectives for today: {[user[0] for user in users_with_objectives]} ---")

# Check which users have subjects
cursor.execute("""
    SELECT DISTINCT user_id, COUNT(*) as subject_count
    FROM subjects
    GROUP BY user_id
""")
users_with_subjects = cursor.fetchall()
print(f"\n--- Users with subjects: {[(user[0], user[1]) for user in users_with_subjects]} ---")

# Check which users have chapters
cursor.execute("""
    SELECT DISTINCT s.user_id, COUNT(*) as chapter_count
    FROM chapters c
    JOIN subjects s ON c.subject_id = s.id
    GROUP BY s.user_id
""")
users_with_chapters = cursor.fetchall()
print(f"\n--- Users with chapters: {[(user[0], user[1]) for user in users_with_chapters]} ---")

# Function to create test objectives for a user
def create_test_objectives_for_user(user_id):
    # Check if the user has any subjects
    subjects = Subject.query.filter_by(user_id=user_id).all()
    if not subjects:
        print(f"User {user_id} has no subjects. Creating a test subject...")
        # Create a test subject
        test_subject = Subject(
            user_id=user_id,
            name="Test Subject"
        )
        db.session.add(test_subject)
        db.session.commit()
        subjects = [test_subject]

    # Get the first subject
    subject = subjects[0]

    # Check if the subject has any chapters
    chapters = Chapter.query.filter_by(subject_id=subject.id).all()
    if not chapters:
        print(f"Subject {subject.id} has no chapters. Creating a test chapter...")
        # Create a test chapter
        test_chapter = Chapter(
            subject_id=subject.id,
            title="Test Chapter",
            chapter_number="Chapter 1"
        )
        db.session.add(test_chapter)
        db.session.commit()
        chapters = [test_chapter]

    # Get the first chapter
    chapter = chapters[0]

    # Check if the user already has objectives for today
    today = date.today()
    existing_objectives = Objective.query.join(
        Chapter, Objective.chapter_id == Chapter.id
    ).join(
        Subject, Chapter.subject_id == Subject.id
    ).filter(
        Subject.user_id == user_id,
        Objective.display_date == today
    ).all()

    if existing_objectives:
        print(f"User {user_id} already has {len(existing_objectives)} objectives for today")
        return

    # Create test objectives for today and upcoming days
    print(f"Creating test objectives for user {user_id}...")

    # Create 3 objectives for today
    for i in range(3):
        objective = Objective(
            chapter_id=chapter.id,
            objective=f"Test objective {i+1} for today",
            display_date=today,
            estimated_time_minutes=30,
            difficulty_level=2,
            completed=False
        )
        db.session.add(objective)

    # Create 3 objectives for tomorrow
    tomorrow = today + timedelta(days=1)
    for i in range(3):
        objective = Objective(
            chapter_id=chapter.id,
            objective=f"Test objective {i+1} for tomorrow",
            display_date=tomorrow,
            estimated_time_minutes=30,
            difficulty_level=2,
            completed=False
        )
        db.session.add(objective)

    # Create 3 objectives for the day after tomorrow
    day_after_tomorrow = today + timedelta(days=2)
    for i in range(3):
        objective = Objective(
            chapter_id=chapter.id,
            objective=f"Test objective {i+1} for day after tomorrow",
            display_date=day_after_tomorrow,
            estimated_time_minutes=30,
            difficulty_level=2,
            completed=False
        )
        db.session.add(objective)

    # Commit the changes
    db.session.commit()
    print(f"Created test objectives for user {user_id}")

# Test within the Flask application context
with app.app_context():
    # Create test objectives for user 2
    create_test_objectives_for_user(2)

    # Try different user IDs
    for user_id in [1, 2]:
        print(f"\n=== Testing with user_id = {user_id} ===")

        # Check if user exists
        user = User.query.get(user_id)
        if user:
            print(f"User exists: {user.full_name} ({user.email})")
        else:
            print(f"User with ID {user_id} does not exist")
            continue

        # Get today's objectives
        today_objectives = get_today_objectives(user_id)
        print(f"\n--- Today's Objectives for User {user_id} ---")
        print(json.dumps(today_objectives, indent=2))

        # Get upcoming objectives
        upcoming_objectives = get_upcoming_objectives(user_id)
        print(f"\n--- Upcoming Objectives for User {user_id} ---")
        print(json.dumps(upcoming_objectives, indent=2))

        # Test the AI assistant's response to a query about today's objectives
        message = "What are my objectives for today?"

        # Call the chat_with_ai function
        response = chat_with_ai(user_id, message)

        # Print the response
        print(f"\n--- AI Response for Today's Objectives (User {user_id}) ---")
        print(json.dumps(response, indent=2))

        # Test the AI assistant's response to a query about upcoming objectives
        message = "What are my upcoming objectives for the next few days?"

        # Call the chat_with_ai function
        response = chat_with_ai(user_id, message)

        # Print the response
        print(f"\n--- AI Response for Upcoming Objectives (User {user_id}) ---")
        print(json.dumps(response, indent=2))

# Close the database connection
cursor.close()
conn.close()
