@use "../src/styles/variables" as *;
@use "sass:color";
@use "../src/styles/scrollbar";

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Base Styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 500;
}

#root {
  width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
  position: relative;

  // Light theme
  background-color: $light-background;

  // Dark theme
  .theme-dark & {
    background-color: $dark-background;
  }
}

body {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: hidden;
  position: relative;

  // Light theme
  background-color: $light-background;

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
  }

  button {
    outline: none !important;
    border: none;
  }
}

.App {
  overflow-y: auto;
  height: 100dvh;
}

.main-dashboard {
  display: flex;
  width: 100dvw;
  min-height: 100vh;
  transition: background-color $transition-speed;
  overflow-y: hidden;

  // Light theme
  background-color: $light-background;
  color: $light-primary-text;

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
    color: $dark-primary-text;

    .sidebar {
      background-color: color.adjust($dark-background, $lightness: 5%);
      border-color: $dark-accent-color-2;
    }

    .header {
      border-color: $dark-accent-color-2;
    }

    .main-content-wrapper {
      background-color: $dark-background;
    }
  }
}

.content-area {
  border-radius: 1.5rem 0 0 0;
  overflow: auto;

  // Light theme
  background-color: $light-background;

  // Dark theme
  .theme-dark & {
    background-color: $dark-background;
  }

  .content {
    background: none;
    padding: 0;
    margin: 0;
  }
}

.dashboard-content {
  border-radius: 2rem 0 0 0;
}

.main-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100dvh;

  // Light theme
  background-color: $light-background;

  // Dark theme
  .theme-dark & {
    background-color: $dark-background;
  }
}

// Material Icons
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

// Responsive Adjustments
@media (max-width: 1200px) {
  .left-sidebar {
    position: fixed;
    z-index: 20;
    height: 100vh;
    transform: translateX(0);
    transition: transform $transition-speed;

    &.collapsed {
      transform: translateX(-100%);
    }
  }

  .main-content-wrapper {
    margin-left: 0;
  }
}

.preferences-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1100;
  display: flex;
  justify-content: center;
  align-items: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(5px);

    // Light theme
    background-color: rgba(0, 0, 0, 0.4);

    // Dark theme
    .theme-dark & {
      background-color: rgba(0, 0, 0, 0.6);
    }
  }

  .modal-content {
    position: relative;
    border-radius: 12px;
    //max-width: 1200px;
    width: 90%;
    max-height: 95dvh;
    overflow-y: auto;
    animation: fadeInUp $transition-speed;

    // Light theme
    background-color: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
      background-color: $dark-background;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      border: 1px solid $dark-accent-color-2;
    }

    // Scrollbar styling is now handled by _scrollbar.scss
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}