const axios = require('axios');

const API_URL = 'http://localhost:5001';

async function testKanbanAPI() {
    try {
        console.log('Testing Kanban API...');
        
        // First, let's try to get tasks without authentication to see the error
        console.log('\n1. Testing GET /api/kanban without auth:');
        try {
            const response = await axios.get(`${API_URL}/api/kanban`);
            console.log('Response:', response.data);
        } catch (error) {
            console.log('Expected error:', error.response?.data || error.message);
        }
        
        // Test with a fake token to see what happens
        console.log('\n2. Testing GET /api/kanban with fake token:');
        try {
            const response = await axios.get(`${API_URL}/api/kanban`, {
                headers: {
                    'Authorization': 'Bearer fake-token'
                }
            });
            console.log('Response:', response.data);
        } catch (error) {
            console.log('Expected error:', error.response?.data || error.message);
        }
        
        // Test POST without auth
        console.log('\n3. Testing POST /api/kanban without auth:');
        try {
            const response = await axios.post(`${API_URL}/api/kanban`, {
                subject_id: 1,
                title: 'Test Task',
                description: 'Test Description',
                priority: 'medium'
            });
            console.log('Response:', response.data);
        } catch (error) {
            console.log('Expected error:', error.response?.data || error.message);
        }
        
        // Test if server is running
        console.log('\n4. Testing server health:');
        try {
            const response = await axios.get(`${API_URL}/`);
            console.log('Server response:', response.status);
        } catch (error) {
            console.log('Server error:', error.message);
        }
        
    } catch (error) {
        console.error('Test error:', error.message);
    }
}

testKanbanAPI();
