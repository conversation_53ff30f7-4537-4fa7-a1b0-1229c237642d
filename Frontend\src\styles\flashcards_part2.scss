@use "sass:color";
@use "_scrollbar";
@use "_mixins" as *;


// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Card Controls
.flashcards-app {

    // Card Controls
    .card-controls {
        margin-bottom: 1.5rem;
        padding: 1rem;
        border-radius: 12px;

        // Light theme
        background-color: $secondary-background;
        border: 1px solid $border-color;

        // Dark theme
        .theme-dark & {
            background-color: $dark-secondary-background;
            border: 1px solid $dark-border-color;
        }

        .control-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;

            .color-picker {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                label {
                    font-size: 0.875rem;

                    // Light theme
                    // color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }

                input[type="color"] {
                    width: 2rem;
                    height: 2rem;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    padding: 0;

                    // Light theme
                    background-color: $window-background;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-window-background;
                    }

                    &::-webkit-color-swatch-wrapper {
                        padding: 0;
                    }

                    &::-webkit-color-swatch {
                        border: none;
                        border-radius: 4px;
                    }
                }
            }

            .difficult-button {
                padding: 0.5rem 1rem;
                border-radius: 8px;
                font-size: 0.875rem;
                font-weight: 500;
                border: none;
                cursor: pointer;
                @include transition;

                // Light theme
                background-color: $secondary-background;
                color: $system-red;
                border: 1px solid $system-red;

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $system-red;
                    border: 1px solid $system-red;
                }

                &:hover {
                    // Light theme
                    background-color: rgba($system-red, 0.1);

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-red, 0.15);
                    }
                }

                &.active {
                    // Light theme
                    background-color: $system-red;
                    color: white;

                    // Dark theme
                    .theme-dark & {
                        background-color: $system-red;
                    }

                    &:hover {
                        // Light theme
                        background-color: darken($system-red, 5%);

                        // Dark theme
                        .theme-dark & {
                            background-color: lighten($system-red, 5%);
                        }
                    }
                }
            }
        }

        .card-meta-info {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;

            .meta-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .meta-label {
                    font-size: 0.75rem;
                    font-weight: 600;

                    // Light theme
                    color: $tertiary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }

                .meta-value {
                    font-size: 0.875rem;
                    font-weight: 500;

                    // Light theme
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }
            }
        }
    }

    // Card Editor
    .card-editor {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        border-radius: 12px;

        // Light theme
        background-color: $window-background;
        border: 1px solid $border-color;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
            background-color: $dark-window-background;
            border: 1px solid $dark-border-color;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        h3 {
            margin: 0 0 1.25rem 0;
            font-size: 1.25rem;
            font-weight: 600;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        // Card Type Selector
        .card-type-selector {
            margin-bottom: 1.25rem;

            label {
                display: block;
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
                font-weight: 600;

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }

            .type-options {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;

                .type-option {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 0.75rem;
                    border-radius: 8px;
                    min-width: 5rem;
                    border: none;
                    cursor: pointer;
                    @include transition;

                    // Light theme
                    background-color: $secondary-background;
                    color: $secondary-text;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-secondary-background;
                        color: $dark-secondary-text;
                    }

                    i {
                        font-size: 1.25rem;
                        margin-bottom: 0.5rem;
                    }

                    span {
                        font-size: 0.75rem;
                        font-weight: 500;
                    }

                    &:hover {
                        // Light theme
                        background-color: darken($secondary-background, 5%);

                        // Dark theme
                        .theme-dark & {
                            background-color: lighten($dark-secondary-background, 5%);
                        }
                    }

                    &.active {
                        // Light theme
                        background-color: $system-blue;
                        color: white;

                        // Dark theme
                        .theme-dark & {
                            background-color: $system-blue;
                        }

                        &:hover {
                            // Light theme
                            background-color: darken($system-blue, 5%);

                            // Dark theme
                            .theme-dark & {
                                background-color: lighten($system-blue, 5%);
                            }
                        }
                    }
                }
            }
        }

        // Instructions
        .cloze-instructions,
        .multi-step-instructions {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 8px;

            // Light theme
            background-color: rgba($system-blue, 0.05);
            border: 1px solid rgba($system-blue, 0.1);

            // Dark theme
            .theme-dark & {
                background-color: rgba($system-blue, 0.1);
                border: 1px solid rgba($system-blue, 0.15);
            }

            p {
                margin: 0;
                font-size: 0.875rem;

                // Light theme
                color: $secondary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }

                code {
                    padding: 0.125rem 0.25rem;
                    border-radius: 4px;
                    font-family: monospace;

                    // Light theme
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;

                    // Dark theme
                    .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                    }
                }
            }
        }
    }

    // Form Groups
    .form-group {
        margin-bottom: 1.25rem;

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;

            // Light theme
            color: $secondary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        textarea,
        input[type="text"] {
            width: 100%;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 1rem;
            line-height: 1.5;
            resize: vertical;

            // Light theme
            background-color: $secondary-background;
            color: $primary-text;
            border: 1px solid $border-color;

            // Dark theme
            .theme-dark & {
                background-color: $dark-secondary-background;
                color: $dark-primary-text;
                border: 1px solid $dark-border-color;
            }

            &:focus {
                outline: none;
                // Light theme
                border-color: $system-blue;

                // Dark theme
                .theme-dark & {
                    border-color: $system-blue;
                }
            }

            &::placeholder {
                // Light theme
                color: $placeholder-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-placeholder-text;
                }
            }
        }

        textarea {
            min-height: 100px;
        }
    }

    // Form Actions
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;

        button {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.9375rem;
            font-weight: 500;
            border: none;
            cursor: pointer;
            @include transition;
        }

        .cancel-button {
            // Light theme
            background-color: $secondary-background;
            color: $tertiary-text;

            // Dark theme
            .theme-dark & {
                background-color: $dark-secondary-background;
                color: $dark-tertiary-text;
            }

            &:hover {
                // Light theme
                background-color: darken($secondary-background, 5%);

                // Dark theme
                .theme-dark & {
                    background-color: lighten($dark-secondary-background, 5%);
                }
            }
        }

        .save-button,
        .add-button {
            // Light theme
            background-color: $system-blue;
            color: white;

            // Dark theme
            .theme-dark & {
                background-color: $system-blue;
            }

            &:hover:not(:disabled) {
                // Light theme
                background-color: darken($system-blue, 5%);

                // Dark theme
                .theme-dark & {
                    background-color: lighten($system-blue, 5%);
                }
            }

            &:disabled {
                // Light theme
                background-color: $system-light-gray;
                color: $tertiary-text;
                cursor: not-allowed;

                // Dark theme
                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $dark-tertiary-text;
                }
            }
        }
    }

    // Cards List Container
    .cards-list-container {
        margin-bottom: 1.5rem;

        .cards-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;

            h3 {
                margin: 0;
                font-size: 1.25rem;
                font-weight: 600;

                // Light theme
                color: $primary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .cards-filter {
                .filter-select {
                    padding: 0.5rem 1rem;
                    border-radius: 8px;
                    font-size: 0.875rem;
                    @include transition;

                    // Light theme
                    background-color: $secondary-background;
                    color: $primary-text;
                    border: 1px solid $border-color;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-secondary-background;
                        color: $dark-primary-text;
                        border: 1px solid $dark-border-color;
                    }

                    &:focus {
                        outline: none;
                        // Light theme
                        border-color: $system-blue;

                        // Dark theme
                        .theme-dark & {
                            border-color: $system-blue;
                        }
                    }
                }
            }
        }

        .cards-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            gap: 1rem;

            .card-item {
                position: relative;
                padding: 1rem;
                border-radius: 12px;
                cursor: pointer;
                @include transition;

                // Light theme
                background-color: $window-background;
                border: 1px solid $border-color;

                // Dark theme
                .theme-dark & {
                    background-color: $dark-window-background;
                    border: 1px solid $dark-border-color;
                }

                &:hover {
                    transform: translateY(-3px);

                    // Light theme
                    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);

                    // Dark theme
                    .theme-dark & {
                        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
                    }
                }

                &.active {
                    // Light theme
                    border: 2px solid $system-blue;

                    // Dark theme
                    .theme-dark & {
                        border: 2px solid $system-blue;
                    }
                }

                &.difficult {
                    // Light theme
                    border-left: 3px solid $system-red;

                    // Dark theme
                    .theme-dark & {
                        border-left: 3px solid $system-red;
                    }
                }

                // Card types
                &.cloze {
                    // Light theme
                    border-left: 3px solid $system-orange;

                    // Dark theme
                    .theme-dark & {
                        border-left: 3px solid $system-orange;
                    }
                }

                &.image,
                &.audio {
                    // Light theme
                    border-left: 3px solid $system-green;

                    // Dark theme
                    .theme-dark & {
                        border-left: 3px solid $system-green;
                    }
                }

                &.multi-step {
                    // Light theme
                    border-left: 3px solid $system-blue;

                    // Dark theme
                    .theme-dark & {
                        border-left: 3px solid $system-blue;
                    }
                }

                .card-type-indicator {
                    position: absolute;
                    top: 0.75rem;
                    right: 0.75rem;
                    width: 1.5rem;
                    height: 1.5rem;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.75rem;

                    // Light theme
                    background-color: $secondary-background;
                    color: $tertiary-text;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-secondary-background;
                        color: $dark-tertiary-text;
                    }
                }

                .card-preview {
                    height: 120px;
                    border-radius: 8px;
                    padding: 0.75rem;
                    margin-bottom: 0.75rem;
                    overflow: hidden;

                    // Light theme
                    background-color: $secondary-background;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-secondary-background;
                    }

                    .card-preview-front {
                        font-size: 0.9375rem;
                        line-height: 1.5;

                        // Light theme
                        color: $primary-text;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-primary-text;
                        }
                    }
                }

                .card-tags-preview {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0.5rem;
                    margin-bottom: 0.75rem;

                    .tag-preview {
                        padding: 0.125rem 0.375rem;
                        border-radius: 4px;
                        font-size: 0.6875rem;

                        // Light theme
                        background-color: rgba($system-blue, 0.1);
                        color: $system-blue;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($system-blue, 0.2);
                        }
                    }

                    .more-tags {
                        padding: 0.125rem 0.375rem;
                        border-radius: 4px;
                        font-size: 0.6875rem;

                        // Light theme
                        background-color: rgba($system-gray, 0.1);
                        color: $tertiary-text;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($system-gray, 0.2);
                            color: $dark-tertiary-text;
                        }
                    }
                }

                .card-actions {
                    display: flex;
                    justify-content: flex-end;
                    gap: 0.5rem;
                    margin-bottom: 0.75rem;

                    button {
                        width: 1.75rem;
                        height: 1.75rem;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: none;
                        cursor: pointer;
                        @include transition;
                        font-size: 0.75rem;
                    }

                    .edit-button {
                        // Light theme
                        background-color: rgba($system-blue, 0.1);
                        color: $system-blue;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($system-blue, 0.2);
                        }

                        &:hover {
                            // Light theme
                            background-color: rgba($system-blue, 0.2);

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-blue, 0.3);
                            }
                        }
                    }

                    .delete-button {
                        // Light theme
                        background-color: rgba($system-red, 0.1);
                        color: $system-red;

                        // Dark theme
                        .theme-dark & {
                            background-color: rgba($system-red, 0.2);
                        }

                        &:hover {
                            // Light theme
                            background-color: rgba($system-red, 0.2);

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-red, 0.3);
                            }
                        }
                    }
                }

                .card-meta {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .meta-left {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;

                        .card-number {
                            font-size: 0.75rem;
                            font-weight: 500;

                            // Light theme
                            color: $tertiary-text;

                            // Dark theme
                            .theme-dark & {
                                color: $dark-tertiary-text;
                            }
                        }

                        .difficult-tag {
                            display: flex;
                            align-items: center;
                            gap: 0.25rem;
                            font-size: 0.75rem;

                            // Light theme
                            color: $system-red;

                            // Dark theme
                            .theme-dark & {
                                color: $system-red;
                            }

                            i {
                                font-size: 0.75rem;
                            }
                        }
                    }

                    .meta-right {
                        .level-badge {
                            padding: 0.125rem 0.375rem;
                            border-radius: 4px;
                            font-size: 0.6875rem;
                            font-weight: 600;

                            // Light theme
                            background-color: rgba($system-blue, 0.1);
                            color: $system-blue;

                            // Dark theme
                            .theme-dark & {
                                background-color: rgba($system-blue, 0.2);
                            }
                        }
                    }
                }
            }
        }
    }

    // Leitner System Overlay
    .leitner-system-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);

        // Light theme
        background-color: rgba(0, 0, 0, 0.4);

        // Dark theme
        .theme-dark & {
            background-color: rgba(0, 0, 0, 0.6);
        }

        .leitner-system {
            width: 90%;
            height: 90%;
            max-width: 1200px;
            border-radius: 12px;
            overflow: auto;

            // Light theme
            box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);

            // Dark theme
            .theme-dark & {
                box-shadow: 0 12px 28px rgba(0, 0, 0, 0.4);
            }
        }
    }

    // Responsive Adjustments
    @media (max-width: 768px) {
        padding: 1rem;

        .app-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;

            .app-controls {
                width: 100%;
                justify-content: space-between;
            }
        }

        .card-controls {
            .control-row {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        .cards-list {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        }
    }

    @media (max-width: 480px) {
        .cards-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;

            .cards-filter {
                width: 100%;

                .filter-select {
                    width: 100%;
                }
            }
        }

        .cards-list {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;

            button {
                width: 100%;
            }
        }
    }
}