CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        full_name VA<PERSON><PERSON><PERSON>(50),
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        preferences_completed BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

	CREATE TABLE IF NOT EXISTS subjects (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


	CREATE TABLE IF NOT EXISTS files (
    id SERIAL PRIMARY KEY,
    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,  -- Unique identifier for the file
    originalname TEXT NOT NULL,  -- Original name of the file
    filetype TEXT,  -- Type of the file (PDF, DOCX, etc.)
    filepath TEXT NOT NULL,  -- Path to the file stored in the files folder
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE IF NOT EXISTS chapters (
    id SERIAL PRIMARY KEY,
    file_id INTEGER REFERENCES files(id) ON DELETE CASCADE,
    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
    chapter_number VARCHAR(50),  -- Chapter number in "Chapter X" format
    title VARCHAR(255) NOT NULL,  -- Title of the chapter
    content TEXT,  -- Content of the chapter (can be generated or extracted from the file)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE IF NOT EXISTS objectives (
    id SERIAL PRIMARY KEY,
    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
    objective TEXT NOT NULL,  -- Objective that the student should focus on
    completed BOOLEAN DEFAULT FALSE,  -- Whether the objective has been completed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
