@use "variables" as *;
@use "sass:color";

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Document Processor Overlay - macOS style
.document-processor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000000;
  backdrop-filter: blur(5px);

  // Light theme
  background-color: rgba(0, 0, 0, 0.4);

  // Dark theme
  &.theme-dark {
    background-color: rgba(0, 0, 0, 0.6);
  }
}

// Document Processor Container - macOS style
.document-processor-container {
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

  // Light theme
  background-color: white;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid $light-accent-color-2;

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid $dark-accent-color-2;
  }

  // Custom scrollbar for light theme
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 20px;
  }

  // Custom scrollbar for dark theme
  &.theme-dark::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  // Popup Header - macOS style
  .popup-header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;

    // Light theme
    border-bottom: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-accent-color-2;
    }

    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;

      // Light theme
      color: $color-primary;

      // Dark theme
      .theme-dark & {
        color: $color-primary;
      }
    }

    .close-button-popup {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      @include transition;

      // Light theme
      color: $light-secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }

      &:hover {
        // Light theme
        background-color: $light-accent-color-2;
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
          background-color: $dark-accent-color-2;
          color: $dark-primary-text;
        }
      }
    }
  }

  // Popup Content - macOS style
  .popup-content {
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    // Light theme
    background-color: white;

    // Dark theme
    .theme-dark & {
      background-color: $dark-background;
    }

    h3 {
      margin: 20px 0 10px;
      font-size: 1.5rem;
      font-weight: 600;

      // Light theme
      color: $color-primary;

      // Dark theme
      .theme-dark & {
        color: $color-primary;
      }
    }

    .document-name {
      margin: 0 0 20px;
      font-weight: 500;

      // Light theme
      color: $color-primary;

      // Dark theme
      .theme-dark & {
        color: $color-primary;
      }
    }

    // Processing content - macOS style
    .processing-content {
      .progress-indicator {
        position: relative;
        margin-bottom: 20px;

        .progress-label {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 1.2rem;
          font-weight: 600;

          // Light theme
          color: $color-primary;

          // Dark theme
          .theme-dark & {
            color: $color-primary;
          }
        }
      }

      .processing-steps {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin: 30px 0;
        width: 100%;
        max-width: 400px;

        .step {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 12px 16px;
          border-radius: 8px;
          @include transition;

          // Light theme
          background-color: $light-background;
          border: 1px solid $light-accent-color-2;

          // Dark theme
          .theme-dark & {
            background-color: color.adjust($dark-background, $lightness: 5%);
            border: 1px solid $dark-accent-color-2;
          }

          &.active {
            // Light theme
            background-color: color.adjust($color-primary, $alpha: -0.9);
            border-color: $color-primary;
            box-shadow: 0 0 15px rgba($color-primary, 0.2);

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($color-primary, $alpha: -0.85);
              border-color: $color-primary;
              box-shadow: 0 0 15px rgba($color-primary, 0.3);
            }
          }

          &.completed {
            // Light theme
            background-color: color.adjust($color-success, $alpha: -0.9);
            border-color: $color-success;

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($color-success, $alpha: -0.85);
              border-color: $color-success;
            }
          }

          .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            @include flex-center;
            font-weight: 600;
            @include transition;

            // Light theme
            background-color: $light-accent-color-2;
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              background-color: $dark-accent-color-2;
              color: $dark-primary-text;
            }
          }

          &.active .step-number {
            // Light theme
            background-color: $color-primary;
            color: white;
            box-shadow: 0 0 10px rgba($color-primary, 0.5);

            // Dark theme
            .theme-dark & {
              background-color: $color-primary;
              color: white;
              box-shadow: 0 0 10px rgba($color-primary, 0.7);
            }
          }

          &.completed .step-number {
            // Light theme
            background-color: $color-success;
            color: white;
            box-shadow: 0 0 10px rgba($color-success, 0.5);

            // Dark theme
            .theme-dark & {
              background-color: $color-success;
              color: white;
              box-shadow: 0 0 10px rgba($color-success, 0.7);
            }
          }

          .step-text {
            flex: 1;
            text-align: left;

            // Light theme
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }
          }
        }
      }

      .processing-note {
        font-size: 0.9rem;
        max-width: 400px;
        margin: 0 auto;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    // Success content - macOS style
    .success-content {
      .success-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        @include flex-center;
        margin: 0 auto 20px;

        // Light theme
        background-color: color.adjust($color-success, $alpha: -0.9);
        border: 1px solid $color-success;
        box-shadow: 0 0 20px rgba($color-success, 0.3);

        // Dark theme
        .theme-dark & {
          background-color: color.adjust($color-success, $alpha: -0.85);
          border: 1px solid $color-success;
          box-shadow: 0 0 20px rgba($color-success, 0.4);
        }

        svg {
          font-size: 40px;

          // Light theme
          color: $color-success;

          // Dark theme
          .theme-dark & {
            color: $color-success;
          }
        }
      }

      .curriculum-stats {
        display: flex;
        justify-content: space-around;
        gap: 20px;
        margin: 30px 0;
        width: 100%;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          svg {
            font-size: 30px;

            // Light theme
            color: $color-primary;

            // Dark theme
            .theme-dark & {
              color: $color-primary;
            }
          }

          .stat-value {
            font-size: 2rem;
            font-weight: 600;

            // Light theme
            color: $color-primary;

            // Dark theme
            .theme-dark & {
              color: $color-primary;
            }
          }

          .stat-label {
            font-size: 0.9rem;

            // Light theme
            color: $light-secondary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 16px;
        margin-top: 20px;

        .macos-button {
          padding: 12px 16px;
          border-radius: 8px;
          font-size: 0.95rem;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          @include transition;

          &.primary {
            // Light theme
            background-color: $color-primary;
            color: white;
            border: none;

            // Dark theme
            .theme-dark & {
              background-color: $color-primary;
              color: white;
            }

            &:hover {
              transform: translateY(-2px);

              // Light theme
              background-color: color.adjust($color-primary, $lightness: -5%);
              box-shadow: 0 4px 8px rgba($color-primary, 0.2);

              // Dark theme
              .theme-dark & {
                background-color: color.adjust($color-primary, $lightness: -5%);
                box-shadow: 0 4px 8px rgba($color-primary, 0.4);
              }
            }

            &:active {
              transform: translateY(0);
            }
          }

          &.secondary {
            // Light theme
            background-color: $light-background;
            border: 1px solid $light-accent-color-2;
            color: $light-secondary-text;

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($dark-background, $lightness: 5%);
              border: 1px solid $dark-accent-color-2;
              color: $dark-secondary-text;
            }

            &:hover {
              transform: translateY(-2px);

              // Light theme
              background-color: color.adjust($light-background, $lightness: -2%);
              color: $light-primary-text;
              border-color: color.adjust($light-accent-color-2, $lightness: -5%);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

              // Dark theme
              .theme-dark & {
                background-color: color.adjust($dark-background, $lightness: 8%);
                color: $dark-primary-text;
                border-color: color.adjust($dark-accent-color-2, $lightness: 5%);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
              }
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }

    // Error content - macOS style
    .error-content {
      .error-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        @include flex-center;
        margin: 0 auto 20px;

        // Light theme
        background-color: color.adjust($color-error, $alpha: -0.9);
        border: 1px solid $color-error;
        box-shadow: 0 0 20px rgba($color-error, 0.3);

        // Dark theme
        .theme-dark & {
          background-color: color.adjust($color-error, $alpha: -0.85);
          border: 1px solid $color-error;
          box-shadow: 0 0 20px rgba($color-error, 0.4);
        }

        svg {
          font-size: 40px;

          // Light theme
          color: $color-error;

          // Dark theme
          .theme-dark & {
            color: $color-error;
          }
        }
      }

      .error-message {
        margin: 20px 0 30px;
        max-width: 400px;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }

      .action-buttons {
        display: flex;
        gap: 16px;

        .macos-button {
          padding: 12px 16px;
          border-radius: 8px;
          font-size: 0.95rem;
          font-weight: 500;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          @include transition;

          &:first-child {
            // Light theme
            background-color: $color-primary;
            color: white;
            border: none;

            // Dark theme
            .theme-dark & {
              background-color: $color-primary;
              color: white;
            }

            &:hover {
              transform: translateY(-2px);

              // Light theme
              background-color: color.adjust($color-primary, $lightness: -5%);
              box-shadow: 0 4px 8px rgba($color-primary, 0.2);

              // Dark theme
              .theme-dark & {
                background-color: color.adjust($color-primary, $lightness: -5%);
                box-shadow: 0 4px 8px rgba($color-primary, 0.4);
              }
            }

            &:active {
              transform: translateY(0);
            }
          }

          &.secondary {
            // Light theme
            background-color: $light-background;
            border: 1px solid $light-accent-color-2;
            color: $light-secondary-text;

            // Dark theme
            .theme-dark & {
              background-color: color.adjust($dark-background, $lightness: 5%);
              border: 1px solid $dark-accent-color-2;
              color: $dark-secondary-text;
            }

            &:hover {
              transform: translateY(-2px);

              // Light theme
              background-color: color.adjust($light-background, $lightness: -2%);
              color: $light-primary-text;
              border-color: color.adjust($light-accent-color-2, $lightness: -5%);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

              // Dark theme
              .theme-dark & {
                background-color: color.adjust($dark-background, $lightness: 8%);
                color: $dark-primary-text;
                border-color: color.adjust($dark-accent-color-2, $lightness: 5%);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
              }
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }
  }
}

// Message popup - macOS style
.message-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
  backdrop-filter: blur(5px);

  // Light theme
  background-color: rgba(0, 0, 0, 0.4);

  // Dark theme
  &.theme-dark {
    background-color: rgba(0, 0, 0, 0.6);
  }
}

// Message popup container - macOS style
.message-popup-container {
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  overflow: hidden;
  animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

  // Light theme
  background-color: white;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid $light-accent-color-2;

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid $dark-accent-color-2;
  }

  // Popup Header - macOS style
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;

    // Light theme
    border-bottom: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
      border-bottom: 1px solid $dark-accent-color-2;
    }

    h2 {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;

      // Light theme
      color: $color-primary;

      // Dark theme
      .theme-dark & {
        color: $color-primary;
      }
    }

    .close-button-popup {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      @include transition;

      // Light theme
      color: $light-secondary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-secondary-text;
      }

      &:hover {
        // Light theme
        background-color: $light-accent-color-2;
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
          background-color: $dark-accent-color-2;
          color: $dark-primary-text;
        }
      }
    }
  }

  // Popup Content - macOS style
  .popup-content {
    padding: 20px;

    // Light theme
    background-color: white;

    // Dark theme
    .theme-dark & {
      background-color: $dark-background;
    }

    p {
      margin: 0;

      // Light theme
      color: $light-primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }
  }
}