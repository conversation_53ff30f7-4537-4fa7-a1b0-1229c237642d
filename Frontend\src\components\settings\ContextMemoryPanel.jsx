import React from 'react';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import '../../styles/SettingsPanels.scss';

const ContextMemoryPanel = ({ settings }) => {
  const { contextMemory, setContextMemory, isDarkMode } = settings;
  
  const memoryOptions = [
    { id: 'short', label: 'Short-term', duration: '1-2 sessions', description: 'AI remembers only recent conversations' },
    { id: 'medium', label: 'Medium-term', duration: '1-2 weeks', description: 'AI maintains context across multiple sessions' },
    { id: 'long', label: 'Long-term', duration: 'Entire semester', description: 'AI retains detailed memory of all interactions' }
  ];
  
  return (
    <div className={`detail-panel context-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Context Memory Duration</h2>
        <p className="panel-description">
          Control how long the AI remembers details from your previous conversations.
        </p>
      </div>
      
      <div className="settings-group memory-options">
        {memoryOptions.map(option => (
          <div 
            key={option.id}
            className={`memory-option ${contextMemory === option.id ? 'active' : ''}`}
            onClick={() => setContextMemory(option.id)}
          >
            <div className="memory-option-header">
              <div className="memory-icon">
                <AccessTimeIcon />
              </div>
              <div className="memory-label">
                <h3>{option.label}</h3>
                <span className="memory-duration">{option.duration}</span>
              </div>
              <div className="memory-selector"></div>
            </div>
            <p className="memory-description">{option.description}</p>
          </div>
        ))}
      </div>
      
      <div className="memory-visualization">
        <div className="memory-timeline">
          <div className="timeline-track">
            <div 
              className="memory-indicator"
              style={{
                width: contextMemory === 'short' ? '30%' : 
                       contextMemory === 'medium' ? '60%' : '90%'
              }}
            ></div>
          </div>
          <div className="timeline-markers">
            <div className="timeline-marker">
              <div className="marker-dot"></div>
              <span>Session</span>
            </div>
            <div className="timeline-marker">
              <div className="marker-dot"></div>
              <span>Week</span>
            </div>
            <div className="timeline-marker">
              <div className="marker-dot"></div>
              <span>Semester</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="info-box">
        <p>
          Context memory affects how much of your conversation history the AI considers 
          when responding to you. Longer memory provides more personalized responses but 
          may use more system resources.
        </p>
      </div>
    </div>
  );
};

export default ContextMemoryPanel;
