@use "variables" as *;
@use "sass:color";

.profile-dropdown-container {
  position: relative;
  margin-left: 15px;

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: $color-primary;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all $transition-speed;
    user-select: none;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba($color-primary, 0.3);
    }
  }

  .dropdown-menu {
    position: absolute;
    right: 0;
    top: 50px;
    width: 300px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    animation: fadeIn $transition-speed ease-out;

    .profile-header {
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 15px;

      .avatar-large {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: $color-primary;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .user-info {
        text-align: left;

        h3 {
          margin: 0;
          font-size: 1.1rem;
          color: $light-primary-text;
          text-transform: capitalize;
        }

        p {
          margin: 3px 0;
          font-size: 0.85rem;
          //color: $secondary-color;
        }

        .user-email {
          font-size: 0.8rem;
          color: $light-primary-text;
          opacity: 0.7;
        }
      }
    }

    .dropdown-divider {
      height: 1px;
      background-color: $light-accent-color-2;
      margin: 5px 0;
    }

    .menu-section {
      padding: 10px 0;

      h4 {
        margin: 0 20px 10px;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        //color: $secondary-color;
        text-align: left;
      }

      .menu-item {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 10px 20px;
        background: none;
        border: none;
        text-align: left;
        cursor: pointer;
        transition: all $transition-speed;
        color: $light-primary-text;
        font-weight: normal;

        .menu-icon {
          margin-right: 15px;
          //color: $secondary-color;
          font-size: 1.2rem;
        }

        span {
          font-size: 0.95rem;
        }

        &:hover {
          background-color: $light-accent-color-2;
          padding-left: 22px;
        }
      }
    }

    .logout-button {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 12px 20px;
      background: none;
      border: none;
      text-align: left;
      cursor: pointer;
      transition: all $transition-speed;
      color: $light-primary-text;
      font-weight: 500;

      .menu-icon {
        margin-right: 15px;
        //color: $secondary-color;
        font-size: 1.2rem;
      }

      &:hover {
        background-color: rgba($color-primary, 0.1);
        color: $color-primary;
      }
    }
  }

  &.theme-dark {
    .dropdown-menu {
      background-color: $dark-background;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

      .profile-header {
        .user-info {
          h3 {
            color: $dark-primary-text;
          }

          .user-email {
            color: $dark-primary-text;
            opacity: 0.7;
          }
        }
      }

      .dropdown-divider {
        background-color: $dark-accent-color-2;
      }

      .menu-section {
        h4 {
          color: $color-secondary;
        }

        .menu-item {
          color: $dark-primary-text;

          .menu-icon {
            color: $color-secondary;
          }

          &:hover {
            background-color: $dark-accent-color-2;
          }
        }
      }

      .logout-button {
        color: $dark-primary-text;

        &:hover {
          background-color: rgba($color-primary, 0.2);
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}