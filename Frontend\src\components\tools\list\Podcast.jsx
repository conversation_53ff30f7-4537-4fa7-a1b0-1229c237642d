import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';


const Podcast = ({ tool, subject }) => {
    const [episodes, setEpisodes] = useState([]);
    const [newEpisode, setNewEpisode] = useState({
        title: '',
        url: '',
        notes: '',
        tags: []
    });
    const [currentTag, setCurrentTag] = useState('');
    const [currentEpisode, setCurrentEpisode] = useState(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(0.8);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const audioRef = useRef(null);

    // Load episodes from localStorage
    useEffect(() => {
        const loadEpisodes = async () => {
            try {
                setLoading(true);
                const savedEpisodes = localStorage.getItem(`${subject}-podcast-episodes`);
                const parsedEpisodes = savedEpisodes ? JSON.parse(savedEpisodes) : [];

                if (Array.isArray(parsedEpisodes)) {
                    setEpisodes(parsedEpisodes);
                    setError(null);
                } else {
                    throw new Error('Invalid episode data format');
                }
            } catch (err) {
                console.error('Error loading episodes:', err);
                setError('Failed to load episodes. Starting with empty list.');
                setEpisodes([]);
            } finally {
                setLoading(false);
            }
        };

        loadEpisodes();
    }, [subject]);

    // Save episodes to localStorage
    useEffect(() => {
        try {
            localStorage.setItem(`${subject}-podcast-episodes`, JSON.stringify(episodes));
        } catch (err) {
            console.error('Error saving episodes:', err);
            setError('Failed to save episodes to storage.');
        }
    }, [episodes, subject]);

    // Handle audio events
    useEffect(() => {
        const audio = audioRef.current;
        if (!audio) return;

        const handleAudioEvents = () => {
            audio.addEventListener('timeupdate', handleTimeUpdate);
            audio.addEventListener('loadedmetadata', handleLoadedMetadata);
            audio.addEventListener('ended', () => setIsPlaying(false));
            audio.addEventListener('error', () => {
                setError('Error playing audio. Please check the URL.');
                setIsPlaying(false);
            });
        };

        handleAudioEvents();
        return () => {
            audio.removeEventListener('timeupdate', handleTimeUpdate);
            audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
            audio.removeEventListener('ended', () => setIsPlaying(false));
            audio.removeEventListener('error', () => setIsPlaying(false));
        };
    }, [currentEpisode]);

    const addEpisode = () => {
        if (!newEpisode.title.trim() || !newEpisode.url.trim()) {
            setError('Title and URL are required');
            return;
        }

        try {
            const episode = {
                ...newEpisode,
                id: Date.now(),
                subject,
                createdAt: new Date().toISOString(),
                listened: false
            };

            setEpisodes(prev => [...prev, episode]);
            setNewEpisode({ title: '', url: '', notes: '', tags: [] });
            setError(null);
        } catch (err) {
            console.error('Error adding episode:', err);
            setError('Failed to add episode');
        }
    };

    const addTag = () => {
        if (currentTag.trim() && !newEpisode.tags.includes(currentTag)) {
            setNewEpisode(prev => ({
                ...prev,
                tags: [...prev.tags, currentTag]
            }));
            setCurrentTag('');
        }
    };

    const removeTag = (tagToRemove) => {
        setNewEpisode(prev => ({
            ...prev,
            tags: prev.tags.filter(tag => tag !== tagToRemove)
        }));
    };

    const playEpisode = (episode) => {
        // Mark as listened if it's the first time playing
        if (!episode.listened) {
            setEpisodes(prev => prev.map(ep =>
                ep.id === episode.id ? { ...ep, listened: true } : ep
            ));
        }

        setCurrentEpisode(episode);
        setIsPlaying(true);
        setError(null);

        // Small delay to ensure audio element is updated
        setTimeout(() => {
            audioRef.current.play().catch(err => {
                console.error('Playback failed:', err);
                setError('Playback failed. Please check the audio URL.');
                setIsPlaying(false);
            });
        }, 100);
    };

    const togglePlayPause = () => {
        if (!currentEpisode) return;

        if (isPlaying) {
            audioRef.current.pause();
        } else {
            audioRef.current.play().catch(err => {
                console.error('Playback failed:', err);
                setError('Playback failed. Please check the audio URL.');
            });
        }
        setIsPlaying(!isPlaying);
    };

    const handleTimeUpdate = () => {
        setCurrentTime(audioRef.current.currentTime);
    };

    const handleLoadedMetadata = () => {
        setDuration(audioRef.current.duration);
        setVolume(audioRef.current.volume);
    };

    const handleSeek = (e) => {
        const seekTime = parseFloat(e.target.value);
        audioRef.current.currentTime = seekTime;
        setCurrentTime(seekTime);
    };

    const handleVolumeChange = (e) => {
        const newVolume = parseFloat(e.target.value);
        audioRef.current.volume = newVolume;
        setVolume(newVolume);
    };

    const formatTime = (time) => {
        if (isNaN(time)) return '0:00';

        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    };

    const removeEpisode = (id) => {
        if (window.confirm('Are you sure you want to delete this episode?')) {
            setEpisodes(prev => prev.filter(episode => episode.id !== id));

            if (currentEpisode && currentEpisode.id === id) {
                audioRef.current.pause();
                setCurrentEpisode(null);
                setIsPlaying(false);
            }
        }
    };

    const resetEpisodes = () => {
        if (window.confirm('Are you sure you want to reset all episodes?')) {
            setEpisodes([]);
            if (currentEpisode) {
                audioRef.current.pause();
                setCurrentEpisode(null);
                setIsPlaying(false);
            }
        }
    };

    const exportEpisodes = () => {
        try {
            const data = JSON.stringify(episodes, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${subject}-podcast-episodes-${new Date().toISOString().slice(0,10)}.json`;
            link.click();
            URL.revokeObjectURL(url);
        } catch (err) {
            console.error('Error exporting episodes:', err);
            setError('Failed to export episodes');
        }
    };

    const importEpisodes = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const importedEpisodes = JSON.parse(event.target.result);
                if (!Array.isArray(importedEpisodes)) {
                    throw new Error('Invalid file format - expected array of episodes');
                }

                setEpisodes(importedEpisodes);
                setError(null);
                e.target.value = ''; // Reset file input
            } catch (err) {
                console.error('Error importing episodes:', err);
                setError('Failed to import episodes. Invalid file format.');
                e.target.value = ''; // Reset file input
            }
        };
        reader.onerror = () => {
            setError('Error reading file');
            e.target.value = ''; // Reset file input
        };
        reader.readAsText(file);
    };

    const filteredEpisodes = episodes
        .filter(ep => ep.subject === subject)
        .filter(ep =>
            ep.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            ep.notes.toLowerCase().includes(searchTerm.toLowerCase()) ||
            ep.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
        );

    return (
        <div className="podcast-app">
            <header className="app-header">
                <h1>{subject} Podcast Episodes</h1>
                <div className="app-controls">
                    <button
                        className="icon-button"
                        onClick={resetEpisodes}
                        title="Reset all episodes"
                    >
                        <i className="fas fa-trash-alt"></i>
                    </button>
                    <button
                        className="icon-button"
                        onClick={exportEpisodes}
                        title="Export episodes"
                    >
                        <i className="fas fa-file-export"></i>
                    </button>
                    <label className="icon-button" title="Import episodes">
                        <i className="fas fa-file-import"></i>
                        <input
                            type="file"
                            accept=".json"
                            onChange={importEpisodes}
                            style={{ display: 'none' }}
                        />
                    </label>
                </div>
            </header>

            {error && (
                <div className="error-message">
                    <i className="fas fa-exclamation-triangle"></i> {error}
                </div>
            )}

            {loading ? (
                <div className="loading-state">
                    <i className="fas fa-spinner fa-spin"></i> Loading episodes...
                </div>
            ) : (
                <div className="podcast-container">
                    <div className="episodes-panel">
                        <div className="search-bar">
                            <input
                                type="text"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Search episodes..."
                            />
                            <i className="fas fa-search"></i>
                        </div>

                        <div className="episodes-list">
                            <h3>Episodes ({filteredEpisodes.length})</h3>

                            {filteredEpisodes.length > 0 ? (
                                <ul>
                                    <AnimatePresence>
                                        {filteredEpisodes.map(episode => (
                                            <motion.li
                                                key={episode.id}
                                                initial={{ opacity: 0, y: 10 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, x: -50 }}
                                                transition={{ duration: 0.2 }}
                                                className={`
                                                    ${currentEpisode?.id === episode.id ? 'active' : ''}
                                                    ${episode.listened ? 'listened' : ''}
                                                `}
                                            >
                                                <div
                                                    className="episode-info"
                                                    onClick={() => playEpisode(episode)}
                                                >
                                                    <h4>{episode.title}</h4>
                                                    <div className="episode-meta">
                                                        <span className="date">
                                                            {new Date(episode.createdAt).toLocaleDateString()}
                                                        </span>
                                                        {episode.tags.length > 0 && (
                                                            <div className="tags">
                                                                {episode.tags.slice(0, 2).map(tag => (
                                                                    <span key={tag} className="tag">{tag}</span>
                                                                ))}
                                                                {episode.tags.length > 2 && (
                                                                    <span className="more-tags">+{episode.tags.length - 2}</span>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="episode-actions">
                                                    <button
                                                        className="play-button"
                                                        onClick={() => playEpisode(episode)}
                                                    >
                                                        {currentEpisode?.id === episode.id && isPlaying ? (
                                                            <i className="fas fa-pause"></i>
                                                        ) : (
                                                            <i className="fas fa-play"></i>
                                                        )}
                                                    </button>
                                                    <button
                                                        className="delete-button"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            removeEpisode(episode.id);
                                                        }}
                                                    >
                                                        <i className="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </motion.li>
                                        ))}
                                    </AnimatePresence>
                                </ul>
                            ) : (
                                <div className="empty-state">
                                    <i className="fas fa-podcast"></i>
                                    <p>No episodes found</p>
                                    {searchTerm && (
                                        <button
                                            className="clear-search"
                                            onClick={() => setSearchTerm('')}
                                        >
                                            Clear search
                                        </button>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="player-panel">
                        {currentEpisode ? (
                            <>
                                <div className="player-card">
                                    <div className="player-header">
                                        <h3>{currentEpisode.title}</h3>
                                        <div className="player-meta">
                                            <span className="date">
                                                {new Date(currentEpisode.createdAt).toLocaleDateString()}
                                            </span>
                                            {currentEpisode.tags.length > 0 && (
                                                <div className="tags">
                                                    {currentEpisode.tags.map(tag => (
                                                        <span key={tag} className="tag">{tag}</span>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <audio
                                        ref={audioRef}
                                        src={currentEpisode.url}
                                        preload="metadata"
                                    />

                                    <div className="player-controls">
                                        <div className="main-controls">
                                            <button
                                                className="play-pause-button"
                                                onClick={togglePlayPause}
                                            >
                                                {isPlaying ? (
                                                    <i className="fas fa-pause"></i>
                                                ) : (
                                                    <i className="fas fa-play"></i>
                                                )}
                                            </button>
                                            <div className="time-display">
                                                <span>{formatTime(currentTime)}</span>
                                                <span>/</span>
                                                <span>{formatTime(duration)}</span>
                                            </div>
                                        </div>

                                        <input
                                            type="range"
                                            className="progress-bar"
                                            min="0"
                                            max={duration || 100}
                                            value={currentTime}
                                            onChange={handleSeek}
                                            style={{
                                                background: `linear-gradient(to right, #4361ee ${(currentTime / (duration || 100)) * 100}%, #e9ecef ${(currentTime / (duration || 100)) * 100}%)`
                                            }}
                                        />

                                        <div className="volume-control">
                                            <i className="fas fa-volume-down"></i>
                                            <input
                                                type="range"
                                                min="0"
                                                max="1"
                                                step="0.01"
                                                value={volume}
                                                onChange={handleVolumeChange}
                                            />
                                            <i className="fas fa-volume-up"></i>
                                        </div>
                                    </div>
                                </div>

                                <div className="episode-notes">
                                    <h4>Episode Notes</h4>
                                    {currentEpisode.notes ? (
                                        <p>{currentEpisode.notes}</p>
                                    ) : (
                                        <p className="no-notes">No notes available for this episode</p>
                                    )}
                                </div>
                            </>
                        ) : (
                            <div className="no-episode-selected">
                                <i className="fas fa-headphones"></i>
                                <p>Select an episode to play</p>
                            </div>
                        )}
                    </div>
                </div>
            )}

            <div className="add-episode-panel">
                <h3>Add New Episode</h3>
                <div className="form-grid">
                    <div className="form-group">
                        <label>Title*</label>
                        <input
                            type="text"
                            value={newEpisode.title}
                            onChange={(e) => setNewEpisode({ ...newEpisode, title: e.target.value })}
                            placeholder="Enter episode title"
                        />
                    </div>

                    <div className="form-group">
                        <label>Audio URL*</label>
                        <input
                            type="text"
                            value={newEpisode.url}
                            onChange={(e) => setNewEpisode({ ...newEpisode, url: e.target.value })}
                            placeholder="Enter audio URL"
                        />
                    </div>

                    <div className="form-group">
                        <label>Notes</label>
                        <textarea
                            value={newEpisode.notes}
                            onChange={(e) => setNewEpisode({ ...newEpisode, notes: e.target.value })}
                            placeholder="Enter episode notes"
                            rows={3}
                        />
                    </div>

                    <div className="form-group tags-group">
                        <label>Tags</label>
                        <div className="tags-input">
                            <input
                                type="text"
                                value={currentTag}
                                onChange={(e) => setCurrentTag(e.target.value)}
                                placeholder="Add tag"
                                onKeyPress={(e) => e.key === 'Enter' && addTag()}
                            />
                            <button
                                className="add-tag-button"
                                onClick={addTag}
                                disabled={!currentTag.trim()}
                            >
                                <i className="fas fa-plus"></i>
                            </button>
                        </div>
                        <div className="tags-list">
                            {newEpisode.tags.map(tag => (
                                <span key={tag} className="tag">
                                    {tag}
                                    <button
                                        className="remove-tag"
                                        onClick={() => removeTag(tag)}
                                    >
                                        <i className="fas fa-times"></i>
                                    </button>
                                </span>
                            ))}
                        </div>
                    </div>
                </div>

                <button
                    className="add-button"
                    onClick={addEpisode}
                    disabled={!newEpisode.title.trim() || !newEpisode.url.trim()}
                >
                    <i className="fas fa-plus"></i> Add Episode
                </button>
            </div>
        </div>
    );
};

export default Podcast;