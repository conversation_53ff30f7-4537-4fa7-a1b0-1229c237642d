@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

$primary-blue: #007aff;
// Variables
$primary-blue: #007aff;
$red: #ff615f;
$yellow: #ffbc49;
$green: #28c840;
$text-dark: #1c2526;
$text-gray: #4a5759;
$text-muted: #6b7280;
$shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
$shadow-hover: 0 8px 16px rgba(0, 0, 0, 0.15);
$border-color: rgba(0, 0, 0, 0.1);

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin hover-lift {
  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

// Dashboard Container - macOS style
.dashboard-container {
  height: calc(100dvh - 6rem);
  overflow: hidden;
  padding: 0;

  // Light theme
  background-color: none;

  // Dark theme
  &.theme-dark {
    background-color: $dark-background;
  }

  // Dashboard Grid Layout
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  gap: 12px;
  grid-auto-flow: row;
  grid-template-areas: "Objectives Objectives Objectives eisenhower eisenhower eisenhower kanban kanban kanban"
  "Objectives Objectives Objectives eisenhower eisenhower eisenhower kanban kanban kanban"
  "Objectives Objectives Objectives eisenhower eisenhower eisenhower kanban kanban kanban"
  "Objectives Objectives Objectives masteryTracker masteryTracker masteryTracker retrospective retrospective retrospective"
  "Objectives Objectives Objectives masteryTracker masteryTracker masteryTracker retrospective retrospective retrospective"
  "Objectives Objectives Objectives masteryTracker masteryTracker masteryTracker retrospective retrospective retrospective"
  "progress progress streak streak streak calender calender calender calender"
  "progress progress streak streak streak calender calender calender calender"
  "progress progress streak streak streak calender calender calender calender";

  // Grid areas
  .Objectives {
    grid-area: Objectives;
  }

  // Objectives Section - macOS style
  .Objectives {
    .daily-objectives {
      border-radius: 16px;
      margin: 0;
      max-width: 600px;
      animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
      z-index: 1;
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      position: relative;

      // Light theme
      background-color: $window-background;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid $border-color;

      // Dark theme
      .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        border: 1px solid $dark-border-color;
      }

      .objectives-content {
        flex: 1;
        overflow-y: auto;
        padding: 0.75rem;

        // macOS-style scrollbar
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: $system-gray;
          border-radius: 4px;
          border: 2px solid transparent;
          background-clip: content-box;

          .theme-dark & {
            background-color: $system-dark-gray;
          }
        }

        &::-webkit-scrollbar-corner {
          background: transparent;
        }
      }

      .objectives-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        width: 100%;
        gap: 1rem;
        padding: 1rem 1rem 0.75rem;
        border-bottom: 1px solid $separator-color;
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        background-color: rgba($window-background, 0.95);

        h2 {
          padding: 0;
          margin: 0;
          height: fit-content !important;
        }

        // Dark theme
        .theme-dark & {
          border-bottom: 1px solid $dark-separator-color;
          background-color: rgba($dark-window-background, 0.95);
        }

        .objectives-number {
          display: flex;
          align-items: center;
          gap: 1rem;

          span {
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            letter-spacing: 0.2px;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            // Light theme
            background-color: $secondary-background;
            color: $primary-text;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

            // Dark theme
            .theme-dark & {
              background-color: $dark-secondary-background;
              color: $dark-primary-text;
              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
            }

            &.completed {
              background-color: rgba($system-green, 0.15);
              color: $system-green;

              // Dark theme
              .theme-dark & {
                background-color: rgba($system-green, 0.2);
                color: color.adjust($system-green, $lightness: 10%);
              }
            }

            &.pending {
              background-color: rgba($system-orange, 0.15);
              color: $system-orange;

              // Dark theme
              .theme-dark & {
                background-color: rgba($system-orange, 0.2);
                color: color.adjust($system-orange, $lightness: 10%);
              }
            }
          }
        }
      }
    }

    h2 {
      font-size: 1.2rem;
      font-weight: 600;
      margin: auto 0;
      //padding-bottom: 10px;
      width: fit-content;
      letter-spacing: -0.2px;

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .subject-section {
      padding-bottom: 0.5rem;
      gap: 0;
      margin-bottom: 1.2rem;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border: 1px solid $border-color;
      height: auto;

      // Light theme
      background-color: $secondary-background;

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        border: 1px solid $dark-border-color;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      h3 {
        font-size: 1rem;
        font-weight: 600;
        text-align: left;
        padding: 0.5rem 1.2rem;
        margin: 0;
        border-bottom: 1px solid $separator-color;
        background-color: rgba($window-background, 0.7);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 12px;

        // Light theme
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
          border-bottom: 1px solid $dark-separator-color;
          background-color: rgba($dark-window-background, 0.7);
        }
      }

      h4 {
        font-size: 1rem;
        font-weight: 600;
        margin: 15px 0 10px;
        padding: 0 1.2rem;

        // Light theme
        color: $system-green;

        // Dark theme
        .theme-dark & {
          color: color.adjust($system-green, $lightness: 5%);
        }
      }

      .no-objectives {
        //padding: 1rem 1.2rem;
        text-align: center;
        font-style: italic;
        margin: 0.5rem;
        border-radius: 8px;

        // Light theme
        background-color: $window-background;
        color: $tertiary-text;
        border: 1px dashed $border-color;

        // Dark theme
        .theme-dark & {
          background-color: $dark-window-background;
          color: $dark-tertiary-text;
          border: 1px dashed $dark-border-color;
        }
      }

      p {
        padding: 0 1.2rem;
        margin: 0.5rem 0;
        font-size: 0.95rem;
        line-height: 1.5;

        // Light theme
        color: $secondary-text !important;

        // Dark theme
        .theme-dark & {
          color: $dark-secondary-text !important;
        }
      }
    }

    .objective {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem;
      text-align: left;
      margin: 0.4rem 0;
      @include transition(all);
      border-radius: 8px;
      margin-left: 0.5rem;
      margin-right: 0.5rem;

      // Light theme
      background-color: $window-background;
      color: $primary-text;
      border: 1px solid $border-color;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);

      // Dark theme
      .theme-dark & {
        background-color: $dark-window-background;
        color: $dark-primary-text;
        border: 1px solid $dark-border-color;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      &:hover {
        // Light theme
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        transform: translateY(-1px);

        // Dark theme
        .theme-dark & {
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }
      }

      span {
        font-size: 0.875rem;
        flex-grow: 1;
        text-decoration: none;
        line-height: 1.4;
        font-weight: 500;
        width: 90%;

        span {
          padding: 0.2rem 0.6rem;
          border-radius: 20px;
          margin-right: .5rem;
          font-size: 0.8rem;
          font-weight: 600;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: fit-content;

          // Light theme
          background-color: rgba($system-blue, 0.1);
          color: $system-blue;

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-blue, 0.15);
            color: color.adjust($system-blue, $lightness: 10%);
          }
        }
      }

      .toggle {
        width: 44px !important;
        height: 26px;
        background: $system-red;
        border-radius: 13px;
        position: relative;
        cursor: pointer;
        @include transition(all);
        box-shadow: 0 1px 3px rgba($system-red, 0.3);
        margin-left: 10px;

        &:before {
          content: '';
          position: absolute;
          top: 3px;
          left: 3px;
          width: 20px;
          height: 20px;
          background: $window-background;
          border-radius: 50%;
          transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1), box-shadow 0.3s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
        }

        &:hover {
          box-shadow: 0 2px 5px rgba($system-red, 0.4);
          transform: scale(1.02);

          &:before {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
          }
        }

        &.done {
          background: $system-green;
          box-shadow: 0 1px 3px rgba($system-green, 0.3);

          &:hover {
            box-shadow: 0 2px 5px rgba($system-green, 0.4);

            &:before {
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            }
          }

          &:before {
            transform: translateX(18px);
          }

          +span {
            text-decoration: line-through;

            // Light theme
            color: $tertiary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-tertiary-text;
            }
          }
        }
      }
    }

    .no-subjects,
    .no-objectives {
      font-size: 1rem;
      text-align: center;
      //padding: 2rem 1.5rem;
      border-radius: 12px;
      font-style: italic;
      margin: 1rem 0;
      border: 1px dashed $border-color;
      display: flex;
      flex-direction: column;
      align-items: center;
      //justify-content: center;
      min-height: 200px;

      h2 {
        padding: 0 !important;
        margin: 0 !important;
        height: fit-content;
      }

      // Light theme
      background-color: $secondary-background;
      color: $tertiary-text;

      // Dark theme
      .theme-dark & {
        background-color: $dark-secondary-background;
        color: $dark-tertiary-text;
        border: 1px dashed $dark-border-color;
      }

      &::before {
        content: '📅';
        font-size: 2rem;
        margin-bottom: 1rem;
        opacity: 0.7;
      }
    }

    .error {
      font-size: 1rem;
      text-align: center;
      padding: 1.5rem;
      margin: 1rem 0;
      border-radius: 12px;
      border: 1px solid rgba($system-red, 0.3);
      background-color: rgba($system-red, 0.05);

      // Light theme
      color: $system-red;

      // Dark theme
      .theme-dark & {
        color: color.adjust($system-red, $lightness: 10%);
        background-color: rgba($system-red, 0.1);
        border: 1px solid rgba($system-red, 0.4);
      }
    }
  }

  // Animations
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }













































  // Eisenhower Section - macOS style
  .eisenhower {
    grid-area: eisenhower;
    border-radius: 16px;
    padding: 0;
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    opacity: 1;
    /* Changed from 0 to 1 to ensure visibility */
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    // Light theme
    background-color: none;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid $border-color;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      border: 1px solid $dark-border-color;
    }
  }

























  .kanban {
    grid-area: kanban;
    border-radius: 16px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  // Kanban specific variables
  $red-glow: rgba($system-red, 0.6);
  $orange-glow: rgba($system-orange, 0.55);
  $green-glow: rgba($system-green, 0.25);

  // Kanban Card
  .kanban-card {
    // Light theme
    background-color: $window-background;
    border: 1px solid $border-color;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    z-index: 1;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      border: 1px solid $dark-border-color;
    }

    border-radius: 16px;
    width: 100%;
    height: 100%;
    padding: .8rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);

      // Dark theme
      .theme-dark & {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
      }
    }

    &:focus {
      outline: 2px solid $system-blue;
      outline-offset: 2px;
    }

    .card-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: .5rem;
      text-align: center;
      padding: 0 0 .5rem 0;
      width: 100%;
      position: relative;
      letter-spacing: -0.2px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 25%;
        right: 25%;
        height: 1px;

        // Light theme
        background-color: $separator-color;

        // Dark theme
        .theme-dark & {
          background-color: $dark-separator-color;
        }
      }

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .task-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: 1fr;
      gap: 16px;
      width: 100%;
      height: 70%;
      margin-top: 0.5rem;

      .task-item {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        border-radius: 14px;
        position: relative;
        overflow: hidden;
        padding: 1.2rem 1rem;
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid transparent;

        // Dark theme
        .theme-dark & {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);

          // Dark theme
          .theme-dark & {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
          }

          .count-number {
            transform: scale(1.05);
          }
        }

        &.todo-column {
          background-color: rgba($system-red, 0.9);
          border-color: $system-red;

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-red, 0.8);
          }

          &::before {
            background: linear-gradient(135deg, $system-red, color.adjust($system-red, $lightness: 10%));
          }
        }

        &.inprogress-column {
          background-color: rgba($system-orange, 0.9);
          border-color: $system-orange;

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-orange, 0.8);
          }

          &::before {
            background: linear-gradient(135deg, $system-orange, $system-yellow);
          }
        }

        &.done-column {
          background-color: rgba($system-green, 0.9);
          border-color: $system-green;

          // Dark theme
          .theme-dark & {
            background-color: rgba($system-green, 0.8);
          }

          &::before {
            background: linear-gradient(135deg, $system-green, color.adjust($system-green, $lightness: 10%));
          }
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          opacity: 0.8;
        }

        .column-title {
          font-size: 1rem;
          font-weight: 600;
          color: white;
          margin: 0 0 1rem 0;
          padding: 0;
          text-align: center;
          letter-spacing: 0.5px;
          text-transform: uppercase;
        }

        .count-number {
          font-size: 2.5rem;
          font-weight: 700;
          color: white;
          margin: 0;
          padding: 0;
          text-align: center;
          transition: transform 0.2s ease;
        }
      }
    }
  }

  // Modal Overlay - macOS style
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Dark theme
    .theme-dark & {
      background: rgba(0, 0, 0, 0.5);
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }
  }

  // Modal Window - macOS style
  .modal-window {
    background-color: $window-background;
    border-radius: 16px;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid $border-color;
    width: 900px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    @include ios-autohide-scrollbar;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
      border: 1px solid $dark-border-color;
    }

    @keyframes slideIn {
      from {
        transform: translateY(20px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .modal-header {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      position: sticky;
      top: 0;
      z-index: 10;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);

      // Light theme
      border-bottom: 1px solid $separator-color;
      background-color: rgba($window-background, 0.8);

      // Dark theme
      .theme-dark & {
        border-bottom: 1px solid $dark-separator-color;
        background-color: rgba($dark-window-background, 0.8);
      }

      .traffic-lights {
        display: flex;
        gap: 8px;

        .traffic-light {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: none;
          cursor: pointer;
          @include transition(all);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

          &.red {
            background: $system-red;
          }

          &.yellow {
            background: $system-orange;
          }

          &.green {
            background: $system-green;
          }

          &:hover {
            transform: scale(1.15);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: scale(0.95);
          }

          &:disabled {
            opacity: 0.4;
            cursor: default;
          }
        }
      }

      .modal-title {
        font-size: 1.3rem;
        font-weight: 600;
        flex: 1;
        text-align: center;
        margin: 0;
        letter-spacing: -0.2px;

        // Light theme
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
        }
      }
    }

    .modal-content {
      padding: 24px;
      @include ios-autohide-scrollbar;

      .kanban-columns {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;

        .kanban-column {
          padding: 20px;
          border-radius: 14px;
          min-height: 200px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          border: 1px solid $border-color;

          // Dark theme
          .theme-dark & {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            border: 1px solid $dark-border-color;
          }

          &.todo {
            background: rgba($system-red, 0.1);
            border-left: 4px solid $system-red;

            // Dark theme
            .theme-dark & {
              background: rgba($system-red, 0.15);
            }
          }

          &.inprogress {
            background: rgba($system-orange, 0.1);
            border-left: 4px solid $system-orange;

            // Dark theme
            .theme-dark & {
              background: rgba($system-orange, 0.15);
            }
          }

          &.done {
            background: rgba($system-green, 0.1);
            border-left: 4px solid $system-green;

            // Dark theme
            .theme-dark & {
              background: rgba($system-green, 0.15);
            }
          }

          h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0 0 16px;
            padding-bottom: 10px;
            border-bottom: 1px solid $separator-color;
            letter-spacing: -0.2px;

            // Dark theme
            .theme-dark & {
              border-bottom: 1px solid $dark-separator-color;
            }

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }

            &.todo-title {
              color: $system-red;
            }

            &.inprogress-title {
              color: $system-orange;
            }

            &.done-title {
              color: $system-green;
            }
          }

          p {
            font-size: 0.95rem;
            line-height: 1.4;
            margin: 0 0 16px;

            // Light theme
            color: $secondary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .task-list {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              font-size: 0.95rem;
              margin-bottom: 10px;
              padding: 12px;
              border-radius: 8px;
              @include transition(all);

              // Light theme
              background-color: $window-background;
              color: $primary-text;
              border: 1px solid $border-color;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);

              // Dark theme
              .theme-dark & {
                background-color: $dark-window-background;
                color: $dark-primary-text;
                border: 1px solid $dark-border-color;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              }

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);

                // Dark theme
                .theme-dark & {
                  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
                }
              }

              .due-date {
                font-size: 0.8rem;
                margin-left: 8px;
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: 500;

                // Light theme
                background-color: $secondary-background;
                color: $tertiary-text;

                // Dark theme
                .theme-dark & {
                  background-color: $dark-secondary-background;
                  color: $dark-tertiary-text;
                }
              }
            }
          }
        }
      }
    }

    .modal-footer {
      padding: 16px 20px;
      border-top: 1px solid $separator-color;
      text-align: right;
      position: sticky;
      bottom: 0;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);

      // Light theme
      background-color: rgba($window-background, 0.8);

      // Dark theme
      .theme-dark & {
        border-top: 1px solid $dark-separator-color;
        background-color: rgba($dark-window-background, 0.8);
      }

      .close-button {
        background: $system-blue;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.2px;

        &:hover {
          background: color.adjust($system-blue, $lightness: 5%);
          transform: translateY(-2px);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }

        &:active {
          transform: translateY(0);
          background: color.adjust($system-blue, $lightness: -5%);
        }

        &:focus {
          outline: 2px solid $system-blue;
          outline-offset: 2px;
        }
      }
    }
  }



























  .retrospective {
    grid-area: retrospective;
    border-radius: 16px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  // Retrospective Card
  .retrospective-card {
    // Light theme
    background-color: $window-background;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid $border-color;
    z-index: 1;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      border: 1px solid $dark-border-color;
    }

    border-radius: 16px;
    width: 100%;
    height: 100%;
    padding: .5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);

      // Dark theme
      .theme-dark & {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
      }
    }

    &:focus {
      outline: 2px solid $system-blue;
      outline-offset: 2px;
    }

    .card-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: .5rem;
      text-align: center;
      padding: 0 0 0.5rem 0;
      width: 100%;
      position: relative;
      letter-spacing: -0.2px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 25%;
        right: 25%;
        height: 1px;

        // Light theme
        background-color: $separator-color;

        // Dark theme
        .theme-dark & {
          background-color: $dark-separator-color;
        }
      }

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .note-grid {
      display: flex;
      flex-direction: row;
      gap: .5rem;
      width: 100%;
      height: 70%;
      justify-content: center;
      align-items: center;
      //margin-top: 1rem;

      .note-item-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: .5rem;
        transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

        &:hover {
          transform: translateY(-5px);

          .note-circle {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(var(--circle-color-rgb), 0.3);

            &::before {
              opacity: 0.8;
            }
          }

          .column-title {
            transform: scale(1.05);
          }
        }

        .note-circle {
          width: 90%;
          max-width: 4rem;
          aspect-ratio: 1 / 1;
          //height: 70px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
          transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

          // Outer circle with subtle glow
          &::before {
            content: '';
            position: absolute;
            width: 86px;
            height: 86px;
            border-radius: 50%;
            border: 2px solid rgba(var(--circle-color-rgb), 0.5);
            box-shadow: 0 0 10px rgba(var(--circle-color-rgb), 0.3);
            z-index: -1;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0.6;
            transition: opacity 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
          }

          &.red-circle {
            background: $system-red;
            --circle-color: $system-red;
            --circle-color-rgb: 255, 59, 48;
          }

          &.orange-circle {
            background: $system-orange;
            --circle-color: $system-orange;
            --circle-color-rgb: 255, 149, 0;
          }

          &.green-circle {
            background: $system-green;
            --circle-color: $system-green;
            --circle-color-rgb: 40, 205, 65;
          }

          .count-number {
            font-size: 2.2rem;
            font-weight: 700;
            color: white;
            margin: 0;
            padding: 0;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
          }
        }

        .column-title {
          font-size: 1rem;
          font-weight: 600;
          color: var(--circle-color);
          margin: 0;
          padding: 0.4rem 0.8rem;
          text-align: center;
          border-radius: 6px;
          background-color: rgba(var(--circle-color-rgb), 0.1);
          transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
          letter-spacing: 0.2px;
        }
      }
    }
  }

  // Modal Overlay - macOS style
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Dark theme
    .theme-dark & {
      background: rgba(0, 0, 0, 0.5);
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }
  }

  // Modal Window - macOS style
  .modal-window {
    background-color: $window-background;
    border-radius: 16px;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid $border-color;
    width: 900px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    @include ios-autohide-scrollbar;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
      border: 1px solid $dark-border-color;
    }

    @keyframes slideIn {
      from {
        transform: translateY(20px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .modal-header {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      position: sticky;
      top: 0;
      z-index: 10;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);

      // Light theme
      border-bottom: 1px solid $separator-color;
      background-color: rgba($window-background, 0.8);

      // Dark theme
      .theme-dark & {
        border-bottom: 1px solid $dark-separator-color;
        background-color: rgba($dark-window-background, 0.8);
      }

      .traffic-lights {
        display: flex;
        gap: 8px;

        .traffic-light {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: none;
          cursor: pointer;
          @include transition(all);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

          &.red {
            background: $system-red;
          }

          &.yellow {
            background: $system-orange;
          }

          &.green {
            background: $system-green;
          }

          &:hover {
            transform: scale(1.15);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: scale(0.95);
          }

          &:disabled {
            opacity: 0.4;
            cursor: default;
          }
        }
      }

      .modal-title {
        font-size: 1.3rem;
        font-weight: 600;
        flex: 1;
        text-align: center;
        margin: 0;
        letter-spacing: -0.2px;

        // Light theme
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          color: $dark-primary-text;
        }
      }
    }

    .modal-content {
      padding: 24px;
      @include ios-autohide-scrollbar;

      .retrospective-columns {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;

        .retrospective-column {
          padding: 20px;
          border-radius: 14px;
          min-height: 200px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          border: 1px solid $border-color;

          // Dark theme
          .theme-dark & {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            border: 1px solid $dark-border-color;
          }

          &.wentwell {
            background: rgba($system-green, 0.1);
            border-left: 4px solid $system-green;

            // Dark theme
            .theme-dark & {
              background: rgba($system-green, 0.15);
            }

            h3 {
              color: $system-green;

              // Dark theme
              .theme-dark & {
                color: color.adjust($system-green, $lightness: 10%);
              }
            }
          }

          &.toimprove {
            background: rgba($system-orange, 0.1);
            border-left: 4px solid $system-orange;

            // Dark theme
            .theme-dark & {
              background: rgba($system-orange, 0.15);
            }

            h3 {
              color: $system-orange;

              // Dark theme
              .theme-dark & {
                color: color.adjust($system-orange, $lightness: 10%);
              }
            }
          }

          &.actionitems {
            background: rgba($system-blue, 0.1);
            border-left: 4px solid $system-blue;

            // Dark theme
            .theme-dark & {
              background: rgba($system-blue, 0.15);
            }

            h3 {
              color: $system-blue;

              // Dark theme
              .theme-dark & {
                color: color.adjust($system-blue, $lightness: 10%);
              }
            }
          }

          h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0 0 16px;
            padding-bottom: 10px;
            border-bottom: 1px solid $separator-color;
            letter-spacing: -0.2px;

            // Dark theme
            .theme-dark & {
              border-bottom: 1px solid $dark-separator-color;
            }

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-primary-text;
            }
          }

          p {
            font-size: 0.95rem;
            line-height: 1.4;
            margin: 0 0 16px;

            // Light theme
            color: $secondary-text;

            // Dark theme
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .note-list {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              font-size: 0.95rem;
              margin-bottom: 10px;
              padding: 12px;
              border-radius: 8px;
              @include transition(all);

              // Light theme
              background-color: $window-background;
              color: $primary-text;
              border: 1px solid $border-color;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);

              // Dark theme
              .theme-dark & {
                background-color: $dark-window-background;
                color: $dark-primary-text;
                border: 1px solid $dark-border-color;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              }

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);

                // Dark theme
                .theme-dark & {
                  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
                }
              }

              strong {
                font-weight: 600;
                color: $system-blue;

                // Dark theme
                .theme-dark & {
                  color: color.adjust($system-blue, $lightness: 10%);
                }
              }
            }
          }
        }
      }
    }

    .modal-footer {
      padding: 16px 20px;
      text-align: right;
      position: sticky;
      bottom: 0;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);

      // Light theme
      border-top: 1px solid $separator-color;
      background-color: rgba($window-background, 0.8);

      // Dark theme
      .theme-dark & {
        border-top: 1px solid $dark-separator-color;
        background-color: rgba($dark-window-background, 0.8);
      }

      text-align: right;

      .close-button {
        background: $system-blue;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.2px;

        &:hover {
          background: color.adjust($system-blue, $lightness: 5%);
          transform: translateY(-2px);
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }

        &:active {
          transform: translateY(0);
          background: color.adjust($system-blue, $lightness: -5%);
        }

        &:focus {
          outline: 2px solid $system-blue;
          outline-offset: 2px;
        }
      }
    }
  }































  .masteryTracker {
    grid-area: masteryTracker;
    border-radius: 16px;
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  // Mastery tracker specific variables
  $mastery-red: $system-red;
  $mastery-yellow: $system-orange;
  $mastery-green: $system-green;
  $mastery-blue: $system-blue;
  $mastery-purple: #AF52DE; // macOS purple

  // Mastery Card
  .mastery-card {
    // Light theme
    background-color: $window-background;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid $border-color;
    z-index: 1;

    // Dark theme
    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      border: 1px solid $dark-border-color;
    }

    border-radius: 16px;
    padding: .5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    width: 100%;
    height: 100%;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);

      // Dark theme
      .theme-dark & {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
      }
    }

    &:focus {
      outline: 2px solid $system-blue;
      outline-offset: 2px;
    }

    .card-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: .5rem;
      text-align: center;
      padding: 0 0 0.5rem 0;
      width: 100%;
      position: relative;
      letter-spacing: -0.2px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 25%;
        right: 25%;
        height: 1px;

        // Light theme
        background-color: $separator-color;

        // Dark theme
        .theme-dark & {
          background-color: $dark-separator-color;
        }
      }

      // Light theme
      color: $primary-text;

      // Dark theme
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .mastery-progress-container {
      width: 100%;
      height: 80%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0.5rem;
      //background: none;

      .mastery-progress-item {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: .5rem;



        .subject-name {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 1.2rem;
          text-align: center;
          padding: 0.5rem 1rem;
          border-radius: 8px;
          background-color: rgba($system-blue, 0.1);
          border: 1px solid rgba($system-blue, 0.2);

          // Light theme
          color: $system-blue;

          // Dark theme
          .theme-dark & {
            color: color.adjust($system-blue, $lightness: 10%);
            background-color: rgba($system-blue, 0.15);
            border: 1px solid rgba($system-blue, 0.3);
          }
        }

        .mastery-gauge-container {
          position: relative;
          width: 100%;
          height: fit-content; // Fixed height of 220px
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
          padding: 0;
          border-radius: 12px;
          background: none !important;
          border: none !important;
          //background-color: $secondary-background;
          //border: 1px solid $border-color;

          // Dark theme
          .theme-dark & {
            background-color: $dark-secondary-background;
            border: 1px solid $dark-border-color;
          }

          // Wrapper for gauge and legend side by side
          .gauge-and-legend-wrapper {
            display: flex;
            width: 100%;
            height: fit-content;
            align-items: center;
            justify-content: center;
            gap: .5rem;



            // Gauge wrapper
            .gauge-wrapper {
              position: relative;
              width: 70%;
              height: fit-content;
              display: flex;
              justify-content: center;
              align-items: center;
              margin: auto;


              // Inner stage indicator (inside gauge) - simplified
              .inner-stage-indicator {
                position: absolute;
                //bottom: 40px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 0.625rem;
                font-weight: 500;
                letter-spacing: 0.5px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                text-transform: uppercase;
                background: $window-background;
                padding: 0.2rem .5rem;
                border-radius: 10px;
                color: $mastery-blue;
                border: 1px solid $border-color;
                transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);

                // Dark theme
                .theme-dark & {
                  background: $dark-window-background;
                  color: $mastery-blue;
                  border: 1px solid $dark-border-color;
                }
              }
            }
          }

          // macOS-inspired styling for the PCLHdM gauge component (simplified)
          .futuristic-gauge {
            width: 100%;
            max-width: 220px;
            height: 100%;
            margin: auto;
            position: relative;

            // Override default gauge styles for a clean macOS look
            svg {
              overflow: visible !important;

              // Background track styling
              path[class*="background-arc"] {
                stroke-width: 4px !important;
                stroke: $secondary-background !important;
                stroke-linecap: round !important;

                // Dark theme
                .theme-dark & {
                  stroke: $dark-secondary-background !important;
                }
              }

              // Value arc styling - clean, no glow
              path[class*="value-arc"] {
                stroke-width: 4px !important;
                stroke: $mastery-blue !important;
                stroke-linecap: round !important;
                transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
              }

              // Dial styling with macOS-like appearance
              circle[class*="dial"] {
                stroke: $border-color;
                stroke-width: 1px;
                fill: $window-background;

                // Dark theme
                .theme-dark & {
                  stroke: $dark-border-color;
                  fill: $dark-window-background;
                }
              }

              // Needle styling - clean, no glow
              path[class*="needle"] {
                fill: $mastery-blue !important;
                transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
              }

              // Center cap styling - clean, no glow
              circle[class*="needle-center"] {
                fill: $mastery-blue !important;

                // Dark theme
                .theme-dark & {
                  fill: lighten($mastery-blue, 5%) !important;
                }
              }

              // Tick marks styling
              line[class*="tick"] {
                stroke: $tertiary-text !important;
                stroke-width: 1px !important;

                // Dark theme
                .theme-dark & {
                  stroke: $dark-tertiary-text !important;
                }

                // Major ticks
                &[class*="major"] {
                  stroke-width: 1.5px !important;
                  stroke: $secondary-text !important;

                  // Dark theme
                  .theme-dark & {
                    stroke: $dark-secondary-text !important;
                  }
                }
              }

              // Tick labels styling with macOS typography
              text[class*="tick-label"] {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                font-weight: 500;
                font-size: 0.6875rem !important;
                letter-spacing: -0.2px;

                // Light theme
                fill: $secondary-text !important;

                // Dark theme
                .theme-dark & {
                  fill: $dark-secondary-text !important;
                }
              }

              // Value text styling with macOS typography
              text[class*="value-text"] {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                font-weight: 600;
                font-size: 1.5rem !important;
                letter-spacing: -0.5px;

                // Light theme
                fill: $primary-text !important;

                // Dark theme
                .theme-dark & {
                  fill: $dark-primary-text !important;
                }
              }

              // Value label styling
              text[class*="value-label"] {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                font-weight: 400;
                font-size: 0.75rem !important;
                letter-spacing: 0;

                // Light theme
                fill: $tertiary-text !important;

                // Dark theme
                .theme-dark & {
                  fill: $dark-tertiary-text !important;
                }
              }
            }
          }

          // PCLHdM legend with macOS styling - vertical version
          .pclhdm-legend {
            display: flex;
            justify-content: space-between;
            padding: .5rem;
            background: rgba($window-background, 0.98);
            border-radius: 12px;
            border: 1px solid $border-color;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);

            // Dark theme
            .theme-dark & {
              background: rgba($dark-window-background, 0.98);
              border: 1px solid $dark-border-color;
              box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
            }

            &.vertical {
              flex-direction: column;
              width: fit-content;
              height: fit-content;
              max-height: 220px;
              margin-left: 20px;
              justify-content: space-around;
              padding: .5rem;
              gap: 0;
              position: relative;

              // Add a subtle header
              &::before {
                content: '';
                position: absolute;
                top: -8px;
                left: 50%;
                transform: translateX(-50%);
                background: $window-background;
                padding: 0 10px;
                font-size: 0.6875rem;
                font-weight: 500;
                color: $tertiary-text;
                letter-spacing: 0.5px;
                text-transform: uppercase;

                // Dark theme
                .theme-dark & {
                  background: $dark-window-background;
                  color: $dark-tertiary-text;
                }
              }

              .legend-item {
                flex-direction: row;
                justify-content: flex-start;
                width: 100%;

                .legend-dot {
                  margin-right: 12px;
                }
              }
            }

            .legend-item {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              transition: all 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);
              padding: 0.2rem 0.5rem;
              border-radius: 8px;
              position: relative;
              margin: 0;

              // Add a subtle hover effect
              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border-radius: 8px;
                background: transparent;
                transition: all 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);
                z-index: -1;
              }

              &:hover {
                transform: translateY(-1px);

                &::after {
                  background-color: rgba($secondary-background, 0.8);
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

                  // Dark theme
                  .theme-dark & {
                    background-color: rgba($dark-secondary-background, 0.8);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                  }
                }
              }

              .legend-dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background-color: var(--stage-color);
                border: 1px solid rgba(var(--stage-color-rgb), 0.3);
                transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
                position: relative;
              }

              .legend-label {
                font-size: 0.8125rem;
                font-weight: 500;
                color: $secondary-text;
                letter-spacing: -0.2px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                transition: all 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);

                // Dark theme
                .theme-dark & {
                  color: $dark-secondary-text;
                }

                // Add a subtle description on hover
                .description {
                  display: block;
                  font-size: 0.6875rem;
                  color: $tertiary-text;
                  margin-top: 2px;
                  font-weight: 400;
                  max-width: 150px;
                  white-space: normal;
                  line-height: 1.3;
                  opacity: 0;
                  height: 0;
                  overflow: hidden;
                  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);

                  // Dark theme
                  .theme-dark & {
                    color: $dark-tertiary-text;
                  }
                }
              }

              &.active {
                background-color: rgba(var(--stage-color-rgb), 0.08);

                .legend-dot {
                  width: 14px;
                  height: 14px;
                  border: 2px solid rgba(var(--stage-color-rgb), 0.5);
                }

                .legend-label {
                  font-weight: 600;
                  color: var(--stage-color);

                  .description {
                    opacity: 1;
                    height: auto;
                    margin-top: 4px;
                  }
                }
              }

              &:hover {
                .legend-dot {
                  border: 2px solid rgba(var(--stage-color-rgb), 0.4);
                }

                .legend-label {
                  color: var(--stage-color);

                  .description {
                    opacity: 1;
                    height: auto;
                    margin-top: 4px;
                  }
                }
              }
            }
          }

          // Simple tooltip with macOS styling
          .tooltip {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: $window-background;
            color: $primary-text;
            font-size: 13px;
            font-weight: 500;
            line-height: 1.4;
            padding: 8px 12px;
            border-radius: 6px;
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
            border: 1px solid $border-color;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            max-width: 280px;
            text-align: center;
            z-index: 10;
            white-space: normal;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            letter-spacing: -0.2px;

            // Dark theme
            .theme-dark & {
              background: $dark-window-background;
              color: $dark-primary-text;
              border: 1px solid $dark-border-color;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            // Add a title to the tooltip
            .tooltip-title {
              font-weight: 600;
              font-size: 14px;
              margin-bottom: 4px;
              color: $mastery-blue;

              // Dark theme
              .theme-dark & {
                color: $mastery-blue;
              }
            }

            // Add a description to the tooltip
            .tooltip-description {
              font-size: 12px;
              color: $secondary-text;

              // Dark theme
              .theme-dark & {
                color: $dark-secondary-text;
              }
            }

            &::before {
              content: '';
              position: absolute;
              top: -6px;
              left: 50%;
              transform: translateX(-50%) rotate(45deg);
              width: 12px;
              height: 12px;
              background: $window-background;
              border-left: 1px solid $border-color;
              border-top: 1px solid $border-color;

              // Dark theme
              .theme-dark & {
                background: $dark-window-background;
                border-left: 1px solid $dark-border-color;
                border-top: 1px solid $dark-border-color;
              }
            }
          }

          &:hover .tooltip {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
        }



        .gauge-chart {
          width: 100%;
          height: 200px;
          position: relative;
          margin-bottom: 2rem;
          margin-top: 1rem;

          .gauge-background {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 240px;
            height: 120px;
            display: flex;
            background-color: transparent;
            overflow: visible;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 240px;
              height: 120px;
              border-radius: 120px 120px 0 0;
              background: linear-gradient(to bottom,
                  rgba(10, 10, 10, 0.95) 0%,
                  rgba(30, 30, 30, 0.95) 100%);
              box-shadow:
                0 0 20px rgba(0, 0, 0, 0.7) inset,
                0 2px 5px rgba(0, 200, 255, 0.2);
            }

            &::after {
              content: '';
              position: absolute;
              top: 5px;
              left: 5px;
              width: 230px;
              height: 115px;
              border-radius: 115px 115px 0 0;
              border-top: 1px solid rgba(0, 200, 255, 0.4);
              border-left: 1px solid rgba(0, 200, 255, 0.3);
              border-right: 1px solid rgba(0, 200, 255, 0.3);
              box-shadow: 0 0 10px rgba(0, 200, 255, 0.2);
              pointer-events: none;
            }

            // Tick marks for the speedometer
            .tick-marks {
              position: absolute;
              top: 0;
              left: 0;
              width: 240px;
              height: 120px;
              border-radius: 120px 120px 0 0;
              overflow: hidden;
              z-index: 2;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 120px;
                width: 120px;
                height: 120px;
                background-image: repeating-conic-gradient(from 0deg,
                    transparent 0deg 4deg,
                    rgba(255, 255, 255, 0.3) 4deg 5deg,
                    transparent 5deg 18deg);
                transform-origin: left center;
              }

              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 120px;
                width: 120px;
                height: 120px;
                background-image: repeating-conic-gradient(from 0deg,
                    transparent 0deg 9deg,
                    rgba(0, 200, 255, 0.4) 9deg 10deg,
                    transparent 10deg 36deg);
                transform-origin: left center;
              }
            }

            .gauge-segment {
              position: absolute;
              top: 0;
              left: 120px;
              width: 120px;
              height: 120px;
              transform-origin: left center;
              display: flex;
              align-items: flex-start;
              justify-content: center;
              padding-top: 15px;
              transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
              z-index: 3;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 120px;
                height: 120px;
                border-radius: 0 120px 120px 0;
                background: var(--segment-color);
                opacity: 0.2;
                z-index: 1;
              }

              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 120px;
                height: 120px;
                border-radius: 0 120px 120px 0;
                background: linear-gradient(to bottom,
                    rgba(255, 255, 255, 0.15) 0%,
                    rgba(0, 0, 0, 0.15) 100%);
                z-index: 2;
                pointer-events: none;
                opacity: 0.7;
              }

              &.completed {
                opacity: 1;

                &::before {
                  opacity: 0.6;
                  box-shadow: 0 0 20px var(--segment-color);
                }

                .stage-label {
                  color: white;
                  text-shadow: 0 0 8px var(--segment-color), 0 0 15px var(--segment-color);
                }
              }

              .stage-label {
                font-size: 0.8rem;
                font-weight: bold;
                color: var(--segment-color);
                text-shadow: 0 0 5px var(--segment-color);
                z-index: 10;
                position: absolute;
                letter-spacing: 1px;
                transform-origin: bottom center;
                white-space: nowrap;
                background-color: rgba(0, 0, 0, 0.5);
                padding: 2px 6px;
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.1);
              }
            }

            // Digital display area at the bottom of the gauge
            .digital-display {
              position: absolute;
              bottom: -25px;
              left: 50%;
              transform: translateX(-50%);
              width: 140px;
              height: 30px;
              background: rgba(0, 0, 0, 0.8);
              border-radius: 5px;
              border: 1px solid rgba(0, 200, 255, 0.5);
              box-shadow:
                0 0 10px rgba(0, 0, 0, 0.5) inset,
                0 0 8px rgba(0, 200, 255, 0.4);
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 5;

              &::before {
                content: '';
                position: absolute;
                top: 2px;
                left: 2px;
                right: 2px;
                height: 1px;
                background: linear-gradient(to right,
                    rgba(0, 200, 255, 0),
                    rgba(0, 200, 255, 0.7),
                    rgba(0, 200, 255, 0));
              }

              &::after {
                content: '';
                position: absolute;
                bottom: 2px;
                left: 10px;
                right: 10px;
                height: 1px;
                background: linear-gradient(to right,
                    rgba(0, 200, 255, 0),
                    rgba(0, 200, 255, 0.3),
                    rgba(0, 200, 255, 0));
              }
            }
          }

          .gauge-needle-container {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 240px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;

            .gauge-needle {
              position: absolute;
              bottom: 0;
              left: 50%;
              width: 3px;
              height: 105px;
              background: linear-gradient(to top, #ff3b30, #ff9500);
              border-radius: 3px;
              transform-origin: bottom center;
              box-shadow: 0 0 15px rgba(255, 59, 48, 0.8);
              transition: transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
              z-index: 5;

              &::before {
                content: '';
                position: absolute;
                top: -8px;
                left: 50%;
                transform: translateX(-50%);
                width: 10px;
                height: 18px;
                background-color: #ff3b30;
                clip-path: polygon(50% 0, 100% 100%, 0 100%);
                box-shadow: 0 0 10px #ff3b30, 0 0 20px rgba(255, 59, 48, 0.5);
              }

              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 1px;
                height: 100%;
                background: rgba(255, 255, 255, 0.8);
                box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
              }
            }

            .gauge-center {
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 36px;
              height: 36px;
              background: radial-gradient(circle, #1a1a1a 0%, #000000 70%);
              border-radius: 50%;
              box-shadow:
                0 0 15px rgba(0, 0, 0, 0.8),
                0 0 8px rgba(0, 200, 255, 0.5);
              z-index: 6;

              &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 24px;
                height: 24px;
                background: radial-gradient(circle, #333333 0%, #111111 70%);
                border-radius: 50%;
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.8) inset;
              }

              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border: 2px solid rgba(0, 200, 255, 0.5);
                border-radius: 50%;
                box-shadow: 0 0 10px rgba(0, 200, 255, 0.3);
              }
            }
          }

          .gauge-progress-text {
            position: absolute;
            bottom: -70px;
            left: 0;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 7;

            .current-stage {
              font-size: 1.1rem;
              font-weight: 600;
              color: #00c8ff;
              margin-bottom: 0.5rem;
              text-shadow: 0 0 10px rgba(0, 200, 255, 0.5);
              position: relative;
              text-transform: uppercase;
              letter-spacing: 1px;
              font-family: 'Courier New', monospace;

              &::before {
                content: 'STAGE';
                position: absolute;
                top: -18px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 0.7rem;
                color: rgba(255, 255, 255, 0.6);
                letter-spacing: 2px;
              }

              &::after {
                content: '';
                position: absolute;
                bottom: -3px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 1px;
                background: linear-gradient(to right,
                    rgba(0, 200, 255, 0),
                    rgba(0, 200, 255, 0.8),
                    rgba(0, 200, 255, 0));
                box-shadow: 0 0 5px rgba(0, 200, 255, 0.5);
              }
            }

            .progress-percentage {
              font-size: 1.8rem;
              font-weight: 700;
              font-family: 'Courier New', monospace;
              color: #ff9500;
              text-shadow: 0 0 10px rgba(255, 149, 0, 0.5);
              position: relative;
              padding: 0 10px;

              &::before {
                content: '%';
                position: absolute;
                top: 2px;
                right: -15px;
                font-size: 0.9rem;
                color: rgba(255, 149, 0, 0.8);
              }

              &::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 1px;
                background: linear-gradient(to right,
                    rgba(255, 149, 0, 0),
                    rgba(255, 149, 0, 0.5),
                    rgba(255, 149, 0, 0));
              }
            }
          }
        }

        .stage-legend {
          display: flex;
          justify-content: space-between;
          width: 100%;
          padding: 0 10px;
          margin-top: 10px;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 10px;
          padding: 8px 15px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.3) inset;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right,
                rgba(255, 255, 255, 0),
                rgba(255, 255, 255, 0.3),
                rgba(255, 255, 255, 0));
          }

          .stage-legend-item {
            display: flex;
            align-items: center;
            gap: 5px;

            .stage-color-dot {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              box-shadow: 0 0 5px var(--dot-color);
            }

            .stage-short-name {
              font-size: 0.8rem;
              font-weight: 500;
              color: var(--dot-color);
              text-shadow: 0 0 5px var(--dot-color, rgba(255, 255, 255, 0.3));
            }
          }
        }
      }
    }
  }

  // Modal content for PCLHdM
  .pclhdm-explanation {
    margin-bottom: 2rem;

    h3 {
      font-size: 1.3rem;
      margin-bottom: 0.5rem;
      color: #007aff;
    }

    .stages-legend {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-top: 1rem;

      .stage-legend-item {
        display: flex;
        align-items: center;
        gap: 1rem;

        .stage-color {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          flex-shrink: 0;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        }

        .stage-info {
          flex: 1;

          strong {
            font-size: 1.1rem;
            display: block;
            margin-bottom: 0.3rem;
          }

          p {
            margin: 0;
            color: $text-gray;
          }
        }
      }
    }
  }

  .subjects-progress {
    h3 {
      font-size: 1.3rem;
      margin-bottom: 1rem;
      color: #007aff;
    }

    .subject-progress-item {
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;

      .subject-header {
        margin-bottom: 1rem;

        h4 {
          font-size: 1.2rem;
          margin-bottom: 0.3rem;
        }

        p {
          color: $text-gray;
          margin: 0;
        }
      }

      .stage-history {
        margin-top: 1.5rem;

        h5 {
          font-size: 1rem;
          margin-bottom: 0.5rem;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            &.completed {
              color: #34c759;
            }

            &.in-progress {
              color: #007aff;
            }

            .stage-name {
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  // Modal Overlay
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }
  }

  // Modal Window
  .modal-window {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 800px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s ease;

    @keyframes slideIn {
      from {
        transform: translateY(20px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .modal-header {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid $border-color;

      .traffic-lights {
        display: flex;
        gap: 8px;

        .traffic-light {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: none;
          cursor: pointer;

          &.red {
            background: $red;
          }

          &.yellow {
            background: $yellow;
          }

          &.green {
            background: $green;
          }

          &:disabled {
            opacity: 0.4;
            cursor: default;
          }
        }
      }

      .modal-title {

        font-size: 18px;
        font-weight: 600;
        color: $text-dark;
        flex: 1;
        text-align: center;
        margin: 0;
      }
    }

    .modal-content {
      padding: 16px;

      .summary-section {
        margin-bottom: 24px;

        h3 {

          font-size: 16px;
          font-weight: 600;
          margin: 0 0 8px;
        }

        p {

          font-size: 14px;
          color: $text-gray;
          margin: 0 0 8px;
        }

        .progress-bar.total-progress {
          .progress-fill {
            background: $primary-blue;
          }
        }
      }

      .skill-details {
        display: grid;
        gap: 16px;

        .skill-section {
          padding: 12px;
          border-radius: 8px;

          &.coding {
            background: rgba(59, 130, 246, 0.1);
          }

          &.design {
            background: rgba(236, 72, 153, 0.1);
          }

          &.leadership {
            background: rgba(16, 185, 129, 0.1);
          }

          h3 {

            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px;
          }

          .skill-description {

            font-size: 14px;
            color: $text-gray;
            margin: 0 0 12px;
          }

          .skill-points {

            font-size: 14px;
            color: $text-dark;
            margin: 8px 0;
          }

          h4 {

            font-size: 14px;
            font-weight: 600;
            margin: 12px 0 8px;
          }

          .progress-bar {
            position: relative;
            height: 8px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              background: $primary-blue;
              transition: width 1s ease-in-out;

              @keyframes fill {
                from {
                  width: 0;
                }
              }

              animation: fill 1s ease-in-out;
            }

            .tooltip {
              position: absolute;
              top: -24px;
              left: 50%;
              transform: translateX(-50%);
              background: $text-dark;
              color: white;

              font-size: 12px;
              padding: 4px 8px;
              border-radius: 4px;
              opacity: 0;
              pointer-events: none;
              transition: opacity 0.2s ease;
            }

            &:hover .tooltip {
              opacity: 1;
            }
          }

          .milestone-list {
            list-style: none;
            padding: 0;
            margin: 0;

            li {

              font-size: 14px;
              color: $text-dark;
              margin-bottom: 8px;
              padding: 8px;
              border-radius: 6px;
              background: rgba(255, 255, 255, 0.8);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

              &.achieved {
                background: rgba(16, 185, 129, 0.2);
              }

              &.pending {
                opacity: 0.6;
              }

              strong {
                font-weight: 600;
              }

              .achieved-icon {
                margin-left: 8px;
              }
            }
          }
        }
      }
    }

    .modal-footer {
      padding: 12px 16px;
      border-top: 1px solid $border-color;
      text-align: right;

      .close-button {
        background: $primary-blue;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;

        font-size: 14px;
        cursor: pointer;
        transition: background 0.2s ease;

        &:hover {
          background: darken($primary-blue, 10%);
        }

        &:focus {
          outline: 2px solid $primary-blue;
          outline-offset: 2px;
        }
      }
    }
  }





























  .pomodoro {
    grid-area: pomodoro;
    border-radius: 1rem;

  }

  // Variables
  $primary-blue: #007aff;
  $red: #ff615f;
  $yellow: #ffbc49;
  $green: #28c840;
  $text-dark: #1c2526;
  $text-gray: #4a5759;
  $text-muted: #6b7280;
  $shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  $shadow-hover: 0 8px 16px rgba(0, 0, 0, 0.15);
  $border-color: rgba(0, 0, 0, 0.1);
  $font-family: system-ui, -apple-system, sans-serif;

  // Pomodoro Card
  .pomodoro-card {
    background-color: $light-background;
    border-radius: 1rem;
    box-shadow: $shadow;
    padding: .5rem;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    margin: 0;

    &:hover {
      transform: translateY(-4px);
      box-shadow: $shadow-hover;
    }

    &:focus {
      outline: 2px solid $primary-blue;
      outline-offset: 2px;
    }

    .card-title {

      font-size: 20px;
      font-weight: 600;
      color: $text-light;
      margin-bottom: 12px;
      text-align: center;
    }

    .timer-display {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .timer-icon {
        font-size: 20px;
      }

      .timer-time {

        font-size: 16px;
        font-weight: 500;
        color: $text-dark;
      }
    }

    p {

      font-size: 14px;
      color: $text-gray;
      margin: 0;
    }
  }

  // Modal Overlay
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }
  }

  // Modal Window
  .modal-window {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 600px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s ease;

    @keyframes slideIn {
      from {
        transform: translateY(20px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .modal-header {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid $border-color;

      .traffic-lights {
        display: flex;
        gap: 8px;

        .traffic-light {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: none;
          cursor: pointer;

          &.red {
            background: $red;
          }

          &.yellow {
            background: $yellow;
          }

          &.green {
            background: $green;
          }

          &:disabled {
            opacity: 0.4;
            cursor: default;
          }
        }
      }

      .modal-title {

        font-size: 18px;
        font-weight: 600;
        color: $text-dark;
        flex: 1;
        text-align: center;
        margin: 0;
      }
    }

    .modal-content {
      padding: 16px;

      .timer-section {
        text-align: center;
        margin-bottom: 24px;

        .timer-circle {
          position: relative;
          width: 200px;
          height: 200px;
          margin: 0 auto;

          .progress-ring {
            .progress-ring-bg {
              fill: none;
              stroke: rgba(0, 0, 0, 0.1);
              stroke-width: 10;
            }

            .progress-ring-fill {
              fill: none;
              stroke: $primary-blue;
              stroke-width: 10;
              transition: stroke-dashoffset 1s linear;
            }
          }

          .timer-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;

            .timer-time {

              font-size: 32px;
              font-weight: 600;
              color: $text-dark;
              display: block;
            }

            .timer-phase {

              font-size: 16px;
              color: $text-gray;
            }
          }
        }

        .timer-controls {
          display: flex;
          justify-content: center;
          gap: 16px;
          margin-top: 16px;

          .control-button {
            background: $primary-blue;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;

            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s ease;

            &:hover {
              background: darken($primary-blue, 10%);
            }

            &:focus {
              outline: 2px solid $primary-blue;
              outline-offset: 2px;
            }

            &.reset {
              background: $red;

              &:hover {
                background: darken($red, 10%);
              }
            }
          }
        }
      }

      .settings-section {
        margin-bottom: 24px;

        h3 {

          font-size: 16px;
          font-weight: 600;
          margin: 0 0 12px;
        }

        .duration-setting {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          label {

            font-size: 14px;
            color: $text-dark;
          }

          input {
            width: 60px;
            padding: 4px;
            border: 1px solid $border-color;
            border-radius: 4px;

            font-size: 14px;

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }

            &:focus {
              outline: 2px solid $primary-blue;
              outline-offset: 2px;
            }
          }
        }
      }

      .history-section {
        h3 {

          font-size: 16px;
          font-weight: 600;
          margin: 0 0 12px;
        }

        p {

          font-size: 14px;
          color: $text-gray;
          margin: 0;
        }

        .history-list {
          list-style: none;
          padding: 0;
          margin: 0;
          max-height: 150px;
          overflow-y: auto;

          li {

            font-size: 14px;
            color: $text-dark;
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          }
        }
      }
    }

    .modal-footer {
      padding: 12px 16px;
      border-top: 1px solid $border-color;
      text-align: right;

      .close-button {
        background: $primary-blue;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;

        font-size: 14px;
        cursor: pointer;
        transition: background 0.2s ease;

        &:hover {
          background: darken($primary-blue, 10%);
        }

        &:focus {
          outline: 2px solid $primary-blue;
          outline-offset: 2px;
        }
      }
    }
  }







































  .progress {
    grid-area: progress;
    border-radius: 1rem;
    padding: 0;
    margin: 0;
  }
































  .streak {
    grid-area: streak;
    border-radius: 1rem;
  }







  .calender {
    grid-area: calender;
    border-radius: 1rem;
  }






  // Loading state
  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 1rem;
    color: $text-light;
  }

  // Pomodoro styles
  .pomodoro {
    grid-area: pomodoro;
  }

  // Pomodoro Card
  .pomodoro-card {
    background-color: $light-background;

    border-radius: 12px;
    width: 100%;
    height: 100%;
    //border: 2px solid $primary-varient;
    padding: 0.5rem;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    margin: 0;

    &:hover {
      transform: translateY(-4px);
      box-shadow: $shadow-hover;
    }

    .card-title {
      font-size: 20px;
      font-weight: 600;
      color: $text-light;
      margin-bottom: 12px;
      text-align: center;
    }

    .timer-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      margin: 0;
      padding: 0;

      .icon {
        font-size: 1.5rem;
        color: $text-light;
        margin-right: 0.5rem;
      }

      .timer {
        flex: 1;
        display: flex;


        .work-time {
          display: flex;
          flex-direction: column;

          .time {
            font-size: 0.8rem;
            color: $text-light;
            opacity: 0.7;
          }

          .timer-time {
            font-size: 1.2rem;
            font-weight: 600;
            color: $text-light;
          }
        }

        .break-time {
          display: flex;
          justify-content: space-between;
          font-size: 0.8rem;
          color: $text-light;
          opacity: 0.7;
          margin-top: 0.25rem;
        }
      }

      .play-button {
        button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: $text-light;
          transition: transform 0.2s ease;

          &:hover {
            transform: scale(1.1);
          }

          &.play {
            color: #4CAF50;
          }

          &.pause {
            color: #FF9800;
          }
        }
      }
    }
  }

  // Mini Timer
  .mini-timer {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 250px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    overflow: hidden;

    .mini-timer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 0.9rem;

      .mini-timer-controls {
        display: flex;
        gap: 8px;

        .mini-timer-button {
          background: none;
          border: none;
          color: white;
          font-size: 1rem;
          cursor: pointer;
          padding: 0;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .mini-timer-body {
      padding: 12px;

      .mini-timer-time {
        font-size: 2rem;
        font-weight: 600;
        color: white;
        text-align: center;
        margin-bottom: 8px;
      }

      .mini-timer-progress {
        height: 6px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        overflow: hidden;

        .mini-timer-progress-bar {
          height: 100%;
          transition: width 1s linear;
        }
      }
    }
  }

  // Settings section
  .settings-section {
    margin-top: 1rem;

    .settings-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h3 {
        margin: 0;
      }

      .edit-settings-button {
        background: $primary-blue;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 0.9rem;
        cursor: pointer;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }

    .duration-setting {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      label {
        flex: 1;
      }

      input {
        width: 60px;
        padding: 4px;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
    }

    .subject-setting {
      margin-top: 1rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
      }

      select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
    }

    .settings-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 1rem;

      .cancel-button {
        background: #f44336;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 0.9rem;
        cursor: pointer;
      }
    }

    .mini-timer-toggle {
      margin-top: 1rem;
      text-align: center;

      .toggle-button {
        background: $primary-blue;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 0.9rem;
        cursor: pointer;
      }
    }
  }

  // Modal footer
  .modal-footer {
    .footer-actions {
      display: flex;
      justify-content: space-between;

      .mini-timer-button {
        background: $primary-blue;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 0.9rem;
        cursor: pointer;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}