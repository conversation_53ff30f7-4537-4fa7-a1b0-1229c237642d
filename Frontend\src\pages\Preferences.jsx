import React, { useState, useEffect } from 'react';
import {
    ArrowBack,
    Psychology,
    Translate,
    Speed,
    AutoFixHigh,
    School,
    TextFields,
    Accessibility,
    Code
} from '@mui/icons-material';
import '../styles/Preferences.scss';

/**
 * Preferences Settings Component
 * Allows users to customize their learning experience
 */
const PreferencesSettings = ({ onClose }) => {
    // State for active tab and user preferences
    const [activeTab, setActiveTab] = useState('general');
    const [preferences, setPreferences] = useState({
        language: 'english',
        difficulty: 'intermediate',
        responseSpeed: 3,
        teachingStyle: 'socratic',
        accessibility: {
            fontSize: 16,
            highContrast: false
        }
    });

    // Theme detection with MutationObserver to detect theme changes
    const [isDarkMode, setIsDarkMode] = useState(false);

    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    const updatePreference = (key, value) => {
        setPreferences(prev => ({ ...prev, [key]: value }));
    };

    const updateNestedPreference = (parentKey, key, value) => {
        setPreferences(prev => ({
            ...prev,
            [parentKey]: {
                ...prev[parentKey],
                [key]: value
            }
        }));
    };

    return (
        <div className={`preferences-settings ${isDarkMode ? 'theme-dark' : ''} open`}>
            <div className="settings-header">
                <button className="back-button" onClick={onClose} aria-label="Go back">
                    <ArrowBack />
                </button>
                <h2>
                    <Psychology className="header-icon" />
                    Learning Preferences
                </h2>
            </div>

            <div className="settings-container">
                <div className="settings-sidebar">
                    {[
                        { id: 'general', icon: <Psychology />, label: 'General' },
                        { id: 'accessibility', icon: <Accessibility />, label: 'Accessibility' },
                        { id: 'advanced', icon: <Code />, label: 'Advanced' }
                    ].map(item => (
                        <button
                            key={item.id}
                            className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                            onClick={() => setActiveTab(item.id)}
                        >
                            {item.icon}
                            <span>{item.label}</span>
                        </button>
                    ))}
                </div>

                <div className="settings-content">
                    {/* General Tab */}
                    {activeTab === 'general' && (
                        <div className="settings-section">
                            <div className="preference-group">
                                <h3>
                                    <Translate className="icon" />
                                    Language
                                </h3>
                                <select
                                    value={preferences.language}
                                    onChange={(e) => updatePreference('language', e.target.value)}
                                    className="preference-select"
                                >
                                    <option value="english">English</option>
                                    <option value="spanish">Spanish</option>
                                    <option value="french">French</option>
                                </select>
                            </div>

                            <div className="preference-group">
                                <h3>
                                    <School className="icon" />
                                    Difficulty Level
                                </h3>
                                <div className="radio-options">
                                    {['beginner', 'intermediate', 'advanced'].map(level => (
                                        <label key={level} className="radio-option">
                                            <input
                                                type="radio"
                                                name="difficulty"
                                                checked={preferences.difficulty === level}
                                                onChange={() => updatePreference('difficulty', level)}
                                            />
                                            <span>{level.charAt(0).toUpperCase() + level.slice(1)}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>

                            <div className="preference-group">
                                <h3>
                                    <Speed className="icon" />
                                    Response Speed
                                </h3>
                                <div className="slider-container">
                                    <input
                                        type="range"
                                        min="1"
                                        max="5"
                                        value={preferences.responseSpeed}
                                        onChange={(e) => updatePreference('responseSpeed', e.target.value)}
                                        className="preference-slider"
                                    />
                                    <div className="slider-labels">
                                        <span>Fast</span>
                                        <span>Balanced</span>
                                        <span>Thorough</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Accessibility Tab */}
                    {activeTab === 'accessibility' && (
                        <div className="settings-section">
                            <div className="preference-group">
                                <h3>
                                    <TextFields className="icon" />
                                    Text Size
                                </h3>
                                <div className="slider-container">
                                    <input
                                        type="range"
                                        min="12"
                                        max="24"
                                        value={preferences.accessibility.fontSize}
                                        onChange={(e) => updateNestedPreference('accessibility', 'fontSize', e.target.value)}
                                        className="preference-slider"
                                    />
                                    <span className="slider-value">
                                        {preferences.accessibility.fontSize}px
                                    </span>
                                </div>
                            </div>

                            <div className="preference-group">
                                <h3>
                                    <Accessibility className="icon" />
                                    Display Options
                                </h3>
                                <label className="toggle-option">
                                    <input
                                        type="checkbox"
                                        checked={preferences.accessibility.highContrast}
                                        onChange={(e) => updateNestedPreference('accessibility', 'highContrast', e.target.checked)}
                                    />
                                    <span>High Contrast Mode</span>
                                </label>
                            </div>
                        </div>
                    )}

                    {/* Advanced Tab */}
                    {activeTab === 'advanced' && (
                        <div className="settings-section">
                            <div className="preference-group">
                                <h3>
                                    <AutoFixHigh className="icon" />
                                    Teaching Method
                                </h3>
                                <select
                                    value={preferences.teachingStyle}
                                    onChange={(e) => updatePreference('teachingStyle', e.target.value)}
                                    className="preference-select"
                                >
                                    <option value="socratic">Socratic (Question-based)</option>
                                    <option value="direct">Direct Instruction</option>
                                    <option value="examples">Example-based</option>
                                </select>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PreferencesSettings;