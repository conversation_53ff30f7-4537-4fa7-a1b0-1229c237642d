// Import variables
@use "variables" as *;

// Apply theme to body
body {
  transition: background-color $transition-speed, color $transition-speed;
  
  // Light theme (default)
  background-color: $window-background;
  color: $primary-text;
  
  // Dark theme
  &.theme-dark {
    background-color: $dark-window-background;
    color: $dark-primary-text;
  }
}

// Common components styling with theme support
.card {
  transition: background-color $transition-speed, border-color $transition-speed, box-shadow $transition-speed;
  border-radius: 10px;
  
  // Light theme
  background-color: $window-background;
  border: 1px solid $border-color;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  
  // Dark theme
  .theme-dark & {
    background-color: $dark-secondary-background;
    border-color: $dark-border-color;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }
}

// Buttons
.button {
  transition: background-color $transition-speed, color $transition-speed, border-color $transition-speed;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  
  // Primary button
  &.primary {
    // Light theme
    background-color: $system-blue;
    color: white;
    
    &:hover {
      background-color: darken($system-blue, 5%);
    }
    
    // Dark theme
    .theme-dark & {
      background-color: $system-blue;
      
      &:hover {
        background-color: lighten($system-blue, 5%);
      }
    }
  }
  
  // Secondary button
  &.secondary {
    // Light theme
    background-color: $secondary-background;
    color: $primary-text;
    border: 1px solid $border-color;
    
    &:hover {
      background-color: darken($secondary-background, 5%);
    }
    
    // Dark theme
    .theme-dark & {
      background-color: $dark-secondary-background;
      color: $dark-primary-text;
      border-color: $dark-border-color;
      
      &:hover {
        background-color: lighten($dark-secondary-background, 5%);
      }
    }
  }
}

// Inputs
input, textarea, select {
  transition: background-color $transition-speed, color $transition-speed, border-color $transition-speed;
  border-radius: 8px;
  padding: 0.5rem;
  
  // Light theme
  background-color: $window-background;
  color: $primary-text;
  border: 1px solid $border-color;
  
  &::placeholder {
    color: $placeholder-text;
  }
  
  &:focus {
    border-color: $system-blue;
    outline: none;
  }
  
  // Dark theme
  .theme-dark & {
    background-color: $dark-secondary-background;
    color: $dark-primary-text;
    border-color: $dark-border-color;
    
    &::placeholder {
      color: $dark-placeholder-text;
    }
    
    &:focus {
      border-color: $system-blue;
    }
  }
}

// Header
.main-header {
  transition: background-color $transition-speed, border-color $transition-speed;
  
  // Light theme
  background-color: rgba($window-background, 0.8);
  border-bottom: 1px solid $border-color;
  
  // Dark theme
  &.theme-dark {
    background-color: rgba($dark-window-background, 0.8);
    border-bottom: 1px solid $dark-border-color;
    
    .search-input {
      background-color: $dark-secondary-background;
      color: $dark-primary-text;
      
      &::placeholder {
        color: $dark-placeholder-text;
      }
    }
    
    .notification-button,
    .theme-toggle,
    .upload {
      color: $dark-primary-text;
      
      &:hover {
        background-color: rgba($dark-secondary-background, 0.8);
      }
    }
    
    .notification-popup {
      background-color: $dark-secondary-background;
      border-color: $dark-border-color;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      
      .notification-header {
        border-bottom-color: $dark-border-color;
      }
      
      .notification-footer {
        border-top-color: $dark-border-color;
      }
    }
  }
}
