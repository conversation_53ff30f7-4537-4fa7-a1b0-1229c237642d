import dotenv from 'dotenv';
import express from 'express';
import { Pool } from 'pg';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import cors from 'cors';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);

dotenv.config();

const app = express();
// Increase JSON payload size limit to 50MB
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cors({
    origin: '*',
    methods: 'GET,HEAD,PUT,POST,DELETE,OPTIONS',
    credentials: true,
    allowedHeaders: 'Content-Type, Authorization',
}));

app.use((req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    next();
});

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const userId = req.user?.userId;
        if (!userId) {
            console.error('Multer destination error: User ID not found in request');
            return cb(new Error('User ID not found in request'));
        }
        const uploadDir = join(__dirname, 'Uploads', userId.toString());
        try {
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
                console.log(`Created upload directory: ${uploadDir}`);
            }
            cb(null, uploadDir);
        } catch (error) {
            console.error('Error creating user upload directory:', error.message, error.stack);
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
        const uniqueName = `${Date.now()}-${sanitizedName}`;
        console.log(`Saving file as: ${uniqueName}`);
        cb(null, uniqueName);
    },
});

const upload = multer({
    storage,
    limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit
}).single('file');

const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'Lukind@1956', // Updated password based on user's memory
    database: process.env.DB_NAME || 'blueprint',
    port: process.env.DB_PORT || 5432,
});

const JWT_SECRET = process.env.JWT_SECRET || 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6';

app.use('/uploads', express.static(join(__dirname, 'Uploads')));

const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        console.error('No token provided in Authorization header');
        return res.status(401).json({ success: false, message: 'Access denied. No token provided.' });
    }
    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log(`Token verified for userId: ${decoded.userId}`);
        req.user = { userId: decoded.userId };
        next();
    } catch (error) {
        console.error('JWT Verification Error:', error.message, 'Token:', token);
        return res.status(403).json({ success: false, message: 'Invalid or expired token.' });
    }
};

async function initializeDatabase() {
    try {

        await pool.query(`
            DO $$
            BEGIN
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'flashcard_system') THEN
                    CREATE TYPE flashcard_system AS ENUM ('STIC', 'Leitner');
                END IF;
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'eisenhower_quadrant') THEN
                    CREATE TYPE eisenhower_quadrant AS ENUM (
                        'Urgent-Important',
                        'Not Urgent-Important',
                        'Urgent-Not Important',
                        'Not Urgent-Not Important'
                    );
                END IF;
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'kanban_status') THEN
                    CREATE TYPE kanban_status AS ENUM ('To Do', 'In Progress', 'Done');
                END IF;
            END
            $$;
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                full_name VARCHAR(50),
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                preferences_completed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS user_preferences (
                user_id INTEGER PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
                language VARCHAR(50),
                role VARCHAR(50),
                field_of_study VARCHAR(100),
                goals JSONB,
                tools JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS subjects (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT unique_user_subject UNIQUE (user_id, name)
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS files (
                id SERIAL PRIMARY KEY,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                filename TEXT NOT NULL,
                originalname TEXT NOT NULL,
                filetype TEXT,
                filepath TEXT,
                youtube_url TEXT,
                study_tools JSONB,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT unique_subject_file UNIQUE (subject_id, originalname)
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS chapters (
                id SERIAL PRIMARY KEY,
                file_id INTEGER REFERENCES files(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                chapter_number VARCHAR(50),
                title VARCHAR(255) NOT NULL,
                content TEXT,
                content_file_path VARCHAR(500),
                content_size INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS objectives (
                id SERIAL PRIMARY KEY,
                chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                objective TEXT NOT NULL,
                completed BOOLEAN DEFAULT FALSE,
                display_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Check if completed column exists in objectives table
        const completedColumnCheck = await pool.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'objectives' AND column_name = 'completed'
        `);

        // Add completed column if it doesn't exist
        if (completedColumnCheck.rows.length === 0) {
            console.log('Adding completed column to objectives table...');
            await pool.query(`
                ALTER TABLE objectives
                ADD COLUMN completed BOOLEAN DEFAULT FALSE
            `);
        }

        await pool.query(`
            CREATE TABLE IF NOT EXISTS study_tools (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS flashcards (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                question TEXT NOT NULL,
                answer TEXT NOT NULL,
                type VARCHAR(50) DEFAULT 'basic',
                system flashcard_system DEFAULT 'STIC',
                box_level INTEGER DEFAULT 1,
                last_reviewed TIMESTAMP,
                next_review_date TIMESTAMP,
                color VARCHAR(50) DEFAULT '#ffffff',
                difficult BOOLEAN DEFAULT FALSE,
                review_count INTEGER DEFAULT 0,
                success_rate FLOAT DEFAULT 0,
                tags VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);

        // Check if pomodoro_sessions table exists
        const tableCheck = await pool.query(`
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'pomodoro_sessions'
        `);

        const existingTables = tableCheck.rows.map(row => row.table_name);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS pomodoro_timer (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                duration_minutes INTEGER NOT NULL,
                break_duration_minutes INTEGER NOT NULL,
                sessions_completed INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS pomodoro_sessions (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                duration_minutes INTEGER NOT NULL,
                type VARCHAR(10) NOT NULL CHECK (type IN ('work', 'break')),
                completed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS eisenhower (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                task TEXT NOT NULL,
                quadrant eisenhower_quadrant NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS kanban_board (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                task TEXT NOT NULL,
                status kanban_status NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS cornell_notes (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                title TEXT,
                cues TEXT,
                notes TEXT,
                summary TEXT,
                key_points JSONB DEFAULT '[]'::jsonb,
                tags JSONB DEFAULT '[]'::jsonb,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS doodles (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                image_data TEXT, -- Base64 encoded image or similar
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS mindmap (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                nodes JSONB, -- JSON structure for nodes and connections
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS retrospective (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                went_well TEXT,
                to_improve TEXT,
                action_items TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS mastery_tracker (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                skill TEXT NOT NULL,
                proficiency_level INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS quizzes (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                question TEXT NOT NULL,
                options JSONB, -- JSON array of answer options
                correct_answer TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS calendar (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                event_title VARCHAR(255) NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS calendar_events (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                event_date DATE NOT NULL,
                event_time TIME,
                event_type VARCHAR(10) NOT NULL CHECK (event_type IN ('exam', 'quiz')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS daily_objectives (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                objective TEXT NOT NULL,
                completed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS streaks (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                current_streak INTEGER DEFAULT 0,
                longest_streak INTEGER DEFAULT 0,
                last_streak_date DATE,
                streak_history JSONB DEFAULT '[]',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Create study_plans table
        await pool.query(`
            CREATE TABLE IF NOT EXISTS study_plans (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                curriculum_id INTEGER REFERENCES files(id) ON DELETE CASCADE,
                name VARCHAR(255) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                daily_study_time_minutes INTEGER NOT NULL,
                preferred_days VARCHAR(50),
                unavailable_periods TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Create schedule_items table
        await pool.query(`
            CREATE TABLE IF NOT EXISTS schedule_items (
                id SERIAL PRIMARY KEY,
                study_plan_id INTEGER REFERENCES study_plans(id) ON DELETE CASCADE,
                objective_id INTEGER REFERENCES objectives(id) ON DELETE CASCADE,
                scheduled_date DATE NOT NULL,
                is_completed BOOLEAN DEFAULT FALSE,
                completion_date TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS podcast (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                title VARCHAR(255) NOT NULL,
                audio_url TEXT,
                duration INTEGER, -- Duration in seconds
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS explainer_videos (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                title VARCHAR(255) NOT NULL,
                video_url TEXT,
                duration INTEGER, -- Duration in seconds
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS mnemonic (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                concept TEXT NOT NULL,
                mnemonic TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await pool.query(`
            CREATE TABLE IF NOT EXISTS chapter_tools (
                id SERIAL PRIMARY KEY,
                chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                tool_id INTEGER REFERENCES study_tools(id) ON DELETE CASCADE,
                settings JSONB DEFAULT '{}',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                CONSTRAINT unique_chapter_tool UNIQUE (chapter_id, tool_id)
            )
        `);

        const toolsCheck = await pool.query('SELECT COUNT(*) FROM study_tools');
        if (parseInt(toolsCheck.rows[0].count) === 0) {
            await pool.query(`
                INSERT INTO study_tools (name, description) VALUES
                ('Pomodoro Timer', 'Time management tool with work and break intervals'),
                ('Eisenhower Matrix', 'Prioritize tasks based on urgency and importance'),
                ('Kanban Board', 'Visualize tasks in To Do, In Progress, Done columns'),
                ('Cornell Notes', 'Structured note-taking with cues, notes, and summary'),
                ('Doodles', 'Create sketches or drawings for visual learning'),
                ('Mindmap', 'Create a visual mindmap of concepts'),
                ('Retrospective', 'Reflect on what went well and what to improve'),
                ('Mastery Tracker', 'Track skill proficiency levels'),
                ('Flash Cards', 'Generate flashcards for quick review'),
                ('Quizzes', 'Generate quizzes based on content'),
                ('Calendar', 'Schedule study events and deadlines'),
                ('Daily Objectives', 'Set and track daily study goals'),
                ('Podcast', 'Create or link to audio content for learning'),
                ('Explainer Videos', 'Create or link to video content for learning'),
                ('Mnemonic', 'Create memory aids for concepts')
            `);
        }

        const schemaCheck = await pool.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'files'
        `);
        console.log('Files table schema:', schemaCheck.rows.map(row => row.column_name));

        console.log('Database initialized successfully');
    } catch (error) {
        console.error('Database initialization failed:', error.message, error.stack);
        throw error;
    }
}

initializeDatabase();

app.get('/api/check-preferences/:userId', authenticateToken, async (req, res) => {
    const { userId } = req.params;
    const authUserId = req.user.userId;

    if (parseInt(userId) !== authUserId) {
        console.error(`Access denied: Requested userId ${userId} does not match authenticated userId ${authUserId}`);
        return res.status(403).json({ success: false, message: 'Access denied' });
    }

    try {
        const { rows } = await pool.query(
            'SELECT preferences_completed FROM users WHERE id = $1',
            [userId]
        );

        if (rows.length === 0) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }

        res.json({ success: true, preferencesCompleted: rows[0].preferences_completed });
    } catch (error) {
        console.error('Error checking preferences:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error checking preferences' });
    }
});

app.get('/api/preferences', authenticateToken, async (req, res) => {
    const userId = req.user.userId;

    try {
        const { rows } = await pool.query(
            'SELECT language, role, field_of_study, goals, tools FROM user_preferences WHERE user_id = $1',
            [userId]
        );

        if (rows.length === 0) {
            return res.json({ success: true, preferences: null });
        }

        res.json({ success: true, preferences: rows[0] });
    } catch (error) {
        console.error('Error fetching preferences:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching preferences' });
    }
});

app.post('/api/preferences', authenticateToken, async (req, res) => {
    const { user_id, language, role, field_of_study, goals, tools } = req.body;
    const authUserId = req.user.userId;

    if (user_id !== authUserId) {
        console.error(`Access denied: Requested user_id ${user_id} does not match authenticated userId ${authUserId}`);
        return res.status(403).json({ success: false, message: 'Access denied' });
    }

    try {
        await pool.query(
            `
            INSERT INTO user_preferences (user_id, language, role, field_of_study, goals, tools)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (user_id)
            DO UPDATE SET
                language = EXCLUDED.language,
                role = EXCLUDED.role,
                field_of_study = EXCLUDED.field_of_study,
                goals = EXCLUDED.goals,
                tools = EXCLUDED.tools,
                created_at = CURRENT_TIMESTAMP
            `,
            [user_id, language, role, field_of_study, JSON.stringify(goals), JSON.stringify(tools)]
        );

        await pool.query(
            'UPDATE users SET preferences_completed = TRUE WHERE id = $1',
            [user_id]
        );

        res.json({ success: true, message: 'Preferences saved successfully' });
    } catch (error) {
        console.error('Error saving preferences:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error saving preferences' });
    }
});

app.post('/api/auth/register', async (req, res) => {
    const { email, password, full_name } = req.body;

    if (!email || !password || !full_name) {
        return res.status(400).json({ success: false, message: 'Email, password, and full name are required.' });
    }

    try {
        const userCheck = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
        if (userCheck.rows.length > 0) {
            return res.status(409).json({ success: false, message: 'User already exists.' });
        }

        const hashedPassword = await bcrypt.hash(password, 10);

        const { rows } = await pool.query(
            'INSERT INTO users (email, password, full_name, preferences_completed) VALUES ($1, $2, $3, $4) RETURNING id, email, full_name',
            [email, hashedPassword, full_name, false]
        );

        const user = rows[0];
        const token = jwt.sign({ userId: user.id }, JWT_SECRET, { expiresIn: '1h' });

        res.status(201).json({
            success: true,
            message: 'User registered successfully.',
            user: {
                id: user.id,
                fullName: user.full_name,
                email: user.email,
            },
            token,
        });
    } catch (error) {
        console.error('Error during registration:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error during registration.' });
    }
});

app.post('/api/auth/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        const { rows: users } = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
        if (users.length === 0) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }

        const user = users[0];

        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }

        const token = jwt.sign({ userId: user.id }, JWT_SECRET, { expiresIn: '1h' });

        res.json({
            success: true,
            message: 'Login successful',
            user: {
                id: user.id,
                fullName: user.full_name,
                email: user.email,
            },
            token,
        });
    } catch (error) {
        console.error('Login error:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Login failed' });
    }
});

app.get('/api/study-tools', authenticateToken, async (req, res) => {
    try {
        const { rows } = await pool.query('SELECT id, name, description FROM study_tools ORDER BY name');
        res.json({ success: true, tools: rows });
    } catch (error) {
        console.error('Error fetching study tools:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching study tools' });
    }
});

// Reset study tools (for development/debugging)
app.post('/api/reset-study-tools', authenticateToken, async (req, res) => {
    try {
        // Only allow this in development
        if (process.env.NODE_ENV !== 'production') {
            // Delete all existing tools
            await pool.query('DELETE FROM study_tools');

            // Insert the correct tools
            await pool.query(`
                INSERT INTO study_tools (name, description) VALUES
                ('Pomodoro Timer', 'Time management tool with work and break intervals'),
                ('Eisenhower Matrix', 'Prioritize tasks based on urgency and importance'),
                ('Kanban Board', 'Visualize tasks in To Do, In Progress, Done columns'),
                ('Cornell Notes', 'Structured note-taking with cues, notes, and summary'),
                ('Doodles', 'Create sketches or drawings for visual learning'),
                ('Mindmap', 'Create a visual mindmap of concepts'),
                ('Retrospective', 'Reflect on what went well and what to improve'),
                ('Mastery Tracker', 'Track skill proficiency levels'),
                ('Flash Cards', 'Generate flashcards for quick review'),
                ('Quizzes', 'Generate quizzes based on content'),
                ('Calendar', 'Schedule study events and deadlines'),
                ('Daily Objectives', 'Set and track daily study goals'),
                ('Podcast', 'Create or link to audio content for learning'),
                ('Explainer Videos', 'Create or link to video content for learning'),
                ('Mnemonic', 'Create memory aids for concepts')
            `);

            const { rows } = await pool.query('SELECT id, name, description FROM study_tools ORDER BY name');
            res.json({ success: true, message: 'Study tools reset successfully', tools: rows });
        } else {
            res.status(403).json({ success: false, message: 'This endpoint is only available in development mode' });
        }
    } catch (error) {
        console.error('Error resetting study tools:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error resetting study tools' });
    }
});

// Get a single chapter by ID
app.get('/api/chapters/:chapterId', authenticateToken, async (req, res) => {
    const { chapterId } = req.params;
    const userId = req.user.userId;

    try {
        console.log(`Fetching chapter ID: ${chapterId} for user ID: ${userId}`);

        // First, check if the chapter exists and belongs to the user
        const chapterResult = await pool.query(
            `SELECT c.id, c.title, c.chapter_number, c.subject_id, c.file_id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterResult.rows.length === 0) {
            console.log(`Chapter ID ${chapterId} not found or doesn't belong to user ${userId}`);
            return res.status(404).json({
                success: false,
                message: 'Chapter not found or access denied'
            });
        }

        const chapter = chapterResult.rows[0];
        console.log(`Found chapter: ${chapter.title}, subject_id: ${chapter.subject_id}`);

        return res.json({
            success: true,
            chapter: chapter
        });
    } catch (error) {
        console.error('Error fetching chapter:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch chapter'
        });
    }
});

// Get a single subject by ID
app.get('/api/subjects/:subjectId', authenticateToken, async (req, res) => {
    const { subjectId } = req.params;
    const userId = req.user.userId;

    try {
        console.log(`Fetching subject ID: ${subjectId} for user ID: ${userId}`);

        // Check if the subject exists and belongs to the user
        const subjectResult = await pool.query(
            'SELECT id, name FROM subjects WHERE id = $1 AND user_id = $2',
            [subjectId, userId]
        );

        if (subjectResult.rows.length === 0) {
            console.log(`Subject ID ${subjectId} not found or doesn't belong to user ${userId}`);
            return res.status(404).json({
                success: false,
                message: 'Subject not found or access denied'
            });
        }

        const subject = subjectResult.rows[0];
        console.log(`Found subject: ${subject.name}`);

        return res.json({
            success: true,
            subject: subject
        });
    } catch (error) {
        console.error('Error fetching subject:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch subject'
        });
    }
});

// Get tools for a specific chapter
app.get('/api/chapters/:chapterId/tools', authenticateToken, async (req, res) => {
    const { chapterId } = req.params;
    const userId = req.user.userId;

    try {
        console.log(`Fetching tools for chapter ID: ${chapterId} for user ID: ${userId}`);

        // First, check if the chapter exists
        const chapterExistsResult = await pool.query(
            'SELECT id, title, chapter_number, subject_id, file_id FROM chapters WHERE id = $1',
            [chapterId]
        );

        if (chapterExistsResult.rows.length === 0) {
            console.log(`Chapter ID ${chapterId} not found`);
            return res.status(404).json({ success: false, message: 'Chapter not found' });
        }

        const chapter = chapterExistsResult.rows[0];
        console.log(`Found chapter: ${chapter.title}, subject_id: ${chapter.subject_id}, file_id: ${chapter.file_id}`);

        // Verify the chapter belongs to the user
        const chapterCheck = await pool.query(
            `SELECT c.id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterCheck.rows.length === 0) {
            console.log(`User ${userId} does not have access to chapter ${chapterId}`);
            return res.status(403).json({ success: false, message: 'Access denied to this chapter' });
        }

        // If file_id is present, check if the file exists and is accessible
        if (chapter.file_id) {
            console.log(`Checking if file ID ${chapter.file_id} exists and is accessible`);

            const fileCheckResult = await pool.query(
                `SELECT f.id
                 FROM files f
                 JOIN subjects s ON f.subject_id = s.id
                 WHERE f.id = $1 AND s.user_id = $2`,
                [chapter.file_id, userId]
            );

            if (fileCheckResult.rows.length === 0) {
                console.log(`File ID ${chapter.file_id} not found or not accessible by user ${userId}`);
                // We'll continue anyway since the file might have been deleted but the chapter is still valid
                console.log(`Continuing anyway since the chapter is still accessible`);
            } else {
                console.log(`File ID ${chapter.file_id} is accessible by user ${userId}`);
            }
        }

        // Get tools for this chapter
        const { rows } = await pool.query(
            `SELECT ct.id, ct.chapter_id, ct.tool_id, ct.settings, st.name, st.description
             FROM chapter_tools ct
             JOIN study_tools st ON ct.tool_id = st.id
             WHERE ct.chapter_id = $1
             ORDER BY st.name`,
            [chapterId]
        );

        console.log(`Found ${rows.length} tools for chapter ID: ${chapterId}`);

        // Log each tool for debugging
        rows.forEach((tool, index) => {
            console.log(`Tool ${index + 1}:`, {
                id: tool.id,
                tool_id: tool.tool_id,
                name: tool.name,
                description: tool.description
            });
        });

        // Get subject information
        const subjectResult = await pool.query(
            'SELECT id, name, user_id FROM subjects WHERE id = $1',
            [chapter.subject_id]
        );

        if (subjectResult.rows.length === 0) {
            console.log(`Subject ID ${chapter.subject_id} not found`);
            return res.status(404).json({
                success: false,
                message: 'Subject not found'
            });
        }

        const subject = subjectResult.rows[0];
        console.log(`Found subject: ${subject.name}, user_id: ${subject.user_id}`);

        res.json({
            success: true,
            tools: rows,
            chapter: {
                id: chapter.id,
                title: chapter.title,
                chapter_number: chapter.chapter_number,
                subject_id: chapter.subject_id
            },
            subject: {
                id: subject.id,
                name: subject.name
            }
        });
    } catch (error) {
        console.error('Error fetching chapter tools:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching chapter tools' });
    }
});

// Add or update a tool for a chapter
app.post('/api/chapters/:chapterId/tools', authenticateToken, async (req, res) => {
    const { chapterId } = req.params;
    const { tool_id, settings } = req.body;
    const userId = req.user.userId;

    try {
        console.log(`Adding tool ID: ${tool_id} to chapter ID: ${chapterId} for user ID: ${userId}`);

        // First, check if the chapter exists
        const chapterExistsResult = await pool.query(
            'SELECT id, title, chapter_number, subject_id, file_id FROM chapters WHERE id = $1',
            [chapterId]
        );

        if (chapterExistsResult.rows.length === 0) {
            console.log(`Chapter ID ${chapterId} not found`);
            return res.status(404).json({ success: false, message: 'Chapter not found' });
        }

        const chapter = chapterExistsResult.rows[0];
        console.log(`Found chapter: ${chapter.title}, subject_id: ${chapter.subject_id}, file_id: ${chapter.file_id}`);

        // Verify the chapter belongs to the user
        const chapterCheck = await pool.query(
            `SELECT c.id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterCheck.rows.length === 0) {
            console.log(`User ${userId} does not have access to chapter ${chapterId}`);
            return res.status(403).json({ success: false, message: 'Access denied to this chapter' });
        }

        // If file_id is present, check if the file exists and is accessible
        if (chapter.file_id) {
            console.log(`Checking if file ID ${chapter.file_id} exists and is accessible`);

            const fileCheckResult = await pool.query(
                `SELECT f.id
                 FROM files f
                 JOIN subjects s ON f.subject_id = s.id
                 WHERE f.id = $1 AND s.user_id = $2`,
                [chapter.file_id, userId]
            );

            if (fileCheckResult.rows.length === 0) {
                console.log(`File ID ${chapter.file_id} not found or not accessible by user ${userId}`);
                // We'll continue anyway since the file might have been deleted but the chapter is still valid
                console.log(`Continuing anyway since the chapter is still accessible`);
            } else {
                console.log(`File ID ${chapter.file_id} is accessible by user ${userId}`);
            }
        }

        // Check if the tool exists
        const toolCheck = await pool.query(
            'SELECT id, name FROM study_tools WHERE id = $1',
            [tool_id]
        );

        if (toolCheck.rows.length === 0) {
            console.log(`Tool ID ${tool_id} not found`);
            return res.status(404).json({ success: false, message: 'Tool not found' });
        }

        console.log(`Found tool: ${toolCheck.rows[0].name} (ID: ${toolCheck.rows[0].id})`);

        // Insert or update the chapter tool
        const { rows } = await pool.query(
            `INSERT INTO chapter_tools (chapter_id, tool_id, settings)
             VALUES ($1, $2, $3)
             ON CONFLICT (chapter_id, tool_id)
             DO UPDATE SET
                settings = $3,
                updated_at = CURRENT_TIMESTAMP
             RETURNING *`,
            [chapterId, tool_id, JSON.stringify(settings || {})]
        );

        res.json({ success: true, tool: rows[0] });
    } catch (error) {
        console.error('Error adding/updating chapter tool:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error adding/updating chapter tool' });
    }
});

// Remove a tool from a chapter
app.delete('/api/chapters/:chapterId/tools/:toolId', authenticateToken, async (req, res) => {
    const { chapterId, toolId } = req.params;
    const userId = req.user.userId;

    try {
        // Verify the chapter belongs to the user
        const chapterCheck = await pool.query(
            `SELECT c.id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterCheck.rows.length === 0) {
            return res.status(403).json({ success: false, message: 'Chapter not found or access denied' });
        }

        // Delete the chapter tool
        await pool.query(
            'DELETE FROM chapter_tools WHERE chapter_id = $1 AND tool_id = $2',
            [chapterId, toolId]
        );

        res.json({ success: true, message: 'Tool removed from chapter successfully' });
    } catch (error) {
        console.error('Error removing chapter tool:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error removing chapter tool' });
    }
});

app.get('/api/files/by-name', authenticateToken, async (req, res) => {
    const { subject_id, originalname } = req.query;
    const userId = req.user.userId;

    if (!subject_id || !originalname) {
        console.error('Validation error: subject_id and originalname are required');
        return res.status(400).json({ success: false, message: 'Subject ID and original file name are required' });
    }

    try {
        const fileCheck = await pool.query(
            `SELECT f.id, f.filename, f.originalname, f.filetype, f.filepath, f.youtube_url, f.study_tools, f.uploaded_at
             FROM files f
             JOIN subjects s ON f.subject_id = s.id
             WHERE f.subject_id = $1 AND f.originalname = $2 AND s.user_id = $3`,
            [subject_id, originalname, userId]
        );

        if (fileCheck.rows.length === 0) {
            return res.json({ success: true, file: null });
        }

        res.json({ success: true, file: fileCheck.rows[0] });
    } catch (error) {
        console.error('Error fetching file by name:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching file' });
    }
});

app.post('/api/files/upload', authenticateToken, (req, res) => {
    upload(req, res, async (err) => {
        if (err) {
            console.error('Multer error:', {
                message: err.message,
                stack: err.stack,
                field: err.field
            });
            return res.status(400).json({ success: false, message: `File upload failed: ${err.message}` });
        }

        try {
            const userId = req.user.userId;
            const { subject_id, file_type, youtube_url, study_tools } = req.body;
            const file = req.file;

            console.log('Upload request:', {
                userId,
                subject_id,
                file_type,
                youtube_url,
                study_tools: study_tools ? JSON.parse(study_tools) : [],
                file: file ? { originalname: file.originalname, filename: file.filename } : null
            });

            if (!subject_id) {
                console.error('Validation error: subject_id is required');
                return res.status(400).json({ success: false, message: 'Subject ID is required' });
            }

            if (!file_type) {
                console.error('Validation error: file_type is required');
                return res.status(400).json({ success: false, message: 'File type is required' });
            }

            const subjectCheck = await pool.query(
                'SELECT id FROM subjects WHERE id = $1 AND user_id = $2',
                [subject_id, userId]
            );

            if (subjectCheck.rows.length === 0) {
                console.error(`Subject check failed: subject_id ${subject_id} not found for userId ${userId}`);
                return res.status(403).json({ success: false, message: 'Subject not found or access denied' });
            }

            let fileData = {};
            let parsedStudyTools = [];
            try {
                parsedStudyTools = study_tools ? JSON.parse(study_tools) : [];
                if (!Array.isArray(parsedStudyTools)) {
                    throw new Error('study_tools must be an array');
                }
            } catch (parseError) {
                console.error('Error parsing study_tools:', {
                    message: parseError.message,
                    study_tools
                });
                return res.status(400).json({ success: false, message: 'Invalid study_tools format' });
            }

            if (file_type === 'youtube') {
                if (!youtube_url) {
                    console.error('Validation error: youtube_url is required for youtube file_type');
                    return res.status(400).json({ success: false, message: 'YouTube URL is required' });
                }
                fileData = {
                    subject_id,
                    filename: youtube_url,
                    originalname: youtube_url,
                    filetype: 'youtube',
                    filepath: '/youtube/link', // Add dummy filepath for YouTube links
                    youtube_url,
                    study_tools: parsedStudyTools
                };

                // Check for existing YouTube link
                const existingFile = await pool.query(
                    'SELECT id FROM files WHERE subject_id = $1 AND originalname = $2',
                    [subject_id, youtube_url]
                );

                if (existingFile.rows.length > 0) {
                    console.log(`YouTube link '${youtube_url}' already exists for subject_id ${subject_id}, returning existing file`);
                    return res.status(200).json({ success: true, file: existingFile.rows[0], message: 'File already exists' });
                }
            } else {
                if (!file) {
                    console.error('Validation error: No file uploaded for file_type:', file_type);
                    return res.status(400).json({ success: false, message: 'No file uploaded' });
                }
                fileData = {
                    subject_id,
                    filename: file.filename,
                    originalname: file.originalname,
                    filetype: file_type || file.mimetype,
                    filepath: join('Uploads', userId.toString(), file.filename),
                    youtube_url: null,
                    study_tools: parsedStudyTools
                };

                // Check for existing file
                const existingFile = await pool.query(
                    'SELECT id, filepath FROM files WHERE subject_id = $1 AND originalname = $2',
                    [subject_id, file.originalname]
                );

                if (existingFile.rows.length > 0) {
                    console.log(`File '${file.originalname}' already exists for subject_id ${subject_id}, updating existing file`);
                    // Delete old file from filesystem
                    if (existingFile.rows[0].filepath && fs.existsSync(existingFile.rows[0].filepath)) {
                        fs.unlinkSync(existingFile.rows[0].filepath);
                        console.log(`Deleted old file: ${existingFile.rows[0].filepath}`);
                    }
                    // Update existing file entry
                    const { rows } = await pool.query(
                        `UPDATE files
                         SET filename = $1, filetype = $2, filepath = $3, study_tools = $4, uploaded_at = CURRENT_TIMESTAMP
                         WHERE id = $5
                         RETURNING *`,
                        [
                            fileData.filename,
                            fileData.filetype,
                            fileData.filepath,
                            JSON.stringify(fileData.study_tools),
                            existingFile.rows[0].id
                        ]
                    );
                    console.log('Updated existing file:', rows[0]);
                    return res.status(200).json({ success: true, file: rows[0], message: 'File updated successfully' });
                }
            }

            console.log('Inserting file into database:', fileData);

            const columnCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'files' AND column_name = 'youtube_url'
            `);
            if (columnCheck.rows.length === 0) {
                console.error('Database schema error: youtube_url column missing in files table');
                return res.status(500).json({ success: false, message: 'Database schema error: youtube_url column missing' });
            }

            const { rows } = await pool.query(
                'INSERT INTO files (subject_id, filename, originalname, filetype, filepath, youtube_url, study_tools) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
                [
                    fileData.subject_id,
                    fileData.filename,
                    fileData.originalname,
                    fileData.filetype,
                    fileData.filepath,
                    fileData.youtube_url,
                    JSON.stringify(fileData.study_tools)
                ]
            );

            console.log('File uploaded successfully:', rows[0]);

            res.json({
                success: true,
                message: 'File uploaded successfully',
                file: rows[0],
            });
        } catch (error) {
            console.error('File upload error:', {
                message: error.message,
                stack: error.stack,
                code: error.code,
                requestBody: req.body,
                file: req.file ? { originalname: req.file.originalname, filename: req.file.filename } : null
            });
            if (error.code === '23503') {
                res.status(400).json({ success: false, message: 'Invalid subject_id' });
            } else if (error.code === '23505') {
                res.status(409).json({ success: false, message: 'File with this name already exists for this subject' });
            } else if (error.message.includes('column "youtube_url" does not exist')) {
                res.status(500).json({ success: false, message: 'Database schema error: youtube_url column missing' });
            } else {
                res.status(500).json({ success: false, message: `File upload failed: ${error.message}` });
            }
        }
    });
});

app.get('/api/files/:subjectId', authenticateToken, async (req, res) => {
    const { subjectId } = req.params;
    const userId = req.user.userId;

    try {
        const subjectCheck = await pool.query(
            'SELECT id FROM subjects WHERE id = $1 AND user_id = $2',
            [subjectId, userId]
        );

        if (subjectCheck.rows.length === 0) {
            return res.status(403).json({ success: false, message: 'Subject not found or access denied' });
        }

        const { rows } = await pool.query(
            'SELECT id, filename, originalname, filetype, uploaded_at, youtube_url, study_tools FROM files WHERE subject_id = $1',
            [subjectId]
        );

        res.json({ success: true, files: rows });
    } catch (error) {
        console.error('Error fetching files:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching files' });
    }
});

app.delete('/api/files/:fileId', authenticateToken, async (req, res) => {
    const { fileId } = req.params;
    const userId = req.user.userId;

    try {
        const fileCheck = await pool.query(
            `SELECT f.id, f.filepath FROM files f
             JOIN subjects s ON f.subject_id = s.id
             WHERE f.id = $1 AND s.user_id = $2`,
            [fileId, userId]
        );

        if (fileCheck.rows.length === 0) {
            return res.status(403).json({ success: false, message: 'File not found or access denied' });
        }

        const file = fileCheck.rows[0];

        await pool.query('DELETE FROM files WHERE id = $1', [fileId]);

        if (file.filepath && fs.existsSync(file.filepath)) {
            fs.unlinkSync(file.filepath);
            console.log(`Deleted file: ${file.filepath}`);
        }

        res.json({ success: true, message: 'File deleted successfully' });
    } catch (error) {
        console.error('Error deleting file:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error deleting file' });
    }
});

app.get('/api/subjects/by-name', authenticateToken, async (req, res) => {
    const { name } = req.query;
    const userId = req.user.userId;

    if (!name) {
        console.error('Validation error: Subject name is required');
        return res.status(400).json({ success: false, message: 'Subject name is required' });
    }

    try {
        const { rows } = await pool.query(
            'SELECT id, name, created_at FROM subjects WHERE user_id = $1 AND name = $2',
            [userId, name]
        );

        if (rows.length === 0) {
            return res.json({ success: true, subject: null });
        }

        res.json({ success: true, subject: rows[0] });
    } catch (error) {
        console.error('Error fetching subject by name:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching subject' });
    }
});

app.post('/api/subjects', authenticateToken, async (req, res) => {
    const { name } = req.body;
    const userId = req.user.userId;

    if (!name) {
        console.error('Validation error: Subject name is required');
        return res.status(400).json({ success: false, message: 'Subject name is required' });
    }

    try {
        const existingSubject = await pool.query(
            'SELECT id, name, created_at FROM subjects WHERE user_id = $1 AND name = $2',
            [userId, name]
        );

        if (existingSubject.rows.length > 0) {
            console.log(`Subject '${name}' already exists for userId ${userId}, returning existing subject`);
            return res.status(200).json({ success: true, subject: existingSubject.rows[0] });
        }

        const { rows } = await pool.query(
            'INSERT INTO subjects (name, user_id) VALUES ($1, $2) RETURNING *',
            [name, userId]
        );

        console.log(`Created new subject '${name}' for userId ${userId}`);
        res.status(201).json({ success: true, subject: rows[0] });
    } catch (error) {
        console.error('Error creating subject:', error.message, error.stack);
        if (error.code === '23505') {
            res.status(409).json({ success: false, message: 'Subject name already exists for this user' });
        } else {
            res.status(500).json({ success: false, message: 'Failed to create subject' });
        }
    }
});

app.get('/api/subjects', authenticateToken, async (req, res) => {
    const userId = req.user.userId;

    try {
        const { rows } = await pool.query(
            'SELECT id, name, created_at FROM subjects WHERE user_id = $1 ORDER BY created_at DESC',
            [userId]
        );

        res.json({ success: true, subjects: rows });
    } catch (error) {
        console.error('Error fetching subjects:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching subjects' });
    }
});

app.delete('/api/subjects/:id', authenticateToken, async (req, res) => {
    const { id } = req.params;
    const userId = req.user.userId;

    try {
        const subjectCheck = await pool.query(
            'SELECT id FROM subjects WHERE id = $1 AND user_id = $2',
            [id, userId]
        );

        if (subjectCheck.rows.length === 0) {
            return res.status(403).json({ success: false, message: 'Subject not found or access denied' });
        }

        await pool.query('DELETE FROM subjects WHERE id = $1', [id]);

        res.json({ success: true, message: 'Subject deleted successfully' });
    } catch (error) {
        console.error('Error deleting subject:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error deleting subject' });
    }
});

app.get('/api/daily_objectives', authenticateToken, async (req, res) => {
    const userId = req.user.userId;
    const { date, user_id } = req.query;

    if (user_id && parseInt(user_id) !== userId) {
        return res.status(403).json({ success: false, message: 'Access denied: User ID mismatch' });
    }

    try {
        const query = `
            SELECT o.*, c.title as chapter_title, c.chapter_number, s.name as subject_name
            FROM objectives o
            JOIN chapters c ON o.chapter_id = c.id
            JOIN subjects s ON c.subject_id = s.id
            WHERE s.user_id = $1
            ${date ? 'AND DATE(o.display_date) = $2' : 'AND DATE(o.display_date) = CURRENT_DATE'}
            ORDER BY o.created_at
        `;
        const params = date ? [userId, date] : [userId];

        console.log('Executing query:', query, 'with params:', params);
        const { rows } = await pool.query(query, params);

        res.json({ success: true, objectives: rows });
    } catch (error) {
        console.error('Error fetching objectives:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            query: req.query
        });
        res.status(500).json({
            success: false,
            message: `Server error fetching objectives: ${error.message}`,
            code: error.code
        });
    }
});

// Get user's streak data
app.get('/api/streak', authenticateToken, async (req, res) => {
    const userId = req.user.userId;

    try {
        // Get or create streak record
        let streakResult = await pool.query(
            'SELECT * FROM streaks WHERE user_id = $1',
            [userId]
        );

        let streak;
        if (streakResult.rows.length === 0) {
            // Create new streak record
            const newStreakResult = await pool.query(
                `INSERT INTO streaks (user_id, current_streak, longest_streak, streak_history)
                 VALUES ($1, 0, 0, '[]') RETURNING *`,
                [userId]
            );
            streak = newStreakResult.rows[0];
        } else {
            streak = streakResult.rows[0];
        }

        // Parse streak history
        const streakHistory = streak.streak_history || [];

        return res.json({
            success: true,
            streak: {
                current_streak: streak.current_streak,
                longest_streak: streak.longest_streak,
                last_streak_date: streak.last_streak_date,
                streak_history: streakHistory
            }
        });
    } catch (error) {
        console.error('Error fetching streak data:', error.message, error.stack);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch streak data',
            error: error.message
        });
    }
});

// Update streak based on daily objectives completion
app.post('/api/streak/update', authenticateToken, async (req, res) => {
    const userId = req.user.userId;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get request parameters
    const { update_completion_only, count_streak } = req.body;

    console.log('Streak update request:', { update_completion_only, count_streak });

    try {
        // Get today's objectives
        const objectivesResult = await pool.query(`
            SELECT o.*
            FROM objectives o
            JOIN chapters c ON o.chapter_id = c.id
            JOIN subjects s ON c.subject_id = s.id
            WHERE s.user_id = $1
        `, [userId]);

        const objectives = objectivesResult.rows;

        if (objectives.length === 0) {
            return res.json({
                success: true,
                message: 'No active objectives found',
                streak_updated: false
            });
        }

        // Calculate completion percentage
        const totalObjectives = objectives.length;
        const completedObjectives = objectives.filter(obj => obj.completed).length;
        const completionPercentage = Math.round((completedObjectives / totalObjectives) * 100);

        // Get or create streak record
        let streakResult = await pool.query(
            'SELECT * FROM streaks WHERE user_id = $1',
            [userId]
        );

        let streak;
        if (streakResult.rows.length === 0) {
            // Create new streak record
            const newStreakResult = await pool.query(
                `INSERT INTO streaks (user_id, current_streak, longest_streak, streak_history)
                 VALUES ($1, 0, 0, '[]') RETURNING *`,
                [userId]
            );
            streak = newStreakResult.rows[0];
        } else {
            streak = streakResult.rows[0];
        }

        // Format today's date as YYYY-MM-DD
        const todayFormatted = today.toISOString().split('T')[0];

        // Parse streak history
        let streakHistory = streak.streak_history || [];

        // Check if we already recorded today
        const todayRecord = streakHistory.find(record => record.date === todayFormatted);

        // Determine if this should count as a streak day
        // If update_completion_only is true, we're just updating the completion percentage
        // If count_streak is true, we're explicitly counting this as a streak day
        // Otherwise, use the default logic (>= 60%)
        const shouldCountAsStreakDay = update_completion_only ? false :
            count_streak ? true :
                completionPercentage >= 60;

        console.log(`Initial streak determination: completionPercentage=${completionPercentage}, shouldCountAsStreakDay=${shouldCountAsStreakDay}, count_streak=${count_streak}, update_completion_only=${update_completion_only}`);

        if (todayRecord) {
            // Update today's record
            console.log(`Updating today's record: completion ${todayRecord.completion}% -> ${completionPercentage}%`);
            todayRecord.completion = completionPercentage;

            // Only update is_streak_day if:
            // 1. We're explicitly counting it as a streak day (count_streak=true)
            // 2. It's not already a streak day and we're not just updating completion and progress >= 60%
            if (count_streak || (!todayRecord.is_streak_day && !update_completion_only && completionPercentage >= 60)) {
                console.log(`Marking today as a streak day (was: ${todayRecord.is_streak_day})`);
                todayRecord.is_streak_day = true;
            }
        } else {
            // Add new record for today
            console.log(`Adding new record for today: completion ${completionPercentage}%, is_streak_day: ${shouldCountAsStreakDay}`);
            streakHistory.push({
                date: todayFormatted,
                completion: completionPercentage,
                is_streak_day: shouldCountAsStreakDay
            });
        }

        // Sort history by date (newest first)
        streakHistory.sort((a, b) => new Date(b.date) - new Date(a.date));

        // Calculate current streak
        let currentStreak = 0;
        let isStreakBroken = false;

        // Determine if today is a streak day based on our parameters
        // If update_completion_only is true, we're just updating the completion percentage
        // If count_streak is true, we're explicitly counting this as a streak day
        // Otherwise, use the default logic (>= 60%)
        // Also check if today's record is already marked as a streak day
        const isTodayStreakDay = (todayRecord && todayRecord.is_streak_day) ? true :
            update_completion_only ? false :
                count_streak ? true :
                    completionPercentage >= 60;

        console.log(`Determining streak status: update_completion_only=${update_completion_only}, count_streak=${count_streak}, completionPercentage=${completionPercentage}, isTodayStreakDay=${isTodayStreakDay}, todayRecord=${todayRecord ? JSON.stringify(todayRecord) : 'none'}`);

        // If we're only updating completion percentage and not counting streak, return early
        if (update_completion_only) {
            // Just update the streak history without changing streak counts
            await pool.query(
                `UPDATE streaks
                 SET streak_history = $1,
                     updated_at = CURRENT_TIMESTAMP
                 WHERE user_id = $2`,
                [
                    JSON.stringify(streakHistory),
                    userId
                ]
            );

            return res.json({
                success: true,
                message: 'Completion percentage updated successfully',
                streak_updated: false,
                streak: {
                    current_streak: streak.current_streak,
                    longest_streak: streak.longest_streak,
                    last_streak_date: streak.last_streak_date,
                    completion_percentage: completionPercentage,
                    is_streak_day: false
                }
            });
        }

        if (isTodayStreakDay) {
            // Check if we've already counted today as a streak day before
            const todayAlreadyCounted = todayRecord && todayRecord.is_streak_day && !count_streak;

            if (todayAlreadyCounted) {
                // We've already counted today as a streak day before, maintain current streak
                console.log('Today already counted as streak day, maintaining current streak');
                currentStreak = streak.current_streak;
            } else {
                // This is the first time we're counting today as a streak day
                console.log('Counting today as a new streak day');
                currentStreak = 1; // Start with today

                // Check previous days
                if (streak.last_streak_date) {
                    const lastDate = new Date(streak.last_streak_date);
                    lastDate.setHours(0, 0, 0, 0);

                    // Calculate days difference
                    const diffTime = Math.abs(today - lastDate);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays === 1) {
                        // Yesterday was a streak day, continue the streak
                        currentStreak = streak.current_streak + 1;
                        console.log(`Yesterday was a streak day, continuing streak: ${currentStreak}`);
                    } else if (diffDays > 3) {
                        // More than 3 days without activity, reset streak
                        isStreakBroken = true;
                        console.log('More than 3 days without activity, resetting streak');
                    }
                }
            }
        } else {
            // Today is not a streak day, check if we need to reset
            const lastDate = streak.last_streak_date ? new Date(streak.last_streak_date) : null;

            if (lastDate) {
                lastDate.setHours(0, 0, 0, 0);
                const diffTime = Math.abs(today - lastDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays <= 3) {
                    // Less than 3 days, maintain current streak
                    currentStreak = streak.current_streak;
                } else {
                    // More than 3 days without activity, reset streak
                    isStreakBroken = true;
                }
            }
        }

        // If streak is broken, reset to 0 or 1 (if today is a streak day)
        if (isStreakBroken) {
            currentStreak = isTodayStreakDay ? 1 : 0;
        }

        // Log the streak update details
        console.log('Updating streak record with:', {
            currentStreak,
            longestStreak: Math.max(streak.longest_streak || 0, currentStreak),
            lastStreakDate: isTodayStreakDay ? todayFormatted : streak.last_streak_date,
            isTodayStreakDay,
            todayRecord: todayRecord ? {
                date: todayRecord.date,
                completion: todayRecord.completion,
                is_streak_day: todayRecord.is_streak_day
            } : null
        });

        // Update streak record
        await pool.query(
            `UPDATE streaks
             SET current_streak = $1,
                 longest_streak = $2,
                 last_streak_date = $3,
                 streak_history = $4,
                 updated_at = CURRENT_TIMESTAMP
             WHERE user_id = $5`,
            [
                currentStreak,
                Math.max(streak.longest_streak || 0, currentStreak),
                isTodayStreakDay ? todayFormatted : streak.last_streak_date,
                JSON.stringify(streakHistory),
                userId
            ]
        );

        return res.json({
            success: true,
            message: 'Streak updated successfully',
            streak_updated: true,
            streak: {
                current_streak: currentStreak,
                longest_streak: Math.max(streak.longest_streak || 0, currentStreak),
                last_streak_date: isTodayStreakDay ? todayFormatted : streak.last_streak_date,
                completion_percentage: completionPercentage,
                is_streak_day: isTodayStreakDay
            }
        });
    } catch (error) {
        console.error('Error updating streak:', error.message, error.stack);
        return res.status(500).json({
            success: false,
            message: 'Failed to update streak',
            error: error.message
        });
    }
});

// Study Plan API Endpoints
// Function to check if a study plan is realistic
async function isStudyPlanRealistic(objectives, startDate, endDate, dailyStudyTime) {
    try {
        // Calculate total days
        const totalDays = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1;

        // Calculate total study time available (in minutes)
        const totalAvailableTime = totalDays * dailyStudyTime;

        // Estimate total time needed for objectives
        // If objectives have estimated_time_minutes, use that, otherwise use a default value
        let totalNeededTime = 0;
        let totalObjectives = objectives.length;

        if (totalObjectives === 0) {
            // No objectives, so plan is realistic by default
            return {
                is_realistic: true
            };
        }

        // Check if objectives have estimated_time_minutes
        const hasTimeEstimates = objectives.some(obj => obj.estimated_time_minutes);

        if (hasTimeEstimates) {
            totalNeededTime = objectives.reduce((sum, obj) => sum + (obj.estimated_time_minutes || 30), 0);
        } else {
            // Use a default estimate of 30 minutes per objective
            totalNeededTime = totalObjectives * 30;
        }

        // Check if the plan is realistic
        const is_realistic = totalNeededTime <= totalAvailableTime;

        // If not realistic, calculate a suggested timeframe
        if (!is_realistic) {
            // Calculate minimum days needed
            const minDaysNeeded = Math.ceil(totalNeededTime / dailyStudyTime);

            // Calculate suggested end date
            const suggestedEndDate = new Date(startDate);
            suggestedEndDate.setDate(suggestedEndDate.getDate() + minDaysNeeded - 1);

            // Format the date as YYYY-MM-DD
            const formatDate = (date) => {
                return date.toISOString().split('T')[0];
            };

            return {
                is_realistic: false,
                suggestions: {
                    message: `The study plan is too ambitious. You need at least ${minDaysNeeded} days to complete all objectives with ${dailyStudyTime} minutes of daily study time.`,
                    suggested_end_date: formatDate(suggestedEndDate),
                    min_days_needed: minDaysNeeded,
                    total_objectives: totalObjectives,
                    total_needed_time: totalNeededTime,
                    total_available_time: totalAvailableTime
                }
            };
        }

        return {
            is_realistic: true
        };
    } catch (error) {
        console.error('Error checking if study plan is realistic:', error);
        return {
            is_realistic: true, // Default to true in case of error
            error: error.message
        };
    }
}

app.post('/api/create_study_plan', authenticateToken, async (req, res) => {
    const { user_id, name, start_date, end_date, daily_study_time_minutes } = req.body;
    let { curriculum_id } = req.body;  // Use let instead of const for curriculum_id
    const authUserId = req.user.userId;

    if (parseInt(user_id) !== authUserId) {
        return res.status(403).json({ success: false, message: 'Access denied' });
    }

    try {
        // Validate required fields
        if (!user_id || !name || !start_date || !end_date || !daily_study_time_minutes) {
            return res.status(400).json({ success: false, message: 'Missing required fields' });
        }

        // If curriculum_id is null or undefined, create a dummy one
        if (!curriculum_id) {
            console.log('No curriculum_id provided, creating a dummy one');
            curriculum_id = -1; // This will trigger the creation of a dummy file below
        }

        // Check if the curriculum_id exists in the files table
        const fileCheck = await pool.query(
            'SELECT id FROM files WHERE id = $1',
            [curriculum_id]
        );

        if (fileCheck.rows.length === 0) {
            // If the curriculum_id doesn't exist, create a dummy file entry
            console.log(`Creating dummy file entry for curriculum_id ${curriculum_id}`);

            // Get the subject_id from the request or use a default
            const subjectCheck = await pool.query(
                'SELECT id FROM subjects WHERE user_id = $1 LIMIT 1',
                [user_id]
            );

            let subject_id;
            if (subjectCheck.rows.length > 0) {
                subject_id = subjectCheck.rows[0].id;
            } else {
                // Create a default subject if none exists
                const subjectResult = await pool.query(
                    'INSERT INTO subjects (user_id, name) VALUES ($1, $2) RETURNING id',
                    [user_id, 'Default Subject']
                );
                subject_id = subjectResult.rows[0].id;
            }

            // Create a dummy file entry
            const fileResult = await pool.query(
                `INSERT INTO files (subject_id, filename, originalname, filetype, filepath, study_tools)
                 VALUES ($1, $2, $3, $4, $5, $6)
                 RETURNING id`,
                [subject_id, name, name, 'text', '/dummy/path', '[]']
            );

            // Use the new file ID as the curriculum_id
            curriculum_id = fileResult.rows[0].id;
        }

        // Get objectives for this curriculum
        const objectivesResult = await pool.query(
            `SELECT o.id, o.objective as text, c.title as chapter_name, s.name as subject_name
             FROM objectives o
             JOIN chapters c ON o.chapter_id = c.id
             JOIN subjects s ON c.subject_id = s.id
             JOIN files f ON c.file_id = f.id
             WHERE f.id = $1`,
            [curriculum_id]
        );

        const objectives = objectivesResult.rows;

        // Check if the study plan is realistic
        const realisticCheck = await isStudyPlanRealistic(
            objectives,
            start_date,
            end_date,
            daily_study_time_minutes
        );

        // Create study plan
        const studyPlanResult = await pool.query(
            `INSERT INTO study_plans (user_id, curriculum_id, name, start_date, end_date, daily_study_time_minutes)
             VALUES ($1, $2, $3, $4, $5, $6)
             RETURNING *`,
            [user_id, curriculum_id, name, start_date, end_date, daily_study_time_minutes]
        );

        const studyPlan = studyPlanResult.rows[0];

        if (objectives.length === 0) {
            return res.status(200).json({
                success: true,
                message: 'Study plan created but no objectives found',
                study_plan_id: studyPlan.id
            });
        }

        // Generate schedule (simple distribution)
        const startDate = new Date(start_date);
        const endDate = new Date(end_date);
        const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;

        // Distribute objectives evenly
        const objectivesPerDay = Math.ceil(objectives.length / totalDays);

        let currentDate = new Date(startDate);
        let objectiveIndex = 0;

        // Create schedule items
        while (objectiveIndex < objectives.length && currentDate <= endDate) {
            const dayObjectives = objectives.slice(
                objectiveIndex,
                Math.min(objectiveIndex + objectivesPerDay, objectives.length)
            );

            for (const objective of dayObjectives) {
                await pool.query(
                    `INSERT INTO schedule_items (study_plan_id, objective_id, scheduled_date)
                     VALUES ($1, $2, $3)`,
                    [studyPlan.id, objective.id, currentDate.toISOString().split('T')[0]]
                );
            }

            objectiveIndex += objectivesPerDay;
            currentDate.setDate(currentDate.getDate() + 1);
        }

        // Return the response with realistic check information
        return res.status(201).json({
            success: true,
            message: 'Study plan created successfully',
            study_plan_id: studyPlan.id,
            is_realistic: realisticCheck.is_realistic,
            suggestions: realisticCheck.suggestions
        });
    } catch (error) {
        console.error('Error creating study plan:', error.message, error.stack);
        return res.status(500).json({ success: false, message: `Error creating study plan: ${error.message}` });
    }
});

app.get('/api/get_study_plans/:userId', authenticateToken, async (req, res) => {
    const { userId } = req.params;
    const authUserId = req.user.userId;

    if (parseInt(userId) !== authUserId) {
        return res.status(403).json({ success: false, message: 'Access denied' });
    }

    try {
        const studyPlansResult = await pool.query(
            `SELECT sp.*, f.originalname as curriculum_title
             FROM study_plans sp
             JOIN files f ON sp.curriculum_id = f.id
             WHERE sp.user_id = $1
             ORDER BY sp.created_at DESC`,
            [userId]
        );

        const studyPlans = studyPlansResult.rows;
        const plansWithProgress = [];

        for (const plan of studyPlans) {
            // Get schedule items for this plan
            const scheduleItemsResult = await pool.query(
                `SELECT id, objective_id, is_completed
                 FROM schedule_items
                 WHERE study_plan_id = $1`,
                [plan.id]
            );

            const scheduleItems = scheduleItemsResult.rows;
            const totalObjectives = scheduleItems.length;
            const completedObjectives = scheduleItems.filter(item => item.is_completed).length;
            const percentage = totalObjectives > 0 ? Math.round((completedObjectives / totalObjectives) * 100) : 0;

            plansWithProgress.push({
                ...plan,
                start_date: plan.start_date.toISOString().split('T')[0],
                end_date: plan.end_date.toISOString().split('T')[0],
                created_at: plan.created_at.toISOString(),
                progress: {
                    total_objectives: totalObjectives,
                    completed_objectives: completedObjectives,
                    percentage
                }
            });
        }

        return res.json({ success: true, study_plans: plansWithProgress });
    } catch (error) {
        console.error('Error fetching study plans:', error.message, error.stack);
        return res.status(500).json({ success: false, message: `Error fetching study plans: ${error.message}` });
    }
});

app.get('/api/get_study_plan_details/:planId', authenticateToken, async (req, res) => {
    const { planId } = req.params;
    const userId = req.user.userId;

    try {
        // Check if the study plan belongs to the user
        const studyPlanResult = await pool.query(
            `SELECT sp.*, f.originalname as curriculum_title
             FROM study_plans sp
             JOIN files f ON sp.curriculum_id = f.id
             WHERE sp.id = $1 AND sp.user_id = $2`,
            [planId, userId]
        );

        if (studyPlanResult.rows.length === 0) {
            return res.status(404).json({ success: false, message: 'Study plan not found' });
        }

        const studyPlan = studyPlanResult.rows[0];

        // Get schedule items for this plan
        const scheduleItemsResult = await pool.query(
            `SELECT si.id, si.scheduled_date, si.is_completed, si.completion_date,
                    o.id as objective_id, o.objective as objective_text,
                    c.id as chapter_id, c.title as chapter_name,
                    s.id as subject_id, s.name as subject_name
             FROM schedule_items si
             JOIN objectives o ON si.objective_id = o.id
             JOIN chapters c ON o.chapter_id = c.id
             JOIN subjects s ON c.subject_id = s.id
             WHERE si.study_plan_id = $1
             ORDER BY si.scheduled_date, si.id`,
            [planId]
        );

        const scheduleItems = scheduleItemsResult.rows;

        // Organize by date
        const scheduleByDate = {};
        for (const item of scheduleItems) {
            const dateStr = item.scheduled_date.toISOString().split('T')[0];

            if (!scheduleByDate[dateStr]) {
                scheduleByDate[dateStr] = [];
            }

            scheduleByDate[dateStr].push({
                id: item.id,
                objective: {
                    id: item.objective_id,
                    text: item.objective_text,
                    estimated_time_minutes: 30, // Default value
                    difficulty_level: 1 // Default value
                },
                chapter: {
                    id: item.chapter_id,
                    name: item.chapter_name
                },
                subject: {
                    id: item.subject_id,
                    name: item.subject_name
                },
                is_completed: item.is_completed,
                completion_date: item.completion_date ? item.completion_date.toISOString() : null
            });
        }

        // Convert to list for easier frontend processing
        const scheduleList = Object.keys(scheduleByDate).map(date => ({
            date,
            items: scheduleByDate[date]
        }));

        // Sort by date
        scheduleList.sort((a, b) => new Date(a.date) - new Date(b.date));

        return res.json({
            success: true,
            study_plan: {
                id: studyPlan.id,
                name: studyPlan.name,
                start_date: studyPlan.start_date.toISOString().split('T')[0],
                end_date: studyPlan.end_date.toISOString().split('T')[0],
                daily_study_time_minutes: studyPlan.daily_study_time_minutes,
                schedule: scheduleList
            }
        });
    } catch (error) {
        console.error('Error fetching study plan details:', error.message, error.stack);
        return res.status(500).json({ success: false, message: `Error fetching study plan details: ${error.message}` });
    }
});

app.put('/api/update_schedule_item/:itemId', authenticateToken, async (req, res) => {
    const { itemId } = req.params;
    const { is_completed } = req.body;
    const userId = req.user.userId;

    try {
        // Check if the schedule item belongs to the user
        const itemCheck = await pool.query(
            `SELECT si.id
             FROM schedule_items si
             JOIN study_plans sp ON si.study_plan_id = sp.id
             WHERE si.id = $1 AND sp.user_id = $2`,
            [itemId, userId]
        );

        if (itemCheck.rows.length === 0) {
            return res.status(404).json({ success: false, message: 'Schedule item not found' });
        }

        // Update the schedule item
        const updateResult = await pool.query(
            `UPDATE schedule_items
             SET is_completed = $1,
                 completion_date = $2
             WHERE id = $3
             RETURNING id, is_completed, completion_date`,
            [is_completed, is_completed ? new Date() : null, itemId]
        );

        return res.json({
            success: true,
            message: 'Schedule item updated successfully',
            item: {
                id: updateResult.rows[0].id,
                is_completed: updateResult.rows[0].is_completed,
                completion_date: updateResult.rows[0].completion_date ? updateResult.rows[0].completion_date.toISOString() : null
            }
        });
    } catch (error) {
        console.error('Error updating schedule item:', error.message, error.stack);
        return res.status(500).json({ success: false, message: `Error updating schedule item: ${error.message}` });
    }
});

app.put('/api/daily_objectives/:id', authenticateToken, async (req, res) => {
    const { id } = req.params;
    const { completed } = req.body;
    const userId = req.user.userId;

    try {
        const objectiveCheck = await pool.query(
            `
            SELECT o.id
            FROM objectives o
            JOIN chapters c ON o.chapter_id = c.id
            JOIN subjects s ON c.subject_id = s.id
            WHERE o.id = $1 AND s.user_id = $2
            `,
            [id, userId]
        );

        if (objectiveCheck.rows.length === 0) {
            return res.status(403).json({ success: false, message: 'Objective not found or access denied' });
        }

        const { rows } = await pool.query(
            'UPDATE objectives SET completed = $1 WHERE id = $2 RETURNING *',
            [completed, id]
        );

        res.json({ success: true, objective: rows[0] });
    } catch (error) {
        console.error('Error updating objective:', {
            message: error.message,
            stack: error.stack,
            code: error.code
        });
        res.status(500).json({
            success: false,
            message: `Server error updating objective: ${error.message}`,
            code: error.code
        });
    }
});

async function callGeminiAPI(content) {
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
    const prompt = `Extract structured data from the following syllabus content. Return a JSON object with subjects, each containing chapters, and each chapter containing objectives. Example format:
    {
        "subjects": [
            {
                "name": "Subject Name",
                "chapters": [
                    {
                        "title": "Chapter Title",
                        "objectives": ["Objective 1", "Objective 2"]
                    }
                ]
            }
        ]
    }
    Content: ${content}`;

    try {
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const jsonStr = response.text().replace(/```json|```/g, '').trim();
        return JSON.parse(jsonStr);
    } catch (error) {
        console.error('Gemini API Error:', error.message, error.stack);
        throw new Error('Failed to process syllabus');
    }
}

app.post('/api/process-syllabus', authenticateToken, async (req, res) => {
    const { file_id, subject_id } = req.body;
    const userId = req.user.userId;

    try {
        const fileCheck = await pool.query(
            `SELECT f.id, f.filepath FROM files f
             JOIN subjects s ON f.subject_id = s.id
             WHERE f.id = $1 AND s.id = $2 AND s.user_id = $3`,
            [file_id, subject_id, userId]
        );

        if (fileCheck.rows.length === 0) {
            return res.status(403).json({ success: false, message: 'File or subject not found or access denied' });
        }

        const file = fileCheck.rows[0];

        const fileContent = fs.readFileSync(file.filepath, 'utf-8');

        const geminiResponse = await callGeminiAPI(fileContent);

        for (const subjectData of geminiResponse.subjects) {
            for (const chapter of subjectData.chapters) {
                const { rows: chapterRows } = await pool.query(
                    'INSERT INTO chapters (file_id, subject_id, title) VALUES ($1, $2, $3) RETURNING id',
                    [file_id, subject_id, chapter.title]
                );
                const chapterId = chapterRows[0].id;

                for (const objective of chapter.objectives) {
                    await pool.query(
                        'INSERT INTO objectives (chapter_id, objective, display_date) VALUES ($1, $2, $3)',
                        [chapterId, objective, new Date().toISOString().split('T')[0]]
                    );
                }
            }
        }

        res.json({ success: true, message: 'Syllabus processed successfully', data: geminiResponse });
    } catch (error) {
        console.error('Syllabus processing error:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Syllabus processing failed' });
    }
});

app.get('/api/chapters/:fileId', authenticateToken, async (req, res) => {
    const { fileId } = req.params;
    const userId = req.user.userId;

    try {
        const fileCheck = await pool.query(
            `SELECT f.id FROM files f
             JOIN subjects s ON f.subject_id = s.id
             WHERE f.id = $1 AND s.user_id = $2`,
            [fileId, userId]
        );

        if (fileCheck.rows.length === 0) {
            return res.status(403).json({ success: false, message: 'File not found or access denied' });
        }

        const { rows } = await pool.query(
            'SELECT id, title, chapter_number, created_at FROM chapters WHERE file_id = $1 ORDER BY created_at',
            [fileId]
        );

        res.json({ success: true, chapters: rows });
    } catch (error) {
        console.error('Error fetching chapters:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching chapters' });
    }
});

// Add endpoint for getting chapters by subject ID
app.get('/api/subjects/:subjectId/chapters', authenticateToken, async (req, res) => {
    const { subjectId } = req.params;
    const userId = req.user.userId;

    try {
        console.log(`Fetching chapters for subject ID: ${subjectId} for user ID: ${userId}`);

        // First check if the subject exists
        const subjectExistsResult = await pool.query(
            'SELECT id, name, user_id FROM subjects WHERE id = $1',
            [subjectId]
        );

        if (subjectExistsResult.rows.length === 0) {
            console.log(`Subject ID ${subjectId} not found`);
            return res.status(404).json({ success: false, message: 'Subject not found' });
        }

        const subject = subjectExistsResult.rows[0];
        console.log(`Found subject: ${subject.name}, user_id: ${subject.user_id}`);

        // For debugging purposes, log all subjects for this user
        const userSubjectsResult = await pool.query(
            'SELECT id, name FROM subjects WHERE user_id = $1',
            [userId]
        );
        console.log(`User ${userId} has ${userSubjectsResult.rows.length} subjects:`);
        userSubjectsResult.rows.forEach(s => {
            console.log(`- Subject ID: ${s.id}, Name: ${s.name}`);
        });

        // Check if the user has access to this subject
        if (subject.user_id !== userId) {
            console.log(`This appears to be a shared subject. Subject ${subjectId} is owned by user ${subject.user_id}, not user ${userId}`);
            console.log(`Allowing access to chapters for subject ${subjectId} despite ownership mismatch`);
        }

        // Get chapters for this subject
        console.log(`Querying chapters for subject ID: ${subjectId}`);

        // First, check if the chapters table exists and has the expected columns
        const tableCheck = await pool.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'chapters'
            ORDER BY column_name
        `);

        console.log(`Chapters table has ${tableCheck.rows.length} columns:`);
        tableCheck.rows.forEach(col => {
            console.log(`- ${col.column_name}`);
        });

        // Now query the chapters
        const chaptersResult = await pool.query(
            'SELECT id, title, chapter_number, created_at, subject_id FROM chapters WHERE subject_id = $1 ORDER BY created_at',
            [subjectId]
        );

        console.log(`Found ${chaptersResult.rows.length} chapters for subject ID: ${subjectId}`);

        // If no chapters found, check if there are any chapters in the database at all
        if (chaptersResult.rows.length === 0) {
            const allChaptersResult = await pool.query(
                'SELECT COUNT(*) as total FROM chapters'
            );
            console.log(`Total chapters in database: ${allChaptersResult.rows[0].total}`);

            // Check if there are any chapters with this subject_id
            const subjectChaptersResult = await pool.query(
                'SELECT COUNT(*) as total FROM chapters WHERE subject_id = $1',
                [subjectId]
            );
            console.log(`Total chapters with subject_id ${subjectId}: ${subjectChaptersResult.rows[0].total}`);
        }

        const chapters = chaptersResult.rows;

        // For each chapter, get its objectives
        for (const chapter of chapters) {
            const objectivesResult = await pool.query(
                'SELECT id, objective as text, completed FROM objectives WHERE chapter_id = $1 ORDER BY created_at',
                [chapter.id]
            );
            chapter.objectives = objectivesResult.rows;
        }

        res.json({
            success: true,
            subject_name: subject.name,
            chapters: chapters
        });
    } catch (error) {
        console.error('Error fetching chapters by subject ID:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching chapters' });
    }
});

// Get a specific chapter by ID
app.get('/api/chapters/:chapterId', authenticateToken, async (req, res) => {
    const { chapterId } = req.params;
    const userId = req.user.userId;

    try {
        console.log(`Fetching chapter ID: ${chapterId} for user ID: ${userId}`);

        // First, check if the chapter exists
        const chapterExistsResult = await pool.query(
            'SELECT id, title, chapter_number, content, subject_id, file_id, created_at FROM chapters WHERE id = $1',
            [chapterId]
        );

        if (chapterExistsResult.rows.length === 0) {
            console.log(`Chapter ID ${chapterId} not found`);
            return res.status(404).json({ success: false, message: 'Chapter not found' });
        }

        const chapter = chapterExistsResult.rows[0];
        console.log(`Found chapter: ${chapter.title}, subject_id: ${chapter.subject_id}, file_id: ${chapter.file_id}`);

        // Now verify the user has access to this chapter's subject
        console.log(`Checking if user ${userId} has access to subject ${chapter.subject_id}`);

        // First, log the subject details to verify it exists
        const subjectDetailsResult = await pool.query(
            'SELECT id, name, user_id FROM subjects WHERE id = $1',
            [chapter.subject_id]
        );

        if (subjectDetailsResult.rows.length === 0) {
            console.log(`Subject ID ${chapter.subject_id} not found in database`);
            return res.status(404).json({ success: false, message: 'Subject not found' });
        }

        const subjectDetails = subjectDetailsResult.rows[0];
        console.log(`Subject details: ID=${subjectDetails.id}, Name=${subjectDetails.name}, Owner User ID=${subjectDetails.user_id}`);

        // For debugging purposes, log all subjects for this user
        const userSubjectsResult = await pool.query(
            'SELECT id, name FROM subjects WHERE user_id = $1',
            [userId]
        );
        console.log(`User ${userId} has ${userSubjectsResult.rows.length} subjects:`);
        userSubjectsResult.rows.forEach(subject => {
            console.log(`- Subject ID: ${subject.id}, Name: ${subject.name}`);
        });

        // Now check if the user has access to this subject
        const subjectAccessResult = await pool.query(
            'SELECT id FROM subjects WHERE id = $1 AND user_id = $2',
            [chapter.subject_id, userId]
        );

        if (subjectAccessResult.rows.length === 0) {
            // For debugging, let's check if this is a shared subject that needs special handling
            if (subjectDetails.user_id !== userId) {
                console.log(`This appears to be a shared subject. Subject ${chapter.subject_id} is owned by user ${subjectDetails.user_id}, not user ${userId}`);

                // For now, we'll allow access to the chapter even if the subject belongs to another user
                // This is a temporary fix until proper subject sharing is implemented
                console.log(`Allowing access to chapter ${chapterId} despite subject ownership mismatch`);
            } else {
                console.log(`User ${userId} does not have access to subject ${chapter.subject_id}`);
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this chapter',
                    details: `This chapter belongs to subject ${chapter.subject_id} which is owned by user ${subjectDetails.user_id}, not user ${userId}`
                });
            }
        }

        // If file_id is present, check if the file exists and is accessible
        if (chapter.file_id) {
            console.log(`Checking if file ID ${chapter.file_id} exists and is accessible`);

            const fileCheckResult = await pool.query(
                `SELECT f.id
                 FROM files f
                 JOIN subjects s ON f.subject_id = s.id
                 WHERE f.id = $1 AND s.user_id = $2`,
                [chapter.file_id, userId]
            );

            if (fileCheckResult.rows.length === 0) {
                console.log(`File ID ${chapter.file_id} not found or not accessible by user ${userId}`);
                // We'll continue anyway since the file might have been deleted but the chapter is still valid
                console.log(`Continuing anyway since the chapter is still accessible`);
            } else {
                console.log(`File ID ${chapter.file_id} is accessible by user ${userId}`);
            }
        }

        // Get objectives for this chapter
        const objectivesResult = await pool.query(
            'SELECT id, objective, completed, created_at FROM objectives WHERE chapter_id = $1 ORDER BY created_at',
            [chapterId]
        );

        chapter.objectives = objectivesResult.rows;

        // Get tools for this chapter
        const toolsResult = await pool.query(
            `SELECT ct.id, ct.chapter_id, ct.tool_id, ct.settings, st.name, st.description
             FROM chapter_tools ct
             JOIN study_tools st ON ct.tool_id = st.id
             WHERE ct.chapter_id = $1
             ORDER BY st.name`,
            [chapterId]
        );

        chapter.tools = toolsResult.rows;

        console.log(`Successfully returning chapter data for chapter ID: ${chapterId} with ${chapter.objectives.length} objectives and ${chapter.tools ? chapter.tools.length : 0} tools`);
        res.json({ success: true, chapter: chapter });
    } catch (error) {
        console.error('Error fetching chapter:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching chapter' });
    }
});

app.get('/api/objectives/:chapterId', authenticateToken, async (req, res) => {
    const { chapterId } = req.params;
    const userId = req.user.userId;

    try {
        // First check if the chapter exists
        const chapterExistsResult = await pool.query(
            'SELECT id, subject_id FROM chapters WHERE id = $1',
            [chapterId]
        );

        if (chapterExistsResult.rows.length === 0) {
            console.log(`Chapter ID ${chapterId} not found`);
            return res.status(404).json({ success: false, message: 'Chapter not found' });
        }

        const chapter = chapterExistsResult.rows[0];

        // Now check if the user has access to this chapter's subject
        const subjectDetailsResult = await pool.query(
            'SELECT id, name, user_id FROM subjects WHERE id = $1',
            [chapter.subject_id]
        );

        if (subjectDetailsResult.rows.length === 0) {
            console.log(`Subject ID ${chapter.subject_id} not found in database`);
            return res.status(404).json({ success: false, message: 'Subject not found' });
        }

        const subjectDetails = subjectDetailsResult.rows[0];

        // Check if the user has access to this subject
        const subjectAccessResult = await pool.query(
            'SELECT id FROM subjects WHERE id = $1 AND user_id = $2',
            [chapter.subject_id, userId]
        );

        // Allow access even if subject belongs to another user (temporary fix for shared subjects)
        if (subjectAccessResult.rows.length === 0 && subjectDetails.user_id !== userId) {
            console.log(`Allowing access to objectives for chapter ${chapterId} despite subject ownership mismatch`);
        } else if (subjectAccessResult.rows.length === 0) {
            console.log(`User ${userId} does not have access to subject ${chapter.subject_id}`);
            return res.status(403).json({ success: false, message: 'Access denied to this chapter' });
        }

        const { rows } = await pool.query(
            'SELECT id, objective, completed, created_at FROM objectives WHERE chapter_id = $1 ORDER BY created_at',
            [chapterId]
        );

        res.json({ success: true, objectives: rows });
    } catch (error) {
        console.error('Error fetching objectives:', error.message, error.stack);
        res.status(500).json({ success: false, message: 'Server error fetching objectives' });
    }
});

// Kanban API endpoints - Complete CRUD operations
app.get('/api/kanban', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const subjectId = req.query.subjectId;
        const chapterId = req.query.chapterId;

        let query = `SELECT kb.*, s.name as subject_name, c.title as chapter_title
                     FROM kanban_board kb
                     LEFT JOIN subjects s ON kb.subject_id = s.id
                     LEFT JOIN chapters c ON kb.chapter_id = c.id
                     WHERE kb.user_id = $1`;
        let params = [userId];

        if (subjectId && subjectId !== 'undefined') {
            query += ' AND kb.subject_id = $2';
            params.push(subjectId);
        }

        if (chapterId && chapterId !== 'undefined') {
            query += ` AND kb.chapter_id = $${params.length + 1}`;
            params.push(chapterId);
        }

        query += ' ORDER BY kb.position ASC, kb.created_at DESC';

        const result = await pool.query(query, params);

        console.log(`Retrieved ${result.rows.length} kanban tasks for user ${userId}`);

        return res.json({
            success: true,
            tasks: result.rows
        });
    } catch (error) {
        console.error('Error fetching kanban tasks:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch kanban tasks'
        });
    }
});

// Create a new kanban task
app.post('/api/kanban', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const {
            subject_id,
            chapter_id,
            title,
            description,
            status = 'To Do',
            priority = 'medium',
            due_date,
            tags,
            position = 0
        } = req.body;

        if (!title || !subject_id) {
            return res.status(400).json({
                success: false,
                message: 'Title and subject_id are required'
            });
        }

        // Verify subject ownership
        const subjectCheck = await pool.query(
            'SELECT id FROM subjects WHERE id = $1 AND user_id = $2',
            [subject_id, userId]
        );

        if (subjectCheck.rows.length === 0) {
            return res.status(403).json({
                success: false,
                message: 'Subject not found or access denied'
            });
        }

        // If chapter_id is provided, verify chapter ownership
        if (chapter_id) {
            const chapterCheck = await pool.query(
                `SELECT c.id FROM chapters c
                 JOIN subjects s ON c.subject_id = s.id
                 WHERE c.id = $1 AND s.user_id = $2`,
                [chapter_id, userId]
            );

            if (chapterCheck.rows.length === 0) {
                return res.status(403).json({
                    success: false,
                    message: 'Chapter not found or access denied'
                });
            }
        }

        // Check which columns exist in the table
        const columnCheck = await pool.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'kanban_board' AND table_schema = 'public'
        `);

        const existingColumns = columnCheck.rows.map(row => row.column_name);
        console.log('Available kanban_board columns:', existingColumns);

        let result;

        if (existingColumns.includes('title')) {
            // New schema - use title, description, etc.
            result = await pool.query(
                `INSERT INTO kanban_board
                 (user_id, subject_id, chapter_id, title, description, status, priority, due_date, tags, position, task)
                 VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $4)
                 RETURNING *`,
                [userId, subject_id, chapter_id, title, description, status, priority, due_date, tags, position]
            );
        } else {
            // Old schema - use task column
            result = await pool.query(
                `INSERT INTO kanban_board
                 (user_id, subject_id, task, status)
                 VALUES ($1, $2, $3, $4)
                 RETURNING *`,
                [userId, subject_id, title, status]
            );
        }

        console.log(`Created kanban task for user ${userId}`);

        return res.status(201).json({
            success: true,
            task: result.rows[0]
        });
    } catch (error) {
        console.error('Error creating kanban task:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to create kanban task'
        });
    }
});

// Update a kanban task
app.put('/api/kanban/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const taskId = req.params.id;
        const {
            title,
            description,
            status,
            priority,
            due_date,
            tags,
            position
        } = req.body;

        // Check if task exists and belongs to user
        const taskCheck = await pool.query(
            `SELECT kb.id FROM kanban_board kb
             JOIN subjects s ON kb.subject_id = s.id
             WHERE kb.id = $1 AND s.user_id = $2`,
            [taskId, userId]
        );

        if (taskCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Task not found or access denied'
            });
        }

        // Build dynamic update query
        const updateFields = [];
        const updateValues = [];
        let paramCount = 1;

        if (title !== undefined) {
            updateFields.push(`title = $${paramCount++}`);
            updateValues.push(title);
        }
        if (description !== undefined) {
            updateFields.push(`description = $${paramCount++}`);
            updateValues.push(description);
        }
        if (status !== undefined) {
            updateFields.push(`status = $${paramCount++}`);
            updateValues.push(status);
        }
        if (priority !== undefined) {
            updateFields.push(`priority = $${paramCount++}`);
            updateValues.push(priority);
        }
        if (due_date !== undefined) {
            updateFields.push(`due_date = $${paramCount++}`);
            updateValues.push(due_date);
        }
        if (tags !== undefined) {
            updateFields.push(`tags = $${paramCount++}`);
            updateValues.push(tags);
        }
        if (position !== undefined) {
            updateFields.push(`position = $${paramCount++}`);
            updateValues.push(position);
        }

        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        updateValues.push(taskId);

        const query = `UPDATE kanban_board SET ${updateFields.join(', ')} WHERE id = $${paramCount} RETURNING *`;

        const result = await pool.query(query, updateValues);

        console.log(`Updated kanban task ${taskId} for user ${userId}`);

        return res.json({
            success: true,
            task: result.rows[0]
        });
    } catch (error) {
        console.error('Error updating kanban task:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update kanban task'
        });
    }
});

// Delete a kanban task
app.delete('/api/kanban/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const taskId = req.params.id;

        // Check if task exists and belongs to user
        const taskCheck = await pool.query(
            `SELECT kb.id FROM kanban_board kb
             JOIN subjects s ON kb.subject_id = s.id
             WHERE kb.id = $1 AND s.user_id = $2`,
            [taskId, userId]
        );

        if (taskCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Task not found or access denied'
            });
        }

        await pool.query(
            'DELETE FROM kanban_board WHERE id = $1',
            [taskId]
        );

        console.log(`Deleted kanban task ${taskId} for user ${userId}`);

        return res.json({
            success: true,
            message: 'Task deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting kanban task:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to delete kanban task'
        });
    }
});

// Bulk update kanban task positions (for drag and drop)
app.put('/api/kanban/bulk-update', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { tasks } = req.body;

        if (!Array.isArray(tasks)) {
            return res.status(400).json({
                success: false,
                message: 'Tasks must be an array'
            });
        }

        // Start transaction
        await pool.query('BEGIN');

        for (const task of tasks) {
            const { id, status, position } = task;

            // Verify task ownership
            const taskCheck = await pool.query(
                `SELECT kb.id FROM kanban_board kb
                 JOIN subjects s ON kb.subject_id = s.id
                 WHERE kb.id = $1 AND s.user_id = $2`,
                [id, userId]
            );

            if (taskCheck.rows.length === 0) {
                await pool.query('ROLLBACK');
                return res.status(403).json({
                    success: false,
                    message: `Task ${id} not found or access denied`
                });
            }

            // Update task
            await pool.query(
                `UPDATE kanban_board
                 SET status = $1, position = $2, updated_at = CURRENT_TIMESTAMP
                 WHERE id = $3`,
                [status, position, id]
            );
        }

        await pool.query('COMMIT');

        console.log(`Bulk updated ${tasks.length} kanban tasks for user ${userId}`);

        return res.json({
            success: true,
            message: 'Tasks updated successfully'
        });
    } catch (error) {
        await pool.query('ROLLBACK');
        console.error('Error bulk updating kanban tasks:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update tasks'
        });
    }
});

// Eisenhower API endpoints - Complete CRUD operations
app.get('/api/eisenhower', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const subjectId = req.query.subjectId;

        let query = 'SELECT * FROM eisenhower WHERE user_id = $1';
        let params = [userId];

        if (subjectId && subjectId !== 'undefined') {
            query += ' AND subject_id = $2';
            params.push(subjectId);
        }

        query += ' ORDER BY created_at DESC';

        const result = await pool.query(query, params);

        console.log(`Retrieved ${result.rows.length} eisenhower tasks for user ${userId}`);

        return res.json({
            success: true,
            tasks: result.rows
        });
    } catch (error) {
        console.error('Error fetching eisenhower tasks:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch eisenhower tasks'
        });
    }
});

// Create a new Eisenhower task
app.post('/api/eisenhower', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { subject_id, task, quadrant, priority, due_date } = req.body;

        if (!task || !quadrant) {
            return res.status(400).json({
                success: false,
                message: 'Task and quadrant are required'
            });
        }

        // Validate quadrant
        const validQuadrants = ['Urgent-Important', 'Not Urgent-Important', 'Urgent-Not Important', 'Not Urgent-Not Important'];
        if (!validQuadrants.includes(quadrant)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid quadrant'
            });
        }

        const result = await pool.query(
            `INSERT INTO eisenhower (user_id, subject_id, task, quadrant, priority, due_date, created_at, updated_at)
             VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
             RETURNING *`,
            [userId, subject_id || null, task, quadrant, priority || 'medium', due_date || null]
        );

        console.log(`Created eisenhower task for user ${userId}`);

        return res.json({
            success: true,
            task: result.rows[0]
        });
    } catch (error) {
        console.error('Error creating eisenhower task:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to create eisenhower task'
        });
    }
});

// Update an Eisenhower task
app.put('/api/eisenhower/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const taskId = req.params.id;
        const { task, quadrant, priority, due_date, completed } = req.body;

        // Check if task exists and belongs to user
        const taskCheck = await pool.query(
            'SELECT id FROM eisenhower WHERE id = $1 AND user_id = $2',
            [taskId, userId]
        );

        if (taskCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Task not found or access denied'
            });
        }

        // Validate quadrant if provided
        if (quadrant) {
            const validQuadrants = ['Urgent-Important', 'Not Urgent-Important', 'Urgent-Not Important', 'Not Urgent-Not Important'];
            if (!validQuadrants.includes(quadrant)) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid quadrant'
                });
            }
        }

        // Build update query dynamically
        let updateFields = [];
        let params = [];
        let paramCount = 1;

        if (task !== undefined) {
            updateFields.push(`task = $${paramCount++}`);
            params.push(task);
        }
        if (quadrant !== undefined) {
            updateFields.push(`quadrant = $${paramCount++}`);
            params.push(quadrant);
        }
        if (priority !== undefined) {
            updateFields.push(`priority = $${paramCount++}`);
            params.push(priority);
        }
        if (due_date !== undefined) {
            updateFields.push(`due_date = $${paramCount++}`);
            params.push(due_date);
        }
        if (completed !== undefined) {
            updateFields.push(`completed = $${paramCount++}`);
            params.push(completed);
        }

        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        params.push(taskId, userId);

        const result = await pool.query(
            `UPDATE eisenhower SET ${updateFields.join(', ')}
             WHERE id = $${paramCount++} AND user_id = $${paramCount++}
             RETURNING *`,
            params
        );

        console.log(`Updated eisenhower task ${taskId} for user ${userId}`);

        return res.json({
            success: true,
            task: result.rows[0]
        });
    } catch (error) {
        console.error('Error updating eisenhower task:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update eisenhower task'
        });
    }
});

// Delete an Eisenhower task
app.delete('/api/eisenhower/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const taskId = req.params.id;

        // Check if task exists and belongs to user
        const taskCheck = await pool.query(
            'SELECT id FROM eisenhower WHERE id = $1 AND user_id = $2',
            [taskId, userId]
        );

        if (taskCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Task not found or access denied'
            });
        }

        await pool.query(
            'DELETE FROM eisenhower WHERE id = $1 AND user_id = $2',
            [taskId, userId]
        );

        console.log(`Deleted eisenhower task ${taskId} for user ${userId}`);

        return res.json({
            success: true,
            message: 'Task deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting eisenhower task:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to delete eisenhower task'
        });
    }
});

// Calendar Events API endpoints
// Get all calendar events for a user
app.get('/api/calendar-events', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { month, year } = req.query;

        let query = `
            SELECT
                id,
                user_id,
                subject_id,
                title,
                description,
                event_date,
                event_time,
                event_type,
                created_at
            FROM calendar_events
            WHERE user_id = $1
        `;

        let params = [userId];

        // Filter by month and year if provided
        if (month && year) {
            query += ` AND EXTRACT(MONTH FROM event_date) = $2 AND EXTRACT(YEAR FROM event_date) = $3`;
            params.push(month, year);
        }

        query += ' ORDER BY event_date, event_time';

        const result = await pool.query(query, params);

        console.log(`Retrieved ${result.rows.length} calendar events for user ${userId}`);

        return res.json({
            success: true,
            events: result.rows
        });
    } catch (error) {
        console.error('Error fetching calendar events:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch calendar events'
        });
    }
});

// Add a new calendar event
app.post('/api/calendar-events', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { subject_id, title, description, event_date, event_time, event_type } = req.body;

        if (!title || !event_date || !event_type) {
            return res.status(400).json({
                success: false,
                message: 'Title, date, and event type are required'
            });
        }

        // Validate event_type
        if (!['exam', 'quiz'].includes(event_type)) {
            return res.status(400).json({
                success: false,
                message: 'Event type must be either "exam" or "quiz"'
            });
        }

        const result = await pool.query(
            `INSERT INTO calendar_events
            (user_id, subject_id, title, description, event_date, event_time, event_type)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *`,
            [userId, subject_id, title, description, event_date, event_time, event_type]
        );

        return res.status(201).json({
            success: true,
            event: result.rows[0],
            message: 'Event created successfully'
        });
    } catch (error) {
        console.error('Error creating calendar event:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to create calendar event'
        });
    }
});

// Update a calendar event
app.put('/api/calendar-events/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const eventId = req.params.id;
        const { subject_id, title, description, event_date, event_time, event_type } = req.body;

        // Check if event exists and belongs to user
        const eventCheck = await pool.query(
            'SELECT id FROM calendar_events WHERE id = $1 AND user_id = $2',
            [eventId, userId]
        );

        if (eventCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Event not found or access denied'
            });
        }

        // Validate event_type if provided
        if (event_type && !['exam', 'quiz'].includes(event_type)) {
            return res.status(400).json({
                success: false,
                message: 'Event type must be either "exam" or "quiz"'
            });
        }

        const result = await pool.query(
            `UPDATE calendar_events
            SET
                subject_id = COALESCE($1, subject_id),
                title = COALESCE($2, title),
                description = COALESCE($3, description),
                event_date = COALESCE($4, event_date),
                event_time = COALESCE($5, event_time),
                event_type = COALESCE($6, event_type)
            WHERE id = $7 AND user_id = $8
            RETURNING *`,
            [subject_id, title, description, event_date, event_time, event_type, eventId, userId]
        );

        return res.json({
            success: true,
            event: result.rows[0],
            message: 'Event updated successfully'
        });
    } catch (error) {
        console.error('Error updating calendar event:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update calendar event'
        });
    }
});

// Delete a calendar event
app.delete('/api/calendar-events/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const eventId = req.params.id;

        // Check if event exists and belongs to user
        const eventCheck = await pool.query(
            'SELECT id FROM calendar_events WHERE id = $1 AND user_id = $2',
            [eventId, userId]
        );

        if (eventCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Event not found or access denied'
            });
        }

        await pool.query(
            'DELETE FROM calendar_events WHERE id = $1 AND user_id = $2',
            [eventId, userId]
        );

        return res.json({
            success: true,
            message: 'Event deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting calendar event:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to delete calendar event'
        });
    }
});

// Add test data insertion function
async function insertTestData() {
    try {
        // Check if tables exist first
        const tableCheck = await pool.query(`
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND (table_name = 'kanban_board' OR table_name = 'eisenhower' OR table_name = 'pomodoro_sessions')
        `);

        const existingTables = tableCheck.rows.map(row => row.table_name);

        // Create tables if they don't exist
        if (!existingTables.includes('kanban_board')) {
            console.log('Creating kanban_board table...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS kanban_board (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    title TEXT NOT NULL,
                    description TEXT,
                    status TEXT NOT NULL DEFAULT 'To Do' CHECK (status IN ('To Do', 'In Progress', 'Done')),
                    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
                    due_date DATE,
                    tags TEXT,
                    position INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);
        } else {
            // Update existing table to add new columns if they don't exist
            console.log('Checking kanban_board table structure...');

            const columnCheck = await pool.query(`
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'kanban_board' AND table_schema = 'public'
            `);

            const existingColumns = columnCheck.rows.map(row => row.column_name);

            // Add missing columns
            if (!existingColumns.includes('chapter_id')) {
                await pool.query('ALTER TABLE kanban_board ADD COLUMN chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE');
                console.log('Added chapter_id column to kanban_board');
            }

            if (!existingColumns.includes('title')) {
                await pool.query('ALTER TABLE kanban_board ADD COLUMN title TEXT');
                // Migrate existing data
                await pool.query('UPDATE kanban_board SET title = task WHERE title IS NULL');
                await pool.query('ALTER TABLE kanban_board ALTER COLUMN title SET NOT NULL');
                console.log('Added title column to kanban_board');
            }

            if (!existingColumns.includes('description')) {
                await pool.query('ALTER TABLE kanban_board ADD COLUMN description TEXT');
                console.log('Added description column to kanban_board');
            }

            if (!existingColumns.includes('priority')) {
                await pool.query(`ALTER TABLE kanban_board ADD COLUMN priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high'))`);
                console.log('Added priority column to kanban_board');
            }

            if (!existingColumns.includes('due_date')) {
                await pool.query('ALTER TABLE kanban_board ADD COLUMN due_date DATE');
                console.log('Added due_date column to kanban_board');
            }

            if (!existingColumns.includes('tags')) {
                await pool.query('ALTER TABLE kanban_board ADD COLUMN tags TEXT');
                console.log('Added tags column to kanban_board');
            }

            if (!existingColumns.includes('position')) {
                await pool.query('ALTER TABLE kanban_board ADD COLUMN position INTEGER DEFAULT 0');
                console.log('Added position column to kanban_board');
            }

            if (!existingColumns.includes('updated_at')) {
                await pool.query('ALTER TABLE kanban_board ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP');
                console.log('Added updated_at column to kanban_board');
            }
        }

        if (!existingTables.includes('eisenhower')) {
            console.log('Creating eisenhower table...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS eisenhower (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                    task TEXT NOT NULL,
                    quadrant TEXT NOT NULL CHECK (quadrant IN ('Urgent-Important', 'Not Urgent-Important', 'Urgent-Not Important', 'Not Urgent-Not Important')),
                    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
                    due_date DATE,
                    completed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);
        } else {
            // Add missing columns if they don't exist
            try {
                await pool.query(`
                    ALTER TABLE eisenhower
                    ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
                    ADD COLUMN IF NOT EXISTS due_date DATE,
                    ADD COLUMN IF NOT EXISTS completed BOOLEAN DEFAULT FALSE,
                    ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                `);
                console.log('Added missing columns to eisenhower table');
            } catch (error) {
                console.log('Columns may already exist or error adding them:', error.message);
            }
        }

        // Note: We're no longer inserting test data with hardcoded user IDs.
        // Test data will be created for each user when they first access the respective features.
        console.log('Database tables created successfully');
    } catch (error) {
        console.error('Error setting up database tables:', error);
    }
}

// Call this function after database connection is established
pool.connect()
    .then(() => {
        console.log('Database connected successfully');
        return insertTestData();
    })
    .then(() => {
        console.log('Database setup complete with test data');
    })
    .catch(err => {
        console.error('Database connection error:', err);
        app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
    });

// Retrospective API endpoints
app.get('/api/retrospectives', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const subjectId = req.query.subjectId;

        let query = 'SELECT * FROM retrospective WHERE user_id = $1';
        let params = [userId];

        if (subjectId && subjectId !== 'undefined') {
            query += ' AND subject_id = $2';
            params.push(subjectId);
        }

        query += ' ORDER BY created_at DESC';

        const result = await pool.query(query, params);

        console.log(`Retrieved ${result.rows.length} retrospective entries for user ${userId}`);

        return res.json({
            success: true,
            retrospectives: result.rows
        });
    } catch (error) {
        console.error('Error fetching retrospective entries:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch retrospective entries'
        });
    }
});

app.post('/api/retrospectives', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { subject_id, went_well, to_improve, action_items } = req.body;

        if (!went_well && !to_improve && !action_items) {
            return res.status(400).json({
                success: false,
                message: 'At least one field (went_well, to_improve, or action_items) is required'
            });
        }

        const result = await pool.query(
            `INSERT INTO retrospective
            (user_id, subject_id, went_well, to_improve, action_items)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *`,
            [userId, subject_id, went_well, to_improve, action_items]
        );

        return res.status(201).json({
            success: true,
            retrospective: result.rows[0],
            message: 'Retrospective created successfully'
        });
    } catch (error) {
        console.error('Error creating retrospective entry:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to create retrospective entry'
        });
    }
});

app.put('/api/retrospectives/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const retrospectiveId = req.params.id;
        const { subject_id, went_well, to_improve, action_items } = req.body;

        // Check if retrospective exists and belongs to user
        const retrospectiveCheck = await pool.query(
            'SELECT id FROM retrospective WHERE id = $1 AND user_id = $2',
            [retrospectiveId, userId]
        );

        if (retrospectiveCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Retrospective not found or access denied'
            });
        }

        const result = await pool.query(
            `UPDATE retrospective
            SET
                subject_id = COALESCE($1, subject_id),
                went_well = COALESCE($2, went_well),
                to_improve = COALESCE($3, to_improve),
                action_items = COALESCE($4, action_items)
            WHERE id = $5 AND user_id = $6
            RETURNING *`,
            [subject_id, went_well, to_improve, action_items, retrospectiveId, userId]
        );

        return res.json({
            success: true,
            retrospective: result.rows[0],
            message: 'Retrospective updated successfully'
        });
    } catch (error) {
        console.error('Error updating retrospective entry:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update retrospective entry'
        });
    }
});

app.delete('/api/retrospectives/:id', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const retrospectiveId = req.params.id;

        // Check if retrospective exists and belongs to user
        const retrospectiveCheck = await pool.query(
            'SELECT id FROM retrospective WHERE id = $1 AND user_id = $2',
            [retrospectiveId, userId]
        );

        if (retrospectiveCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Retrospective not found or access denied'
            });
        }

        await pool.query(
            'DELETE FROM retrospective WHERE id = $1 AND user_id = $2',
            [retrospectiveId, userId]
        );

        return res.json({
            success: true,
            message: 'Retrospective deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting retrospective entry:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to delete retrospective entry'
        });
    }
});

// Pomodoro API endpoints
app.get('/api/pomodoro', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        // Get user's pomodoro settings
        const settingsResult = await pool.query(
            'SELECT * FROM pomodoro_timer WHERE user_id = $1 ORDER BY created_at DESC LIMIT 1',
            [userId]
        );

        // Get user's pomodoro sessions
        const sessionsResult = await pool.query(
            'SELECT * FROM pomodoro_sessions WHERE user_id = $1 ORDER BY created_at DESC LIMIT 20',
            [userId]
        );

        let settings = null;
        if (settingsResult.rows.length > 0) {
            settings = settingsResult.rows[0];
        } else {
            // Create default settings if none exist
            const defaultSettings = await pool.query(
                `INSERT INTO pomodoro_timer
                (user_id, duration_minutes, break_duration_minutes, sessions_completed)
                VALUES ($1, 25, 5, 0)
                RETURNING *`,
                [userId]
            );
            settings = defaultSettings.rows[0];
        }

        return res.json({
            success: true,
            settings: settings,
            sessions: sessionsResult.rows
        });
    } catch (error) {
        console.error('Error fetching pomodoro data:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch pomodoro data'
        });
    }
});

app.post('/api/pomodoro/settings', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { duration_minutes, break_duration_minutes } = req.body;

        if (!duration_minutes || !break_duration_minutes) {
            return res.status(400).json({
                success: false,
                message: 'Duration and break duration are required'
            });
        }

        // Update or create settings
        const result = await pool.query(
            `INSERT INTO pomodoro_timer
            (user_id, duration_minutes, break_duration_minutes)
            VALUES ($1, $2, $3)
            RETURNING *`,
            [userId, duration_minutes, break_duration_minutes]
        );

        return res.status(201).json({
            success: true,
            settings: result.rows[0],
            message: 'Pomodoro settings updated successfully'
        });
    } catch (error) {
        console.error('Error updating pomodoro settings:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update pomodoro settings'
        });
    }
});

app.post('/api/pomodoro/sessions', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { duration_minutes, type, completed, subject_id } = req.body;

        if (!duration_minutes || !type) {
            return res.status(400).json({
                success: false,
                message: 'Duration and session type are required'
            });
        }

        // Create new session record
        const result = await pool.query(
            `INSERT INTO pomodoro_sessions
            (user_id, subject_id, duration_minutes, type, completed)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *`,
            [userId, subject_id, duration_minutes, type, completed]
        );

        // If it's a completed work session, increment the sessions_completed counter
        if (type === 'work' && completed) {
            await pool.query(
                `UPDATE pomodoro_timer
                SET sessions_completed = sessions_completed + 1
                WHERE user_id = $1
                ORDER BY created_at DESC
                LIMIT 1`,
                [userId]
            );
        }

        return res.status(201).json({
            success: true,
            session: result.rows[0],
            message: 'Pomodoro session recorded successfully'
        });
    } catch (error) {
        console.error('Error recording pomodoro session:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to record pomodoro session'
        });
    }
});

// Leitner System API endpoints
app.get('/api/leitner/stats', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        // Check if the leitner_system table exists
        const leitnerTableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'leitner_system'
            );
        `);

        if (!leitnerTableCheck.rows[0].exists) {
            console.log('Leitner system table does not exist. Creating it...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS leitner_system (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    flashcard_id INTEGER REFERENCES flashcards(id) ON DELETE CASCADE,
                    box_level INTEGER NOT NULL DEFAULT 1,
                    last_reviewed TIMESTAMP,
                    next_review_date TIMESTAMP,
                    review_count INTEGER DEFAULT 0,
                    success_rate FLOAT DEFAULT 0,
                    streak INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT unique_user_flashcard UNIQUE (user_id, flashcard_id)
                );
            `);
        }

        // Get total number of flashcards for the user
        const totalFlashcardsQuery = `
            SELECT COUNT(*) as total_flashcards
            FROM flashcards
            WHERE user_id = $1
        `;

        const totalFlashcardsResult = await pool.query(totalFlashcardsQuery, [userId]);
        const totalFlashcards = parseInt(totalFlashcardsResult.rows[0].total_flashcards) || 0;

        // Get Leitner system stats for the user
        const leitnerStatsQuery = `
            SELECT
                COUNT(*) as tracked_cards,
                SUM(CASE WHEN box_level = 1 THEN 1 ELSE 0 END) as box1_count,
                SUM(CASE WHEN box_level = 2 THEN 1 ELSE 0 END) as box2_count,
                SUM(CASE WHEN box_level = 3 THEN 1 ELSE 0 END) as box3_count,
                SUM(CASE WHEN next_review_date <= CURRENT_DATE THEN 1 ELSE 0 END) as due_today
            FROM leitner_system
            WHERE user_id = $1
        `;

        const leitnerStatsResult = await pool.query(leitnerStatsQuery, [userId]);
        const leitnerStats = leitnerStatsResult.rows[0];

        // We'll skip tracking cards for now as we're only calculating stats
        // This will be used in future implementations

        // Get count of cards due today (including untracked cards)
        let dueToday = parseInt(leitnerStats.due_today) || 0;

        // If there are untracked cards, they are considered in box 1 and due today
        const untrackedCardsCount = totalFlashcards - (parseInt(leitnerStats.tracked_cards) || 0);

        // Calculate actual box counts
        const box1Count = (parseInt(leitnerStats.box1_count) || 0) + untrackedCardsCount;
        const box2Count = parseInt(leitnerStats.box2_count) || 0;
        const box3Count = parseInt(leitnerStats.box3_count) || 0;

        // Add untracked cards to due today count
        dueToday += untrackedCardsCount;

        // Calculate mastery percentage (percentage of cards in box 3)
        const masteryPercentage = totalFlashcards > 0 ? (box3Count / totalFlashcards) * 100 : 0;

        return res.json({
            success: true,
            stats: {
                totalCards: totalFlashcards,
                box1Count: box1Count,
                box2Count: box2Count,
                box3Count: box3Count,
                dueToday: dueToday,
                masteryPercentage: masteryPercentage,
                untrackedCards: untrackedCardsCount
            }
        });
    } catch (error) {
        console.error('Error fetching Leitner stats:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch Leitner stats'
        });
    }
});

// Cornell Notes API endpoints
// Get Cornell notes for a specific chapter
app.get('/api/chapters/:chapterId/cornell-notes', authenticateToken, async (req, res) => {
    try {
        const { chapterId } = req.params;
        const userId = req.user.userId;

        // First, check if the chapter exists and belongs to the user
        const chapterExistsResult = await pool.query(
            `SELECT c.id, c.title, c.chapter_number, c.subject_id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterExistsResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Chapter not found or access denied'
            });
        }

        const chapter = chapterExistsResult.rows[0];

        // Get all Cornell notes for this chapter
        const notesResult = await pool.query(
            `SELECT cn.id, cn.title, cn.cues, cn.notes, cn.summary, cn.key_points, cn.tags, cn.attached_image, cn.created_at, cn.last_updated
             FROM cornell_notes cn
             WHERE cn.user_id = $1 AND cn.chapter_id = $2
             ORDER BY cn.last_updated DESC`,
            [userId, chapterId]
        );

        return res.json({
            success: true,
            chapter: {
                id: chapter.id,
                title: chapter.title,
                chapter_number: chapter.chapter_number,
                subject_id: chapter.subject_id
            },
            notes: notesResult.rows
        });
    } catch (error) {
        console.error('Error fetching Cornell notes:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch Cornell notes'
        });
    }
});

// Create a new Cornell note for a chapter
app.post('/api/chapters/:chapterId/cornell-notes/new', authenticateToken, async (req, res) => {
    try {
        const { chapterId } = req.params;
        const { title, cues, notes, summary, key_points, tags, attached_image } = req.body;
        const userId = req.user.userId;

        // Check if the chapter exists and belongs to the user
        const chapterExistsResult = await pool.query(
            `SELECT c.id, c.subject_id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterExistsResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Chapter not found or access denied'
            });
        }

        const chapter = chapterExistsResult.rows[0];

        // Insert new notes
        const result = await pool.query(
            `INSERT INTO cornell_notes (user_id, subject_id, chapter_id, title, cues, notes, summary, key_points, tags, attached_image, created_at, last_updated)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
             RETURNING id`,
            [userId, chapter.subject_id, chapterId, title, cues, notes, summary, JSON.stringify(key_points), JSON.stringify(tags), attached_image]
        );

        const noteId = result.rows[0].id;

        return res.json({
            success: true,
            message: 'New Cornell note created successfully',
            note_id: noteId
        });
    } catch (error) {
        console.error('Error creating new Cornell note:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to create new Cornell note'
        });
    }
});

// Update an existing Cornell note
app.post('/api/chapters/:chapterId/cornell-notes/:noteId', authenticateToken, async (req, res) => {
    try {
        const { chapterId, noteId } = req.params;
        const { title, cues, notes, summary, key_points, tags, attached_image } = req.body;
        const userId = req.user.userId;

        // Check if the chapter exists and belongs to the user
        const chapterExistsResult = await pool.query(
            `SELECT c.id, c.subject_id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterExistsResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Chapter not found or access denied'
            });
        }

        // Check if the note exists and belongs to the user
        const noteExistsResult = await pool.query(
            'SELECT id FROM cornell_notes WHERE id = $1 AND user_id = $2 AND chapter_id = $3',
            [noteId, userId, chapterId]
        );

        if (noteExistsResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Note not found or access denied'
            });
        }

        // Update the note
        await pool.query(
            `UPDATE cornell_notes
             SET title = $1, cues = $2, notes = $3, summary = $4, key_points = $5, tags = $6, attached_image = $7, last_updated = CURRENT_TIMESTAMP
             WHERE id = $8`,
            [title, cues, notes, summary, JSON.stringify(key_points), JSON.stringify(tags), attached_image, noteId]
        );

        return res.json({
            success: true,
            message: 'Cornell note updated successfully',
            note_id: noteId
        });
    } catch (error) {
        console.error('Error updating Cornell note:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update Cornell note'
        });
    }
});

// Delete a Cornell note
app.delete('/api/chapters/:chapterId/cornell-notes/:noteId', authenticateToken, async (req, res) => {
    try {
        const { chapterId, noteId } = req.params;
        const userId = req.user.userId;

        // Check if the chapter exists and belongs to the user
        const chapterExistsResult = await pool.query(
            `SELECT c.id, c.subject_id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterExistsResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Chapter not found or access denied'
            });
        }

        // Check if the note exists and belongs to the user
        const noteExistsResult = await pool.query(
            'SELECT id, title FROM cornell_notes WHERE id = $1 AND user_id = $2 AND chapter_id = $3',
            [noteId, userId, chapterId]
        );

        if (noteExistsResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Note not found or access denied'
            });
        }

        const noteTitle = noteExistsResult.rows[0].title;

        // Delete the note
        await pool.query(
            'DELETE FROM cornell_notes WHERE id = $1',
            [noteId]
        );

        return res.json({
            success: true,
            message: `Cornell note "${noteTitle || 'Untitled'}" deleted successfully`
        });
    } catch (error) {
        console.error('Error deleting Cornell note:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to delete Cornell note'
        });
    }
});

// For backward compatibility - redirects to the appropriate endpoint
app.post('/api/chapters/:chapterId/cornell-notes', authenticateToken, async (req, res) => {
    try {
        const { chapterId } = req.params;
        const { id } = req.body; // Check if an ID is provided
        const userId = req.user.userId;

        if (id) {
            // If ID is provided, redirect to update endpoint
            const noteExistsResult = await pool.query(
                'SELECT id FROM cornell_notes WHERE id = $1 AND user_id = $2 AND chapter_id = $3',
                [id, userId, chapterId]
            );

            if (noteExistsResult.rows.length > 0) {
                // Forward to the update endpoint
                return res.redirect(307, `/api/chapters/${chapterId}/cornell-notes/${id}`);
            }
        }

        // If no ID or note not found, create a new note
        return res.redirect(307, `/api/chapters/${chapterId}/cornell-notes/new`);
    } catch (error) {
        console.error('Error in Cornell notes compatibility endpoint:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to process Cornell notes request'
        });
    }
});

app.get('/api/leitner/due-today', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        // Get flashcards that are already in the Leitner system and due for review today
        const dueTrackedCardsQuery = `
            SELECT
                f.id, f.question, f.answer, f.type, f.color, f.difficult, f.tags, f.chapter_id,
                l.box_level, l.last_reviewed, l.next_review_date, l.review_count, l.success_rate
            FROM leitner_system l
            JOIN flashcards f ON l.flashcard_id = f.id
            WHERE l.user_id = $1 AND l.next_review_date <= CURRENT_DATE
            ORDER BY l.box_level, l.next_review_date
        `;

        const dueTrackedCardsResult = await pool.query(dueTrackedCardsQuery, [userId]);

        // Get IDs of cards already in the Leitner system
        const trackedCardsQuery = `
            SELECT flashcard_id
            FROM leitner_system
            WHERE user_id = $1
        `;

        // Get tracked cards
        const trackedCardsResult = await pool.query(trackedCardsQuery, [userId]);
        const trackedCardIds = trackedCardsResult.rows.map(row => row.flashcard_id);

        // Get untracked flashcards (not in the Leitner system yet)
        let untrackedCardsQuery = `
            SELECT
                id, question, answer, type, color, difficult, tags, chapter_id
            FROM flashcards
            WHERE user_id = $1
        `;

        // If there are tracked cards, exclude them
        if (trackedCardIds.length > 0) {
            untrackedCardsQuery += ` AND id NOT IN (${trackedCardIds.join(',')})`;
        }

        untrackedCardsQuery += ` ORDER BY id`;

        const untrackedCardsResult = await pool.query(untrackedCardsQuery, [userId]);

        // Add default Leitner properties to untracked cards
        const untrackedCards = untrackedCardsResult.rows.map(card => ({
            ...card,
            box_level: 1,
            last_reviewed: null,
            next_review_date: null,
            review_count: 0,
            success_rate: 0
        }));

        // Combine tracked and untracked cards
        const allDueCards = [...dueTrackedCardsResult.rows, ...untrackedCards];

        return res.json({
            success: true,
            cards: allDueCards
        });
    } catch (error) {
        console.error('Error fetching due cards:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch due cards'
        });
    }
});

app.post('/api/leitner/update', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { flashcardId, correct } = req.body;

        if (!flashcardId) {
            return res.status(400).json({
                success: false,
                message: 'Flashcard ID is required'
            });
        }

        // Get current Leitner data for this flashcard
        const currentDataQuery = `
            SELECT box_level, review_count, success_rate, streak
            FROM leitner_system
            WHERE user_id = $1 AND flashcard_id = $2
        `;

        const currentDataResult = await pool.query(currentDataQuery, [userId, flashcardId]);

        let boxLevel = 1;
        let reviewCount = 0;
        let successRate = 0;
        let streak = 0;

        if (currentDataResult.rows.length > 0) {
            // Update existing record
            const currentData = currentDataResult.rows[0];
            boxLevel = currentData.box_level;
            reviewCount = currentData.review_count;
            successRate = currentData.success_rate;
            streak = currentData.streak;

            // Update box level based on answer
            if (correct) {
                // If correct, move up a box (max box 3)
                boxLevel = Math.min(boxLevel + 1, 3);
                streak += 1;
            } else {
                // If incorrect, move back to box 1
                boxLevel = 1;
                streak = 0;
            }

            // Update review count and success rate
            reviewCount += 1;
            successRate = ((successRate * (reviewCount - 1)) + (correct ? 100 : 0)) / reviewCount;

            // Calculate next review date based on box level
            let nextReviewDate;
            const today = new Date();

            if (boxLevel === 1) {
                // Box 1: Review tomorrow
                nextReviewDate = new Date(today);
                nextReviewDate.setDate(today.getDate() + 1);
            } else if (boxLevel === 2) {
                // Box 2: Review in 3 days
                nextReviewDate = new Date(today);
                nextReviewDate.setDate(today.getDate() + 3);
            } else {
                // Box 3: Review in 7 days
                nextReviewDate = new Date(today);
                nextReviewDate.setDate(today.getDate() + 7);
            }

            // Update the Leitner system record
            await pool.query(`
                UPDATE leitner_system
                SET
                    box_level = $1,
                    last_reviewed = CURRENT_TIMESTAMP,
                    next_review_date = $2,
                    review_count = $3,
                    success_rate = $4,
                    streak = $5,
                    updated_at = CURRENT_TIMESTAMP
                WHERE user_id = $6 AND flashcard_id = $7
            `, [boxLevel, nextReviewDate, reviewCount, successRate, streak, userId, flashcardId]);
        } else {
            // Get chapter ID for this flashcard
            const flashcardQuery = `
                SELECT chapter_id FROM flashcards WHERE id = $1 AND user_id = $2
            `;

            const flashcardResult = await pool.query(flashcardQuery, [flashcardId, userId]);

            if (flashcardResult.rows.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Flashcard not found'
                });
            }

            const chapterId = flashcardResult.rows[0].chapter_id;

            // Create new record
            boxLevel = correct ? 2 : 1;
            reviewCount = 1;
            successRate = correct ? 100 : 0;
            streak = correct ? 1 : 0;

            // Calculate next review date based on box level
            let nextReviewDate;
            const today = new Date();

            if (boxLevel === 1) {
                // Box 1: Review tomorrow
                nextReviewDate = new Date(today);
                nextReviewDate.setDate(today.getDate() + 1);
            } else {
                // Box 2: Review in 3 days
                nextReviewDate = new Date(today);
                nextReviewDate.setDate(today.getDate() + 3);
            }

            // Insert new Leitner system record
            await pool.query(`
                INSERT INTO leitner_system (
                    user_id, chapter_id, flashcard_id, box_level,
                    last_reviewed, next_review_date, review_count,
                    success_rate, streak
                )
                VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, $5, $6, $7, $8)
            `, [userId, chapterId, flashcardId, boxLevel, nextReviewDate, reviewCount, successRate, streak]);
        }

        return res.json({
            success: true,
            message: 'Flashcard updated successfully',
            data: {
                flashcardId,
                boxLevel,
                reviewCount,
                successRate,
                streak
            }
        });
    } catch (error) {
        console.error('Error updating flashcard:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to update flashcard'
        });
    }
});

// Flashcards API endpoints
app.get('/api/chapters/:chapterId/flashcards', authenticateToken, async (req, res) => {
    try {
        const { chapterId } = req.params;
        const userId = req.user.userId;

        console.log(`Querying flashcards for chapter_id=${chapterId} and user_id=${userId}`);

        // First, check if the chapter exists and belongs to the user
        const chapterExistsResult = await pool.query(
            `SELECT c.id, c.title, c.chapter_number, c.subject_id, c.file_id
             FROM chapters c
             JOIN subjects s ON c.subject_id = s.id
             WHERE c.id = $1 AND s.user_id = $2`,
            [chapterId, userId]
        );

        if (chapterExistsResult.rows.length === 0) {
            console.log(`Chapter ID ${chapterId} not found or doesn't belong to user ${userId}`);
            return res.status(404).json({
                success: false,
                message: 'Chapter not found or access denied'
            });
        }

        // Check if the leitner_system table exists
        const leitnerTableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'leitner_system'
            );
        `);

        if (!leitnerTableCheck.rows[0].exists) {
            console.log('Leitner system table does not exist. Creating it...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS leitner_system (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    flashcard_id INTEGER REFERENCES flashcards(id) ON DELETE CASCADE,
                    box_level INTEGER NOT NULL DEFAULT 1,
                    last_reviewed TIMESTAMP,
                    next_review_date TIMESTAMP,
                    review_count INTEGER DEFAULT 0,
                    success_rate FLOAT DEFAULT 0,
                    streak INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT unique_user_flashcard UNIQUE (user_id, flashcard_id)
                );

                CREATE INDEX IF NOT EXISTS idx_leitner_user_chapter
                ON leitner_system (user_id, chapter_id);

                CREATE OR REPLACE FUNCTION update_leitner_timestamp()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = NOW();
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;

                DROP TRIGGER IF EXISTS update_leitner_timestamp ON leitner_system;

                CREATE TRIGGER update_leitner_timestamp
                BEFORE UPDATE ON leitner_system
                FOR EACH ROW
                EXECUTE FUNCTION update_leitner_timestamp();
            `);
            console.log('Leitner system table created successfully');
        }

        // Get chapter information from the result
        const chapterInfo = chapterExistsResult.rows[0];
        console.log(`Found chapter: ${chapterInfo.title}, subject_id: ${chapterInfo.subject_id}, file_id: ${chapterInfo.file_id}`);

        // Now verify the user has access to this chapter's subject
        const subjectAccessResult = await pool.query(
            'SELECT id, name, user_id FROM subjects WHERE id = $1 AND user_id = $2',
            [chapterInfo.subject_id, userId]
        );

        if (subjectAccessResult.rows.length === 0) {
            console.log(`User ${userId} does not have access to subject ${chapterInfo.subject_id}`);
            return res.status(403).json({
                success: false,
                message: 'Access denied to this chapter'
            });
        }

        const subject = subjectAccessResult.rows[0];
        console.log(`User ${userId} has access to subject ${subject.name} (ID: ${subject.id})`);

        // If file_id is present, check if the file exists and is accessible
        if (chapterInfo.file_id) {
            console.log(`Checking if file ID ${chapterInfo.file_id} exists and is accessible`);

            const fileCheckResult = await pool.query(
                `SELECT f.id
                 FROM files f
                 JOIN subjects s ON f.subject_id = s.id
                 WHERE f.id = $1 AND s.user_id = $2`,
                [chapterInfo.file_id, userId]
            );

            if (fileCheckResult.rows.length === 0) {
                console.log(`File ID ${chapterInfo.file_id} not found or not accessible by user ${userId}`);
                // We'll continue anyway since the file might have been deleted but the chapter is still valid
                console.log(`Continuing anyway since the chapter is still accessible`);
            } else {
                console.log(`File ID ${chapterInfo.file_id} is accessible by user ${userId}`);
            }
        }

        // Get flashcards for this chapter
        console.log(`Querying flashcards for chapter_id=${chapterId} and user_id=${userId}`);

        // First check if the flashcards table exists
        const tableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'flashcards'
            )
        `);

        if (!tableCheck.rows[0].exists) {
            console.log('Flashcards table does not exist. Creating it...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS flashcards (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    question TEXT NOT NULL,
                    answer TEXT NOT NULL,
                    type VARCHAR(50) DEFAULT 'basic',
                    box_level INTEGER DEFAULT 1,
                    last_reviewed TIMESTAMP,
                    next_review_date TIMESTAMP,
                    color VARCHAR(50) DEFAULT '#ffffff',
                    difficult BOOLEAN DEFAULT FALSE,
                    review_count INTEGER DEFAULT 0,
                    success_rate FLOAT DEFAULT 0,
                    tags VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);
            console.log('Flashcards table created successfully');
        }

        // Get chapter information from the result
        const chapter = chapterExistsResult.rows[0];

        // Use a JOIN query to get flashcards with Leitner system data and chapter information in a single query
        // This is more efficient than separate queries and client-side merging
        const flashcardsQuery = `
            SELECT
                f.id, f.question, f.answer, f.type, f.system,
                f.color, f.difficult, f.tags, f.created_at, f.updated_at,
                c.title as chapter_title, c.chapter_number,
                COALESCE(l.box_level, f.box_level) as box_level,
                COALESCE(l.last_reviewed, f.last_reviewed) as last_reviewed,
                COALESCE(l.next_review_date, f.next_review_date) as next_review_date,
                COALESCE(l.review_count, f.review_count) as review_count,
                COALESCE(l.success_rate, f.success_rate) as success_rate,
                COALESCE(l.streak, 0) as leitner_streak
            FROM
                flashcards f
            LEFT JOIN
                leitner_system l ON f.id = l.flashcard_id AND l.user_id = $2
            LEFT JOIN
                chapters c ON f.chapter_id = c.id
            WHERE
                f.chapter_id = $1 AND f.user_id = $2
            ORDER BY f.created_at
        `;

        const flashcardsResult = await pool.query(flashcardsQuery, [chapterId, userId]);

        console.log(`Query executed. Found ${flashcardsResult.rows.length} flashcards.`);

        // If no flashcards found, return empty array but still success
        if (flashcardsResult.rows.length === 0) {
            console.log(`No flashcards found for chapter ID: ${chapterId}`);
            return res.json({
                success: true,
                flashcards: [],
                chapter: {
                    id: chapter.id,
                    title: chapter.title,
                    chapter_number: chapter.chapter_number,
                    subject_id: chapter.subject_id
                }
            });
        }

        return res.json({
            success: true,
            flashcards: flashcardsResult.rows,
            chapter: {
                id: chapter.id,
                title: chapter.title,
                chapter_number: chapter.chapter_number,
                subject_id: chapter.subject_id
            }
        });
    } catch (error) {
        console.error('Error fetching flashcards:', error.message, error.stack);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch flashcards',
            error: error.message
        });
    }
});

app.post('/api/chapters/:chapterId/flashcards', authenticateToken, async (req, res) => {
    try {
        const { chapterId } = req.params;
        const { flashcards, update_leitner_only } = req.body;
        const userId = req.user.userId;

        console.log(`Saving flashcards for chapter ID: ${chapterId}, user ID: ${userId}`);

        // Check if we're only updating Leitner system data
        if (update_leitner_only) {
            console.log('Update Leitner only flag is set - will only update Leitner system data and review dates');
        }

        if (!Array.isArray(flashcards)) {
            console.log('Invalid request format: flashcards is not an array');
            return res.status(400).json({
                success: false,
                message: 'Invalid request format. Expected an array of flashcards.'
            });
        }

        // Log the number of flashcards being processed
        console.log(`Received ${flashcards.length} flashcards to save`);

        // If there are too many flashcards, process them in batches
        const BATCH_SIZE = 50; // Process 50 flashcards at a time

        // First, check if the chapter exists
        const chapterExistsResult = await pool.query(
            'SELECT id, title, chapter_number, subject_id, file_id FROM chapters WHERE id = $1',
            [chapterId]
        );

        if (chapterExistsResult.rows.length === 0) {
            console.log(`Chapter ID ${chapterId} not found`);
            return res.status(404).json({
                success: false,
                message: 'Chapter not found'
            });
        }

        const chapter = chapterExistsResult.rows[0];
        console.log(`Found chapter: ${chapter.title}, subject_id: ${chapter.subject_id}, file_id: ${chapter.file_id}`);

        // Now verify the user has access to this chapter's subject
        const subjectAccessResult = await pool.query(
            'SELECT id, name, user_id FROM subjects WHERE id = $1 AND user_id = $2',
            [chapter.subject_id, userId]
        );

        if (subjectAccessResult.rows.length === 0) {
            console.log(`User ${userId} does not have access to subject ${chapter.subject_id}`);
            return res.status(403).json({
                success: false,
                message: 'Access denied to this chapter'
            });
        }

        const subject = subjectAccessResult.rows[0];
        console.log(`User ${userId} has access to subject ${subject.name} (ID: ${subject.id})`);

        // If file_id is present, check if the file exists and is accessible
        if (chapter.file_id) {
            console.log(`Checking if file ID ${chapter.file_id} exists and is accessible`);

            const fileCheckResult = await pool.query(
                `SELECT f.id
                 FROM files f
                 JOIN subjects s ON f.subject_id = s.id
                 WHERE f.id = $1 AND s.user_id = $2`,
                [chapter.file_id, userId]
            );

            if (fileCheckResult.rows.length === 0) {
                console.log(`File ID ${chapter.file_id} not found or not accessible by user ${userId}`);
                // We'll continue anyway since the file might have been deleted but the chapter is still valid
                console.log(`Continuing anyway since the chapter is still accessible`);
            } else {
                console.log(`File ID ${chapter.file_id} is accessible by user ${userId}`);
            }
        }

        const subjectId = chapter.subject_id;

        // First check if the flashcards table exists
        const tableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'flashcards'
            )
        `);

        if (!tableCheck.rows[0].exists) {
            console.log('Flashcards table does not exist. Creating it...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS flashcards (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    question TEXT NOT NULL,
                    answer TEXT NOT NULL,
                    type VARCHAR(50) DEFAULT 'basic',
                    box_level INTEGER DEFAULT 1,
                    last_reviewed TIMESTAMP,
                    next_review_date TIMESTAMP,
                    color VARCHAR(50) DEFAULT '#ffffff',
                    difficult BOOLEAN DEFAULT FALSE,
                    review_count INTEGER DEFAULT 0,
                    success_rate FLOAT DEFAULT 0,
                    tags VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            `);
            console.log('Flashcards table created successfully');
        }

        // Check if the leitner_system table exists
        const leitnerTableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'leitner_system'
            );
        `);

        if (!leitnerTableCheck.rows[0].exists) {
            console.log('Leitner system table does not exist. Creating it...');
            await pool.query(`
                CREATE TABLE IF NOT EXISTS leitner_system (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
                    flashcard_id INTEGER REFERENCES flashcards(id) ON DELETE CASCADE,
                    box_level INTEGER NOT NULL DEFAULT 1,
                    last_reviewed TIMESTAMP,
                    next_review_date TIMESTAMP,
                    review_count INTEGER DEFAULT 0,
                    success_rate FLOAT DEFAULT 0,
                    streak INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT unique_user_flashcard UNIQUE (user_id, flashcard_id)
                );

                CREATE INDEX IF NOT EXISTS idx_leitner_user_chapter
                ON leitner_system (user_id, chapter_id);

                CREATE OR REPLACE FUNCTION update_leitner_timestamp()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = NOW();
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;

                DROP TRIGGER IF EXISTS update_leitner_timestamp ON leitner_system;

                CREATE TRIGGER update_leitner_timestamp
                BEFORE UPDATE ON leitner_system
                FOR EACH ROW
                EXECUTE FUNCTION update_leitner_timestamp();
            `);
            console.log('Leitner system table created successfully');
        }

        // Start a transaction
        await pool.query('BEGIN');

        try {
            // Get existing flashcards to preserve IDs
            const existingFlashcardsResult = await pool.query(
                'SELECT id, question FROM flashcards WHERE chapter_id = $1 AND user_id = $2',
                [chapterId, userId]
            );

            // Create a map of question to existing flashcard ID
            const existingFlashcardMap = {};
            existingFlashcardsResult.rows.forEach(card => {
                existingFlashcardMap[card.question] = card.id;
            });

            // Process flashcards in batches for better performance
            const processedFlashcards = [];
            const totalCards = flashcards.length;

            // Process flashcards in batches
            for (let i = 0; i < totalCards; i += BATCH_SIZE) {
                const batch = flashcards.slice(i, i + BATCH_SIZE);
                console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(totalCards / BATCH_SIZE)} (${batch.length} cards)`);

                // Process each card in the current batch
                for (const card of batch) {
                    const {
                        id,
                        question,
                        answer,
                        type = 'basic',
                        box_level = 1,
                        last_reviewed = null,
                        next_review_date = null,
                        color = '#ffffff',
                        difficult = false,
                        review_count = 0,
                        success_rate = 0,
                        tags = [],
                        leitner_streak = 0
                    } = card;

                    // Convert tags array to string if needed
                    const tagsString = Array.isArray(tags) ? tags.join(',') : tags;

                    let flashcardId = id;

                    if (update_leitner_only) {
                        // When updating only Leitner data, we need a valid flashcard ID
                        if (!flashcardId) {
                            console.error('Cannot update Leitner data without a flashcard ID');
                            continue; // Skip to the next card
                        }

                        // Verify the flashcard exists and belongs to this user
                        const flashcardCheck = await pool.query(
                            'SELECT id FROM flashcards WHERE id = $1 AND user_id = $2',
                            [flashcardId, userId]
                        );

                        if (flashcardCheck.rows.length === 0) {
                            console.error(`Flashcard ID ${flashcardId} not found or does not belong to user ${userId}`);
                            continue; // Skip to the next card
                        }

                        // Only update the review dates in the flashcards table
                        await pool.query(
                            `UPDATE flashcards SET
                                last_reviewed = $1,
                                next_review_date = $2,
                                updated_at = NOW()
                            WHERE id = $3`,
                            [
                                last_reviewed, next_review_date, flashcardId
                            ]
                        );
                    } else {
                        // Normal flashcard update/insert logic for full flashcard operations
                        // If the card has the same question as an existing card, use that ID
                        if (!flashcardId && existingFlashcardMap[question]) {
                            flashcardId = existingFlashcardMap[question];

                            // Update the existing flashcard
                            await pool.query(
                                `UPDATE flashcards SET
                                    answer = $1,
                                    type = $2,
                                    box_level = $3,
                                    last_reviewed = $4,
                                    next_review_date = $5,
                                    color = $6,
                                    difficult = $7,
                                    review_count = $8,
                                    success_rate = $9,
                                    tags = $10,
                                    updated_at = NOW()
                                WHERE id = $11`,
                                [
                                    answer, type, box_level, last_reviewed, next_review_date,
                                    color, difficult, review_count, success_rate, tagsString,
                                    flashcardId
                                ]
                            );
                        } else {
                            // Insert a new flashcard
                            const result = await pool.query(
                                `INSERT INTO flashcards (
                                    user_id, chapter_id, subject_id, question, answer,
                                    type, box_level, last_reviewed, next_review_date,
                                    color, difficult, review_count, success_rate, tags
                                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                                RETURNING id`,
                                [
                                    userId, chapterId, subjectId, question, answer,
                                    type, box_level, last_reviewed, next_review_date,
                                    color, difficult, review_count, success_rate, tagsString
                                ]
                            );

                            flashcardId = result.rows[0].id;
                        }
                    }

                    // Save or update Leitner system data
                    try {
                        await pool.query(
                            `INSERT INTO leitner_system (
                                user_id, chapter_id, flashcard_id, box_level,
                                last_reviewed, next_review_date, review_count,
                                success_rate, streak
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                            ON CONFLICT (user_id, flashcard_id)
                            DO UPDATE SET
                                box_level = $4,
                                last_reviewed = $5,
                                next_review_date = $6,
                                review_count = $7,
                                success_rate = $8,
                                streak = $9,
                                updated_at = NOW()`,
                            [
                                userId, chapterId, flashcardId, box_level,
                                last_reviewed, next_review_date, review_count,
                                success_rate, leitner_streak
                            ]
                        );
                    } catch (error) {
                        console.error('Error saving Leitner system data:', error.message, error.stack);
                        // Continue processing other cards even if this one fails
                    }

                    processedFlashcards.push({
                        id: flashcardId,
                        question,
                        answer,
                        type,
                        box_level,
                        last_reviewed,
                        next_review_date,
                        color,
                        difficult,
                        review_count,
                        success_rate,
                        tags: tagsString,
                        leitner_streak
                    });
                }

                // Commit the batch to avoid long-running transactions
                if (i + BATCH_SIZE < totalCards) {
                    await pool.query('COMMIT');
                    await pool.query('BEGIN');
                    console.log(`Committed batch ${Math.floor(i / BATCH_SIZE) + 1}`);
                }
            }

            // Commit the transaction
            await pool.query('COMMIT');

            // Return a success response without the full flashcards array to reduce payload size
            return res.json({
                success: true,
                message: update_leitner_only
                    ? 'Leitner system data updated successfully'
                    : 'Flashcards and Leitner system data saved successfully',
                count: processedFlashcards.length,
                chapter: {
                    id: chapter.id,
                    title: chapter.title,
                    chapter_number: chapter.chapter_number,
                    subject_id: chapter.subject_id
                }
            });
        } catch (error) {
            // Rollback the transaction in case of error
            await pool.query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Error saving flashcards:', error.message, error.stack);
        return res.status(500).json({
            success: false,
            message: 'Failed to save flashcards',
            error: error.message
        });
    }
});

// Make sure the server is listening on a port
const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
