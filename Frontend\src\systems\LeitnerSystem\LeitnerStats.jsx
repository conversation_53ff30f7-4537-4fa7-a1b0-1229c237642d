import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

// Material Icons
import BarChartIcon from '@mui/icons-material/BarChart';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import PieChartIcon from '@mui/icons-material/PieChart';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import Looks4Icon from '@mui/icons-material/Looks4';
import Looks5Icon from '@mui/icons-material/Looks5';

const LeitnerStats = ({ boxes, onBack }) => {
  const { darkMode } = useTheme();
  const [stats, setStats] = useState({
    totalCards: 0,
    cardsPerBox: [0, 0, 0],
    masteryPercentage: 0,
    reviewsDue: 0,
    reviewsCompleted: 0,
    averageBoxLevel: 0
  });

  // Calculate statistics based on boxes
  useEffect(() => {
    if (!boxes) return;

    // Count total cards
    const totalCards = Object.values(boxes).flat().length;

    // Count cards per box
    const cardsPerBox = [
      boxes[1].length,
      boxes[2].length,
      boxes[3].length
    ];

    // Calculate mastery percentage (cards in box 3)
    const masteryPercentage = totalCards > 0
      ? (boxes[3].length / totalCards) * 100
      : 0;

    // Calculate average box level
    const totalBoxLevel = Object.entries(boxes).reduce((sum, [boxNum, cards]) => {
      return sum + (parseInt(boxNum) * cards.length);
    }, 0);

    const averageBoxLevel = totalCards > 0
      ? totalBoxLevel / totalCards
      : 0;

    // Count reviews due (simplified - in a real app you'd check dates)
    const reviewsDue = boxes[1].length + Math.floor(boxes[2].length / 3) + Math.floor(boxes[3].length / 7);

    // Count reviews completed (simplified - in a real app you'd track this)
    const reviewsCompleted = Object.values(boxes).flat().reduce((sum, card) => {
      return sum + (card.review_count || 0);
    }, 0);

    setStats({
      totalCards,
      cardsPerBox,
      masteryPercentage,
      reviewsDue,
      reviewsCompleted,
      averageBoxLevel
    });
  }, [boxes]);

  return (
    <div className={`leitner-stats ${darkMode ? 'theme-dark' : ''}`}>
      <div className="stats-header">
        <h2>
          <BarChartIcon style={{ marginRight: '0.5rem' }} />
          Leitner System Statistics
        </h2>
      </div>

      <div className="stats-overview">
        <motion.div
          className="stat-card total-cards"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="stat-icon">
            <PieChartIcon />
          </div>
          <div className="stat-content">
            <h3>Total Cards</h3>
            <div className="stat-value">{stats.totalCards}</div>
          </div>
        </motion.div>

        <motion.div
          className="stat-card mastery"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="stat-icon">
            <TrendingUpIcon />
          </div>
          <div className="stat-content">
            <h3>Mastery Level</h3>
            <div className="stat-value">{stats.masteryPercentage.toFixed(1)}%</div>
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{
                  width: `${stats.masteryPercentage}%`,
                  backgroundColor: stats.masteryPercentage > 75
                    ? '#28CD41'
                    : stats.masteryPercentage > 50
                      ? '#007AFF'
                      : stats.masteryPercentage > 25
                        ? '#FF9500'
                        : '#FF3B30'
                }}
              ></div>
            </div>
          </div>
        </motion.div>

        <motion.div
          className="stat-card reviews"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="stat-icon">
            <CalendarTodayIcon />
          </div>
          <div className="stat-content">
            <h3>Reviews</h3>
            <div className="stat-value">{stats.reviewsCompleted} completed</div>
            <div className="stat-secondary">{stats.reviewsDue} due</div>
          </div>
        </motion.div>
      </div>

      <div className="box-distribution">
        <h3>Cards Distribution</h3>

        <div className="distribution-chart">
          {stats.cardsPerBox.map((count, index) => {
            const boxNumber = index + 1;
            const percentage = stats.totalCards > 0
              ? (count / stats.totalCards) * 100
              : 0;

            // Get box icon
            let BoxIcon;
            switch (boxNumber) {
              case 1: BoxIcon = LooksOneIcon; break;
              case 2: BoxIcon = LooksTwoIcon; break;
              case 3: BoxIcon = Looks3Icon; break;
              default: BoxIcon = LooksOneIcon;
            }

            // Get box color
            let boxColor;
            switch (boxNumber) {
              case 1: boxColor = '#FF3B30'; break; // Red
              case 2: boxColor = '#FF9500'; break; // Orange
              case 3: boxColor = '#28CD41'; break; // Green
              default: boxColor = '#8E8E93'; // Gray
            }

            return (
              <motion.div
                key={boxNumber}
                className="box-stat"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 * boxNumber }}
              >
                <div className="box-label">
                  <BoxIcon style={{ color: boxColor }} />
                  <span>Box {boxNumber}</span>
                </div>

                <div className="box-bar-container">
                  <div
                    className="box-bar"
                    style={{
                      width: `${percentage}%`,
                      backgroundColor: boxColor
                    }}
                  ></div>
                </div>

                <div className="box-count">{count}</div>
              </motion.div>
            );
          })}
        </div>
      </div>

      <div className="stats-footer">
        <div className="average-level">
          <h3>Average Box Level</h3>
          <div className="level-indicator">
            <div
              className="level-fill"
              style={{ width: `${(stats.averageBoxLevel / 3) * 100}%` }}
            ></div>
            <div className="level-value">{stats.averageBoxLevel.toFixed(1)}</div>
          </div>
        </div>

        <div className="stats-actions">
          <button onClick={onBack}>Back to Boxes</button>
        </div>
      </div>
    </div>
  );
};

export default LeitnerStats;
