-- Database Schema for BleuprintAI
-- Updated with proper relationships and auto-increment
-- Database: blueprint

-- DROP TABLES (in reverse dependency order to avoid foreign key conflicts)
DROP TABLE IF EXISTS schedule_items CASCADE;
DROP TABLE IF EXISTS chapter_tools CASCADE;
DROP TABLE IF EXISTS leitner_system CASCADE;
DROP TABLE IF EXISTS flashcards CASCADE;
DROP TABLE IF EXISTS cornell_notes CASCADE;
DROP TABLE IF EXISTS daily_objectives CASCADE;
DROP TABLE IF EXISTS calendar_events CASCADE;
DROP TABLE IF EXISTS calendar CASCADE;
DROP TABLE IF EXISTS quizzes CASCADE;
DROP TABLE IF EXISTS pomodoro_sessions CASCADE;
DROP TABLE IF EXISTS pomodoro_timer CASCADE;
DROP TABLE IF EXISTS podcast CASCADE;
DROP TABLE IF EXISTS explainer_videos CASCADE;
DROP TABLE IF EXISTS mnemonic CASCADE;
DROP TABLE IF EXISTS mindmap CASCADE;
DROP TABLE IF EXISTS mastery_tracker CASCADE;
DROP TABLE IF EXISTS kanban_board CASCADE;
DROP TABLE IF EXISTS retrospective CASCADE;
DROP TABLE IF EXISTS eisenhower CASCADE;
DROP TABLE IF EXISTS doodles CASCADE;
DROP TABLE IF EXISTS streaks CASCADE;
DROP TABLE IF EXISTS objectives CASCADE;
DROP TABLE IF EXISTS chapters CASCADE;
DROP TABLE IF EXISTS study_plans CASCADE;
DROP TABLE IF EXISTS files CASCADE;
DROP TABLE IF EXISTS curricula CASCADE;
DROP TABLE IF EXISTS user_preferences CASCADE;
DROP TABLE IF EXISTS subjects CASCADE;
DROP TABLE IF EXISTS study_tools CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- DROP ENUM types
DROP TYPE IF EXISTS flashcard_system CASCADE;
DROP TYPE IF EXISTS kanban_status CASCADE;
DROP TYPE IF EXISTS quadrant_type CASCADE;

-- Create ENUM types first
CREATE TYPE IF NOT EXISTS quadrant_type AS ENUM ('urgent_important', 'not_urgent_important', 'urgent_not_important', 'not_urgent_not_important');
CREATE TYPE IF NOT EXISTS kanban_status AS ENUM ('todo', 'in_progress', 'done');
CREATE TYPE IF NOT EXISTS flashcard_system AS ENUM ('STIC', 'FAST', 'Leitner');

-- Table: users (Core table - must be created first)
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    full_name VARCHAR(50),
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    preferences_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: subjects
CREATE TABLE IF NOT EXISTS subjects (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table: files
CREATE TABLE IF NOT EXISTS files (
    id SERIAL PRIMARY KEY,
    subject_id INTEGER NOT NULL,
    filename TEXT NOT NULL,
    originalname TEXT NOT NULL,
    filetype TEXT,
    filepath TEXT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    youtube_url TEXT,
    study_tools JSONB,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: chapters
CREATE TABLE IF NOT EXISTS chapters (
    id SERIAL PRIMARY KEY,
    file_id INTEGER,
    subject_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    content_file_path VARCHAR(500),
    content_size INTEGER DEFAULT 0,
    chapter_number VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: objectives
CREATE TABLE IF NOT EXISTS objectives (
    id SERIAL PRIMARY KEY,
    chapter_id INTEGER NOT NULL,
    objective TEXT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    display_date DATE,
    estimated_time_minutes INTEGER DEFAULT 30,
    difficulty_level INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE
);

-- Table: study_tools
CREATE TABLE IF NOT EXISTS study_tools (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: curricula
CREATE TABLE IF NOT EXISTS curricula (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table: study_plans
CREATE TABLE IF NOT EXISTS study_plans (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    curriculum_id INTEGER,
    name VARCHAR(255) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    daily_study_time_minutes INTEGER NOT NULL DEFAULT 60,
    preferred_days VARCHAR(50),
    unavailable_periods TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (curriculum_id) REFERENCES files(id) ON DELETE CASCADE
);

-- Table: schedule_items
CREATE TABLE IF NOT EXISTS schedule_items (
    id SERIAL PRIMARY KEY,
    study_plan_id INTEGER NOT NULL,
    objective_id INTEGER NOT NULL,
    scheduled_date DATE NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    completion_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (study_plan_id) REFERENCES study_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (objective_id) REFERENCES objectives(id) ON DELETE CASCADE
);

-- Table: user_preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    user_id INTEGER PRIMARY KEY,
    language VARCHAR(50),
    role VARCHAR(50),
    field_of_study VARCHAR(100),
    goals JSONB,
    tools JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table: flashcards
CREATE TABLE IF NOT EXISTS flashcards (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    chapter_id INTEGER,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'basic',
    system flashcard_system DEFAULT 'STIC',
    box_level INTEGER DEFAULT 1,
    last_reviewed TIMESTAMP,
    next_review_date TIMESTAMP,
    color VARCHAR(50) DEFAULT '#ffffff',
    difficult BOOLEAN DEFAULT FALSE,
    review_count INTEGER DEFAULT 0,
    success_rate DOUBLE PRECISION DEFAULT 0,
    tags VARCHAR(255),
    ai_generated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
    CONSTRAINT unique_flashcard_question UNIQUE (user_id, subject_id, chapter_id, question)
);

-- Table: leitner_system
CREATE TABLE IF NOT EXISTS leitner_system (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    chapter_id INTEGER,
    flashcard_id INTEGER NOT NULL,
    box_level INTEGER NOT NULL DEFAULT 1,
    last_reviewed TIMESTAMP,
    next_review_date TIMESTAMP,
    review_count INTEGER DEFAULT 0,
    success_rate DOUBLE PRECISION DEFAULT 0,
    streak INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
    FOREIGN KEY (flashcard_id) REFERENCES flashcards(id) ON DELETE CASCADE,
    CONSTRAINT unique_user_flashcard UNIQUE (user_id, flashcard_id)
);

-- Table: chapter_tools
CREATE TABLE IF NOT EXISTS chapter_tools (
    id SERIAL PRIMARY KEY,
    chapter_id INTEGER NOT NULL,
    tool_id INTEGER NOT NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
    FOREIGN KEY (tool_id) REFERENCES study_tools(id) ON DELETE CASCADE,
    CONSTRAINT unique_chapter_tool UNIQUE (chapter_id, tool_id)
);

-- Table: kanban_board
CREATE TABLE IF NOT EXISTS kanban_board (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    task TEXT NOT NULL,
    status kanban_status NOT NULL DEFAULT 'todo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: mastery_tracker
CREATE TABLE IF NOT EXISTS mastery_tracker (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    skill TEXT NOT NULL,
    proficiency_level INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: mindmap
CREATE TABLE IF NOT EXISTS mindmap (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    nodes JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: mnemonic
CREATE TABLE IF NOT EXISTS mnemonic (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    concept TEXT NOT NULL,
    mnemonic TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: podcast
CREATE TABLE IF NOT EXISTS podcast (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    audio_url TEXT,
    duration INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: pomodoro_sessions
CREATE TABLE IF NOT EXISTS pomodoro_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    duration_minutes INTEGER NOT NULL,
    type VARCHAR(10) NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: pomodoro_timer
CREATE TABLE IF NOT EXISTS pomodoro_timer (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    duration_minutes INTEGER NOT NULL,
    break_duration_minutes INTEGER NOT NULL,
    sessions_completed INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: quizzes (Enhanced structure for full quiz management)
CREATE TABLE IF NOT EXISTS quizzes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    chapter_id INTEGER, -- Optional reference to specific chapter
    title VARCHAR(255) NOT NULL,
    description TEXT,
    difficulty VARCHAR(20) DEFAULT 'medium' CHECK (difficulty IN ('easy', 'medium', 'hard')),
    time_limit INTEGER DEFAULT 0, -- in minutes, 0 means no limit
    total_questions INTEGER DEFAULT 0,
    total_points INTEGER DEFAULT 0,
    question_types JSONB, -- Array of question types: ["multiple_choice", "short_answer"]
    ai_generated BOOLEAN DEFAULT FALSE,
    ai_prompt TEXT, -- Store the original prompt used for AI generation
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL
);

-- Table: quiz_questions (Individual questions for each quiz)
CREATE TABLE IF NOT EXISTS quiz_questions (
    id SERIAL PRIMARY KEY,
    quiz_id INTEGER NOT NULL,
    question_number INTEGER NOT NULL,
    question_text TEXT NOT NULL,
    question_type VARCHAR(20) NOT NULL CHECK (question_type IN ('multiple_choice', 'short_answer')),
    options JSONB, -- For multiple choice: ["option1", "option2", "option3", "option4"]
    correct_answer TEXT NOT NULL,
    explanation TEXT,
    points INTEGER DEFAULT 1,
    difficulty_level VARCHAR(20) DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE
);

-- Table: retrospective
CREATE TABLE IF NOT EXISTS retrospective (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    went_well TEXT,
    to_improve TEXT,
    action_items TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: streaks
CREATE TABLE IF NOT EXISTS streaks (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_streak_date DATE,
    streak_history JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table: calendar
CREATE TABLE IF NOT EXISTS calendar (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    event_title VARCHAR(255) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: calendar_events
CREATE TABLE IF NOT EXISTS calendar_events (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME,
    event_type VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: cornell_notes
CREATE TABLE IF NOT EXISTS cornell_notes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    chapter_id INTEGER,
    title TEXT,
    cues TEXT,
    notes TEXT,
    summary TEXT,
    key_points JSONB DEFAULT '[]',
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE
);

-- Table: daily_objectives
CREATE TABLE IF NOT EXISTS daily_objectives (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    objective TEXT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: doodles
CREATE TABLE IF NOT EXISTS doodles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    image_data TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: eisenhower
CREATE TABLE IF NOT EXISTS eisenhower (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    task TEXT NOT NULL,
    quadrant quadrant_type NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Table: explainer_videos
CREATE TABLE IF NOT EXISTS explainer_videos (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subject_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    video_url TEXT,
    duration INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subjects_user_id ON subjects(user_id);
CREATE INDEX IF NOT EXISTS idx_files_subject_id ON files(subject_id);
CREATE INDEX IF NOT EXISTS idx_chapters_subject_id ON chapters(subject_id);
CREATE INDEX IF NOT EXISTS idx_chapters_file_id ON chapters(file_id);
CREATE INDEX IF NOT EXISTS idx_objectives_chapter_id ON objectives(chapter_id);
CREATE INDEX IF NOT EXISTS idx_objectives_display_date ON objectives(display_date);
CREATE INDEX IF NOT EXISTS idx_study_plans_user_id ON study_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_study_plans_subject_id ON study_plans(subject_id);
CREATE INDEX IF NOT EXISTS idx_schedule_items_study_plan_id ON schedule_items(study_plan_id);
CREATE INDEX IF NOT EXISTS idx_schedule_items_objective_id ON schedule_items(objective_id);
CREATE INDEX IF NOT EXISTS idx_schedule_items_scheduled_date ON schedule_items(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_flashcards_user_id ON flashcards(user_id);
CREATE INDEX IF NOT EXISTS idx_flashcards_subject_id ON flashcards(subject_id);
CREATE INDEX IF NOT EXISTS idx_flashcards_chapter_id ON flashcards(chapter_id);
CREATE INDEX IF NOT EXISTS idx_leitner_system_user_id ON leitner_system(user_id);
CREATE INDEX IF NOT EXISTS idx_leitner_system_flashcard_id ON leitner_system(flashcard_id);
CREATE INDEX IF NOT EXISTS idx_leitner_system_next_review_date ON leitner_system(next_review_date);
CREATE INDEX IF NOT EXISTS idx_cornell_notes_user_id ON cornell_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_cornell_notes_subject_id ON cornell_notes(subject_id);
CREATE INDEX IF NOT EXISTS idx_cornell_notes_chapter_id ON cornell_notes(chapter_id);
CREATE INDEX IF NOT EXISTS idx_quizzes_user_id ON quizzes(user_id);
CREATE INDEX IF NOT EXISTS idx_quizzes_subject_id ON quizzes(subject_id);
CREATE INDEX IF NOT EXISTS idx_quizzes_chapter_id ON quizzes(chapter_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_user_id ON calendar_events(user_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_event_date ON calendar_events(event_date);
CREATE INDEX IF NOT EXISTS idx_streaks_user_id ON streaks(user_id);

-- Insert default study tools if they don't exist
INSERT INTO study_tools (name, description) VALUES
    ('Flash Cards', 'Generate flashcards for quick review'),
    ('Quizzes', 'Generate quizzes based on content'),
    ('Calendar', 'Schedule study events and deadlines'),
    ('Daily Objectives', 'Set and track daily study goals'),
    ('Podcast', 'Create or link to audio content for learning'),
    ('Explainer Videos', 'Create or link to video content for learning'),
    ('Mnemonic', 'Create memory aids for concepts'),
    ('Cornell Notes', 'Take structured notes using Cornell method'),
    ('Mind Map', 'Create visual mind maps for concepts'),
    ('Pomodoro Timer', 'Use Pomodoro technique for focused study'),
    ('Leitner System', 'Spaced repetition flashcard system'),
    ('Kanban Board', 'Organize tasks using Kanban methodology'),
    ('Eisenhower Matrix', 'Prioritize tasks using urgency/importance matrix'),
    ('Retrospective', 'Reflect on learning progress and improvements'),
    ('Mastery Tracker', 'Track skill proficiency levels'),
    ('Doodles', 'Create visual sketches and drawings'),
    ('Streaks', 'Track learning consistency and streaks')
ON CONFLICT (name) DO NOTHING;

