#!/usr/bin/env python3
"""
Migration script to add content storage fields to the chapters table
and create the chapter_content directory structure.

This script:
1. Adds content_file_path and content_size columns to the chapters table
2. Creates the chapter_content directory structure
3. Provides a function to migrate existing placeholder content

Run this script after updating the models and before starting the application.
"""

import os
import sys
import psycopg2
import psycopg2.extras
from pathlib import Path

# Add the Backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Import database configuration
from app import get_pg_connection

def migrate_database_schema():
    """Add new columns to the chapters table if they don't exist"""
    try:
        # Connect to PostgreSQL database
        pg_conn = get_pg_connection()
        if not pg_conn:
            print("Error: Could not connect to PostgreSQL database")
            return False
        
        pg_cursor = pg_conn.cursor()
        
        print("Checking current chapters table schema...")
        
        # Check if the new columns already exist
        pg_cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chapters' AND table_schema = 'public'
        """)
        
        existing_columns = [row[0] for row in pg_cursor.fetchall()]
        print(f"Existing columns: {existing_columns}")
        
        # Add content_file_path column if it doesn't exist
        if 'content_file_path' not in existing_columns:
            print("Adding content_file_path column...")
            pg_cursor.execute("""
                ALTER TABLE chapters 
                ADD COLUMN content_file_path VARCHAR(500)
            """)
            print("✓ Added content_file_path column")
        else:
            print("✓ content_file_path column already exists")
        
        # Add content_size column if it doesn't exist
        if 'content_size' not in existing_columns:
            print("Adding content_size column...")
            pg_cursor.execute("""
                ALTER TABLE chapters 
                ADD COLUMN content_size INTEGER DEFAULT 0
            """)
            print("✓ Added content_size column")
        else:
            print("✓ content_size column already exists")
        
        # Update content_size for existing records with content
        print("Updating content_size for existing records...")
        pg_cursor.execute("""
            UPDATE chapters 
            SET content_size = LENGTH(content) 
            WHERE content IS NOT NULL AND content_size = 0
        """)
        
        updated_rows = pg_cursor.rowcount
        print(f"✓ Updated content_size for {updated_rows} existing records")
        
        # Commit changes
        pg_conn.commit()
        print("✓ Database schema migration completed successfully")
        
        pg_cursor.close()
        pg_conn.close()
        
        return True
        
    except Exception as e:
        print(f"Error during database migration: {str(e)}")
        if pg_conn:
            pg_conn.rollback()
            pg_conn.close()
        return False


def create_content_directories():
    """Create the chapter_content directory structure"""
    try:
        # Create the main chapter_content directory
        content_dir = os.path.join(os.path.dirname(__file__), 'chapter_content')
        os.makedirs(content_dir, exist_ok=True)
        print(f"✓ Created content directory: {content_dir}")
        
        # Create a README file explaining the directory structure
        readme_path = os.path.join(content_dir, 'README.md')
        readme_content = """# Chapter Content Storage

This directory stores large chapter content files that exceed the database storage threshold.

## Structure
- Each user has their own subdirectory (e.g., `1/`, `2/`, etc.)
- Chapter content is stored as JSON files with the format: `chapter_{subject_id}_{timestamp}.json`
- Each JSON file contains:
  - `chapter_id`: The chapter ID
  - `title`: Chapter title
  - `content`: The full chapter content
  - `created_at`: Timestamp when the file was created
  - `size`: Content size in characters

## File Naming Convention
- `chapter_{chapter_id}_{timestamp}.json` - For regular uploads
- `chapter_{subject_id}_{timestamp}_fallback.json` - For fallback scenarios

## Storage Threshold
- Content smaller than 50KB is stored directly in the database
- Content larger than 50KB is stored in JSON files and referenced in the database

## Security
- Access to content files is controlled by user authentication
- File paths are stored in the database with proper user association
"""
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✓ Created README file: {readme_path}")
        
        return True
        
    except Exception as e:
        print(f"Error creating content directories: {str(e)}")
        return False


def verify_migration():
    """Verify that the migration was successful"""
    try:
        # Connect to PostgreSQL database
        pg_conn = get_pg_connection()
        if not pg_conn:
            print("Error: Could not connect to PostgreSQL database for verification")
            return False
        
        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Check the updated schema
        pg_cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'chapters' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        columns = pg_cursor.fetchall()
        
        print("\n=== Updated Chapters Table Schema ===")
        for col in columns:
            print(f"- {col['column_name']}: {col['data_type']} "
                  f"({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})"
                  f"{' DEFAULT ' + str(col['column_default']) if col['column_default'] else ''}")
        
        # Check if we have any chapters with content
        pg_cursor.execute("""
            SELECT COUNT(*) as total_chapters,
                   COUNT(CASE WHEN content IS NOT NULL AND content != '' THEN 1 END) as chapters_with_content,
                   COUNT(CASE WHEN content_file_path IS NOT NULL THEN 1 END) as chapters_with_files,
                   AVG(content_size) as avg_content_size
            FROM chapters
        """)
        
        stats = pg_cursor.fetchone()
        
        print(f"\n=== Chapter Content Statistics ===")
        print(f"- Total chapters: {stats['total_chapters']}")
        print(f"- Chapters with content: {stats['chapters_with_content']}")
        print(f"- Chapters with file references: {stats['chapters_with_files']}")
        print(f"- Average content size: {stats['avg_content_size']:.0f} characters" if stats['avg_content_size'] else "- Average content size: 0 characters")
        
        pg_cursor.close()
        pg_conn.close()
        
        return True
        
    except Exception as e:
        print(f"Error during verification: {str(e)}")
        return False


def main():
    """Run the complete migration process"""
    print("=== Chapter Content Storage Migration ===")
    print("This script will update the database schema and create necessary directories.\n")
    
    # Step 1: Migrate database schema
    print("Step 1: Migrating database schema...")
    if not migrate_database_schema():
        print("❌ Database migration failed")
        return False
    
    # Step 2: Create content directories
    print("\nStep 2: Creating content directories...")
    if not create_content_directories():
        print("❌ Directory creation failed")
        return False
    
    # Step 3: Verify migration
    print("\nStep 3: Verifying migration...")
    if not verify_migration():
        print("❌ Migration verification failed")
        return False
    
    print("\n✅ Migration completed successfully!")
    print("\nNext steps:")
    print("1. Restart the application to use the new content storage system")
    print("2. Upload new documents to test the intelligent content extraction")
    print("3. Check that large content is properly stored in JSON files")
    
    return True


if __name__ == "__main__":
    main()
