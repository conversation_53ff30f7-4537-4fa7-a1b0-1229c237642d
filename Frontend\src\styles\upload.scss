// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Common mixins
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
    transition: $property $duration $timing;
}

@mixin focus-ring {
    box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
}

// Upload Popup Overlay
.upload-popup-overlay {
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    backdrop-filter: blur(5px);
    background-color: rgba(0, 0, 0, 0.4);

    .theme-dark & {
        background-color: rgba(0, 0, 0, 0.6);
    }
}

// Upload Popup Container
.upload-popup-container {
    //position: absolute;
    min-width: 800px;
    width: fit-content !important;
    max-width: 90dvw;
    height: fit-content !important;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    z-index: 1000 !important;
    /* Ensure this is higher than the overlay */
    flex-direction: column;
    animation: popIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    font-family: -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: $window-background;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid $border-color;

    .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        border: 1px solid $dark-border-color;
    }

    @keyframes popIn {
        from {
            transform: scale(0.95);
            opacity: 0;
        }

        to {
            transform: scale(1);
            opacity: 1;
        }
    }
}

// Popup Header
.popup-header-main {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    border-bottom: 1px solid $separator-color;

    .theme-dark & {
        border-bottom: 1px solid $dark-separator-color;
    }

    h2 {
        margin: 0;
        padding: 0;
        font-size: 1.25rem;
        font-weight: 600;
        text-align: center;
        width: 100%;
        color: $primary-text;

        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    .close-button-popup {
        background: none;
        border: none;
        font-size: 1.375rem;
        cursor: pointer;
        padding: 4px 10px;
        border-radius: 4px;
        position: absolute;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);
        @include transition;
        color: $tertiary-text;

        .theme-dark & {
            color: $dark-tertiary-text;
        }

        &:hover {
            background-color: rgba($system-red, 0.1);
            color: $system-red;

            .theme-dark & {
                background-color: rgba($system-red, 0.2);
                color: $system-red;
            }
        }
    }
}

// Step Indicator
.step-indicator {
    display: flex;
    padding: 16px 20px;
    margin: 0;
    background-color: $secondary-background;
    border-bottom: 1px solid $separator-color;

    .theme-dark & {
        background-color: $dark-secondary-background;
        border-bottom: 1px solid $dark-separator-color;
    }

    .step {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        &:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 12px;
            left: 50%;
            right: -50%;
            height: 2px;
            z-index: 1;
            background-color: $border-color;

            .theme-dark & {
                background-color: $dark-border-color;
            }
        }

        span {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
            background-color: $border-color;
            color: $tertiary-text;

            .theme-dark & {
                background-color: $dark-border-color;
                color: $dark-tertiary-text;
            }
        }

        p {
            margin: 0;
            font-size: 0.75rem;
            font-weight: 500;
            color: $tertiary-text;

            .theme-dark & {
                color: $dark-tertiary-text;
            }
        }

        &.active {
            span {
                background-color: $system-blue;
                color: white;
                box-shadow: 0 0 0 4px rgba($system-blue, 0.2);

                .theme-dark & {
                    background-color: $system-blue;
                }
            }

            p {
                color: $system-blue;

                .theme-dark & {
                    color: $system-blue;
                }
            }

            &:not(:last-child)::after {
                background-color: $system-blue;

                .theme-dark & {
                    background-color: $system-blue;
                }
            }
        }
    }
}

// Popup Content
.popup-content {
    padding: 20px;
    height: 100% !important;
    overflow-y: auto;
    flex: 1;
    margin: 0;
    background-color: $window-background;

    .theme-dark & {
        background-color: $dark-window-background;
    }

    .step-content {
        animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h3 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.125rem;
            font-weight: 600;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }
}

// Form Controls
.form-group {
    margin-bottom: .5rem !important;

    label {
        display: block;
        margin: 0;
        font-size: 0.875rem;
        font-weight: 500;
        color: $secondary-text;
        padding: 0;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    input[type="text"],
    input[type="url"] {
        width: 100%;
        padding: .5rem !important;
        border-radius: 6px;
        font-size: 0.9375rem;
        border: 1px solid $border-color;
        background-color: $window-background;
        color: $primary-text;
        margin: 0;
        @include transition;

        .theme-dark & {
            border: 1px solid $dark-border-color;
            background-color: $dark-secondary-background;
            color: $dark-primary-text;
        }

        &:focus {
            outline: none;
            border-color: $system-blue;
            @include focus-ring;
        }

        &::placeholder {
            color: $placeholder-text;

            .theme-dark & {
                color: $dark-placeholder-text;
            }
        }
    }

    select {
        width: 100%;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.9375rem;
        border: 1px solid $border-color;
        background-color: $window-background;
        color: $primary-text;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
        @include transition;

        .theme-dark & {
            border: 1px solid $dark-border-color;
            background-color: $dark-secondary-background;
            color: $dark-primary-text;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23AEAEB2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
        }

        &:focus {
            outline: none;
            border-color: $system-blue;
            @include focus-ring;
        }
    }
}

// File Type Selector
.file-type-selector {
    margin-bottom: 24px;

    .options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: .5rem !important;

        button {
            padding: .3rem !important;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            border: 1px solid $border-color;
            background-color: $window-background;
            color: $primary-text;
            @include transition;

            .theme-dark & {
                border: 1px solid $dark-border-color;
                background-color: $dark-secondary-background;
                color: $dark-primary-text;
            }

            &:hover {
                transform: translateY(-2px);
                border-color: $system-blue;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

                .theme-dark & {
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                }
            }

            &.active {
                background-color: rgba($system-blue, 0.1);
                color: $system-blue;
                border-color: $system-blue;

                .theme-dark & {
                    background-color: rgba($system-blue, 0.2);
                }
            }

            .option-icon {
                font-size: 1.5rem;
            }
        }
    }
}

// Study Tools Selection
.form-group {
    .tool-selection-info {
        font-size: 0.9rem;
        color: $secondary-text;
        margin-bottom: 16px;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    .options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;

        .tool-option-container {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .tool-button {
                position: relative;
                padding: 10px 12px;
                border-radius: 8px;
                font-weight: 500;
                text-align: left;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border: 1px solid $border-color;
                background-color: $window-background;
                color: $primary-text;
                @include transition;

                .theme-dark & {
                    border: 1px solid $dark-border-color;
                    background-color: $dark-secondary-background;
                    color: $dark-primary-text;
                }

                &:hover {
                    border-color: $system-blue;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

                    .theme-dark & {
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
                    }
                }

                &.active {
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;
                    border-color: $system-blue;

                    .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                    }
                }

                .tool-selected-indicator {
                    font-size: 1rem;
                    color: $system-blue;
                }
            }

            .tool-description {
                font-size: 0.8rem;
                color: $tertiary-text;
                margin: 0;
                padding: 0 4px;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }
        }
    }
}

// Drop Zone
.drop-zone {
    border-radius: 8px;
    padding: .5rem !important;
    text-align: center;
    margin-bottom: 24px;
    border: 1px dashed $border-color;
    background-color: $secondary-background;
    @include transition;

    .theme-dark & {
        border: 1px dashed $dark-border-color;
        background-color: $dark-secondary-background;
    }

    &.drag-active {
        border-color: $system-blue;
        background-color: rgba($system-blue, 0.05);

        .theme-dark & {
            background-color: rgba($system-blue, 0.1);
        }
    }

    h2 {
        font-size: 1.125rem;
        font-weight: 500;
        margin: 0 0 12px;
        color: $primary-text;
        text-align: center;

        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    svg {
        width: 48px;
        height: 48px;
        margin-bottom: 12px;
        color: $system-blue;
    }

    .hint {
        font-size: 0.875rem;
        color: $tertiary-text;
        margin: 8px 0 0;

        .theme-dark & {
            color: $dark-tertiary-text;
        }
    }

    button {
        margin-top: 16px;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        background-color: $system-blue;
        color: white;
        border: none;
        @include transition;

        &:hover {
            background-color: darken($system-blue, 5%);
            transform: translateY(-1px);
        }

        &:active {
            transform: translateY(0);
        }
    }
}

// Selected Files Preview
.selected-files-preview {
    margin-top: 16px;

    .file-card {
        display: flex;
        align-items: center;
        padding: .5rem !important;
        margin: 0 !important;
        border-radius: 8px;
        background-color: $secondary-background;
        border: 1px solid $border-color;
        @include transition;

        .theme-dark & {
            background-color: $dark-secondary-background;
            border: 1px solid $dark-border-color;
        }

        .file-icon {
            font-size: 1.5rem;
            margin-right: 16px;
            color: $system-blue;
        }

        .file-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 16px;

            .file-name {
                margin: 0;
                font-weight: 500;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .file-size {
                margin: 0;
                font-size: 0.813rem;
                color: $tertiary-text;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }
        }

        button {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 4px;
            cursor: pointer;
            color: $tertiary-text;
            @include transition;

            .theme-dark & {
                color: $dark-tertiary-text;
            }

            &:hover {
                background-color: rgba($system-red, 0.1);
                color: $system-red;

                .theme-dark & {
                    background-color: rgba($system-red, 0.2);
                }
            }
        }
    }
}

// Upload Summary
.upload-summary {
    background-color: $secondary-background;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    .theme-dark & {
        background-color: $dark-secondary-background;
    }

    .summary-title {
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 1rem;
        font-weight: 600;
        color: $primary-text;

        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    .summary-item {
        display: flex;
        flex-direction: column;
        padding: 10px 0;
        border-bottom: 1px solid $separator-color;

        .theme-dark & {
            border-bottom: 1px solid $dark-separator-color;
        }

        &:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-weight: 600;
            color: $secondary-text;
            margin-bottom: 6px;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .summary-value {
            color: $primary-text;
            padding-left: 4px;

            .theme-dark & {
                color: $dark-primary-text;
            }

            .selected-tools-list {
                display: flex;
                flex-direction: column;
                gap: 6px;

                .selected-tool-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 6px 10px;
                    background-color: rgba($system-blue, 0.05);
                    border-radius: 6px;
                    border-left: 3px solid $system-blue;

                    .theme-dark & {
                        background-color: rgba($system-blue, 0.1);
                    }

                    .tool-check-icon {
                        color: $system-blue;
                        font-weight: bold;
                    }
                }
            }
        }
    }
}



// Upload Progress
.upload-progress-container {
    margin-top: 20px;

    .progress-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.875rem;
        color: $secondary-text;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    .progress-bar-container {
        height: 4px;
        background-color: $separator-color;
        border-radius: 2px;
        overflow: hidden;

        .theme-dark & {
            background-color: $dark-separator-color;
        }

        .progress-bar {
            height: 100%;
            background-color: $system-blue;
            transition: width 0.3s ease;
        }
    }
}

// Buttons
.button {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    @include transition;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    &.primary {
        background-color: $system-blue;
        color: white;
        border: none;

        &:hover:not(:disabled) {
            background-color: darken($system-blue, 5%);
            transform: translateY(-1px);
        }

        &:active:not(:disabled) {
            transform: translateY(0);
        }
    }

    &.secondary {
        background-color: $secondary-background;
        color: $system-blue;
        border: 1px solid $border-color;

        .theme-dark & {
            background-color: $dark-secondary-background;
            border: 1px solid $dark-border-color;
        }

        &:hover:not(:disabled) {
            background-color: darken($secondary-background, 2%);
            transform: translateY(-1px);

            .theme-dark & {
                background-color: lighten($dark-secondary-background, 2%);
            }
        }

        &:active:not(:disabled) {
            transform: translateY(0);
        }
    }

    .spinner {
        display: inline-block;
        width: 14px;
        height: 14px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top-color: white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

// Popup Footer
.popup-footer {
    padding: 16px 20px;
    border-top: 1px solid $separator-color;
    display: flex;
    justify-content: space-between;
    background-color: $secondary-background;

    .theme-dark & {
        border-top: 1px solid $dark-separator-color;
        background-color: $dark-secondary-background;
    }

    button {
        min-width: 100px;
    }
}

// Error Message
.error-message {
    font-size: 0.813rem;
    color: $system-red;
    margin-top: 4px;
}

// Message Popup Overlay
.message-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999 !important;
    /* Ensure this is the highest z-index */
    backdrop-filter: blur(5px);
    background-color: rgba(0, 0, 0, 0.4);

    .theme-dark & {
        background-color: rgba(0, 0, 0, 0.6);
    }
}

// Message Popup Container
.message-popup-container {
    width: 400px;
    max-width: 90%;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: popIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    background-color: $window-background;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid $border-color;

    .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        border: 1px solid $dark-border-color;
    }

    .popup-header {
        padding: 16px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        border-bottom: 1px solid $separator-color;

        .theme-dark & {
            border-bottom: 1px solid $dark-separator-color;
        }

        h2 {
            margin: 0;
            padding: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        .close-button-popup {
            background: none;
            border: none;
            font-size: 1.375rem;
            cursor: pointer;
            padding: 4px 10px;
            border-radius: 4px;
            color: $tertiary-text;
            @include transition;

            .theme-dark & {
                color: $dark-tertiary-text;
            }

            &:hover {
                background-color: rgba($system-red, 0.1);
                color: $system-red;

                .theme-dark & {
                    background-color: rgba($system-red, 0.2);
                    color: $system-red;
                }
            }
        }
    }

    .popup-content {
        padding: 20px;
        background-color: $window-background;

        .theme-dark & {
            background-color: $dark-window-background;
        }

        p {
            margin: 0;
            font-size: 1rem;
            line-height: 1.5;
            color: $secondary-text;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }
    }
}