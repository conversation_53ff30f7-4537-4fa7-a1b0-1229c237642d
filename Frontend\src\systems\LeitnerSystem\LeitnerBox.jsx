import React from 'react';
import { motion } from 'framer-motion';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import VisibilityIcon from '@mui/icons-material/Visibility';

const LeitnerBox = ({ boxNumber, cards, icon, onStudy, onPreview }) => {
  // Calculate review schedule based on box number
  const getReviewSchedule = () => {
    switch (boxNumber) {
      case 1:
        return 'Every day';
      case 2:
        return 'Every 3 days';
      case 3:
        return 'Every week';
      default:
        return 'Unknown';
    }
  };

  // Get box description
  const getBoxDescription = () => {
    switch (boxNumber) {
      case 1:
        return 'New & difficult cards';
      case 2:
        return 'Learning cards';
      case 3:
        return 'Mastered cards';
      default:
        return '';
    }
  };

  return (
    <motion.div
      className={`leitner-box box-${boxNumber}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: boxNumber * 0.1 }}
    >
      <div className="box-header">
        <h3>
          {icon}
          Box {boxNumber}
        </h3>
        <div className="card-count">{cards.length}</div>
      </div>

      <div className="box-description">
        <p>{getBoxDescription()}</p>
        <p className="review-schedule">Review: {getReviewSchedule()}</p>
      </div>

      {cards.length > 0 ? (
        <div className="box-preview">
          {cards.slice(0, 3).map((card, index) => (
            <div
              key={card.id}
              className="preview-card"
              style={{
                transform: `translateY(${index * 5}px)`,
                zIndex: 3 - index
              }}
            >
              <div className="preview-content">
                {card.question.length > 50
                  ? card.question.substring(0, 50) + '...'
                  : card.question}
              </div>
            </div>
          ))}

          {cards.length > 3 && (
            <div className="more-cards">+{cards.length - 3} more</div>
          )}
        </div>
      ) : (
        <div className="empty-box">
          <p>No cards in this box</p>
        </div>
      )}

      <div className="box-actions">
        <button
          className="study-button"
          onClick={onStudy}
          disabled={cards.length === 0}
        >
          <PlayArrowIcon />
          Study
        </button>

        <button
          className="preview-button"
          onClick={onPreview}
          disabled={cards.length === 0}
        >
          <VisibilityIcon />
          Preview
        </button>
      </div>
    </motion.div>
  );
};

export default LeitnerBox;
