import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';

// Animation modes
const ANIMATION_MODES = {
    IDLE: 'idle',
    LISTENING: 'listening',
    PROCESSING: 'processing',
    SPEAKING: 'speaking'
};

// Emotion types that affect color and animation style
const EMOTIONS = {
    NEUTRAL: 'neutral',
    HAPPY: 'happy',
    SAD: 'sad',
    ANGRY: 'angry',
    SURPRISED: 'surprised'
};

// Default colors for futuristic AI theme
const defaultColors = {
    primary: '#00FFFF',     // Cyan
    secondary: '#FF00FF',   // Magenta
    accent: '#FFFFFF',      // White
    glow: 'rgba(0, 255, 255, 0.5)' // Cyan glow
};

// Emotion color mappings
const EMOTION_COLORS = {
    [EMOTIONS.NEUTRAL]: {
        primary: '#00FFFF',   // Cyan
        secondary: '#0088FF', // Blue
        accent: '#FFFFFF',    // White
        glow: 'rgba(0, 255, 255, 0.5)'
    },
    [EMOTIONS.HAPPY]: {
        primary: '#00FF88',   // Green-cyan
        secondary: '#88FF00', // Lime
        accent: '#FFFFFF',    // White
        glow: 'rgba(0, 255, 136, 0.5)'
    },
    [EMOTIONS.SAD]: {
        primary: '#0088FF',   // Blue
        secondary: '#0044AA', // Dark blue
        accent: '#AADDFF',    // Light blue
        glow: 'rgba(0, 136, 255, 0.5)'
    },
    [EMOTIONS.ANGRY]: {
        primary: '#FF0088',   // Pink
        secondary: '#FF0000', // Red
        accent: '#FFDD00',    // Yellow
        glow: 'rgba(255, 0, 136, 0.5)'
    },
    [EMOTIONS.SURPRISED]: {
        primary: '#FF00FF',   // Magenta
        secondary: '#AA00FF', // Purple
        accent: '#FFFFFF',    // White
        glow: 'rgba(255, 0, 255, 0.5)'
    }
};

const DEFAULT_SPEED = 1;
const DEFAULT_SIZE = 200;
const DEFAULT_EMOTION = EMOTIONS.NEUTRAL;
const DEFAULT_BAR_COUNT = 64;
const DEFAULT_PARTICLE_COUNT = 100;

// Styled components
const WaveformContainer = styled.div`
    position: relative;
    width: ${props => props.size}px;
    height: ${props => props.size}px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;
`;

const Canvas = styled.canvas`
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    margin: 0 auto;
`;

// Particle class for background effects
class Particle {
    constructor(canvas, colors) {
        this.canvas = canvas;
        this.colors = colors;
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.size = Math.random() * 2 + 1;
        this.speedX = (Math.random() - 0.5) * 0.5;
        this.speedY = (Math.random() - 0.5) * 0.5;
        this.alpha = Math.random() * 0.5 + 0.2;

        // Calculate distance from center for glow effect
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const dx = this.x - centerX;
        const dy = this.y - centerY;
        this.distanceFromCenter = Math.sqrt(dx * dx + dy * dy);
        this.maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);
    }

    update(intensity) {
        // Move the particle
        this.x += this.speedX * intensity;
        this.y += this.speedY * intensity;

        // Wrap around edges
        if (this.x > this.canvas.width) this.x = 0;
        if (this.x < 0) this.x = this.canvas.width;
        if (this.y > this.canvas.height) this.y = 0;
        if (this.y < 0) this.y = this.canvas.height;

        // Recalculate distance from center
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const dx = this.x - centerX;
        const dy = this.y - centerY;
        this.distanceFromCenter = Math.sqrt(dx * dx + dy * dy);
    }

    draw(ctx) {
        // Fade particles based on distance from center
        const fadeRatio = 1 - (this.distanceFromCenter / this.maxDistance);
        ctx.globalAlpha = this.alpha * fadeRatio;

        ctx.fillStyle = this.colors.accent;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();

        ctx.globalAlpha = 1;
    }
}

const EnergySphere = ({
    mode = ANIMATION_MODES.IDLE,
    colors = defaultColors,
    speed = DEFAULT_SPEED,
    active = true,
    size = DEFAULT_SIZE,
    emotion = DEFAULT_EMOTION,
    audioData = null, // Optional audio data for more precise visualization
    barCount = DEFAULT_BAR_COUNT,
    particleCount = DEFAULT_PARTICLE_COUNT
}) => {
    const canvasRef = useRef(null);
    const animationRef = useRef(null);
    const lastTimeRef = useRef(0);
    const particlesRef = useRef([]);
    const barsRef = useRef([]);
    const timeRef = useRef(0);

    // Get current colors based on emotion
    const getEmotionColors = useCallback(() => {
        return EMOTION_COLORS[emotion] || EMOTION_COLORS[EMOTIONS.NEUTRAL];
    }, [emotion]);

    // Get animation parameters based on mode and emotion
    const getAnimationParams = useCallback(() => {
        let baseIntensity = 0;
        let rhythmVariability = 0;
        let pulseSpeed = 0;
        let glowIntensity = 0;
        let particleSpeed = 0;

        switch (mode) {
            case ANIMATION_MODES.LISTENING:
                baseIntensity = 0.4 * speed;
                rhythmVariability = 0.3;
                pulseSpeed = 1.0 * speed;
                glowIntensity = 0.6;
                particleSpeed = 1.2 * speed;
                break;
            case ANIMATION_MODES.PROCESSING:
                baseIntensity = 0.2 * speed;
                rhythmVariability = 0.1;
                pulseSpeed = 0.7 * speed;
                glowIntensity = 0.4;
                particleSpeed = 0.8 * speed;
                break;
            case ANIMATION_MODES.SPEAKING:
                baseIntensity = 0.8 * speed;
                rhythmVariability = 0.6;
                pulseSpeed = 1.5 * speed;
                glowIntensity = 0.8;
                particleSpeed = 1.5 * speed;
                break;
            case ANIMATION_MODES.IDLE:
            default:
                baseIntensity = 0.1 * speed;
                rhythmVariability = 0.05;
                pulseSpeed = 0.5 * speed;
                glowIntensity = 0.3;
                particleSpeed = 0.5 * speed;
                break;
        }

        // Adjust based on emotion
        switch (emotion) {
            case EMOTIONS.HAPPY:
                baseIntensity *= 1.2;
                rhythmVariability *= 1.3;
                pulseSpeed *= 1.2;
                glowIntensity *= 1.2;
                break;
            case EMOTIONS.SAD:
                baseIntensity *= 0.8;
                rhythmVariability *= 0.7;
                pulseSpeed *= 0.8;
                glowIntensity *= 0.9;
                break;
            case EMOTIONS.ANGRY:
                baseIntensity *= 1.4;
                rhythmVariability *= 1.1;
                pulseSpeed *= 1.3;
                glowIntensity *= 1.3;
                break;
            case EMOTIONS.SURPRISED:
                baseIntensity *= 1.3;
                rhythmVariability *= 1.4;
                pulseSpeed *= 1.5;
                glowIntensity *= 1.4;
                break;
            case EMOTIONS.NEUTRAL:
            default:
                // No adjustment for neutral
                break;
        }

        return {
            baseIntensity,
            rhythmVariability,
            pulseSpeed,
            glowIntensity,
            particleSpeed
        };
    }, [mode, speed, emotion]);

    // Generate spectrum data (simulated audio frequencies)
    const generateSpectrumData = useCallback(() => {
        const params = getAnimationParams();
        const emotionColors = getEmotionColors();

        // Create bars with random heights that will be animated
        return Array(barCount).fill(0).map((_, index) => {
            // Base height with some randomness
            let height = params.baseIntensity;

            // Add variability based on position (center bars taller)
            const positionFactor = 1 - Math.abs((index / barCount) - 0.5) * 2;
            height += positionFactor * 0.3;

            // Add random variability
            height += (Math.random() - 0.5) * params.rhythmVariability;

            // Ensure height is between 0.1 and 1
            height = Math.max(0.1, Math.min(1, height));

            // Determine color based on position and emotion
            const colorRatio = index / barCount;
            let color;

            if (colorRatio < 0.5) {
                // Gradient from primary to secondary
                const ratio = colorRatio * 2;
                color = blendColors(emotionColors.primary, emotionColors.secondary, ratio);
            } else {
                // Gradient from secondary to primary
                const ratio = (colorRatio - 0.5) * 2;
                color = blendColors(emotionColors.secondary, emotionColors.primary, ratio);
            }

            return {
                height,
                targetHeight: height,
                color,
                phase: Math.random() * Math.PI * 2, // Random starting phase for oscillation
                speed: 0.5 + Math.random() * 0.5 // Random speed multiplier
            };
        });
    }, [barCount, getAnimationParams, getEmotionColors]);

    // Initialize particles for background effect
    const initParticles = useCallback(() => {
        if (!canvasRef.current) return;

        const canvas = canvasRef.current;
        const emotionColors = getEmotionColors();

        particlesRef.current = Array(particleCount)
            .fill(null)
            .map(() => {
                const particle = {
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    size: Math.random() * 2 + 1,
                    speedX: (Math.random() - 0.5) * 0.5,
                    speedY: (Math.random() - 0.5) * 0.5,
                    alpha: Math.random() * 0.5 + 0.2,
                    color: emotionColors.accent
                };

                // Calculate distance from center for glow effect
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const dx = particle.x - centerX;
                const dy = particle.y - centerY;
                particle.distanceFromCenter = Math.sqrt(dx * dx + dy * dy);
                particle.maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);

                return particle;
            });
    }, [particleCount, getEmotionColors]);

    // Helper function to blend two colors
    const blendColors = (color1, color2, ratio) => {
        // Convert hex to RGB
        const parseColor = (hexColor) => {
            const r = parseInt(hexColor.slice(1, 3), 16);
            const g = parseInt(hexColor.slice(3, 5), 16);
            const b = parseInt(hexColor.slice(5, 7), 16);
            return { r, g, b };
        };

        const c1 = parseColor(color1);
        const c2 = parseColor(color2);

        // Blend colors
        const r = Math.round(c1.r * (1 - ratio) + c2.r * ratio);
        const g = Math.round(c1.g * (1 - ratio) + c2.g * ratio);
        const b = Math.round(c1.b * (1 - ratio) + c2.b * ratio);

        // Convert back to hex
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    };

    // Update spectrum bars based on current animation frame
    const updateBars = useCallback((deltaTime) => {
        const params = getAnimationParams();
        const bars = barsRef.current;

        // Update time reference for wave animation
        timeRef.current += deltaTime * 0.001 * params.pulseSpeed;

        // If we have audio data, use it to determine bar heights
        if (audioData && mode === ANIMATION_MODES.SPEAKING) {
            // This would be implemented to analyze audio frequencies
            // For now, we'll simulate with oscillating heights
        }

        // Update each bar
        bars.forEach((bar, index) => {
            // Calculate new target height based on oscillation and position
            const oscillation = Math.sin(timeRef.current * bar.speed + bar.phase);
            const positionFactor = 1 - Math.abs((index / bars.length) - 0.5) * 2;

            let newTargetHeight = params.baseIntensity;

            // Add oscillation effect
            newTargetHeight += oscillation * params.rhythmVariability * positionFactor;

            // Add mode-specific patterns
            if (mode === ANIMATION_MODES.SPEAKING) {
                // More dynamic pattern for speaking
                const fastOscillation = Math.sin(timeRef.current * 5 * bar.speed + bar.phase);
                newTargetHeight += fastOscillation * 0.2 * positionFactor;
            } else if (mode === ANIMATION_MODES.LISTENING) {
                // Subtle pulse for listening
                const pulse = Math.sin(timeRef.current * 2);
                newTargetHeight += pulse * 0.1;
            }

            // Ensure height is between 0.1 and 1
            bar.targetHeight = Math.max(0.1, Math.min(1, newTargetHeight));

            // Smoothly transition to target height
            const transitionRate = 0.1 * params.pulseSpeed;
            bar.height += (bar.targetHeight - bar.height) * transitionRate;
        });

        return bars;
    }, [mode, audioData, getAnimationParams]);

    // Update particles for background effect
    const updateParticles = useCallback((deltaTime) => {
        if (!canvasRef.current) return;

        const canvas = canvasRef.current;
        const params = getAnimationParams();
        const particles = particlesRef.current;

        particles.forEach(particle => {
            // Move the particle
            particle.x += particle.speedX * params.particleSpeed;
            particle.y += particle.speedY * params.particleSpeed;

            // Wrap around edges
            if (particle.x > canvas.width) particle.x = 0;
            if (particle.x < 0) particle.x = canvas.width;
            if (particle.y > canvas.height) particle.y = 0;
            if (particle.y < 0) particle.y = canvas.height;

            // Recalculate distance from center
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const dx = particle.x - centerX;
            const dy = particle.y - centerY;
            particle.distanceFromCenter = Math.sqrt(dx * dx + dy * dy);
        });

        return particles;
    }, [getAnimationParams]);

    // Draw the spectrum visualization
    const drawSpectrum = useCallback((ctx, bars, canvasWidth, canvasHeight) => {
        const params = getAnimationParams();
        const emotionColors = getEmotionColors();

        // Clear canvas with transparent background
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // Draw background glow
        const centerX = canvasWidth / 2;
        const centerY = canvasHeight / 2;
        const radius = Math.min(canvasWidth, canvasHeight) * 0.4;

        // Create animated gradient background
        const gradientAngle = timeRef.current * 0.2;
        const gradientX1 = centerX + Math.cos(gradientAngle) * radius * 0.5;
        const gradientY1 = centerY + Math.sin(gradientAngle) * radius * 0.5;
        const gradientX2 = centerX + Math.cos(gradientAngle + Math.PI) * radius * 0.5;
        const gradientY2 = centerY + Math.sin(gradientAngle + Math.PI) * radius * 0.5;

        const backgroundGradient = ctx.createLinearGradient(
            gradientX1, gradientY1,
            gradientX2, gradientY2
        );
        backgroundGradient.addColorStop(0, emotionColors.primary);
        backgroundGradient.addColorStop(0.5, emotionColors.secondary);
        backgroundGradient.addColorStop(1, emotionColors.primary);

        // Draw outer glow
        const glowGradient = ctx.createRadialGradient(
            centerX, centerY, 0,
            centerX, centerY, radius
        );
        glowGradient.addColorStop(0, emotionColors.glow);
        glowGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        ctx.globalAlpha = params.glowIntensity;
        ctx.fillStyle = glowGradient;
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        ctx.globalAlpha = 1.0;

        // Draw particles with connecting lines for abstract effect
        ctx.globalAlpha = 0.5;

        // Draw connecting lines between nearby particles
        particlesRef.current.forEach((particle, i) => {
            for (let j = i + 1; j < particlesRef.current.length; j++) {
                const otherParticle = particlesRef.current[j];
                const dx = particle.x - otherParticle.x;
                const dy = particle.y - otherParticle.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // Only connect particles that are close to each other
                const maxDistance = radius * 0.3;
                if (distance < maxDistance) {
                    // Calculate opacity based on distance
                    const opacity = (1 - distance / maxDistance) * 0.2;

                    // Create gradient line
                    const lineGradient = ctx.createLinearGradient(
                        particle.x, particle.y,
                        otherParticle.x, otherParticle.y
                    );
                    lineGradient.addColorStop(0, emotionColors.primary);
                    lineGradient.addColorStop(1, emotionColors.secondary);

                    ctx.beginPath();
                    ctx.moveTo(particle.x, particle.y);
                    ctx.lineTo(otherParticle.x, otherParticle.y);
                    ctx.strokeStyle = lineGradient;
                    ctx.globalAlpha = opacity;
                    ctx.lineWidth = 1;
                    ctx.stroke();
                }
            }
        });

        // Draw particles with enhanced effects
        ctx.globalCompositeOperation = 'lighter';

        particlesRef.current.forEach(particle => {
            // Fade particles based on distance from center
            const fadeRatio = 1 - (particle.distanceFromCenter / particle.maxDistance);

            // Add pulsing effect to particles
            const pulseEffect = 0.7 + Math.sin(timeRef.current * 2 + particle.x * 0.01) * 0.3;

            // Adjust opacity based on mode
            let modeOpacityFactor = 1.0;
            if (mode === ANIMATION_MODES.SPEAKING) {
                modeOpacityFactor = 1.2;
            } else if (mode === ANIMATION_MODES.LISTENING) {
                modeOpacityFactor = 0.9;
            }

            ctx.globalAlpha = particle.alpha * fadeRatio * pulseEffect * modeOpacityFactor;

            // Create small glow effect for particles
            ctx.shadowColor = particle.color;
            ctx.shadowBlur = 3 * params.glowIntensity;

            // Draw particle
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size * pulseEffect, 0, Math.PI * 2);
            ctx.fill();

            // Reset shadow
            ctx.shadowBlur = 0;

            // Draw connecting lines between some particles for network effect
            if (Math.random() < 0.05 && mode === ANIMATION_MODES.SPEAKING) {
                const nearestParticles = findNearestParticles(particle, 2);
                nearestParticles.forEach(otherParticle => {
                    const lineOpacity = 0.1 * fadeRatio * pulseEffect;
                    ctx.globalAlpha = lineOpacity;

                    // Create gradient line
                    const lineGradient = ctx.createLinearGradient(
                        particle.x, particle.y,
                        otherParticle.x, otherParticle.y
                    );
                    lineGradient.addColorStop(0, particle.color);
                    lineGradient.addColorStop(1, otherParticle.color);

                    ctx.beginPath();
                    ctx.moveTo(particle.x, particle.y);
                    ctx.lineTo(otherParticle.x, otherParticle.y);
                    ctx.strokeStyle = lineGradient;
                    ctx.lineWidth = 0.5;
                    ctx.stroke();
                });
            }
        });

        // Helper function to find nearest particles
        function findNearestParticles(particle, count) {
            return particlesRef.current
                .filter(p => p !== particle)
                .sort((a, b) => {
                    const distA = Math.hypot(a.x - particle.x, a.y - particle.y);
                    const distB = Math.hypot(b.x - particle.x, b.y - particle.y);
                    return distA - distB;
                })
                .slice(0, count);
        }

        ctx.globalAlpha = 1.0;
        ctx.globalCompositeOperation = 'source-over';

        // Calculate bar width and spacing
        const totalBars = bars.length;
        const barWidth = (canvasWidth * 0.8) / totalBars;
        const barSpacing = barWidth * 0.2;

        // Draw futuristic circular spectrum
        const angleStep = (Math.PI * 2) / totalBars;
        const innerRadius = Math.min(canvasWidth, canvasHeight) * 0.2;
        const outerRadius = Math.min(canvasWidth, canvasHeight) * 0.45;

        // Set blend mode for additive blending to enhance glow effect
        ctx.globalCompositeOperation = 'lighter';

        // Draw bars with dynamic effects
        bars.forEach((bar, index) => {
            const angle = index * angleStep;

            // Add dynamic height variation based on mode
            let heightMultiplier = 1.0;
            if (mode === ANIMATION_MODES.SPEAKING) {
                // More dramatic variation during speaking
                const fastOscillation = Math.sin(timeRef.current * 8 + index * 0.2);
                heightMultiplier += fastOscillation * 0.3;
            } else if (mode === ANIMATION_MODES.LISTENING) {
                // Gentle pulsing during listening
                const slowOscillation = Math.sin(timeRef.current * 3 + index * 0.1);
                heightMultiplier += slowOscillation * 0.15;
            }

            const barHeight = bar.height * (outerRadius - innerRadius) * heightMultiplier;

            // Calculate start and end points with slight offset for visual interest
            const angleOffset = Math.sin(timeRef.current + index * 0.1) * 0.02;
            const adjustedAngle = angle + angleOffset;

            const startRadius = innerRadius;
            const endRadius = innerRadius + barHeight;

            const startX = centerX + Math.cos(adjustedAngle) * startRadius;
            const startY = centerY + Math.sin(adjustedAngle) * startRadius;
            const endX = centerX + Math.cos(adjustedAngle) * endRadius;
            const endY = centerY + Math.sin(adjustedAngle) * endRadius;

            // Create gradient for each bar with 3 color stops for more visual interest
            const barGradient = ctx.createLinearGradient(startX, startY, endX, endY);

            // Alternate color patterns for more visual variety
            if (index % 4 === 0) {
                barGradient.addColorStop(0, emotionColors.primary);
                barGradient.addColorStop(0.5, emotionColors.secondary);
                barGradient.addColorStop(1, emotionColors.accent);
            } else if (index % 4 === 1) {
                barGradient.addColorStop(0, emotionColors.secondary);
                barGradient.addColorStop(0.7, emotionColors.primary);
                barGradient.addColorStop(1, emotionColors.secondary);
            } else if (index % 4 === 2) {
                barGradient.addColorStop(0, emotionColors.accent);
                barGradient.addColorStop(0.3, emotionColors.primary);
                barGradient.addColorStop(1, emotionColors.secondary);
            } else {
                barGradient.addColorStop(0, emotionColors.primary);
                barGradient.addColorStop(0.5, emotionColors.accent);
                barGradient.addColorStop(1, emotionColors.primary);
            }

            // Draw bar with varying width based on position and time
            const widthVariation = 0.8 + Math.sin(timeRef.current * 2 + index * 0.3) * 0.2;
            const dynamicBarWidth = (barWidth - barSpacing) * widthVariation;

            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.lineWidth = dynamicBarWidth;
            ctx.lineCap = 'round';
            ctx.strokeStyle = barGradient;
            ctx.stroke();

            // Add glow effect with varying intensity
            const glowIntensity = params.glowIntensity * (0.7 + Math.sin(timeRef.current * 3 + index * 0.5) * 0.3);
            ctx.shadowColor = bar.color;
            ctx.shadowBlur = 12 * glowIntensity;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.lineWidth = dynamicBarWidth * 0.6;
            ctx.stroke();
            ctx.shadowBlur = 0;

            // Add small particle effects at the end of some bars for extra visual interest
            if (index % 5 === 0 && mode === ANIMATION_MODES.SPEAKING) {
                const particleSize = 1.5 + Math.random() * 1.5;
                ctx.beginPath();
                ctx.arc(endX, endY, particleSize, 0, Math.PI * 2);
                ctx.fillStyle = emotionColors.accent;
                ctx.globalAlpha = 0.7;
                ctx.fill();
                ctx.globalAlpha = 1.0;
            }
        });

        // Reset composite operation
        ctx.globalCompositeOperation = 'source-over';

        // Create an abstract animation with millions of tiny dots in the center
        ctx.globalCompositeOperation = 'source-over';

        // Draw a semi-transparent dark circle in the center to create depth
        ctx.beginPath();
        ctx.arc(centerX, centerY, innerRadius * 1.2, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        ctx.fill();

        // Set blend mode for additive blending to enhance glow effect
        ctx.globalCompositeOperation = 'lighter';

        // Generate a large number of tiny dots
        const dotCount = 5000; // Large number of dots for rich effect

        // Create different dot clusters with varying behaviors
        const clusterCount = 5;

        for (let cluster = 0; cluster < clusterCount; cluster++) {
            // Different parameters for each cluster
            const clusterRadius = innerRadius * (0.4 + cluster * 0.15);
            const clusterDotCount = Math.floor(dotCount / clusterCount);
            const clusterSpeed = 0.2 + cluster * 0.1;
            const clusterDirection = cluster % 2 === 0 ? 1 : -1; // Alternate directions

            // Determine cluster color based on emotion
            let clusterColor;
            if (cluster % 3 === 0) {
                clusterColor = emotionColors.primary;
            } else if (cluster % 3 === 1) {
                clusterColor = emotionColors.secondary;
            } else {
                clusterColor = emotionColors.accent;
            }

            // Adjust cluster behavior based on mode
            let intensityFactor = 1.0;
            let sizeFactor = 1.0;
            let opacityFactor = 1.0;

            if (mode === ANIMATION_MODES.SPEAKING) {
                intensityFactor = 1.5;
                sizeFactor = 1.2;
                opacityFactor = 1.3;
            } else if (mode === ANIMATION_MODES.LISTENING) {
                intensityFactor = 1.2;
                sizeFactor = 1.1;
                opacityFactor = 1.1;
            } else if (mode === ANIMATION_MODES.PROCESSING) {
                intensityFactor = 0.9;
                sizeFactor = 0.9;
                opacityFactor = 0.9;
            }

            // Draw dots for this cluster
            for (let i = 0; i < clusterDotCount; i++) {
                // Create base angle and radius for dot position
                const baseAngle = (i / clusterDotCount) * Math.PI * 2;

                // Add time-based rotation to create motion
                const rotationSpeed = clusterSpeed * clusterDirection * intensityFactor;
                const rotationAngle = baseAngle + timeRef.current * rotationSpeed;

                // Add wave motion to radius for dynamic effect
                const waveFrequency = 2 + cluster * 0.5;
                const waveAmplitude = 0.2 + cluster * 0.05;
                const radiusVariation = Math.sin(timeRef.current * waveFrequency + i * 0.01) * waveAmplitude;

                // Calculate dot radius with variation
                const dotRadius = clusterRadius * (1 + radiusVariation);

                // Add horizontal motion
                const horizontalOffset = Math.sin(timeRef.current * (0.5 + cluster * 0.1) + i * 0.01) * innerRadius * 0.2;

                // Calculate dot position
                const x = centerX + Math.cos(rotationAngle) * dotRadius + horizontalOffset;
                const y = centerY + Math.sin(rotationAngle) * dotRadius;

                // Vary dot size based on position and time
                const baseDotSize = 0.5 + Math.random() * 1.0; // Tiny dots
                const sizePulse = 0.8 + Math.sin(timeRef.current * 2 + i * 0.01) * 0.2;
                const dotSize = baseDotSize * sizePulse * sizeFactor;

                // Calculate distance from center for glow and opacity
                const distanceFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                const maxDistance = innerRadius * 1.2;
                const distanceRatio = distanceFromCenter / maxDistance;

                // Fade dots based on distance from center
                const baseOpacity = 0.1 + Math.random() * 0.3; // Low base opacity for subtle effect
                const opacityVariation = Math.sin(timeRef.current + i * 0.01) * 0.1;
                let dotOpacity = (baseOpacity + opacityVariation) * (1 - distanceRatio * 0.8) * opacityFactor;

                // Boost opacity for some dots to create highlights
                if (Math.random() < 0.05) {
                    dotOpacity *= 3;
                }

                // Set dot color with slight variation
                const hueShift = Math.sin(timeRef.current * 0.2 + i * 0.01) * 20;
                const dotColor = shiftHue(clusterColor, hueShift);

                // Draw the dot with glow
                ctx.globalAlpha = dotOpacity;
                ctx.fillStyle = dotColor;

                // Add glow to some dots
                if (Math.random() < 0.2) {
                    ctx.shadowColor = dotColor;
                    ctx.shadowBlur = 3 * params.glowIntensity;
                }

                ctx.beginPath();
                ctx.arc(x, y, dotSize, 0, Math.PI * 2);
                ctx.fill();

                // Reset shadow
                ctx.shadowBlur = 0;
            }
        }

        // Helper function to shift hue of a color
        function shiftHue(hexColor, amount) {
            // Convert hex to RGB
            const r = parseInt(hexColor.slice(1, 3), 16);
            const g = parseInt(hexColor.slice(3, 5), 16);
            const b = parseInt(hexColor.slice(5, 7), 16);

            // Convert RGB to HSL
            const [h, s, l] = rgbToHsl(r, g, b);

            // Shift hue and convert back to RGB
            const newHue = (h + amount / 360) % 1;
            const [newR, newG, newB] = hslToRgb(newHue, s, l);

            // Convert back to hex
            return `#${Math.round(newR).toString(16).padStart(2, '0')}${Math.round(newG).toString(16).padStart(2, '0')}${Math.round(newB).toString(16).padStart(2, '0')}`;
        }

        // Helper function to convert RGB to HSL
        function rgbToHsl(r, g, b) {
            r /= 255;
            g /= 255;
            b /= 255;

            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;

            if (max === min) {
                h = s = 0; // achromatic
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }

                h /= 6;
            }

            return [h, s, l];
        }

        // Helper function to convert HSL to RGB
        function hslToRgb(h, s, l) {
            let r, g, b;

            if (s === 0) {
                r = g = b = l; // achromatic
            } else {
                const hue2rgb = (p, q, t) => {
                    if (t < 0) t += 1;
                    if (t > 1) t -= 1;
                    if (t < 1 / 6) return p + (q - p) * 6 * t;
                    if (t < 1 / 2) return q;
                    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
                    return p;
                };

                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;

                r = hue2rgb(p, q, h + 1 / 3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1 / 3);
            }

            return [r * 255, g * 255, b * 255];
        }

        // Reset composite operation
        ctx.globalCompositeOperation = 'source-over';

        // Create a dense core of tiny dots instead of a solid core
        // Set parameters for core dots
        const coreDotCount = 1000; // Many dots for dense core
        const coreRadius = innerRadius * 0.3;

        // Adjust core behavior based on mode
        let coreIntensity = 1.0;
        let coreDensity = 1.0;

        if (mode === ANIMATION_MODES.SPEAKING) {
            coreIntensity = 1.5;
            coreDensity = 1.3;
        } else if (mode === ANIMATION_MODES.LISTENING) {
            coreIntensity = 1.2;
            coreDensity = 1.1;
        }

        // Create a subtle glow background for the core
        const coreGlowGradient = ctx.createRadialGradient(
            centerX, centerY, 0,
            centerX, centerY, coreRadius * 1.5
        );

        coreGlowGradient.addColorStop(0, `${emotionColors.primary}40`); // Semi-transparent
        coreGlowGradient.addColorStop(0.5, `${emotionColors.secondary}20`);
        coreGlowGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

        ctx.beginPath();
        ctx.arc(centerX, centerY, coreRadius * 1.5, 0, Math.PI * 2);
        ctx.fillStyle = coreGlowGradient;
        ctx.globalAlpha = 0.4;
        ctx.fill();
        ctx.globalAlpha = 1.0;

        // Draw dense core dots
        for (let i = 0; i < coreDotCount; i++) {
            // Calculate position with spiral pattern
            const angle = (i / coreDotCount) * Math.PI * 20; // Multiple rotations for spiral
            const distanceFromCenter = (i / coreDotCount) * coreRadius * coreDensity;

            // Add time-based rotation and pulsing
            const rotationSpeed = 0.5 * coreIntensity;
            const pulseFrequency = 3 * coreIntensity;
            const pulseAmplitude = 0.1;

            // Calculate position with rotation and pulsing
            const rotatedAngle = angle + timeRef.current * rotationSpeed;
            const pulsedDistance = distanceFromCenter * (1 + Math.sin(timeRef.current * pulseFrequency + i * 0.1) * pulseAmplitude);

            // Add horizontal motion
            const horizontalOffset = Math.sin(timeRef.current * 0.8 + i * 0.05) * coreRadius * 0.15 * coreIntensity;

            // Calculate final position
            const x = centerX + Math.cos(rotatedAngle) * pulsedDistance + horizontalOffset;
            const y = centerY + Math.sin(rotatedAngle) * pulsedDistance;

            // Vary dot size - smaller near center, larger at edges
            const sizeVariation = 0.5 + (distanceFromCenter / coreRadius) * 0.5;
            const dotSize = (0.5 + Math.random() * 1.0) * sizeVariation;

            // Vary opacity - more opaque near center
            const opacityVariation = 1 - (distanceFromCenter / coreRadius) * 0.5;
            const dotOpacity = (0.4 + Math.random() * 0.6) * opacityVariation;

            // Determine dot color with variation
            let dotColor;
            const colorRandom = Math.random();

            if (colorRandom < 0.4) {
                dotColor = emotionColors.primary;
            } else if (colorRandom < 0.7) {
                dotColor = emotionColors.secondary;
            } else {
                dotColor = emotionColors.accent;
            }

            // Add slight hue variation
            const hueShift = Math.sin(timeRef.current * 0.3 + i * 0.02) * 15;
            const finalColor = shiftHue(dotColor, hueShift);

            // Draw the dot with glow
            ctx.globalAlpha = dotOpacity;
            ctx.fillStyle = finalColor;

            // Add glow to some dots
            if (Math.random() < 0.3) {
                ctx.shadowColor = finalColor;
                ctx.shadowBlur = 3 * params.glowIntensity;
            }

            ctx.beginPath();
            ctx.arc(x, y, dotSize, 0, Math.PI * 2);
            ctx.fill();

            // Reset shadow
            ctx.shadowBlur = 0;
        }

        // Add a few brighter highlight dots in the center
        const highlightCount = 20;
        for (let i = 0; i < highlightCount; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * coreRadius * 0.5;

            // Add horizontal motion
            const horizontalOffset = Math.sin(timeRef.current * 1.2 + i) * coreRadius * 0.1;

            const x = centerX + Math.cos(angle) * distance + horizontalOffset;
            const y = centerY + Math.sin(angle) * distance;

            const dotSize = 1 + Math.random() * 2;

            // Draw bright highlight dot
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = emotionColors.accent;
            ctx.shadowColor = emotionColors.accent;
            ctx.shadowBlur = 8 * params.glowIntensity;

            ctx.beginPath();
            ctx.arc(x, y, dotSize, 0, Math.PI * 2);
            ctx.fill();

            ctx.shadowBlur = 0;
        }

        ctx.globalAlpha = 1.0;

        // Add small clusters of tiny particles around the sphere
        // These will be smaller and more numerous than the main clusters
        const microClusterCount = 8;
        const microDotsPerCluster = 200;

        for (let cluster = 0; cluster < microClusterCount; cluster++) {
            // Determine cluster position - distributed around the sphere
            const clusterAngle = (cluster / microClusterCount) * Math.PI * 2;
            const clusterDistance = innerRadius * 0.6;

            // Add time-based motion to cluster position
            const clusterSpeed = 0.2 + (cluster % 3) * 0.1;
            const clusterMotion = Math.sin(timeRef.current * clusterSpeed + cluster) * innerRadius * 0.1;

            // Calculate cluster center with horizontal emphasis
            const clusterCenterX = centerX + Math.cos(clusterAngle) * clusterDistance + clusterMotion * 1.5;
            const clusterCenterY = centerY + Math.sin(clusterAngle) * clusterDistance + clusterMotion * 0.5;

            // Determine cluster color
            let clusterColor;
            if (cluster % 3 === 0) {
                clusterColor = emotionColors.primary;
            } else if (cluster % 3 === 1) {
                clusterColor = emotionColors.secondary;
            } else {
                clusterColor = emotionColors.accent;
            }

            // Draw tiny dots in this cluster
            for (let i = 0; i < microDotsPerCluster; i++) {
                // Random position within cluster with gaussian-like distribution
                // More dots near the center, fewer at the edges
                const angle = Math.random() * Math.PI * 2;
                const r1 = Math.random();
                const r2 = Math.random();
                // Use Box-Muller transform for gaussian distribution
                const gaussianR = Math.sqrt(-2 * Math.log(r1)) * Math.cos(2 * Math.PI * r2);
                const normalizedR = Math.min(Math.abs(gaussianR), 3) / 3; // Limit to 3 standard deviations

                // Cluster radius varies by cluster
                const clusterRadius = innerRadius * 0.1 * (1 + cluster % 3 * 0.3);
                const distance = normalizedR * clusterRadius;

                // Add time-based motion within cluster
                const dotSpeed = 0.5 + Math.random() * 0.5;
                const dotMotion = Math.sin(timeRef.current * dotSpeed + i * 0.1) * clusterRadius * 0.2;

                // Calculate dot position with horizontal emphasis
                const x = clusterCenterX + Math.cos(angle) * distance + dotMotion * 1.2;
                const y = clusterCenterY + Math.sin(angle) * distance + dotMotion * 0.5;

                // Very tiny dots
                const dotSize = 0.3 + Math.random() * 0.7;

                // Vary opacity based on distance from cluster center
                const normalizedDistance = distance / clusterRadius;
                const baseOpacity = 0.1 + Math.random() * 0.2;
                const dotOpacity = baseOpacity * (1 - normalizedDistance * 0.7);

                // Vary color slightly
                const hueShift = Math.sin(timeRef.current * 0.1 + i * 0.01) * 15;
                const dotColor = shiftHue(clusterColor, hueShift);

                // Draw the tiny dot
                ctx.globalAlpha = dotOpacity;
                ctx.fillStyle = dotColor;

                // Add subtle glow to some dots
                if (Math.random() < 0.1) {
                    ctx.shadowColor = dotColor;
                    ctx.shadowBlur = 2 * params.glowIntensity;
                }

                ctx.beginPath();
                ctx.arc(x, y, dotSize, 0, Math.PI * 2);
                ctx.fill();

                // Reset shadow
                ctx.shadowBlur = 0;
            }
        }

        // Add a few "floating" particles that move more independently
        const floatingParticleCount = 50;
        for (let i = 0; i < floatingParticleCount; i++) {
            // Calculate position with more random movement
            const baseAngle = (i / floatingParticleCount) * Math.PI * 2;
            const baseDistance = innerRadius * (0.3 + Math.random() * 0.5);

            // Add time-based motion with horizontal emphasis
            const particleSpeed = 0.3 + Math.random() * 0.4;
            const horizontalMotion = Math.sin(timeRef.current * particleSpeed + i) * innerRadius * 0.15;
            const verticalMotion = Math.cos(timeRef.current * particleSpeed * 0.7 + i) * innerRadius * 0.05;

            // Calculate position
            const x = centerX + Math.cos(baseAngle) * baseDistance + horizontalMotion;
            const y = centerY + Math.sin(baseAngle) * baseDistance + verticalMotion;

            // Slightly larger than the micro dots
            const particleSize = 0.8 + Math.random() * 1.2;

            // Determine color
            let particleColor;
            const colorRandom = Math.random();

            if (colorRandom < 0.4) {
                particleColor = emotionColors.primary;
            } else if (colorRandom < 0.7) {
                particleColor = emotionColors.secondary;
            } else {
                particleColor = emotionColors.accent;
            }

            // Draw the particle with glow
            ctx.globalAlpha = 0.4 + Math.random() * 0.3;
            ctx.fillStyle = particleColor;
            ctx.shadowColor = particleColor;
            ctx.shadowBlur = 3 * params.glowIntensity;

            ctx.beginPath();
            ctx.arc(x, y, particleSize, 0, Math.PI * 2);
            ctx.fill();

            // Reset shadow
            ctx.shadowBlur = 0;
        }

        ctx.globalAlpha = 1.0;
    }, [getAnimationParams, getEmotionColors]);

    // Animation loop
    const animate = useCallback((timestamp) => {
        if (!canvasRef.current || !active) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        // Calculate delta time for smooth animation
        const deltaTime = timestamp - (lastTimeRef.current || timestamp);
        lastTimeRef.current = timestamp;

        // Update bars and particles
        const bars = updateBars(deltaTime);
        updateParticles(deltaTime);

        // Draw spectrum visualization
        drawSpectrum(ctx, bars, canvas.width, canvas.height);

        // Continue animation loop
        animationRef.current = requestAnimationFrame(animate);
    }, [active, updateBars, updateParticles, drawSpectrum]);

    // Initialize bars and particles
    useEffect(() => {
        barsRef.current = generateSpectrumData();
        initParticles();
    }, [generateSpectrumData, initParticles, emotion]);

    // Setup canvas and start animation
    useEffect(() => {
        if (!canvasRef.current) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        // Set canvas dimensions
        const setCanvasDimensions = () => {
            const dpr = window.devicePixelRatio || 1;
            canvas.width = size * dpr;
            canvas.height = size * dpr;
            ctx.scale(dpr, dpr);
        };

        setCanvasDimensions();

        // Start animation if active
        if (active) {
            animationRef.current = requestAnimationFrame(animate);
        }

        // Cleanup
        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [size, active, animate]);

    return (
        <WaveformContainer size={size}>
            <Canvas ref={canvasRef} />
        </WaveformContainer>
    );
};

// Demo component to test the futuristic AI voice animation
export const EnergySphereDemo = () => {
    const [mode, setMode] = useState(ANIMATION_MODES.IDLE);
    const [isActive, setIsActive] = useState(false);
    const [emotion, setEmotion] = useState(EMOTIONS.NEUTRAL);
    const [barCount, setBarCount] = useState(64);
    const [speed, setSpeed] = useState(1);

    // Simulate different voice states
    const simulateVoiceActivity = () => {
        setIsActive(true);
        setMode(ANIMATION_MODES.LISTENING);

        setTimeout(() => {
            setMode(ANIMATION_MODES.PROCESSING);

            setTimeout(() => {
                setMode(ANIMATION_MODES.SPEAKING);

                setTimeout(() => {
                    setMode(ANIMATION_MODES.IDLE);
                    setIsActive(false);
                }, 3000);
            }, 2000);
        }, 2000);
    };

    return (
        <div style={{ maxWidth: '300px', margin: '2rem auto' }}>
            <h3>Futuristic AI Voice Visualization</h3>
            <EnergySphere
                mode={mode}
                active={isActive}
                emotion={emotion}
                barCount={barCount}
                speed={speed}
            />
            <div style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem' }}>
                <button onClick={simulateVoiceActivity}>Simulate Conversation</button>
                <button onClick={() => setIsActive(!isActive)}>
                    {isActive ? 'Stop' : 'Start'}
                </button>
            </div>
            <div style={{ marginTop: '1rem' }}>
                <label>
                    Mode:
                    <select
                        value={mode}
                        onChange={(e) => setMode(e.target.value)}
                    >
                        <option value={ANIMATION_MODES.IDLE}>Idle</option>
                        <option value={ANIMATION_MODES.LISTENING}>Listening</option>
                        <option value={ANIMATION_MODES.PROCESSING}>Processing</option>
                        <option value={ANIMATION_MODES.SPEAKING}>Speaking</option>
                    </select>
                </label>
            </div>
            <div style={{ marginTop: '1rem' }}>
                <label>
                    Emotion:
                    <select
                        value={emotion}
                        onChange={(e) => setEmotion(e.target.value)}
                    >
                        <option value={EMOTIONS.NEUTRAL}>Neutral</option>
                        <option value={EMOTIONS.HAPPY}>Happy</option>
                        <option value={EMOTIONS.SAD}>Sad</option>
                        <option value={EMOTIONS.ANGRY}>Angry</option>
                        <option value={EMOTIONS.SURPRISED}>Surprised</option>
                    </select>
                </label>
            </div>
            <div style={{ marginTop: '1rem' }}>
                <label>
                    Bars:
                    <input
                        type="range"
                        min="16"
                        max="128"
                        step="8"
                        value={barCount}
                        onChange={(e) => setBarCount(parseInt(e.target.value))}
                    />
                    {barCount}
                </label>
            </div>
            <div style={{ marginTop: '1rem' }}>
                <label>
                    Speed:
                    <input
                        type="range"
                        min="0.5"
                        max="2"
                        step="0.1"
                        value={speed}
                        onChange={(e) => setSpeed(parseFloat(e.target.value))}
                    />
                    {speed.toFixed(1)}
                </label>
            </div>
        </div>
    );
};

export default EnergySphere;