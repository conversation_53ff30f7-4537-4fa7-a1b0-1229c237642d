"""
<PERSON><PERSON><PERSON> to fix the flashcard duplication issue.

This script:
1. Adds a unique constraint to the flashcards table
2. Removes duplicate flashcards, keeping the most recently created one
3. Limits the number of flashcards per chapter

Run this script from the project root directory:
    python fix_flashcards.py
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def run_command(command):
    """Run a shell command and return the output"""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        logging.error(f"Command failed: {e}")
        logging.error(f"Error output: {e.stderr}")
        return None

def main():
    """Main function to run the fixes"""
    logging.info("Starting flashcard fix script")
    
    # Check if we're in the right directory
    if not os.path.exists('Backend') or not os.path.exists('Frontend'):
        logging.error("Please run this script from the project root directory")
        sys.exit(1)
    
    # Step 1: Run the constraint script
    logging.info("Step 1: Adding unique constraint to flashcards table")
    if os.path.exists('Backend/add_flashcard_constraint.py'):
        result = run_command('python Backend/add_flashcard_constraint.py')
        if result:
            logging.info("Successfully added unique constraint")
        else:
            logging.error("Failed to add unique constraint")
            return
    else:
        logging.error("add_flashcard_constraint.py not found")
        return
    
    # Step 2: Verify the changes to app.py
    logging.info("Step 2: Verifying changes to app.py")
    if os.path.exists('Backend/app.py'):
        with open('Backend/app.py', 'r') as f:
            content = f.read()
            if 'MAX_FLASHCARDS_PER_CHAPTER = 20' in content and 'DELETE FROM flashcards' in content:
                logging.info("app.py has been successfully modified")
            else:
                logging.warning("app.py may not have been properly modified")
    else:
        logging.error("app.py not found")
        return
    
    # Step 3: Verify the changes to document_processor.py
    logging.info("Step 3: Verifying changes to document_processor.py")
    if os.path.exists('Backend/document_processor.py'):
        with open('Backend/document_processor.py', 'r') as f:
            content = f.read()
            if 'MAX_FLASHCARDS_PER_CHAPTER = 20' in content and 'chapter_flashcard_count' in content:
                logging.info("document_processor.py has been successfully modified")
            else:
                logging.warning("document_processor.py may not have been properly modified")
    else:
        logging.error("document_processor.py not found")
        return
    
    # Step 4: Count current flashcards in the database
    logging.info("Step 4: Counting flashcards in the database")
    count_script = """
import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '5432')
DB_NAME = os.environ.get('DB_NAME', 'blueprint')
DB_USER = os.environ.get('DB_USER', 'postgres')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Lukind@1956')

# Connect to the database
conn = psycopg2.connect(
    host=DB_HOST,
    port=DB_PORT,
    dbname=DB_NAME,
    user=DB_USER,
    password=DB_PASSWORD
)

cursor = conn.cursor()

# Count total flashcards
cursor.execute("SELECT COUNT(*) FROM flashcards")
total_count = cursor.fetchone()[0]
print(f"Total flashcards: {total_count}")

# Count flashcards by user
cursor.execute("SELECT user_id, COUNT(*) FROM flashcards GROUP BY user_id")
user_counts = cursor.fetchall()
for user_id, count in user_counts:
    print(f"User {user_id}: {count} flashcards")

# Count AI-generated flashcards
cursor.execute("SELECT COUNT(*) FROM flashcards WHERE ai_generated = TRUE")
ai_generated_count = cursor.fetchone()[0]
print(f"AI-generated flashcards: {ai_generated_count}")

# Close the connection
cursor.close()
conn.close()
"""
    
    with open('count_flashcards.py', 'w') as f:
        f.write(count_script)
    
    result = run_command('python count_flashcards.py')
    if result:
        logging.info(f"Flashcard counts:\n{result}")
    else:
        logging.error("Failed to count flashcards")
    
    logging.info("Flashcard fix script completed")
    logging.info("The following changes have been made:")
    logging.info("1. Added a unique constraint to prevent duplicate flashcards")
    logging.info("2. Removed existing duplicate flashcards")
    logging.info("3. Limited the number of flashcards per chapter to 20")
    logging.info("4. Added code to delete existing AI-generated flashcards before generating new ones")

if __name__ == "__main__":
    main()
