# Leitner System Unique Constraint Fix

This document explains the fix for the unique constraint violation in the Leitner System component.

## Problem

The server error message reveals the exact issue:

```
Error saving flashcards: duplicate key value violates unique constraint "unique_flashcard_question"
```

This occurs because:

1. The Leitner System component is trying to save flashcards to the database
2. The database has a unique constraint on the `question` field
3. When trying to save a card with a question that already exists, it violates this constraint

## Root Cause

The fundamental issue is that the Leitner System component should not be creating new flashcards. It should only be updating the Leitner system data (box_level, streak, etc.) for existing flashcards.

The error message from the server indicates that we're trying to insert a new flashcard with a question that already exists, rather than updating an existing flashcard.

## Solution

The solution is to modify the LeitnerSystem component to:

1. Only update existing flashcards (those with an ID)
2. Include the original question and answer when updating to avoid unique constraint violations
3. Focus on updating the Leitner-specific fields rather than creating new flashcards

## Implementation

We've made the following changes to the LeitnerSystem.jsx file:

### 1. Only Process Existing Cards

```jsx
// Skip cards without an ID since we can only update Leitner data for existing flashcards
if (!card.id) {
  console.log('Skipping card without ID - cannot update Leitner data for non-existent flashcard');
  continue; // Skip to the next card
}
```

### 2. Include Original Question and Answer

```jsx
// Create a minimal flashcard object with just the ID and required fields
// This will update the existing flashcard without changing its question/answer
const cardToSave = {
  id: card.id, // Keep the ID for identifying the flashcard
  question: originalCard.question, // Keep the original question to avoid unique constraint violation
  answer: originalCard.answer, // Keep the original answer
  type: originalCard.type || 'basic', // Keep the original type
  box_level: card.box_level || 1,
  chapter_id: chapterId,
  subject_id: subjectId,
  // Convert any Date objects to ISO strings
  last_reviewed: card.last_reviewed ? (card.last_reviewed instanceof Date ? card.last_reviewed.toISOString() : card.last_reviewed) : null,
  next_review_date: card.next_review_date ? (card.next_review_date instanceof Date ? card.next_review_date.toISOString() : card.next_review_date) : null,
  // Include Leitner-specific fields
  leitner_streak: card.leitner_streak || 0,
  review_count: card.review_count || 0,
  success_rate: card.success_rate || 0
};
```

### 3. Only Update Modified Cards

```jsx
// Include card if:
// 1. It has been modified (different from original)
// 2. It has the isModified flag set
if (originalCard && (
  card.isModified ||
  card.box_level !== originalCard.box_level ||
  card.leitner_streak !== originalCard.leitner_streak ||
  card.next_review_date !== originalCard.next_review_date)) {
  // ...
}
```

## How It Works

1. When a user clicks "Correct" or "Incorrect", the component updates the card's Leitner data (box_level, streak, etc.)
2. When saving, we only include cards that have been modified
3. For each card, we include its original question and answer to avoid unique constraint violations
4. The server will update the existing flashcard instead of trying to create a new one

## Server-Side Handling

The server already has the necessary code to handle this situation:

```javascript
// If the card has the same question as an existing card, use that ID
if (!flashcardId && existingFlashcardMap[question]) {
    flashcardId = existingFlashcardMap[question];

    // Update the existing flashcard
    await pool.query(
        `UPDATE flashcards SET
            answer = $1,
            type = $2,
            box_level = $3,
            last_reviewed = $4,
            next_review_date = $5,
            color = $6,
            difficult = $7,
            review_count = $8,
            success_rate = $9,
            tags = $10,
            updated_at = NOW()
        WHERE id = $11`,
        [
            answer, type, box_level, last_reviewed, next_review_date,
            color, difficult, review_count, success_rate, tagsString,
            flashcardId
        ]
    );
}
```

## Expected Behavior After Fix

After applying this fix:

1. When a user clicks on the "Correct" or "Incorrect" button, the component will only update existing flashcards
2. No more unique constraint violations
3. The streak counter will update properly
4. Cards will move between boxes as expected

## Technical Details

The fix addresses the unique constraint violation by:

1. **Only Updating Existing Cards**: We skip any cards without an ID, ensuring we only update existing flashcards.
2. **Including Original Question**: We include the original question when updating to avoid unique constraint violations.
3. **Focusing on Leitner Data**: We focus on updating the Leitner-specific fields (box_level, streak, etc.) rather than creating new flashcards.

This approach ensures that we don't violate the unique constraint on the `question` field while still allowing users to update cards and move them between boxes.
