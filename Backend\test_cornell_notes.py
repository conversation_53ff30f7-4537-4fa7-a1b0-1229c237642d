#!/usr/bin/env python3
"""
Test script for Cornell Notes AI endpoint
"""

import requests
import jwt
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_URL = "http://localhost:5000"
JWT_SECRET = os.environ.get('JWT_SECRET', 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6')
USER_ID = 1  # Test with user ID 1

def generate_test_token(user_id):
    """Generate a valid JWT token for testing"""
    import time

    # Use time.time() to match backend expectations
    now = int(time.time())
    exp = now + 3600  # 1 hour from now

    payload = {
        'iat': now,
        'nbf': now,
        'exp': exp,
        'userId': user_id,
        'jti': 'test-token-123'
    }

    print(f"Token payload: {payload}")
    print(f"Current time: {now}")
    print(f"Expires at: {exp}")
    print(f"JWT Secret: {JWT_SECRET[:10]}...")

    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def test_cornell_notes_endpoint():
    """Test the Cornell Notes AI endpoint"""
    print("=== Testing Cornell Notes AI Endpoint ===")

    # Generate a valid token
    token = generate_test_token(USER_ID)
    print(f"Generated token for user {USER_ID}")

    # Test data - using the chapter ID from our database check
    test_data = {
        "chapter_id": 145,  # Biology Chapter 1: Pathogens and Barriers
        "subject": "Biology",
        "chapter_title": "Pathogens and Barriers"
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    print(f"Testing with data: {test_data}")
    print(f"Making request to: {API_URL}/api/ai/generate-concepts-from-file")

    try:
        response = requests.post(
            f"{API_URL}/api/ai/generate-concepts-from-file",
            json=test_data,
            headers=headers,
            timeout=30
        )

        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Response: {result}")
            if result.get('success'):
                print(f"Generated concepts: {result.get('concepts', 'N/A')[:200]}...")
            else:
                print(f"Error in response: {result.get('error', 'Unknown error')}")
        else:
            print("❌ FAILED!")
            try:
                error_data = response.json()
                print(f"Error response: {error_data}")
            except:
                print(f"Raw response: {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_cornell_notes_endpoint()
