import React from 'react';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import AccessibilityNewIcon from '@mui/icons-material/AccessibilityNew';
import '../../styles/SettingsPanels.scss';

const MotionSettingsPanel = ({ settings }) => {
  const { reducedMotion, setReducedMotion, isDarkMode } = settings;
  
  return (
    <div className={`detail-panel motion-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Motion Settings</h2>
        <p className="panel-description">
          Adjust animation and motion effects to suit your preferences or accessibility needs.
        </p>
      </div>
      
      <div className="settings-group">
        <div className="setting-item toggle-setting">
          <div className="toggle-container">
            <div className="toggle-label">
              <AccessibilityNewIcon />
              <span>Reduced Motion</span>
            </div>
            <div className="toggle-description">
              <p>
                When enabled, reduces or eliminates animations and motion effects 
                throughout the application for improved accessibility.
              </p>
            </div>
            <label className="toggle-switch">
              <input 
                type="checkbox" 
                checked={reducedMotion} 
                onChange={() => setReducedMotion(!reducedMotion)}
              />
              <span className="toggle-slider"></span>
              <span className="toggle-status">{reducedMotion ? 'Enabled' : 'Disabled'}</span>
            </label>
          </div>
        </div>
      </div>
      
      <div className="motion-preview">
        <div className="preview-header">
          <h3>Animation Preview</h3>
        </div>
        
        <div className="animation-container">
          <div className={`animation-demo ${reducedMotion ? 'reduced' : ''}`}>
            {!reducedMotion ? (
              <div className="animated-elements">
                {[...Array(5)].map((_, i) => (
                  <div 
                    key={i} 
                    className="animated-element"
                    style={{
                      animationDelay: `${i * 0.2}s`,
                      backgroundColor: i % 2 === 0 ? '#007AFF' : '#28CD41'
                    }}
                  >
                    <AutoAwesomeIcon />
                  </div>
                ))}
              </div>
            ) : (
              <div className="static-elements">
                {[...Array(5)].map((_, i) => (
                  <div 
                    key={i} 
                    className="static-element"
                    style={{
                      backgroundColor: i % 2 === 0 ? '#007AFF' : '#28CD41'
                    }}
                  >
                    <AutoAwesomeIcon />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <div className="preview-description">
          <p>
            {reducedMotion ? 
              'Reduced motion is enabled. Animations are minimized or disabled.' : 
              'Standard animations are enabled. Elements move and transition smoothly.'}
          </p>
        </div>
      </div>
      
      <div className="motion-options">
        <div className="option-group">
          <h3>Additional Motion Settings</h3>
          
          <div className="option-item">
            <label className="option-label">
              <span>Animation Speed</span>
              <select className="option-select" disabled={reducedMotion}>
                <option value="slow">Slow</option>
                <option value="normal" selected>Normal</option>
                <option value="fast">Fast</option>
              </select>
            </label>
          </div>
          
          <div className="option-item">
            <label className="option-label">
              <span>Transition Effects</span>
              <select className="option-select" disabled={reducedMotion}>
                <option value="minimal">Minimal</option>
                <option value="standard" selected>Standard</option>
                <option value="enhanced">Enhanced</option>
              </select>
            </label>
          </div>
        </div>
      </div>
      
      <div className="info-box">
        <p>
          Reduced motion settings help users who experience discomfort from motion 
          and animations. These settings comply with WCAG accessibility guidelines.
        </p>
      </div>
    </div>
  );
};

export default MotionSettingsPanel;
