import React from 'react';

const ToolCard = ({ tool, onClick, onRemove, isAddMode = false }) => {
    const handleRemove = (e) => {
        e.stopPropagation();
        onRemove && onRemove();
    };

    return (
        <div className={`tool-card ${isAddMode ? 'add-mode' : ''}`} onClick={onClick}>
            {onRemove && !isAddMode && (
                <button
                    className="remove-button"
                    onClick={handleRemove}
                    title="Remove Tool"
                >
                    ×
                </button>
            )}
            <img src={tool.image} alt={tool.name} className="tool-icon" />
            <h3>{tool.name}</h3>
            {/*tool.description && <p>{tool.description}</p>*/}
            {isAddMode && (
                <div className="add-indicator">
                    <span>+</span>
                </div>
            )}
        </div>
    );
};

export default ToolCard;