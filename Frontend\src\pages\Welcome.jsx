import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "../../authContext";
import { useNavigate } from "react-router-dom";
import axios from 'axios';
import '../styles/Welcome.scss';

import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

// Define all steps and their corresponding components
const stepConfig = [

    {
        name: "Chronotype",
        component: ({ formData, setFormData }) => (
            <>
                <h2>Your Natural Study Time</h2>
                <p>When are you most productive for studying?</p>
                <div className="chronotype-options">
                    {['Early Bird', 'Night Owl', 'Neutral'].map(type => (
                        <button
                            key={type}
                            className={formData.chronotype === type ? "selected" : ""}
                            onClick={() => setFormData({ ...formData, chronotype: type })}
                        >
                            {type === 'Early Bird' && '🌅'}
                            {type === 'Night Owl' && '🌙'}
                            {type === 'Neutral' && '⏱️'}
                            <span className="chronotype-label">{type}</span>
                        </button>
                    ))}
                </div>
            </>
        ),
        canProceed: (formData) => !!formData.chronotype
    },
    {
        name: "About You",
        component: ({ formData, setFormData }) => (
            <>
                <h2>Tell Us About Yourself</h2>
                <p>This helps us personalize your experience</p>
                <div className="role-options">
                    {[
                        { name: "Student", icon: "👨‍🎓" },
                        { name: "Teacher", icon: "👨‍🏫" },
                        { name: "Parent", icon: "👪" },
                        { name: "Professor", icon: "🧑‍🏫" },
                        { name: "Self-learner", icon: "🧠" }
                    ].map((role) => (
                        <button
                            key={role.name}
                            className={formData.role === role.name ? "selected" : ""}
                            onClick={() => setFormData({ ...formData, role: role.name })}
                        >
                            <span className="role-icon">{role.icon}</span>
                            <span className="role-name">{role.name}</span>
                        </button>
                    ))}
                </div>
            </>
        ),
        canProceed: (formData) => !!formData.role
    },

    {
        name: "Goals",
        component: ({ formData, toggleItem }) => (
            <>
                <h2>Your Learning Goals</h2>
                <p>Select all that apply</p>
                <div className="goal-options">
                    {[
                        { name: "Exam Preparation", icon: "📚" },
                        { name: "Skill Development", icon: "🔧" },
                        { name: "Career Advancement", icon: "📈" },
                        { name: "Teaching", icon: "👨‍🏫" },
                        { name: "Personal Interest", icon: "❤️" },
                        { name: "Research", icon: "🔬" }
                    ].map((goal) => (
                        <button
                            key={goal.name}
                            className={formData.goals.includes(goal.name) ? "selected" : ""}
                            onClick={() => toggleItem("goals", goal.name)}
                        >
                            <span className="goal-icon">{goal.icon}</span>
                            <span className="goal-name">{goal.name}</span>
                        </button>
                    ))}
                </div>
            </>
        ),
        canProceed: (formData) => true
    },
    {
        name: "Tools",
        component: ({ formData, toggleItem }) => (
            <>
                <h2>Preferred Learning Systems</h2>
                <p>Select the learning systems you'd like to use</p>
                <div className="tool-options">
                    {[
                        { name: "STIC Method", icon: "📝" },
                        { name: "FAST Recall", icon: "⚡" },
                        { name: "Early Bird v/s Night Owl", icon: "🌓" },
                        { name: "Mnemonics", icon: "🧠" },
                        { name: "Leitner System", icon: "🗃️" },
                        { name: "Feynman Technique", icon: "🔍" }
                    ].map((tool) => (
                        <button
                            key={tool.name}
                            className={formData.tools.includes(tool.name) ? "selected" : ""}
                            onClick={() => toggleItem("tools", tool.name)}
                        >
                            <span className="tool-icon">{tool.icon}</span>
                            <span className="tool-name">{tool.name}</span>
                        </button>
                    ))}
                </div>
            </>
        ),
        canProceed: (formData) => true
    },
    {
        name: "Finish",
        component: ({ isSubmitting, handleSubmit, showPricingModal, togglePricingModal }) => (
            <>
                <h2>Complete Your Setup</h2>
                <div className="premium-container">
                    <div className="premium-header">
                        <span className="premium-badge">✨ PREMIUM</span>
                        <h3>Unlock Premium Features</h3>
                    </div>

                    <div className="premium-features">
                        <div className="feature-item">
                            <span className="feature-icon">📊</span>
                            <div className="feature-text">
                                <h4>Advanced Analytics</h4>
                            </div>
                        </div>
                        <div className="feature-item">
                            <span className="feature-icon">🔄</span>
                            <div className="feature-text">
                                <h4>Unlimited Materials</h4>
                            </div>
                        </div>
                        <div className="feature-item">
                            <span className="feature-icon">🎯</span>
                            <div className="feature-text">
                                <h4>Personalized Plans</h4>
                            </div>
                        </div>
                        <div className="feature-item">
                            <span className="feature-icon">🔔</span>
                            <div className="feature-text">
                                <h4>Priority Support</h4>
                            </div>
                        </div>
                    </div>

                    <div className="premium-actions">
                        <button
                            className="premium-button"
                            onClick={() => {
                                console.log('Premium button clicked');
                                togglePricingModal();
                            }}
                        >
                            Upgrade to Premium
                        </button>
                        <button
                            className="continue-free-button"
                            onClick={handleSubmit}
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? "Saving..." : "Continue with Free Plan"}
                        </button>
                    </div>
                </div>


            </>
        ),
        canProceed: (formData) => true,
        isFinalStep: true
    }
];

const StepsPage = ({ onComplete }) => {
    const { user, token, loading, logout } = useAuth();
    const navigate = useNavigate();
    const [step, setStep] = useState(0);
    const [formData, setFormData] = useState({
        language: "english",
        chronotype: "",
        role: "",
        field_of_study: "",
        goals: [],
        tools: [],
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [fieldSuggestions, setFieldSuggestions] = useState([]);
    const [showPricingModal, setShowPricingModal] = useState(false);
    const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

    const commonFields = [
        "Computer Science",
        "Medicine",
        "Engineering",
        "Business",
        "Arts",
        "Mathematics",
        "Physics",
        "Psychology",
        "Law",
        "Education"
    ];

    const togglePricingModal = () => {
        console.log('togglePricingModal called, current state:', showPricingModal);
        setShowPricingModal(prev => !prev);
        console.log('togglePricingModal after state update, new state will be:', !showPricingModal);
    };

    // Add keyboard event listener for modal and handle body class
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === 'Escape' && showPricingModal) {
                togglePricingModal();
            }
        };

        // Add/remove body class to prevent scrolling when modal is open
        if (showPricingModal) {
            document.body.classList.add('modal-open');
        } else {
            document.body.classList.remove('modal-open');
        }

        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            document.body.classList.remove('modal-open');
        };
    }, [showPricingModal, togglePricingModal]);

    // Define fetchPreferences callback first since it's used in the useEffect
    const fetchPreferences = useCallback(async () => {
        console.log('StepsPage.jsx: Fetching preferences data');
        if (!token) {
            console.error('StepsPage.jsx: No token available. Redirecting to login.');
            logout();
            navigate('/login', { replace: true });
            return;
        }

        try {
            const response = await axios.get(`${API_URL}/api/preferences`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            if (response.data.success && response.data.preferences) {
                console.log('StepsPage.jsx: Preferences data received:', response.data.preferences);
                setFormData({
                    language: response.data.preferences.language || "english",
                    chronotype: response.data.preferences.chronotype || "",
                    role: response.data.preferences.role || "",
                    field_of_study: response.data.preferences.field_of_study || "",
                    goals: response.data.preferences.goals || [],
                    tools: response.data.preferences.tools || []
                });
            } else {
                console.log('StepsPage.jsx: No preferences data found, using defaults');
            }
        } catch (error) {
            console.error("StepsPage.jsx: Failed to fetch preferences:", error);
            if (error.response?.status === 403) {
                console.error('StepsPage.jsx: Invalid or expired token. Redirecting to login.');
                logout();
                navigate('/login', { replace: true });
            }
        }
    }, [token, logout, navigate, API_URL]);



    useEffect(() => {
        console.log('StepsPage.jsx: User state:', user, 'Token:', !!token);

        // Skip if still loading
        if (loading) {
            return;
        }

        // If no user or token, redirect to login
        if (!user || !token) {
            console.log('StepsPage.jsx: No user or token. Redirecting to login.');
            navigate("/login", { replace: true });
            return;
        }

        // User is logged in, check preferences status
        console.log('StepsPage.jsx: User is logged in, checking preferences status');

        let isMounted = true;

        const checkPreferencesStatus = async () => {
            try {
                const response = await axios.get(`${API_URL}/api/check-preferences/${user.id}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });

                if (!isMounted) return;

                if (response.data.preferencesCompleted) {
                    console.log('StepsPage.jsx: User has completed preferences, redirecting');
                    // User has already completed preferences, redirect to last visited path or dashboard
                    const lastVisitedPath = sessionStorage.getItem('lastVisitedPath') || '/dashboard';
                    navigate(lastVisitedPath, { replace: true });
                } else {
                    console.log('StepsPage.jsx: User has not completed preferences, fetching data');
                    // If preferences not completed, fetch existing preferences data
                    fetchPreferences();
                }
            } catch (error) {
                if (!isMounted) return;

                console.error('StepsPage.jsx: Error checking preferences status:', error);
                if (error.response?.status === 403) {
                    console.log('StepsPage.jsx: Token expired, logging out');
                    logout();
                    navigate('/login', { replace: true });
                } else {
                    console.log('StepsPage.jsx: Other error, fetching preferences anyway');
                    fetchPreferences();
                }
            }
        };

        checkPreferencesStatus();

        return () => {
            isMounted = false;
        };
    }, [user, token, loading, navigate, logout, fetchPreferences]);

    const next = () => {
        if (stepConfig[step].canProceed(formData)) {
            setStep((s) => Math.min(s + 1, stepConfig.length - 1));
        }
    };

    const prev = () => setStep((s) => Math.max(s - 1, 0));

    const toggleItem = (key, value) => {
        setFormData((prev) => {
            const arr = new Set(prev[key]);
            arr.has(value) ? arr.delete(value) : arr.add(value);
            return { ...prev, [key]: Array.from(arr) };
        });
    };

    const handleFieldInput = (e) => {
        const value = e.target.value;
        setFormData({ ...formData, field_of_study: value });

        if (value.length > 2) {
            const filtered = commonFields.filter(field =>
                field.toLowerCase().includes(value.toLowerCase())
            );
            setFieldSuggestions(filtered);
        } else {
            setFieldSuggestions([]);
        }
    };

    const selectSuggestion = (field) => {
        setFormData({ ...formData, field_of_study: field });
        setFieldSuggestions([]);
    };

    const handleSubmit = async () => {
        console.log('StepsPage.jsx: handleSubmit called with user:', user, 'token:', token, 'formData:', formData);
        setIsSubmitting(true);

        if (!token) {
            console.error('StepsPage.jsx: No token available. Redirecting to login.');
            logout();
            navigate('/login', { replace: true });
            return;
        }

        try {
            await axios.post(`${API_URL}/api/preferences`, {
                user_id: user.id,
                ...formData
            }, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            onComplete?.(formData);
            navigate("/dashboard");

        } catch (error) {
            console.error('StepsPage.jsx: Failed to save preferences:', error);
            if (error.response?.status === 403) {
                console.error('StepsPage.jsx: Invalid or expired token. Redirecting to login.');
                logout();
                navigate('/login', { replace: true });
            } else {
                alert(`Failed to save preferences: ${error.response?.data?.message || error.message}`);
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    if (loading || !user) {
        return <div className="loading-container">Loading...</div>;
    }

    const currentStep = stepConfig[step];

    return (
        <div className="steps-container">
            {/* Pricing Modal */}
            {console.log('Main component render, showPricingModal:', showPricingModal)}
            {showPricingModal && (
                <div className="modal-overlay-welcome" onClick={togglePricingModal}>
                    <div className="pricing-modal" onClick={e => e.stopPropagation()}>
                        <button className="close-modal" onClick={togglePricingModal}>×</button>
                        <div className="modal-header">
                            <span className="premium-badge">✨ PREMIUM</span>
                            <h3>Premium Subscription</h3>
                            <p>Unlock all features and take your learning to the next level</p>
                        </div>

                        <div className="pricing-details">
                            <div className="price-tag">
                                <span className="currency">$</span>
                                <span className="amount">9</span>
                                <span className="period">/month</span>
                            </div>
                            <p className="price-note">Cancel anytime • 7-day free trial</p>
                        </div>

                        <div className="pricing-features">
                            <div className="pricing-feature">
                                <span className="feature-check">✓</span>
                                <p>Advanced analytics and progress tracking</p>
                            </div>
                            <div className="pricing-feature">
                                <span className="feature-check">✓</span>
                                <p>Unlimited study materials and resources</p>
                            </div>
                            <div className="pricing-feature">
                                <span className="feature-check">✓</span>
                                <p>AI-powered personalized study plans</p>
                            </div>
                            <div className="pricing-feature">
                                <span className="feature-check">✓</span>
                                <p>Priority support from our team</p>
                            </div>
                            <div className="pricing-feature">
                                <span className="feature-check">✓</span>
                                <p>Ad-free experience across all platforms</p>
                            </div>
                        </div>

                        <button className="subscribe-button">Subscribe Now</button>
                    </div>
                </div>
            )}

            <div className="progress-track">
                <div
                    className="progress-bar"
                    style={{ width: `${(step / (stepConfig.length - 1)) * 100}%` }}
                />
            </div>
            <div className="step-indicators">
                {stepConfig.map((stepItem, i) => (
                    <div
                        key={i}
                        className={`step-indicator ${i <= step ? "active" : ""}`}
                        onClick={() => setStep(i)}
                    >
                        <div className="step-number">{i + 1}</div>
                        <div className="step-label">{stepItem.name}</div>
                    </div>
                ))}
            </div>

            <div className="step-content">
                <div className="step-panel">
                    {currentStep.component({
                        formData,
                        setFormData,
                        toggleItem,
                        handleFieldInput,
                        fieldSuggestions,
                        selectSuggestion,
                        isSubmitting,
                        handleSubmit,
                        showPricingModal,
                        togglePricingModal
                    })}

                    <div className="nav-buttons">
                        {step > 0 && (
                            <button onClick={prev} className="nav-button prev">
                                <ArrowBackIcon />
                                Back
                            </button>
                        )}

                        {currentStep.isFinalStep ? (
                            <div></div> /* No navigation buttons in final step as they're replaced by premium buttons */
                        ) : (
                            <button
                                onClick={next}
                                disabled={!currentStep.canProceed(formData)}
                                className="nav-button primary"
                            >
                                {step === stepConfig.length - 2 ? "Finish" : "Next"}
                                <ArrowForwardIcon />
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StepsPage;