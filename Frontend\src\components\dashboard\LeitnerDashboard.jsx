import { useState, useEffect } from 'react';
import { useAuth } from '../../../authContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';
import axios from 'axios';
import '../../styles/leitner_dashboard_new.scss';
import { openLeitnerModal, updateLeitnerModal, closeLeitnerModal } from './LeitnerModalManager.jsx';

// Get API URL from environment or use default
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

const LeitnerDashboard = () => {
  const { user } = useAuth();
  const { darkMode } = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [leitnerStats, setLeitnerStats] = useState({
    totalCards: 0,
    box1Count: 0,
    box2Count: 0,
    box3Count: 0,
    dueToday: 0,
    masteryPercentage: 0
  });

  // Get auth token
  const getAuthToken = () => {
    return user?.token || localStorage.getItem('token');
  };

  // Fetch Leitner system stats
  useEffect(() => {
    const fetchLeitnerStats = async () => {
      const token = getAuthToken();
      if (!token) return;

      try {
        setLoading(true);
        console.log(`Fetching Leitner stats from: ${API_URL}/api/leitner/stats`);

        const response = await axios.get(`${API_URL}/api/leitner/stats`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Leitner stats response:', response.data);

        if (response.data.success) {
          const newStats = {
            totalCards: response.data.stats.totalCards || 0,
            box1Count: response.data.stats.box1Count || 0,
            box2Count: response.data.stats.box2Count || 0,
            box3Count: response.data.stats.box3Count || 0,
            dueToday: response.data.stats.dueToday || 0,
            masteryPercentage: response.data.stats.masteryPercentage || 0,
            untrackedCards: response.data.stats.untrackedCards || 0
          };
          console.log('🎯 [LEITNER] Updated stats:', newStats);
          setLeitnerStats(newStats);
        } else {
          setError(response.data.message || 'Failed to fetch Leitner stats');
        }
      } catch (error) {
        console.error('Error fetching Leitner stats:', error);

        // If we get a 404, it means the endpoint doesn't exist yet
        if (error.response && error.response.status === 404) {
          setError('API endpoint not found. Make sure the server is running with the latest code.');
        } else {
          setError('Failed to connect to the server. Check console for details.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchLeitnerStats();

    // Set up interval to refresh stats every 30 seconds
    const intervalId = setInterval(() => {
      fetchLeitnerStats();
    }, 30 * 1000);

    return () => clearInterval(intervalId);
  }, [user]);

  // Fetch cards due today
  const fetchDueTodayCards = async () => {
    const token = getAuthToken();
    if (!token) return;

    try {
      setLoading(true);
      console.log(`Fetching cards due today from: ${API_URL}/api/leitner/due-today`);

      const response = await axios.get(`${API_URL}/api/leitner/due-today`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Due today cards response:', response.data);

      if (response.data.success) {
        console.log('🎯 [LEITNER] Fetched cards for retry:', response.data.cards?.length || 0);
        // Cards will be handled by the modal manager
      } else {
        setError(response.data.message || 'Failed to fetch cards due today');
      }
    } catch (error) {
      console.error('Error fetching cards due today:', error);

      // If we get a 404, it means the endpoint doesn't exist yet
      if (error.response && error.response.status === 404) {
        setError('API endpoint not found. Make sure the server is running with the latest code.');
      } else {
        setError('Failed to connect to the server. Check console for details.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Open study modal
  const openStudyModal = async () => {
    console.log('🎯 [LEITNER] Study button clicked! Opening study modal...');
    console.log('🎯 [LEITNER] Current stats:', leitnerStats);
    console.log('🎯 [LEITNER] Cards due today:', leitnerStats.dueToday);

    try {
      // Show loading modal first
      openLeitnerModal({
        loading: true,
        error: null,
        dueTodayCards: [],
        currentCardIndex: 0,
        isFlipped: false,
        answered: false,
        sessionStats: { correct: 0, incorrect: 0 },
        onClose: () => closeLeitnerModal(),
        onFlip: flipCard,
        onAnswer: handleAnswer,
        onNext: nextCard,
        onRetry: fetchDueTodayCards
      });

      console.log('🎯 [LEITNER] Fetching cards due today...');

      // Fetch cards due today
      const token = getAuthToken();
      if (!token) return;

      const response = await axios.get(`${API_URL}/api/leitner/due-today`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Due today cards response:', response.data);

      if (response.data.success) {
        const cards = response.data.cards || [];
        console.log('🎯 [LEITNER] Fetched cards:', cards.length);

        // Update modal with cards
        updateLeitnerModal({
          loading: false,
          dueTodayCards: cards,
          currentCardIndex: 0,
          isFlipped: false,
          answered: false,
          sessionStats: { correct: 0, incorrect: 0 }
        });
      } else {
        updateLeitnerModal({
          loading: false,
          error: response.data.message || 'Failed to fetch cards due today'
        });
      }
    } catch (error) {
      console.error('🎯 [LEITNER] Error in openStudyModal:', error);
      updateLeitnerModal({
        loading: false,
        error: 'Failed to connect to the server. Check console for details.'
      });
    }
  };

  // Handle card flip
  const flipCard = () => {
    updateLeitnerModal({ isFlipped: true });
  };

  // Handle card answer
  const handleAnswer = async (correct) => {
    // This will be called from the modal context, so we need to get current state
    console.log('🎯 [LEITNER] Handle answer called with correct:', correct);

    const token = getAuthToken();
    if (!token) return;

    // Update modal to show answered state
    updateLeitnerModal({ answered: true });

    try {
      // For now, just mark as answered - the actual card update logic will be handled by the modal
      console.log('🎯 [LEITNER] Card answered:', correct ? 'correct' : 'incorrect');
    } catch (error) {
      console.error('🎯 [LEITNER] Error in handleAnswer:', error);
    }
  };

  // Move to next card
  const nextCard = () => {
    console.log('🎯 [LEITNER] Next card called');

    // This will be handled by the modal context
    updateLeitnerModal({
      currentCardIndex: 0, // Will be updated by modal logic
      isFlipped: false,
      answered: false
    });
  };





  return (
    <div className={`leitner-dashboard-card ${darkMode ? 'theme-dark' : ''}`}>
      <div className="widget-header">
        <h3 className="widget-title">Leitner System</h3>
        <div className="mastery-percentage">{Math.round(leitnerStats.masteryPercentage)}%</div>
      </div>

      {error && (
        <div className="error-message">
          <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2" />
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2" />
          </svg>
          <span>Connection error</span>
        </div>
      )}

      {loading && !error && leitnerStats.totalCards === 0 ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <span>Loading...</span>
        </div>
      ) : (
        <div className="leitner-content">
          <div className="mastery-overview">
            <div className="mastery-circle">
              <svg viewBox="0 0 120 120" className="progress-ring">
                <defs>
                  <linearGradient id="leitnerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#FF6B6B" />
                    <stop offset="50%" stopColor="#4ECDC4" />
                    <stop offset="100%" stopColor="#45B7D1" />
                  </linearGradient>
                </defs>

                {/* Background circle */}
                <circle
                  cx="60"
                  cy="60"
                  r="45"
                  fill="none"
                  stroke="rgba(200, 200, 200, 0.2)"
                  strokeWidth="8"
                />

                {/* Progress circle */}
                <circle
                  cx="60"
                  cy="60"
                  r="45"
                  fill="none"
                  stroke="url(#leitnerGradient)"
                  strokeWidth="8"
                  strokeLinecap="round"
                  strokeDasharray={`${(leitnerStats.masteryPercentage / 100) * 283} 283`}
                  transform="rotate(-90 60 60)"
                  className="progress-circle"
                />

                {/* Center text */}
                <text x="60" y="65" textAnchor="middle" className="progress-text">
                  {Math.round(leitnerStats.masteryPercentage)}%
                </text>
              </svg>
            </div>

            <div className="box-indicators">
              <div className="box-indicator">
                <div className="box-dot box-1">
                  <span className="box-count">{leitnerStats.box1Count}</span>
                </div>
                <span className="box-label">Box 1</span>
              </div>
              <div className="box-indicator">
                <div className="box-dot box-2">
                  <span className="box-count">{leitnerStats.box2Count}</span>
                </div>
                <span className="box-label">Box 2</span>
              </div>
              <div className="box-indicator">
                <div className="box-dot box-3">
                  <span className="box-count">{leitnerStats.box3Count}</span>
                </div>
                <span className="box-label">Box 3</span>
              </div>
            </div>
          </div>

          <div className="study-action">
            <div
              className={`study-button ${leitnerStats.dueToday === 0 ? 'disabled' : ''}`}
              onClick={() => {
                console.log('🎯 [LEITNER] Study button clicked!');
                console.log('🎯 [LEITNER] Due today:', leitnerStats.dueToday);
                console.log('🎯 [LEITNER] Button enabled:', leitnerStats.dueToday > 0);

                if (leitnerStats.dueToday > 0) {
                  console.log('🎯 [LEITNER] Calling openStudyModal...');
                  openStudyModal();
                } else {
                  console.log('🎯 [LEITNER] Button is disabled, no cards due');
                }
              }}
            >
              <div className="study-icon">
                <svg viewBox="0 0 24 24" width="20" height="20" fill="none">
                  <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" stroke="currentColor" strokeWidth="2" />
                </svg>
              </div>
              <div className="study-info">
                <span className="study-label">
                  {leitnerStats.dueToday > 0 ? 'Study Now' : 'No Cards Due'}
                </span>
                <span className="study-count">
                  {leitnerStats.dueToday > 0
                    ? `${leitnerStats.dueToday} cards`
                    : 'All caught up!'
                  }
                </span>
              </div>
              {leitnerStats.dueToday > 0 && (
                <div className="study-arrow">
                  <svg viewBox="0 0 24 24" width="16" height="16" fill="none">
                    <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default LeitnerDashboard;
