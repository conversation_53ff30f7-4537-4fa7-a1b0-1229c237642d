import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../authContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';
import axios from 'axios';
import '../../styles/leitner_dashboard.scss';
import LeitnerModal from './LeitnerModal.jsx';

// Material UI Icons
import SchoolIcon from '@mui/icons-material/School';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { motion, AnimatePresence } from 'framer-motion';

// Get API URL from environment or use default
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

const LeitnerDashboard = () => {
  const { user } = useAuth();
  const { darkMode } = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [leitnerStats, setLeitnerStats] = useState({
    totalCards: 0,
    box1Count: 0,
    box2Count: 0,
    box3Count: 0,
    dueToday: 0,
    masteryPercentage: 0
  });
  const [showModal, setShowModal] = useState(false);
  const [dueTodayCards, setDueTodayCards] = useState([]);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [answered, setAnswered] = useState(false);
  const [sessionStats, setSessionStats] = useState({
    correct: 0,
    incorrect: 0
  });
  // Get auth token
  const getAuthToken = () => {
    return user?.token || localStorage.getItem('token');
  };

  // Fetch Leitner system stats
  useEffect(() => {
    const fetchLeitnerStats = async () => {
      const token = getAuthToken();
      if (!token) return;

      try {
        setLoading(true);
        console.log(`Fetching Leitner stats from: ${API_URL}/api/leitner/stats`);

        const response = await axios.get(`${API_URL}/api/leitner/stats`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Leitner stats response:', response.data);

        if (response.data.success) {
          setLeitnerStats({
            totalCards: response.data.stats.totalCards || 0,
            box1Count: response.data.stats.box1Count || 0,
            box2Count: response.data.stats.box2Count || 0,
            box3Count: response.data.stats.box3Count || 0,
            dueToday: response.data.stats.dueToday || 0,
            masteryPercentage: response.data.stats.masteryPercentage || 0,
            untrackedCards: response.data.stats.untrackedCards || 0
          });
        } else {
          setError(response.data.message || 'Failed to fetch Leitner stats');
        }
      } catch (error) {
        console.error('Error fetching Leitner stats:', error);

        // If we get a 404, it means the endpoint doesn't exist yet
        if (error.response && error.response.status === 404) {
          setError('API endpoint not found. Make sure the server is running with the latest code.');
        } else {
          setError('Failed to connect to the server. Check console for details.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchLeitnerStats();

    // Set up interval to refresh stats every 30 seconds
    const intervalId = setInterval(() => {
      fetchLeitnerStats();
    }, 30 * 1000);

    return () => clearInterval(intervalId);
  }, [user]);

  // Fetch cards due today
  const fetchDueTodayCards = async () => {
    const token = getAuthToken();
    if (!token) return;

    try {
      setLoading(true);
      console.log(`Fetching cards due today from: ${API_URL}/api/leitner/due-today`);

      const response = await axios.get(`${API_URL}/api/leitner/due-today`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Due today cards response:', response.data);

      if (response.data.success) {
        setDueTodayCards(response.data.cards || []);
      } else {
        setError(response.data.message || 'Failed to fetch cards due today');
      }
    } catch (error) {
      console.error('Error fetching cards due today:', error);

      // If we get a 404, it means the endpoint doesn't exist yet
      if (error.response && error.response.status === 404) {
        setError('API endpoint not found. Make sure the server is running with the latest code.');
      } else {
        setError('Failed to connect to the server. Check console for details.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Open study modal
  const openStudyModal = async () => {
    console.log('Opening study modal...');
    try {
      // First set loading to true
      setLoading(true);

      // Fetch cards due today
      await fetchDueTodayCards();

      // Reset state for new session
      setCurrentCardIndex(0);
      setIsFlipped(false);
      setAnswered(false);
      setSessionStats({
        correct: 0,
        incorrect: 0
      });

      // Set loading to false before showing modal
      setLoading(false);

      // Show the modal
      setShowModal(true);
    } catch (error) {
      console.error('Error in openStudyModal:', error);
      setLoading(false);
    }
  };

  // Handle card flip
  const flipCard = () => {
    setIsFlipped(!isFlipped);
  };

  // Handle card answer
  const handleAnswer = async (correct) => {
    if (!dueTodayCards.length || answered) return;

    const currentCard = dueTodayCards[currentCardIndex];
    const token = getAuthToken();
    if (!token) return;

    setAnswered(true);
    setSessionStats(prev => ({
      ...prev,
      correct: correct ? prev.correct + 1 : prev.correct,
      incorrect: !correct ? prev.incorrect + 1 : prev.incorrect
    }));

    try {
      console.log(`Updating card ${currentCard.id} as ${correct ? 'correct' : 'incorrect'}`);

      // Update the local state to reflect the change immediately for better UX
      const updatedCards = [...dueTodayCards];
      const cardIndex = updatedCards.findIndex(card => card.id === currentCard.id);

      if (cardIndex !== -1) {
        updatedCards[cardIndex] = {
          ...updatedCards[cardIndex],
          box_level: correct
            ? Math.min(updatedCards[cardIndex].box_level + 1, 3)
            : 1,
          last_reviewed: new Date().toISOString()
        };

        setDueTodayCards(updatedCards);
      }

      // Send the update to the server
      const response = await axios.post(`${API_URL}/api/leitner/update`, {
        flashcardId: currentCard.id,
        correct
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Update response:', response.data);

      if (!response.data.success) {
        console.error('Server reported error:', response.data.message);
      }
    } catch (error) {
      console.error('Error updating card:', error);

      // If we get a 404, it means the endpoint doesn't exist yet
      if (error.response && error.response.status === 404) {
        console.error('API endpoint not found. Make sure the server is running with the latest code.');
      }
    }
  };

  // Move to next card
  const nextCard = () => {
    if (currentCardIndex < dueTodayCards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1);
      setIsFlipped(false);
      setAnswered(false);
    } else {
      // End of session
      setShowModal(false);

      // Fetch updated stats after completing a session
      const fetchUpdatedStats = async () => {
        const token = getAuthToken();
        if (!token) return;

        try {
          const response = await axios.get(`${API_URL}/api/leitner/stats`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.data.success) {
            setLeitnerStats({
              totalCards: response.data.stats.totalCards || 0,
              box1Count: response.data.stats.box1Count || 0,
              box2Count: response.data.stats.box2Count || 0,
              box3Count: response.data.stats.box3Count || 0,
              dueToday: response.data.stats.dueToday || 0,
              masteryPercentage: response.data.stats.masteryPercentage || 0,
              untrackedCards: response.data.stats.untrackedCards || 0
            });
          }
        } catch (error) {
          console.error('Error fetching updated stats:', error);
        }
      };

      fetchUpdatedStats();
    }
  };

  // Get box icon
  const getBoxIcon = (boxNum) => {
    switch (boxNum) {
      case 1: return <LooksOneIcon />;
      case 2: return <LooksTwoIcon />;
      case 3: return <Looks3Icon />;
      default: return <LooksOneIcon />;
    }
  };

  // Calculate box distribution percentages
  const totalCards = leitnerStats.totalCards || 0;
  const box1Percentage = totalCards > 0 ? (leitnerStats.box1Count / totalCards) * 100 : 0;
  const box2Percentage = totalCards > 0 ? (leitnerStats.box2Count / totalCards) * 100 : 0;
  const box3Percentage = totalCards > 0 ? (leitnerStats.box3Count / totalCards) * 100 : 0;

  return (
    <div className={`leitner-dashboard-card ${darkMode ? 'theme-dark' : ''}`}>
      <div className="card-header">
        <h2 className="card-title">Leitner System</h2>
        {loading && <div className="loading-indicator"></div>}
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {loading && !error && leitnerStats.totalCards === 0 ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading Leitner system data...</p>
        </div>
      ) : (
        <>
          <div className="actual-content">
            {/* Overall Tracker Section */}
            <div className="leitner-tracker-section">

              <div className="leitner-stats">
                <div className="mastery-progress">
                  <div className="progress-label">
                    <span>Mastery Progress</span>
                    <span className="percentage">{Math.round(leitnerStats.masteryPercentage)}%</span>
                  </div>
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{ width: `${leitnerStats.masteryPercentage}%` }}
                    ></div>
                  </div>
                </div>

                {/*<div className="total-cards-info">
                <span className="total-label">Total Flashcards:</span>
                <span className="total-count">{leitnerStats.totalCards}</span>
                {leitnerStats.untrackedCards > 0 && (
                  <span className="untracked-info">
                    ({leitnerStats.untrackedCards} untracked)
                  </span>
                )}
              </div>*/}

                <div className="box-distribution">
                  <div className="box-row">
                    <div className="box-icon box-1">{getBoxIcon(1)}</div>
                    <div className="box-bar-container">
                      <div className="box-bar box-1-bar" style={{ width: `${box1Percentage}%` }}></div>
                    </div>
                    <div className="box-count">
                      {leitnerStats.box1Count}
                      {/*{leitnerStats.untrackedCards > 0 && (
                      <span className="untracked-count">
                        ({leitnerStats.untrackedCards} new)
                      </span>
                    )}*/}
                    </div>
                  </div>

                  <div className="box-row">
                    <div className="box-icon box-2">{getBoxIcon(2)}</div>
                    <div className="box-bar-container">
                      <div className="box-bar box-2-bar" style={{ width: `${box2Percentage}%` }}></div>
                    </div>
                    <div className="box-count">{leitnerStats.box2Count}</div>
                  </div>

                  <div className="box-row">
                    <div className="box-icon box-3">{getBoxIcon(3)}</div>
                    <div className="box-bar-container">
                      <div className="box-bar box-3-bar" style={{ width: `${box3Percentage}%` }}></div>
                    </div>
                    <div className="box-count">{leitnerStats.box3Count}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Daily Reminder Section */}
            <div className="leitner-reminder-section">
              <h3 className="section-title">Daily Review Reminder</h3>
              <div
                className={`due-today-reminder ${leitnerStats.dueToday === 0 ? 'no-cards' : 'has-cards'}`}
                onClick={leitnerStats.dueToday > 0 ? () => openStudyModal() : null}
                style={leitnerStats.dueToday > 0 ? { cursor: 'pointer' } : {}}
              >
                <NotificationsActiveIcon className="reminder-icon" />
                <div className="reminder-text">
                  <h3>Cards Due Today</h3>
                  <p>
                    {leitnerStats.dueToday > 0
                      ? `${leitnerStats.dueToday} cards need review`
                      : "No cards due for review today"}
                  </p>
                </div>
                {leitnerStats.dueToday > 0 && <ArrowForwardIcon className="arrow-icon" />}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Study Modal */}
      <LeitnerModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        loading={loading}
        error={error}
        dueTodayCards={dueTodayCards}
        currentCardIndex={currentCardIndex}
        isFlipped={isFlipped}
        answered={answered}
        sessionStats={sessionStats}
        onFlip={flipCard}
        onAnswer={handleAnswer}
        onNext={nextCard}
        onRetry={fetchDueTodayCards}
      />
    </div>
  );
};

export default LeitnerDashboard;
