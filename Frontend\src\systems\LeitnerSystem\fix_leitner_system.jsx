/**
 * This file contains the modified saveBoxes function for the LeitnerSystem component
 * to fix the 500 error when saving flashcards.
 * 
 * The issue is the same as in FlashCards.jsx - the component is trying to save all flashcards
 * without checking if they've been modified, which causes a unique constraint violation.
 * 
 * Replace the saveBoxes function in LeitnerSystem.jsx with this version.
 */

// Track original cards loaded from the database to avoid re-saving them
const [originalCards, setOriginalCards] = useState([]);

// Modified loadCards function to track original cards
const loadCards = async () => {
  if (!chapterId) {
    setError('No chapter ID provided');
    setLoading(false);
    return;
  }

  try {
    setLoading(true);
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication token not found');
    }

    // Fetch flashcards for this chapter
    const response = await axios.get(`http://localhost:5001/api/chapters/${chapterId}/flashcards`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    if (response.data.success) {
      const fetchedCards = response.data.flashcards || [];
      
      // Store original cards to avoid re-saving them
      setOriginalCards(fetchedCards.map(card => ({
        ...card,
        isModified: false // Mark cards from database as not modified
      })));
      
      setCards(fetchedCards);

      // Distribute cards into Leitner boxes based on box_level
      const newBoxes = {
        1: [],
        2: [],
        3: []
      };

      // Track the highest streak among all cards
      let highestStreak = 0;

      fetchedCards.forEach(card => {
        // Convert old 5-box system to new 3-box system
        let newBoxLevel = 1;
        const oldBoxLevel = card.box_level || 1;

        if (oldBoxLevel <= 1) {
          newBoxLevel = 1; // New & difficult cards
        } else if (oldBoxLevel <= 3) {
          newBoxLevel = 2; // Learning cards
        } else {
          newBoxLevel = 3; // Mastered cards
        }

        // Update the card's box_level
        newBoxes[newBoxLevel].push({ ...card, box_level: newBoxLevel });

        // Update highest streak
        if (card.leitner_streak && card.leitner_streak > highestStreak) {
          highestStreak = card.leitner_streak;
        }
      });

      setBoxes(newBoxes);

      // Set the streak based on the highest streak found
      if (highestStreak > 0) {
        setStreak(highestStreak);
      }

      setError(null);
    } else {
      throw new Error(response.data.message || 'Failed to load flashcards');
    }
  } catch (error) {
    console.error('Failed to load flashcards for Leitner system:', error);
    setError('Failed to load flashcards. Please try again later.');
  } finally {
    setLoading(false);
  }
};

// Modified saveBoxes function to only save modified cards
const saveBoxes = async () => {
  if (!chapterId) return;

  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication token not found');
    }

    // Flatten all boxes into a single array of cards
    const allCards = Object.values(boxes).flat();
    
    // Step 1: Identify only new or modified cards that need to be saved
    const cardsToUpdate = [];
    
    for (const card of allCards) {
      // Skip cards that were originally loaded from the database and haven't been modified
      const originalCard = originalCards.find(oc => oc.id === card.id);
      
      // Include card if:
      // 1. It's a new card (not in originalCards)
      // 2. It has been modified (different from original)
      // 3. It has the isModified flag set
      if (!originalCard || 
          card.isModified || 
          card.box_level !== originalCard.box_level ||
          card.leitner_streak !== originalCard.leitner_streak ||
          card.next_review_date !== originalCard.next_review_date) {
        
        // Mark as modified so we know to save it
        card.isModified = true;
        cardsToUpdate.push(card);
      }
    }
    
    // If no cards need to be updated, return success
    if (cardsToUpdate.length === 0) {
      console.log("No cards need to be saved - all cards are unchanged from database");
      return;
    }
    
    console.log(`Saving ${cardsToUpdate.length} modified flashcards to server...`);
    setLoading(true);

    // Process cards in smaller batches to avoid payload size issues
    const BATCH_SIZE = 20; // Process 20 cards at a time
    const batches = [];
    
    for (let i = 0; i < cardsToUpdate.length; i += BATCH_SIZE) {
      batches.push(cardsToUpdate.slice(i, i + BATCH_SIZE));
    }
    
    console.log(`Processing ${cardsToUpdate.length} cards in ${batches.length} batches`);
    
    // Process each batch
    let successCount = 0;
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} cards)`);
      
      try {
        const response = await axios.post(`http://localhost:5001/api/chapters/${chapterId}/flashcards`, {
          flashcards: batch
        }, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.data.success) {
          successCount += batch.length;
          console.log(`Successfully saved batch ${i + 1} (${batch.length} cards)`);
        } else {
          console.error(`Failed to save batch ${i + 1}:`, response.data.message);
        }
      } catch (batchError) {
        console.error(`Error saving batch ${i + 1}:`, batchError);
        // Continue with the next batch even if this one fails
      }
    }
    
    // Update originalCards to include the newly saved cards
    if (successCount > 0) {
      const updatedOriginalCards = [...originalCards];
      
      // Add any new cards to originalCards
      for (const card of cardsToUpdate) {
        const existingIndex = updatedOriginalCards.findIndex(c => c.id === card.id);
        if (existingIndex >= 0) {
          // Update existing card
          updatedOriginalCards[existingIndex] = {...card, isModified: false};
        } else {
          // Add new card
          updatedOriginalCards.push({...card, isModified: false});
        }
      }
      
      setOriginalCards(updatedOriginalCards);
      
      console.log(`Leitner boxes saved successfully. Saved ${successCount} flashcards.`);
      
      // No need to reload cards since we've already updated our state
      setLoading(false);
    } else {
      throw new Error(`Failed to save any of the ${cardsToUpdate.length} cards`);
    }
  } catch (error) {
    console.error('Failed to save Leitner boxes:', error);
    setError('Failed to save changes. Please try again later.');
    setLoading(false);
  }
};
