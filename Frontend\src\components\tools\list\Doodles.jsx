import { useState, useRef, useEffect, useCallback } from 'react';
import {
    Brush as BrushIcon,
    Delete as EraserIcon,
    Undo as UndoIcon,
    Redo as RedoIcon,
    Save as SaveIcon,
    Download as DownloadIcon,
    PhotoLibrary as PhotoLibraryIcon,
    ZoomIn as ZoomInIcon,
    ZoomOut as ZoomOutIcon,
    GridOn as GridOnIcon,
    <PERSON>lette as PaletteIcon,
    TextFields as TextFieldsIcon,
    CropSquare as CropSquareIcon,
    RadioButtonUnchecked as RadioButtonUncheckedIcon,
    Timeline as TimelineIcon,
    ArrowForward as ArrowForwardIcon,
    Close as CloseIcon,
    Settings as SettingsIcon,
    Visibility as VisibilityIcon,
    Layers as LayersIcon,
    AutoFixHigh as AutoFixHighIcon
} from '@mui/icons-material';
import '../../../styles/advanced-doodle.scss';

const Doodles = ({ tool, subject, initialDrawing, onSave }) => {
    const canvasRef = useRef(null);
    const overlayCanvasRef = useRef(null);
    const [isDarkMode, setIsDarkMode] = useState(false);

    // Drawing state
    const [isDrawing, setIsDrawing] = useState(false);
    const [activeTool, setActiveTool] = useState('brush');
    const [color, setColor] = useState('#007AFF');
    const [brushSize, setBrushSize] = useState(8);
    const [opacity, setOpacity] = useState(100);
    const [brushType, setBrushType] = useState('round');

    // Advanced features
    const [zoom, setZoom] = useState(1);
    const [panX, setPanX] = useState(0);
    const [panY, setPanY] = useState(0);
    const [showGrid, setShowGrid] = useState(false);
    const [showRulers, setShowRulers] = useState(false);
    const [layers, setLayers] = useState([{ id: 1, name: 'Layer 1', visible: true, opacity: 100 }]);
    const [activeLayer, setActiveLayer] = useState(1);

    // UI state
    const [showToolbar, setShowToolbar] = useState(true);
    const [showColorPalette, setShowColorPalette] = useState(false);
    const [showLayerPanel, setShowLayerPanel] = useState(false);
    const [showSettings, setShowSettings] = useState(false);
    const [notes, setNotes] = useState('');
    const [savedDoodles, setSavedDoodles] = useState([]);
    const [showGallery, setShowGallery] = useState(false);

    // History for undo/redo
    const [history, setHistory] = useState([]);
    const [historyIndex, setHistoryIndex] = useState(-1);

    // Shape drawing
    const [startPoint, setStartPoint] = useState(null);
    const [isShapeMode, setIsShapeMode] = useState(false);

    // macOS system colors palette
    const systemColors = [
        '#007AFF', '#28CD41', '#FF3B30', '#FF9500',
        '#FFCC00', '#8E8E93', '#E5E5EA', '#636366', '#fbb034'
    ];

    // Theme detection
    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        checkDarkMode();
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    // Initialize canvas
    useEffect(() => {
        const canvas = canvasRef.current;
        const overlayCanvas = overlayCanvasRef.current;

        if (!canvas || !overlayCanvas) return;

        const ctx = canvas.getContext('2d');
        const overlayCtx = overlayCanvas.getContext('2d');

        // Set canvas size
        const resizeCanvas = () => {
            const rect = canvas.parentElement.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
            overlayCanvas.width = rect.width;
            overlayCanvas.height = rect.height;

            // Set high DPI support
            const dpr = window.devicePixelRatio || 1;
            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;
            overlayCanvas.width = rect.width * dpr;
            overlayCanvas.height = rect.height * dpr;

            ctx.scale(dpr, dpr);
            overlayCtx.scale(dpr, dpr);

            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
            overlayCanvas.style.width = rect.width + 'px';
            overlayCanvas.style.height = rect.height + 'px';

            // Redraw if we have initial drawing
            if (initialDrawing) {
                const img = new Image();
                img.onload = () => {
                    ctx.drawImage(img, 0, 0);
                    saveToHistory();
                };
                img.src = initialDrawing;
            } else {
                saveToHistory();
            }
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        return () => window.removeEventListener('resize', resizeCanvas);
    }, [initialDrawing]);

    // Utility functions
    const saveToHistory = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const dataUrl = canvas.toDataURL();
        setHistory(prev => {
            const newHistory = prev.slice(0, historyIndex + 1);
            newHistory.push(dataUrl);
            return newHistory.slice(-20); // Keep only last 20 states
        });
        setHistoryIndex(prev => Math.min(prev + 1, 19));
    }, [historyIndex]);

    const undo = useCallback(() => {
        if (historyIndex > 0) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.onload = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0);
            };
            img.src = history[historyIndex - 1];
            setHistoryIndex(prev => prev - 1);
        }
    }, [history, historyIndex]);

    const redo = useCallback(() => {
        if (historyIndex < history.length - 1) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.onload = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0);
            };
            img.src = history[historyIndex + 1];
            setHistoryIndex(prev => prev + 1);
        }
    }, [history, historyIndex]);

    // Update drawing styles when they change
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        ctx.strokeStyle = color;
        ctx.lineWidth = brushSize;
        ctx.lineCap = brushType === 'round' ? 'round' : brushType === 'square' ? 'square' : 'butt';
        ctx.lineJoin = brushType === 'round' ? 'round' : 'miter';
        ctx.globalAlpha = opacity / 100;
    }, [color, brushSize, brushType, opacity]);

    // Drawing functions
    const getPosition = useCallback((e) => {
        const canvas = canvasRef.current;
        const rect = canvas.getBoundingClientRect();
        const clientX = e.clientX || (e.touches && e.touches[0]?.clientX) || 0;
        const clientY = e.clientY || (e.touches && e.touches[0]?.clientY) || 0;

        return {
            x: (clientX - rect.left) / zoom + panX,
            y: (clientY - rect.top) / zoom + panY
        };
    }, [zoom, panX, panY]);

    const startDrawing = useCallback((e) => {
        e.preventDefault();
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        const pos = getPosition(e);

        if (activeTool === 'brush' || activeTool === 'eraser') {
            ctx.beginPath();
            ctx.moveTo(pos.x, pos.y);
            setIsDrawing(true);
        } else if (['rectangle', 'circle', 'line', 'arrow'].includes(activeTool)) {
            setStartPoint(pos);
            setIsShapeMode(true);
        }
    }, [activeTool, getPosition]);

    const draw = useCallback((e) => {
        e.preventDefault();
        if (!isDrawing && !isShapeMode) return;

        const canvas = canvasRef.current;
        const overlayCanvas = overlayCanvasRef.current;
        const ctx = canvas.getContext('2d');
        const overlayCtx = overlayCanvas.getContext('2d');
        const pos = getPosition(e);

        if (isDrawing) {
            if (activeTool === 'brush') {
                ctx.lineTo(pos.x, pos.y);
                ctx.stroke();
            } else if (activeTool === 'eraser') {
                ctx.save();
                ctx.globalCompositeOperation = 'destination-out';
                ctx.beginPath();
                ctx.arc(pos.x, pos.y, brushSize / 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        } else if (isShapeMode && startPoint) {
            // Clear overlay and draw preview
            overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
            overlayCtx.strokeStyle = color;
            overlayCtx.lineWidth = brushSize;
            overlayCtx.globalAlpha = opacity / 100;
            overlayCtx.setLineDash([5, 5]);

            drawShape(overlayCtx, activeTool, startPoint, pos);
        }
    }, [isDrawing, isShapeMode, activeTool, startPoint, getPosition, color, brushSize, opacity]);

    const stopDrawing = useCallback((e) => {
        if (isDrawing) {
            setIsDrawing(false);
            saveToHistory();
        } else if (isShapeMode && startPoint) {
            const canvas = canvasRef.current;
            const overlayCanvas = overlayCanvasRef.current;
            const ctx = canvas.getContext('2d');
            const overlayCtx = overlayCanvas.getContext('2d');
            const pos = getPosition(e);

            // Draw final shape on main canvas
            ctx.strokeStyle = color;
            ctx.lineWidth = brushSize;
            ctx.globalAlpha = opacity / 100;
            ctx.setLineDash([]);

            drawShape(ctx, activeTool, startPoint, pos);

            // Clear overlay
            overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

            setIsShapeMode(false);
            setStartPoint(null);
            saveToHistory();
        }
    }, [isDrawing, isShapeMode, startPoint, getPosition, color, brushSize, opacity, activeTool, saveToHistory]);

    const drawShape = useCallback((ctx, tool, start, end) => {
        ctx.beginPath();

        switch (tool) {
            case 'rectangle':
                const width = end.x - start.x;
                const height = end.y - start.y;
                ctx.rect(start.x, start.y, width, height);
                break;
            case 'circle':
                const radius = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
                ctx.arc(start.x, start.y, radius, 0, Math.PI * 2);
                break;
            case 'line':
                ctx.moveTo(start.x, start.y);
                ctx.lineTo(end.x, end.y);
                break;
            case 'arrow':
                drawArrow(ctx, start.x, start.y, end.x, end.y);
                break;
        }

        ctx.stroke();
    }, []);

    const drawArrow = useCallback((ctx, fromX, fromY, toX, toY) => {
        const headLength = 15;
        const angle = Math.atan2(toY - fromY, toX - fromX);

        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
        ctx.moveTo(toX, toY);
        ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
    }, []);

    // Canvas utility functions
    const clearCanvas = useCallback(() => {
        const canvas = canvasRef.current;
        const overlayCanvas = overlayCanvasRef.current;
        const ctx = canvas.getContext('2d');
        const overlayCtx = overlayCanvas.getContext('2d');

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
        saveToHistory();
    }, [saveToHistory]);

    const zoomIn = useCallback(() => {
        setZoom(prev => Math.min(prev * 1.2, 5));
    }, []);

    const zoomOut = useCallback(() => {
        setZoom(prev => Math.max(prev / 1.2, 0.1));
    }, []);

    const resetZoom = useCallback(() => {
        setZoom(1);
        setPanX(0);
        setPanY(0);
    }, []);

    const saveDoodle = useCallback(() => {
        const canvas = canvasRef.current;
        const dataUrl = canvas.toDataURL('image/png');

        const newDoodle = {
            id: Date.now(),
            image: dataUrl,
            date: new Date().toLocaleString(),
            notes: notes,
            settings: { color, brushSize, brushType, opacity, zoom, panX, panY }
        };

        setSavedDoodles(prev => [...prev, newDoodle]);

        if (onSave) {
            onSave(newDoodle);
        }
    }, [notes, color, brushSize, brushType, opacity, zoom, panX, panY, onSave]);

    const loadDoodle = useCallback((doodle) => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        clearCanvas();

        const img = new Image();
        img.onload = () => {
            ctx.drawImage(img, 0, 0);
            saveToHistory();
        };
        img.src = doodle.image;

        setNotes(doodle.notes);
        setColor(doodle.settings.color);
        setBrushSize(doodle.settings.brushSize);
        setBrushType(doodle.settings.brushType);
        setOpacity(doodle.settings.opacity);

        if (doodle.settings.zoom) setZoom(doodle.settings.zoom);
        if (doodle.settings.panX) setPanX(doodle.settings.panX);
        if (doodle.settings.panY) setPanY(doodle.settings.panY);
    }, [clearCanvas, saveToHistory]);

    const exportAsPNG = useCallback(() => {
        const canvas = canvasRef.current;
        const link = document.createElement('a');
        link.download = `doodle-${subject}-${new Date().toISOString().slice(0, 10)}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
    }, [subject]);

    const exportAsSVG = useCallback(() => {
        // SVG export functionality would be implemented here
        console.log('SVG export not yet implemented');
    }, []);

    const exportAsPDF = useCallback(() => {
        // PDF export functionality would be implemented here
        console.log('PDF export not yet implemented');
    }, []);

    return (
        <div className={`advanced-doodle-app ${isDarkMode ? 'theme-dark' : ''}`}>
            {/* Advanced Floating Toolbar */}
            <div className={`floating-toolbar ${showToolbar ? 'visible' : 'hidden'}`}>
                <div className="toolbar-section">
                    <button
                        className={`tool-btn ${activeTool === 'brush' ? 'active' : ''}`}
                        onClick={() => setActiveTool('brush')}
                        title="Brush Tool"
                    >
                        <BrushIcon />
                    </button>
                    <button
                        className={`tool-btn ${activeTool === 'eraser' ? 'active' : ''}`}
                        onClick={() => setActiveTool('eraser')}
                        title="Eraser Tool"
                    >
                        <EraserIcon />
                    </button>
                    <button
                        className={`tool-btn ${activeTool === 'rectangle' ? 'active' : ''}`}
                        onClick={() => setActiveTool('rectangle')}
                        title="Rectangle Tool"
                    >
                        <CropSquareIcon />
                    </button>
                    <button
                        className={`tool-btn ${activeTool === 'circle' ? 'active' : ''}`}
                        onClick={() => setActiveTool('circle')}
                        title="Circle Tool"
                    >
                        <RadioButtonUncheckedIcon />
                    </button>
                    <button
                        className={`tool-btn ${activeTool === 'line' ? 'active' : ''}`}
                        onClick={() => setActiveTool('line')}
                        title="Line Tool"
                    >
                        <TimelineIcon />
                    </button>
                    <button
                        className={`tool-btn ${activeTool === 'arrow' ? 'active' : ''}`}
                        onClick={() => setActiveTool('arrow')}
                        title="Arrow Tool"
                    >
                        <ArrowForwardIcon />
                    </button>
                    <button
                        className={`tool-btn ${activeTool === 'text' ? 'active' : ''}`}
                        onClick={() => setActiveTool('text')}
                        title="Text Tool"
                    >
                        <TextFieldsIcon />
                    </button>
                </div>

                <div className="toolbar-divider"></div>

                <div className="toolbar-section">
                    <button
                        className="tool-btn"
                        onClick={undo}
                        disabled={historyIndex <= 0}
                        title="Undo"
                    >
                        <UndoIcon />
                    </button>
                    <button
                        className="tool-btn"
                        onClick={redo}
                        disabled={historyIndex >= history.length - 1}
                        title="Redo"
                    >
                        <RedoIcon />
                    </button>
                </div>

                <div className="toolbar-divider"></div>

                <div className="toolbar-section">
                    <button
                        className="tool-btn"
                        onClick={zoomIn}
                        title="Zoom In"
                    >
                        <ZoomInIcon />
                    </button>
                    <button
                        className="tool-btn"
                        onClick={zoomOut}
                        title="Zoom Out"
                    >
                        <ZoomOutIcon />
                    </button>
                    <button
                        className="tool-btn"
                        onClick={resetZoom}
                        title="Reset Zoom"
                    >
                        <AutoFixHighIcon />
                    </button>
                    <button
                        className={`tool-btn ${showGrid ? 'active' : ''}`}
                        onClick={() => setShowGrid(!showGrid)}
                        title="Toggle Grid"
                    >
                        <GridOnIcon />
                    </button>
                </div>

                <div className="toolbar-divider"></div>

                <div className="toolbar-section">
                    <button
                        className={`tool-btn ${showColorPalette ? 'active' : ''}`}
                        onClick={() => setShowColorPalette(!showColorPalette)}
                        title="Color Palette"
                    >
                        <PaletteIcon />
                    </button>
                    <button
                        className={`tool-btn ${showLayerPanel ? 'active' : ''}`}
                        onClick={() => setShowLayerPanel(!showLayerPanel)}
                        title="Layers"
                    >
                        <LayersIcon />
                    </button>
                    <button
                        className={`tool-btn ${showSettings ? 'active' : ''}`}
                        onClick={() => setShowSettings(!showSettings)}
                        title="Settings"
                    >
                        <SettingsIcon />
                    </button>
                </div>

                <div className="toolbar-divider"></div>

                <div className="toolbar-section">
                    <button
                        className="tool-btn"
                        onClick={saveDoodle}
                        title="Save Doodle"
                    >
                        <SaveIcon />
                    </button>
                    <button
                        className="tool-btn"
                        onClick={exportAsPNG}
                        title="Export as PNG"
                    >
                        <DownloadIcon />
                    </button>
                    <button
                        className={`tool-btn ${showGallery ? 'active' : ''}`}
                        onClick={() => setShowGallery(!showGallery)}
                        title="Gallery"
                    >
                        <PhotoLibraryIcon />
                    </button>
                </div>
            </div>

            {/* Header */}
            <header className="doodle-header">
                <div className="header-content">
                    <h1 className="canvas-title">Creative Canvas</h1>
                    <span className="subject-badge">{subject}</span>
                </div>
                <div className="header-controls">
                    <span className="zoom-indicator">{Math.round(zoom * 100)}%</span>
                    <button
                        className="toolbar-toggle"
                        onClick={() => setShowToolbar(!showToolbar)}
                        title="Toggle Toolbar"
                    >
                        <VisibilityIcon />
                    </button>
                </div>
            </header>

            {/* Main Canvas Area */}
            <div className="canvas-container">
                {/* Color Palette Panel */}
                {showColorPalette && (
                    <div className="floating-panel color-palette-panel">
                        <div className="panel-header">
                            <h3>Colors</h3>
                            <button
                                className="close-btn"
                                onClick={() => setShowColorPalette(false)}
                            >
                                <CloseIcon />
                            </button>
                        </div>
                        <div className="panel-content">
                            <div className="current-color">
                                <div
                                    className="color-preview"
                                    style={{ backgroundColor: color }}
                                ></div>
                                <span className="color-value">{color}</span>
                            </div>

                            <div className="system-colors">
                                <h4>System Colors</h4>
                                <div className="color-grid">
                                    {systemColors.map((sysColor, index) => (
                                        <button
                                            key={index}
                                            className={`color-swatch ${color === sysColor ? 'active' : ''}`}
                                            style={{ backgroundColor: sysColor }}
                                            onClick={() => setColor(sysColor)}
                                            title={sysColor}
                                        />
                                    ))}
                                </div>
                            </div>

                            <div className="custom-color">
                                <h4>Custom Color</h4>
                                <input
                                    type="color"
                                    value={color}
                                    onChange={(e) => setColor(e.target.value)}
                                    className="color-picker"
                                />
                            </div>
                        </div>
                    </div>
                )}

                {/* Settings Panel */}
                {showSettings && (
                    <div className="floating-panel settings-panel">
                        <div className="panel-header">
                            <h3>Brush Settings</h3>
                            <button
                                className="close-btn"
                                onClick={() => setShowSettings(false)}
                            >
                                <CloseIcon />
                            </button>
                        </div>
                        <div className="panel-content">
                            <div className="setting-group">
                                <label>Size</label>
                                <div className="slider-container">
                                    <input
                                        type="range"
                                        min="1"
                                        max="100"
                                        value={brushSize}
                                        onChange={(e) => setBrushSize(Number(e.target.value))}
                                        className="slider"
                                    />
                                    <span className="value">{brushSize}px</span>
                                </div>
                                <div className="brush-preview">
                                    <div
                                        className="preview-dot"
                                        style={{
                                            width: `${brushSize}px`,
                                            height: `${brushSize}px`,
                                            backgroundColor: color,
                                            opacity: opacity / 100
                                        }}
                                    />
                                </div>
                            </div>

                            <div className="setting-group">
                                <label>Opacity</label>
                                <div className="slider-container">
                                    <input
                                        type="range"
                                        min="10"
                                        max="100"
                                        value={opacity}
                                        onChange={(e) => setOpacity(Number(e.target.value))}
                                        className="slider"
                                    />
                                    <span className="value">{opacity}%</span>
                                </div>
                            </div>

                            <div className="setting-group">
                                <label>Brush Type</label>
                                <div className="brush-types">
                                    {['round', 'square', 'butt'].map(type => (
                                        <button
                                            key={type}
                                            className={`brush-type-btn ${brushType === type ? 'active' : ''}`}
                                            onClick={() => setBrushType(type)}
                                        >
                                            {type.charAt(0).toUpperCase() + type.slice(1)}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Layer Panel */}
                {showLayerPanel && (
                    <div className="floating-panel layer-panel">
                        <div className="panel-header">
                            <h3>Layers</h3>
                            <button
                                className="close-btn"
                                onClick={() => setShowLayerPanel(false)}
                            >
                                <CloseIcon />
                            </button>
                        </div>
                        <div className="panel-content">
                            <div className="layer-list">
                                {layers.map(layer => (
                                    <div
                                        key={layer.id}
                                        className={`layer-item ${activeLayer === layer.id ? 'active' : ''}`}
                                        onClick={() => setActiveLayer(layer.id)}
                                    >
                                        <button
                                            className="layer-visibility"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setLayers(prev => prev.map(l =>
                                                    l.id === layer.id
                                                        ? { ...l, visible: !l.visible }
                                                        : l
                                                ));
                                            }}
                                        >
                                            <VisibilityIcon />
                                        </button>
                                        <span className="layer-name">{layer.name}</span>
                                        <span className="layer-opacity">{layer.opacity}%</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {/* Canvas Area */}
                <div className="canvas-wrapper">
                    {showGrid && <div className="grid-overlay"></div>}
                    <canvas
                        ref={canvasRef}
                        className="main-canvas"
                        onMouseDown={startDrawing}
                        onMouseMove={draw}
                        onMouseUp={stopDrawing}
                        onMouseLeave={stopDrawing}
                        onTouchStart={startDrawing}
                        onTouchMove={draw}
                        onTouchEnd={stopDrawing}
                        style={{
                            transform: `scale(${zoom}) translate(${panX}px, ${panY}px)`,
                            transformOrigin: 'top left'
                        }}
                    />
                    <canvas
                        ref={overlayCanvasRef}
                        className="overlay-canvas"
                        style={{
                            transform: `scale(${zoom}) translate(${panX}px, ${panY}px)`,
                            transformOrigin: 'top left'
                        }}
                    />
                </div>

                {/* Notes Panel */}
                <div className="notes-panel">
                    <div className="panel-header">
                        <h3>Notes</h3>
                    </div>
                    <div className="panel-content">
                        <textarea
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            placeholder="Add notes about your creation..."
                            className="notes-textarea"
                        />
                    </div>
                </div>
            </div>

            {/* Advanced Gallery Modal */}
            {showGallery && (
                <div className="gallery-modal">
                    <div className="gallery-backdrop" onClick={() => setShowGallery(false)} />
                    <div className="gallery-content">
                        <div className="gallery-header">
                            <h2>Doodle Gallery</h2>
                            <button
                                className="close-btn"
                                onClick={() => setShowGallery(false)}
                            >
                                <CloseIcon />
                            </button>
                        </div>

                        <div className="gallery-body">
                            {savedDoodles.length > 0 ? (
                                <div className="doodle-grid">
                                    {savedDoodles.map(doodle => (
                                        <div key={doodle.id} className="doodle-card">
                                            <div className="card-image">
                                                <img
                                                    src={doodle.image}
                                                    alt="Saved doodle"
                                                    onClick={() => {
                                                        loadDoodle(doodle);
                                                        setShowGallery(false);
                                                    }}
                                                />
                                            </div>
                                            <div className="card-content">
                                                <div className="card-meta">
                                                    <span className="date">{doodle.date}</span>
                                                    {doodle.notes && (
                                                        <p className="notes-preview">
                                                            {doodle.notes.substring(0, 50)}
                                                            {doodle.notes.length > 50 ? '...' : ''}
                                                        </p>
                                                    )}
                                                </div>
                                                <div className="card-actions">
                                                    <button
                                                        className="load-btn"
                                                        onClick={() => {
                                                            loadDoodle(doodle);
                                                            setShowGallery(false);
                                                        }}
                                                    >
                                                        Load
                                                    </button>
                                                    <button
                                                        className="export-btn"
                                                        onClick={() => {
                                                            const link = document.createElement('a');
                                                            link.download = `doodle-${doodle.id}.png`;
                                                            link.href = doodle.image;
                                                            link.click();
                                                        }}
                                                    >
                                                        <DownloadIcon />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="empty-gallery">
                                    <div className="empty-icon">
                                        <PhotoLibraryIcon />
                                    </div>
                                    <h3>No doodles saved yet</h3>
                                    <p>Start creating and save your first masterpiece!</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Doodles;