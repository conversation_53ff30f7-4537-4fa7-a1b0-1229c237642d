@use "variables" as *;

// Advanced macOS-like Doodle App Styling
.advanced-doodle-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: $window-background;
    color: $primary-text;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    &.theme-dark {
        background-color: $dark-window-background;
        color: $dark-primary-text;
    }
}

// Floating Toolbar
.floating-toolbar {
    position: absolute;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: rgba($window-background, 0.85);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba($border-color, 0.3);
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    .theme-dark & {
        background: rgba($dark-window-background, 0.85);
        border-color: rgba($dark-border-color, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    &.hidden {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
        pointer-events: none;
    }

    &.visible {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
        pointer-events: all;
    }

    .toolbar-section {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .toolbar-divider {
        width: 1px;
        height: 1.5rem;
        background-color: $separator-color;
        margin: 0 0.5rem;

        .theme-dark & {
            background-color: $dark-separator-color;
        }
    }

    .tool-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        border: none;
        border-radius: 0.5rem;
        background: transparent;
        color: $secondary-text;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
        position: relative;
        overflow: hidden;

        .theme-dark & {
            color: $dark-secondary-text;
        }

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba($system-blue, 0.1) 0%, rgba($system-blue, 0) 100%);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        &:hover {
            background-color: rgba($system-blue, 0.1);
            color: $system-blue;
            transform: scale(1.05);

            &::before {
                opacity: 1;
            }

            .theme-dark & {
                background-color: rgba($system-blue, 0.15);
                color: $system-blue;
            }
        }

        &:active {
            transform: scale(0.95);
        }

        &.active {
            background-color: $system-blue;
            color: white;
            box-shadow: 0 2px 8px rgba($system-blue, 0.3);

            &:hover {
                background-color: darken($system-blue, 5%);
                transform: scale(1.02);
            }
        }

        &:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;

            &:hover {
                background: transparent;
                color: $secondary-text;
                transform: none;

                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }
        }

        svg {
            width: 1.25rem;
            height: 1.25rem;
        }
    }
}

// Header
.doodle-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: rgba($secondary-background, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid $separator-color;
    z-index: 100;

    .theme-dark & {
        background: rgba($dark-secondary-background, 0.8);
        border-bottom-color: $dark-separator-color;
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 1rem;

        .canvas-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        .subject-badge {
            padding: 0.25rem 0.75rem;
            background-color: $system-blue;
            color: white;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
    }

    .header-controls {
        display: flex;
        align-items: center;
        gap: 1rem;

        .zoom-indicator {
            font-size: 0.875rem;
            color: $secondary-text;
            font-weight: 500;
            min-width: 3rem;
            text-align: center;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .toolbar-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            border: none;
            border-radius: 0.375rem;
            background: transparent;
            color: $secondary-text;
            cursor: pointer;
            transition: all 0.2s ease;

            .theme-dark & {
                color: $dark-secondary-text;
            }

            &:hover {
                background-color: rgba($system-blue, 0.1);
                color: $system-blue;
            }

            svg {
                width: 1rem;
                height: 1rem;
            }
        }
    }
}

// Canvas Container
.canvas-container {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;
}

.canvas-wrapper {
    flex: 1;
    position: relative;
    background: white;
    overflow: hidden;
    cursor: crosshair;

    .theme-dark & {
        background: $dark-secondary-background;
    }

    .grid-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            linear-gradient(rgba($system-gray, 0.2) 1px, transparent 1px),
            linear-gradient(90deg, rgba($system-gray, 0.2) 1px, transparent 1px);
        background-size: 20px 20px;
        pointer-events: none;
        z-index: 1;

        .theme-dark & {
            background-image:
                linear-gradient(rgba($dark-tertiary-text, 0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba($dark-tertiary-text, 0.3) 1px, transparent 1px);
        }
    }

    .main-canvas,
    .overlay-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        touch-action: none;
    }

    .overlay-canvas {
        pointer-events: none;
        z-index: 2;
    }
}

// Floating Panels
.floating-panel {
    position: absolute;
    background: rgba($window-background, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba($border-color, 0.3);
    border-radius: 1rem;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    z-index: 500;
    min-width: 280px;
    max-width: 400px;
    animation: panelSlideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    .theme-dark & {
        background: rgba($dark-window-background, 0.95);
        border-color: rgba($dark-border-color, 0.3);
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
    }

    .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem 1.25rem;
        border-bottom: 1px solid rgba($separator-color, 0.5);

        .theme-dark & {
            border-bottom-color: rgba($dark-separator-color, 0.5);
        }

        h3 {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        h4 {
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
            font-weight: 500;
            color: $secondary-text;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .close-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.75rem;
            height: 1.75rem;
            border: none;
            border-radius: 0.375rem;
            background: transparent;
            color: $tertiary-text;
            cursor: pointer;
            transition: all 0.2s ease;

            .theme-dark & {
                color: $dark-tertiary-text;
            }

            &:hover {
                background-color: rgba($system-red, 0.1);
                color: $system-red;
            }

            svg {
                width: 1rem;
                height: 1rem;
            }
        }
    }

    .panel-content {
        padding: 1.25rem;
    }
}

@keyframes panelSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// Color Palette Panel
.color-palette-panel {
    top: 5rem;
    left: 1rem;

    .current-color {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        padding: 0.75rem;
        background-color: rgba($secondary-background, 0.5);
        border-radius: 0.75rem;

        .theme-dark & {
            background-color: rgba($dark-secondary-background, 0.5);
        }

        .color-preview {
            width: 2rem;
            height: 2rem;
            border-radius: 0.5rem;
            border: 2px solid rgba($border-color, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            .theme-dark & {
                border-color: rgba($dark-border-color, 0.3);
            }
        }

        .color-value {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            color: $secondary-text;
            font-weight: 500;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }
    }

    .system-colors {
        margin-bottom: 1.5rem;

        .color-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-top: 0.75rem;

            .color-swatch {
                width: 2.5rem;
                height: 2.5rem;
                border: 2px solid transparent;
                border-radius: 0.5rem;
                cursor: pointer;
                transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
                    opacity: 0;
                    transition: opacity 0.2s ease;
                }

                &:hover {
                    transform: scale(1.1);
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

                    &::before {
                        opacity: 1;
                    }
                }

                &.active {
                    border-color: $primary-text;
                    transform: scale(1.05);
                    box-shadow: 0 0 0 2px rgba($system-blue, 0.3);

                    .theme-dark & {
                        border-color: $dark-primary-text;
                    }
                }
            }
        }
    }

    .custom-color {
        .color-picker {
            width: 100%;
            height: 3rem;
            border: 2px solid rgba($border-color, 0.3);
            border-radius: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;

            .theme-dark & {
                border-color: rgba($dark-border-color, 0.3);
            }

            &:hover {
                border-color: $system-blue;
                box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
            }
        }
    }
}

// Settings Panel
.settings-panel {
    top: 5rem;
    right: 1rem;

    .setting-group {
        margin-bottom: 1.5rem;

        &:last-child {
            margin-bottom: 0;
        }

        label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: $secondary-text;
            margin-bottom: 0.75rem;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;

            .slider {
                flex: 1;
                height: 0.375rem;
                border-radius: 0.1875rem;
                background: $system-light-gray;
                outline: none;
                cursor: pointer;
                transition: all 0.2s ease;

                .theme-dark & {
                    background: $dark-separator-color;
                }

                &::-webkit-slider-thumb {
                    appearance: none;
                    width: 1.25rem;
                    height: 1.25rem;
                    border-radius: 50%;
                    background: $system-blue;
                    cursor: pointer;
                    box-shadow: 0 2px 8px rgba($system-blue, 0.3);
                    transition: all 0.2s ease;

                    &:hover {
                        transform: scale(1.1);
                        box-shadow: 0 4px 12px rgba($system-blue, 0.4);
                    }
                }

                &::-moz-range-thumb {
                    width: 1.25rem;
                    height: 1.25rem;
                    border-radius: 50%;
                    background: $system-blue;
                    cursor: pointer;
                    border: none;
                    box-shadow: 0 2px 8px rgba($system-blue, 0.3);
                    transition: all 0.2s ease;

                    &:hover {
                        transform: scale(1.1);
                        box-shadow: 0 4px 12px rgba($system-blue, 0.4);
                    }
                }
            }

            .value {
                font-size: 0.875rem;
                font-weight: 500;
                color: $primary-text;
                min-width: 3rem;
                text-align: right;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }
        }

        .brush-preview {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 3rem;
            background-color: rgba($secondary-background, 0.5);
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;

            .theme-dark & {
                background-color: rgba($dark-secondary-background, 0.5);
            }

            .preview-dot {
                border-radius: 50%;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
        }

        .brush-types {
            display: flex;
            gap: 0.5rem;

            .brush-type-btn {
                flex: 1;
                padding: 0.75rem;
                border: 1px solid rgba($border-color, 0.3);
                border-radius: 0.5rem;
                background: transparent;
                color: $secondary-text;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);

                .theme-dark & {
                    border-color: rgba($dark-border-color, 0.3);
                    color: $dark-secondary-text;
                }

                &:hover {
                    background-color: rgba($system-blue, 0.1);
                    border-color: rgba($system-blue, 0.3);
                    color: $system-blue;
                }

                &.active {
                    background-color: $system-blue;
                    border-color: $system-blue;
                    color: white;
                    box-shadow: 0 2px 8px rgba($system-blue, 0.3);
                }
            }
        }
    }
}

// Layer Panel
.layer-panel {
    top: 5rem;
    left: 50%;
    transform: translateX(-50%);

    .layer-list {
        .layer-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.5rem;

            &:last-child {
                margin-bottom: 0;
            }

            &:hover {
                background-color: rgba($system-blue, 0.05);
            }

            &.active {
                background-color: rgba($system-blue, 0.1);
                border: 1px solid rgba($system-blue, 0.3);
            }

            .layer-visibility {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 1.5rem;
                height: 1.5rem;
                border: none;
                border-radius: 0.25rem;
                background: transparent;
                color: $tertiary-text;
                cursor: pointer;
                transition: all 0.2s ease;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }

                &:hover {
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;
                }

                svg {
                    width: 1rem;
                    height: 1rem;
                }
            }

            .layer-name {
                flex: 1;
                font-size: 0.875rem;
                font-weight: 500;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .layer-opacity {
                font-size: 0.75rem;
                color: $tertiary-text;
                font-weight: 500;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }
        }
    }
}

// Notes Panel
.notes-panel {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    width: 300px;
    background: rgba($window-background, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba($border-color, 0.3);
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    z-index: 100;

    .theme-dark & {
        background: rgba($dark-window-background, 0.95);
        border-color: rgba($dark-border-color, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .panel-header {
        padding: 1rem 1.25rem 0.5rem;
        border-bottom: 1px solid rgba($separator-color, 0.3);

        .theme-dark & {
            border-bottom-color: rgba($dark-separator-color, 0.3);
        }

        h3 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }

    .panel-content {
        padding: 1rem 1.25rem 1.25rem;

        .notes-textarea {
            width: 100%;
            height: 120px;
            border: 1px solid rgba($border-color, 0.3);
            border-radius: 0.75rem;
            padding: 0.75rem;
            font-family: inherit;
            font-size: 0.875rem;
            line-height: 1.5;
            color: $primary-text;
            background-color: rgba($secondary-background, 0.3);
            resize: vertical;
            transition: all 0.2s ease;

            .theme-dark & {
                border-color: rgba($dark-border-color, 0.3);
                color: $dark-primary-text;
                background-color: rgba($dark-secondary-background, 0.3);
            }

            &::placeholder {
                color: $placeholder-text;

                .theme-dark & {
                    color: $dark-placeholder-text;
                }
            }

            &:focus {
                outline: none;
                border-color: $system-blue;
                box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
                background-color: rgba($window-background, 0.8);

                .theme-dark & {
                    background-color: rgba($dark-window-background, 0.8);
                }
            }
        }
    }
}

// Gallery Modal
.gallery-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    .gallery-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }

    .gallery-content {
        position: relative;
        width: 90%;
        max-width: 1200px;
        max-height: 85vh;
        background: rgba($window-background, 0.98);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba($border-color, 0.2);
        border-radius: 1.5rem;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        animation: modalSlideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

        .theme-dark & {
            background: rgba($dark-window-background, 0.98);
            border-color: rgba($dark-border-color, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .gallery-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba($separator-color, 0.3);

            .theme-dark & {
                border-bottom-color: rgba($dark-separator-color, 0.3);
            }

            h2 {
                margin: 0;
                font-size: 1.5rem;
                font-weight: 600;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .close-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 2rem;
                height: 2rem;
                border: none;
                border-radius: 0.5rem;
                background: transparent;
                color: $tertiary-text;
                cursor: pointer;
                transition: all 0.2s ease;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }

                &:hover {
                    background-color: rgba($system-red, 0.1);
                    color: $system-red;
                }

                svg {
                    width: 1.25rem;
                    height: 1.25rem;
                }
            }
        }

        .gallery-body {
            padding: 2rem;
            max-height: calc(85vh - 120px);
            overflow-y: auto;

            .doodle-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 1.5rem;

                .doodle-card {
                    background: rgba($secondary-background, 0.5);
                    border: 1px solid rgba($border-color, 0.2);
                    border-radius: 1rem;
                    overflow: hidden;
                    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
                    cursor: pointer;

                    .theme-dark & {
                        background: rgba($dark-secondary-background, 0.5);
                        border-color: rgba($dark-border-color, 0.2);
                    }

                    &:hover {
                        transform: translateY(-4px);
                        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
                        border-color: rgba($system-blue, 0.3);

                        .theme-dark & {
                            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
                        }
                    }

                    .card-image {
                        position: relative;
                        width: 100%;
                        height: 200px;
                        overflow: hidden;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                            background: white;
                            transition: transform 0.3s ease;

                            .theme-dark & {
                                background: $dark-secondary-background;
                            }
                        }

                        &:hover img {
                            transform: scale(1.05);
                        }
                    }

                    .card-content {
                        padding: 1rem;

                        .card-meta {
                            margin-bottom: 1rem;

                            .date {
                                font-size: 0.75rem;
                                color: $tertiary-text;
                                font-weight: 500;

                                .theme-dark & {
                                    color: $dark-tertiary-text;
                                }
                            }

                            .notes-preview {
                                margin: 0.5rem 0 0 0;
                                font-size: 0.875rem;
                                color: $secondary-text;
                                line-height: 1.4;

                                .theme-dark & {
                                    color: $dark-secondary-text;
                                }
                            }
                        }

                        .card-actions {
                            display: flex;
                            gap: 0.5rem;

                            .load-btn {
                                flex: 1;
                                padding: 0.5rem 1rem;
                                border: 1px solid rgba($system-blue, 0.3);
                                border-radius: 0.5rem;
                                background: rgba($system-blue, 0.1);
                                color: $system-blue;
                                font-size: 0.875rem;
                                font-weight: 500;
                                cursor: pointer;
                                transition: all 0.2s ease;

                                &:hover {
                                    background: $system-blue;
                                    color: white;
                                    border-color: $system-blue;
                                }
                            }

                            .export-btn {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 2rem;
                                height: 2rem;
                                border: 1px solid rgba($border-color, 0.3);
                                border-radius: 0.5rem;
                                background: transparent;
                                color: $secondary-text;
                                cursor: pointer;
                                transition: all 0.2s ease;

                                .theme-dark & {
                                    border-color: rgba($dark-border-color, 0.3);
                                    color: $dark-secondary-text;
                                }

                                &:hover {
                                    background-color: rgba($system-green, 0.1);
                                    border-color: rgba($system-green, 0.3);
                                    color: $system-green;
                                }

                                svg {
                                    width: 1rem;
                                    height: 1rem;
                                }
                            }
                        }
                    }
                }
            }

            .empty-gallery {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 4rem 2rem;
                text-align: center;

                .empty-icon {
                    width: 4rem;
                    height: 4rem;
                    border-radius: 50%;
                    background: rgba($system-gray, 0.1);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 1.5rem;

                    .theme-dark & {
                        background: rgba($dark-tertiary-text, 0.1);
                    }

                    svg {
                        width: 2rem;
                        height: 2rem;
                        color: $system-gray;

                        .theme-dark & {
                            color: $dark-tertiary-text;
                        }
                    }
                }

                h3 {
                    margin: 0 0 0.5rem 0;
                    font-size: 1.25rem;
                    font-weight: 600;
                    color: $primary-text;

                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }

                p {
                    margin: 0;
                    font-size: 0.875rem;
                    color: $secondary-text;

                    .theme-dark & {
                        color: $dark-secondary-text;
                    }
                }
            }
        }
    }
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

// Responsive Design
@media (max-width: 1024px) {
    .floating-toolbar {
        flex-wrap: wrap;
        max-width: 90%;

        .toolbar-section {
            flex-wrap: wrap;
        }
    }

    .floating-panel {
        max-width: 90%;
        margin: 0 1rem;
    }

    .color-palette-panel,
    .settings-panel,
    .layer-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .notes-panel {
        position: relative;
        bottom: auto;
        right: auto;
        width: 100%;
        margin: 1rem;
    }
}

@media (max-width: 768px) {
    .advanced-doodle-app {
        .doodle-header {
            padding: 0.75rem 1rem;

            .header-content .canvas-title {
                font-size: 1.25rem;
            }
        }

        .floating-toolbar {
            top: auto;
            bottom: 1rem;
            left: 1rem;
            right: 1rem;
            transform: none;
            max-width: none;

            .toolbar-section {
                gap: 0.125rem;
            }

            .tool-btn {
                width: 2rem;
                height: 2rem;

                svg {
                    width: 1rem;
                    height: 1rem;
                }
            }
        }

        .gallery-content {
            width: 95%;
            max-height: 90vh;

            .gallery-header {
                padding: 1rem 1.5rem;

                h2 {
                    font-size: 1.25rem;
                }
            }

            .gallery-body {
                padding: 1rem;

                .doodle-grid {
                    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
                    gap: 1rem;
                }
            }
        }
    }
}