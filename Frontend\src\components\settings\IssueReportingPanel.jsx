import React from 'react';
import BugReportIcon from '@mui/icons-material/BugReport';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import ErrorIcon from '@mui/icons-material/Error';
import '../../styles/SettingsPanels.scss';

const IssueReportingPanel = ({ settings }) => {
  const { issueCategory, setIssueCategory, isDarkMode } = settings;
  
  const categories = [
    { id: 'bug', label: 'Bug Report', icon: <BugReportIcon />, description: 'Report a technical issue or error in the application' },
    { id: 'urgent', label: 'Urgent Issue', icon: <ErrorIcon />, description: 'Report a critical problem that needs immediate attention' },
    { id: 'suggestion', label: 'Suggestion', icon: <LightbulbIcon />, description: 'Suggest a new feature or improvement to the application' }
  ];
  
  return (
    <div className={`detail-panel issue-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Issue Reporting</h2>
        <p className="panel-description">
          Select a category for your issue to help us route it to the right team.
        </p>
      </div>
      
      <div className="settings-group issue-categories">
        {categories.map(category => (
          <div 
            key={category.id}
            className={`issue-category ${issueCategory === category.id ? 'active' : ''}`}
            onClick={() => setIssueCategory(category.id)}
          >
            <div className="category-icon">{category.icon}</div>
            <div className="category-content">
              <h3>{category.label}</h3>
              <p>{category.description}</p>
            </div>
            <div className="category-selector"></div>
          </div>
        ))}
      </div>
      
      <div className="issue-form">
        <div className="form-group">
          <label htmlFor="issue-title">Issue Title</label>
          <input 
            type="text" 
            id="issue-title" 
            placeholder="Brief description of the issue"
            className="form-control"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="issue-description">Description</label>
          <textarea 
            id="issue-description" 
            placeholder="Please provide details about the issue..."
            className="form-control"
            rows="4"
          ></textarea>
        </div>
        
        <div className="form-group">
          <label htmlFor="issue-steps">Steps to Reproduce (if applicable)</label>
          <textarea 
            id="issue-steps" 
            placeholder="1. First step&#10;2. Second step&#10;3. ..."
            className="form-control"
            rows="3"
          ></textarea>
        </div>
        
        <button className="submit-issue-button">
          <BugReportIcon />
          <span>Submit Issue</span>
        </button>
      </div>
      
      <div className="info-box">
        <p>
          Selecting the correct category helps us route your issue to the appropriate team 
          and resolve it more quickly. For urgent issues, you may also contact us directly.
        </p>
      </div>
    </div>
  );
};

export default IssueReportingPanel;
