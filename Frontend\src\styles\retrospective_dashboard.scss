@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// Retrospective Card - Minimalistic Design
.retrospective-card {
    width: 100%;
    height: 220px;
    padding: 1rem;
    cursor: pointer;
    @include transition(all);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    // Light theme
    background: $window-background;
    border: 1px solid rgba($border-color, 0.3);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    // Dark theme
    &.theme-dark {
        background: $dark-window-background;
        border: 1px solid rgba($dark-border-color, 0.3);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    }

    // Widget Header
    .widget-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        width: 100%;

        .widget-title {
            //font-size: 0.9rem;
            font-weight: 600;
            margin: 0;
            color: $secondary-text;
            letter-spacing: -0.1px;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .total-count {
            font-size: 1.25rem;
            font-weight: 700;
            color: $primary-text;
            background: rgba($system-blue, 0.08);
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            min-width: 2rem;
            text-align: center;

            .theme-dark & {
                color: $dark-primary-text;
                background: rgba($system-blue, 0.15);
            }
        }
    }

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

        &.theme-dark {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .insight-item {
            transform: scale(1.02);
        }
    }

    &:focus {
        outline: 2px solid $system-blue;
        outline-offset: 2px;
    }

    // Retrospective Preview
    .retrospective-preview {
        display: flex;
        width: 100%;
        //flex-direction: column;
        gap: 0.5rem;
        flex: 1;

        .insight-item {
            border-radius: 10px;
            @include transition(all);
            position: relative;
            overflow: hidden;
            border: 1px solid transparent;
            aspect-ratio: 1;



            .insight-content {
                padding: 0.75rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                z-index: 2;
                display: flex;
                flex-direction: column;
                gap: 1rem;

                .insight-info {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    flex-direction: column;

                    .insight-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 32px;
                        height: 32px;
                        border-radius: 8px;
                        background: rgba(255, 255, 255, 0.2);
                        flex-shrink: 0;

                        svg {
                            width: 16px;
                            height: 16px;
                        }
                    }

                    .insight-details {
                        display: flex;
                        flex-direction: column;
                        gap: 0.125rem;

                        .insight-label {
                            font-size: 0.8rem;
                            font-weight: 600;
                            line-height: 1;
                        }

                        .insight-desc {
                            font-size: 0.7rem;
                            font-weight: 500;
                            opacity: 0.8;
                            line-height: 1;
                        }
                    }
                }

                .insight-count {
                    font-size: 1.25rem;
                    font-weight: 700;
                    line-height: 1;
                    flex-shrink: 0;
                }
            }

            // Attractive color scheme for insights
            &.went-well {
                background: linear-gradient(135deg, #4ECDC4 0%, #26A69A 100%);
                border-color: rgba(#4ECDC4, 0.3);
                color: white;

                .theme-dark & {
                    background: linear-gradient(135deg, #319795 0%, #2C7A7B 100%);
                }
            }

            &.to-improve {
                background: linear-gradient(135deg, #FFD93D 0%, #FFC107 100%);
                border-color: rgba(#FFD93D, 0.3);
                color: #2D3748;

                .theme-dark & {
                    background: linear-gradient(135deg, #D69E2E 0%, #B7791F 100%);
                    color: white;
                }
            }

            &.action-items {
                background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
                border-color: rgba(#FF6B6B, 0.3);
                color: white;

                .theme-dark & {
                    background: linear-gradient(135deg, #E53E3E 0%, #C53030 100%);
                }
            }

            // Subtle overlay for depth
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
                z-index: 1;
                border-radius: 10px;
            }
        }
    }

    // Responsive Design
    @media (max-width: 768px) {
        height: 200px;
        padding: 0.75rem;

        .widget-header {
            margin-bottom: 0.75rem;

            .widget-title {
                font-size: 0.85rem;
            }

            .total-count {
                font-size: 1.1rem;
                padding: 0.2rem 0.4rem;
            }
        }

        .retrospective-preview {
            gap: 0.5rem;

            .insight-item {
                border-radius: 8px;

                .insight-content {
                    padding: 0.5rem;

                    .insight-info {
                        gap: 0.5rem;

                        .insight-icon {
                            width: 28px;
                            height: 28px;

                            svg {
                                width: 14px;
                                height: 14px;
                            }
                        }

                        .insight-details {
                            .insight-label {
                                font-size: 0.75rem;
                            }

                            .insight-desc {
                                font-size: 0.65rem;
                            }
                        }
                    }

                    .insight-count {
                        font-size: 1.1rem;
                    }
                }
            }
        }
    }
}

// Modal Overlay - macOS style
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: rgba(0, 0, 0, 0.5);

    // Dark theme
    .theme-dark & {
        background-color: rgba(0, 0, 0, 0.7);
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }
}

// Modal Window - macOS style
.modal-window {
    width: 900px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    border-radius: 16px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    // Light theme
    background-color: $window-background;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid $border-color;

    // Dark theme
    .theme-dark & {
        background-color: $dark-window-background;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        border: 1px solid $dark-border-color;
    }

    @keyframes slideIn {
        from {
            transform: translateY(20px);
            opacity: 0;
        }

        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    // Enhanced Modal Header - macOS style
    .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        border-bottom: 1px solid $separator-color;
        background-color: rgba($window-background, 0.8);

        // Dark theme
        &.theme-dark {
            border-bottom: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
            justify-content: center;

            .header-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 8px;
                background: rgba($system-blue, 0.1);

                &.theme-dark {
                    background: rgba($system-blue, 0.2);
                }

                svg {
                    color: $system-blue;

                    .theme-dark & {
                        color: color.adjust($system-blue, $lightness: 10%);
                    }
                }
            }
        }

        .header-stats {
            display: flex;
            gap: 1rem;

            .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.125rem;

                .stat-number {
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: $system-blue;
                    line-height: 1;

                    .theme-dark & {
                        color: color.adjust($system-blue, $lightness: 10%);
                    }
                }

                .stat-label {
                    font-size: 0.7rem;
                    font-weight: 500;
                    color: $tertiary-text;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }
            }
        }

        .traffic-lights {
            display: flex;
            gap: 8px;
            margin-right: 16px;

            .traffic-light {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: none;
                cursor: pointer;
                @include transition(all);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                &.red {
                    background: $system-red;
                }

                &.yellow {
                    background: $system-orange;
                }

                &.green {
                    background: $system-green;
                }

                &:hover {
                    transform: scale(1.15);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
                }

                &:active {
                    transform: scale(0.95);
                }

                &:disabled {
                    opacity: 0.4;
                    cursor: default;
                }
            }
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            flex: 1;
            text-align: center;
            margin: 0;
            letter-spacing: -0.2px;

            // Light theme
            color: $primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }

    // Enhanced Modal Content
    .modal-content {
        padding: 24px;
        @include ios-autohide-scrollbar;

        &.theme-dark {
            // Dark mode specific styles handled by individual components
        }

        // Enhanced Controls Section
        .controls-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 12px;
            background: rgba($secondary-background, 0.5);
            border: 1px solid $border-color;

            &.theme-dark {
                background: rgba($dark-secondary-background, 0.5);
                border: 1px solid $dark-border-color;
            }

            .controls-left {
                .filter-group {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    .filter-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 24px;
                        height: 24px;
                        color: $tertiary-text;

                        &.theme-dark {
                            color: $dark-tertiary-text;
                        }
                    }

                    .filter-select {
                        padding: 0.5rem 0.75rem;
                        border-radius: 8px;
                        border: 1px solid $border-color;
                        background: $window-background;
                        color: $primary-text;
                        font-size: 0.9rem;
                        min-width: 200px;
                        @include transition(all);

                        &.theme-dark {
                            background: $dark-window-background;
                            border: 1px solid $dark-border-color;
                            color: $dark-primary-text;
                        }

                        &:focus {
                            outline: none;
                            border-color: $system-blue;
                            box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
                        }
                    }
                }
            }

            .controls-right {
                .add-button {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    border-radius: 8px;
                    font-size: 0.9rem;
                    font-weight: 500;
                    cursor: pointer;
                    @include transition(all);
                    border: none;

                    // Light theme
                    background-color: $system-blue;
                    color: white;
                    box-shadow: 0 2px 8px rgba($system-blue, 0.3);

                    // Dark theme
                    &.theme-dark {
                        background-color: $system-blue;
                        color: white;
                        box-shadow: 0 2px 8px rgba($system-blue, 0.5);
                    }

                    &:hover {
                        background-color: darken($system-blue, 5%);
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba($system-blue, 0.4);

                        &.theme-dark {
                            background-color: darken($system-blue, 5%);
                            box-shadow: 0 4px 12px rgba($system-blue, 0.6);
                        }
                    }

                    &.active {
                        background-color: $system-red;
                        box-shadow: 0 2px 8px rgba($system-red, 0.3);

                        &:hover {
                            background-color: darken($system-red, 5%);
                            box-shadow: 0 4px 12px rgba($system-red, 0.4);
                        }

                        &.theme-dark {
                            background-color: $system-red;
                            box-shadow: 0 2px 8px rgba($system-red, 0.5);

                            &:hover {
                                background-color: darken($system-red, 5%);
                                box-shadow: 0 4px 12px rgba($system-red, 0.6);
                            }
                        }
                    }

                    &:focus {
                        outline: 2px solid $system-blue;
                        outline-offset: 2px;
                    }
                }
            }
        }

        // Enhanced Form Container
        .form-container {
            margin-bottom: 2rem;

            .retrospective-form {
                background: $window-background;
                border: 1px solid $border-color;
                border-radius: 16px;
                padding: 1.5rem;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

                &.theme-dark {
                    background: $dark-window-background;
                    border: 1px solid $dark-border-color;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                }

                .form-header {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin-bottom: 1.5rem;
                    padding-bottom: 1rem;
                    border-bottom: 1px solid $separator-color;

                    &.theme-dark {
                        border-bottom: 1px solid $dark-separator-color;
                    }

                    .form-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 40px;
                        height: 40px;
                        border-radius: 10px;
                        background: rgba($system-blue, 0.1);

                        &.theme-dark {
                            background: rgba($system-blue, 0.2);
                        }

                        svg {
                            color: $system-blue;

                            &.theme-dark {
                                color: color.adjust($system-blue, $lightness: 10%);
                            }
                        }
                    }

                    .form-title {
                        font-size: 1.25rem;
                        font-weight: 600;
                        margin: 0;
                        color: $primary-text;

                        &.theme-dark {
                            color: $dark-primary-text;
                        }
                    }
                }

                .form-grid {
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: 1.5rem;

                    .form-group {
                        display: flex;
                        flex-direction: column;
                        gap: 0.5rem;

                        &.full-width {
                            grid-column: 1 / -1;
                        }

                        .form-label {
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                            font-size: 0.9rem;
                            font-weight: 600;
                            color: $secondary-text;

                            &.theme-dark {
                                color: $dark-secondary-text;
                            }

                            svg {
                                color: $tertiary-text;

                                &.theme-dark {
                                    color: $dark-tertiary-text;
                                }
                            }
                        }

                        .form-select,
                        .form-textarea {
                            padding: 0.75rem;
                            border-radius: 8px;
                            border: 1px solid $border-color;
                            font-size: 0.9rem;
                            font-family: inherit;
                            @include transition(all);

                            // Light theme
                            background: $window-background;
                            color: $primary-text;

                            // Dark theme
                            &.theme-dark {
                                background: $dark-window-background;
                                border: 1px solid $dark-border-color;
                                color: $dark-primary-text;
                            }

                            &:focus {
                                outline: none;
                                border-color: $system-blue;
                                box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
                            }

                            &::placeholder {
                                color: $tertiary-text;

                                &.theme-dark {
                                    color: $dark-tertiary-text;
                                }
                            }
                        }

                        .form-textarea {
                            resize: vertical;
                            min-height: 100px;
                        }
                    }
                }

                .form-actions {
                    display: flex;
                    gap: 0.75rem;
                    margin-top: 1.5rem;
                    padding-top: 1rem;
                    border-top: 1px solid $separator-color;

                    &.theme-dark {
                        border-top: 1px solid $dark-separator-color;
                    }

                    .save-button,
                    .cancel-button {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        padding: 0.75rem 1rem;
                        border-radius: 8px;
                        font-size: 0.9rem;
                        font-weight: 500;
                        cursor: pointer;
                        @include transition(all);
                        border: none;
                    }

                    .save-button {
                        background-color: $system-blue;
                        color: white;
                        box-shadow: 0 2px 8px rgba($system-blue, 0.3);

                        &.theme-dark {
                            background-color: $system-blue;
                            color: white;
                            box-shadow: 0 2px 8px rgba($system-blue, 0.5);
                        }

                        &:hover {
                            background-color: darken($system-blue, 5%);
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba($system-blue, 0.4);

                            &.theme-dark {
                                background-color: darken($system-blue, 5%);
                                box-shadow: 0 4px 12px rgba($system-blue, 0.6);
                            }
                        }
                    }

                    .cancel-button {
                        background-color: $system-gray;
                        color: white;
                        box-shadow: 0 2px 8px rgba($system-gray, 0.3);

                        &.theme-dark {
                            background-color: $system-gray;
                            color: white;
                            box-shadow: 0 2px 8px rgba($system-gray, 0.5);
                        }

                        &:hover {
                            background-color: darken($system-gray, 5%);
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba($system-gray, 0.4);

                            &.theme-dark {
                                background-color: darken($system-gray, 5%);
                                box-shadow: 0 4px 12px rgba($system-gray, 0.6);
                            }
                        }
                    }
                }
            }
        }

        // Enhanced Loading and Error States
        .loading-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 2rem;
            color: $secondary-text;

            &.theme-dark {
                color: $dark-secondary-text;
            }

            .loading-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid $border-color;
                border-top: 2px solid $system-blue;
                border-radius: 50%;
                animation: spin 1s linear infinite;

                &.theme-dark {
                    border: 2px solid $dark-border-color;
                    border-top: 2px solid $system-blue;
                }
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }
        }

        .error-message {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-align: center;
            color: $system-red;
            font-size: 1rem;
            padding: 2rem;
            background: rgba($system-red, 0.05);
            border: 1px solid rgba($system-red, 0.2);
            border-radius: 12px;
            margin-bottom: 1rem;

            &.theme-dark {
                color: color.adjust($system-red, $lightness: 10%);
                background: rgba($system-red, 0.1);
                border: 1px solid rgba($system-red, 0.3);
            }
        }

        // Enhanced Retrospective Columns
        .retrospective-columns {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;

            .retrospective-column {
                border-radius: 16px;
                min-height: 400px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                overflow: hidden;
                @include transition(all);

                // Light theme
                background-color: $window-background;
                border: 1px solid $border-color;

                // Dark theme
                &.theme-dark {
                    background-color: $dark-window-background;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                    border: 1px solid $dark-border-color;
                }

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

                    &.theme-dark {
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
                    }
                }

                .column-header {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1.25rem;
                    border-bottom: 1px solid $separator-color;

                    &.theme-dark {
                        border-bottom: 1px solid $dark-separator-color;
                    }

                    .column-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 40px;
                        height: 40px;
                        border-radius: 10px;
                        background: rgba($system-blue, 0.1);

                        &.theme-dark {
                            background: rgba($system-blue, 0.2);
                        }

                        svg {
                            color: $system-blue;

                            &.theme-dark {
                                color: color.adjust($system-blue, $lightness: 10%);
                            }
                        }
                    }

                    .column-info {
                        flex: 1;

                        h3 {
                            font-size: 1.1rem;
                            font-weight: 600;
                            margin: 0 0 0.25rem 0;
                            color: $primary-text;

                            &.theme-dark {
                                color: $dark-primary-text;
                            }
                        }

                        .item-count {
                            font-size: 0.8rem;
                            color: $tertiary-text;
                            font-weight: 500;

                            &.theme-dark {
                                color: $dark-tertiary-text;
                            }
                        }
                    }
                }

                // Color-coded headers for different columns
                &.went-well .column-header {
                    background: linear-gradient(135deg, rgba(#4ECDC4, 0.1) 0%, rgba(#26A69A, 0.05) 100%);
                    border-bottom-color: rgba(#4ECDC4, 0.2);

                    .column-icon {
                        background: rgba(#4ECDC4, 0.15);

                        svg {
                            color: #4ECDC4;
                        }
                    }
                }

                &.to-improve .column-header {
                    background: linear-gradient(135deg, rgba(#FFD93D, 0.1) 0%, rgba(#FFC107, 0.05) 100%);
                    border-bottom-color: rgba(#FFD93D, 0.2);

                    .column-icon {
                        background: rgba(#FFD93D, 0.15);

                        svg {
                            color: #FFD93D;
                        }
                    }
                }

                &.action-items .column-header {
                    background: linear-gradient(135deg, rgba(#FF6B6B, 0.1) 0%, rgba(#FF5252, 0.05) 100%);
                    border-bottom-color: rgba(#FF6B6B, 0.2);

                    .column-icon {
                        background: rgba(#FF6B6B, 0.15);

                        svg {
                            color: #FF6B6B;
                        }
                    }
                }

                .note-list {
                    padding: 1rem;
                    max-height: 400px;
                    overflow-y: auto;
                    @include ios-autohide-scrollbar;

                    .note-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        padding: 1rem;
                        margin-bottom: 0.75rem;
                        border-radius: 12px;
                        background: $secondary-background;
                        border: 1px solid $border-color;
                        @include transition(all);

                        &.theme-dark {
                            background: $dark-secondary-background;
                            border: 1px solid $dark-border-color;
                        }

                        &:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

                            &.theme-dark {
                                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                            }
                        }

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .note-content {
                            flex: 1;
                            margin-right: 0.75rem;

                            .note-text {
                                margin-bottom: 0.5rem;

                                .note-title {
                                    font-size: 0.9rem;
                                    font-weight: 600;
                                    margin: 0 0 0.25rem 0;
                                    color: $primary-text;
                                    line-height: 1.3;

                                    &.theme-dark {
                                        color: $dark-primary-text;
                                    }
                                }

                                .note-details {
                                    font-size: 0.85rem;
                                    color: $secondary-text;
                                    margin: 0;
                                    line-height: 1.4;

                                    &.theme-dark {
                                        color: $dark-secondary-text;
                                    }
                                }
                            }

                            .note-meta {
                                display: flex;
                                gap: 0.5rem;
                                flex-wrap: wrap;

                                .subject-tag,
                                .date-tag {
                                    display: flex;
                                    align-items: center;
                                    gap: 0.25rem;
                                    font-size: 0.75rem;
                                    padding: 0.25rem 0.5rem;
                                    border-radius: 6px;
                                    font-weight: 500;
                                }

                                .subject-tag {
                                    background: rgba($system-blue, 0.1);
                                    color: $system-blue;
                                    border: 1px solid rgba($system-blue, 0.2);

                                    &.theme-dark {
                                        background: rgba($system-blue, 0.2);
                                        color: color.adjust($system-blue, $lightness: 10%);
                                        border: 1px solid rgba($system-blue, 0.3);
                                    }
                                }

                                .date-tag {
                                    background: rgba($tertiary-text, 0.1);
                                    color: $tertiary-text;
                                    border: 1px solid rgba($tertiary-text, 0.2);

                                    &.theme-dark {
                                        background: rgba($dark-tertiary-text, 0.2);
                                        color: $dark-tertiary-text;
                                        border: 1px solid rgba($dark-tertiary-text, 0.3);
                                    }
                                }
                            }
                        }

                        .note-actions {
                            display: flex;
                            gap: 0.25rem;
                            flex-shrink: 0;

                            .action-button {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 32px;
                                height: 32px;
                                border-radius: 6px;
                                border: none;
                                cursor: pointer;
                                @include transition(all);

                                &.edit-button {
                                    background: rgba($system-blue, 0.1);
                                    color: $system-blue;

                                    &:hover {
                                        background: rgba($system-blue, 0.2);
                                        transform: scale(1.05);
                                    }

                                    &.theme-dark {
                                        background: rgba($system-blue, 0.2);
                                        color: color.adjust($system-blue, $lightness: 10%);

                                        &:hover {
                                            background: rgba($system-blue, 0.3);
                                        }
                                    }
                                }

                                &.delete-button {
                                    background: rgba($system-red, 0.1);
                                    color: $system-red;

                                    &:hover {
                                        background: rgba($system-red, 0.2);
                                        transform: scale(1.05);
                                    }

                                    &.theme-dark {
                                        background: rgba($system-red, 0.2);
                                        color: color.adjust($system-red, $lightness: 10%);

                                        &:hover {
                                            background: rgba($system-red, 0.3);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .empty-state {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        padding: 2rem;
                        text-align: center;
                        color: $tertiary-text;

                        &.theme-dark {
                            color: $dark-tertiary-text;
                        }

                        svg {
                            margin-bottom: 0.75rem;
                            opacity: 0.6;
                        }

                        p {
                            margin: 0;
                            font-size: 0.9rem;
                            font-weight: 500;
                        }
                    }
                }

                .add-note-form {
                    margin-top: 16px;

                    textarea {
                        width: 100%;
                        min-height: 80px;
                        padding: 12px;
                        border-radius: 8px;
                        border: 1px solid $border-color;
                        font-size: 0.9rem;
                        font-family: inherit;
                        resize: vertical;
                        @include transition(all);

                        // Light theme
                        background: $window-background;
                        color: $primary-text;

                        // Dark theme
                        .theme-dark & {
                            background: $dark-window-background;
                            border: 1px solid $dark-border-color;
                            color: $dark-primary-text;
                        }

                        &:focus {
                            outline: none;
                            border-color: $system-blue;
                            box-shadow: 0 0 0 3px rgba($system-blue, 0.1);
                        }

                        &::placeholder {
                            color: $tertiary-text;

                            .theme-dark & {
                                color: $dark-tertiary-text;
                            }
                        }
                    }

                    .add-note-button {
                        margin-top: 8px;
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-size: 0.9rem;
                        font-weight: 500;
                        cursor: pointer;
                        @include transition(all);

                        // Light theme
                        background-color: $system-blue;
                        color: white;
                        border: none;
                        box-shadow: 0 1px 3px rgba($system-blue, 0.3);

                        // Dark theme
                        .theme-dark & {
                            background-color: $system-blue;
                            color: white;
                            box-shadow: 0 1px 3px rgba($system-blue, 0.5);
                        }

                        &:hover {
                            background-color: darken($system-blue, 5%);
                            transform: translateY(-1px);
                            box-shadow: 0 2px 5px rgba($system-blue, 0.4);

                            .theme-dark & {
                                background-color: darken($system-blue, 5%);
                                box-shadow: 0 2px 5px rgba($system-blue, 0.6);
                            }
                        }

                        &:active {
                            background-color: darken($system-blue, 10%);
                            transform: translateY(0);
                            box-shadow: 0 1px 2px rgba($system-blue, 0.3);

                            .theme-dark & {
                                background-color: darken($system-blue, 10%);
                                box-shadow: 0 1px 2px rgba($system-blue, 0.5);
                            }
                        }

                        &:focus {
                            outline: 2px solid $system-blue;
                            outline-offset: 2px;
                        }

                        &:disabled {
                            opacity: 0.6;
                            cursor: not-allowed;
                            transform: none;
                        }
                    }
                }
            }
        }

        .error-message {
            text-align: center;
            color: $system-red;
            font-size: 1rem;
            padding: 40px;

            .theme-dark & {
                color: color.adjust($system-red, $lightness: 10%);
            }
        }
    }

    // Modal Footer
    .modal-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 16px 20px;
        border-top: 1px solid $separator-color;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        // Light theme
        background-color: rgba($window-background, 0.8);

        // Dark theme
        .theme-dark & {
            border-top: 1px solid $dark-separator-color;
            background-color: rgba($dark-window-background, 0.8);
        }

        .close-button {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            @include transition(all);

            // Light theme
            background-color: $system-blue;
            color: white;
            border: none;
            box-shadow: 0 1px 3px rgba($system-blue, 0.3);

            // Dark theme
            .theme-dark & {
                background-color: $system-blue;
                color: white;
                box-shadow: 0 1px 3px rgba($system-blue, 0.5);
            }

            &:hover {
                background-color: darken($system-blue, 5%);
                transform: translateY(-1px);
                box-shadow: 0 2px 5px rgba($system-blue, 0.4);

                .theme-dark & {
                    background-color: darken($system-blue, 5%);
                    box-shadow: 0 2px 5px rgba($system-blue, 0.6);
                }
            }

            &:active {
                background-color: darken($system-blue, 10%);
                transform: translateY(0);
                box-shadow: 0 1px 2px rgba($system-blue, 0.3);

                .theme-dark & {
                    background-color: darken($system-blue, 10%);
                    box-shadow: 0 1px 2px rgba($system-blue, 0.5);
                }
            }

            &:focus {
                outline: 2px solid $system-blue;
                outline-offset: 2px;
            }
        }
    }
}