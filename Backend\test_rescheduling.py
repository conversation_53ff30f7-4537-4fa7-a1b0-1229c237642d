import requests
import json
from datetime import date, timedelta

# API endpoints
BACKEND_API_URL = "http://127.0.0.1:5000"  # Backend server
FRONTEND_API_URL = "http://localhost:5001"  # Frontend server

# For testing, we'll use a hardcoded token
# In a real application, you would get this token from the login endpoint
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************.Yx-Ij_Yw-XZ-Yx-Ij_Yw-XZ-Yx-Ij_Yw-XZ"

# Print a warning about using a hardcoded token
print("WARNING: Using a hardcoded token for testing purposes.")
print("In a real application, you would get this token from the login endpoint.")
print("The token may be invalid or expired, which would cause the tests to fail.")

# Headers for API requests
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {TOKEN}"
}

def get_today_objectives():
    """Get today's objectives"""
    response = requests.get(f"{BACKEND_API_URL}/api/ai/today_objectives", headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error getting today's objectives: {response.status_code}")
        print(response.text)
        return None

def get_upcoming_objectives(days=7):
    """Get upcoming objectives"""
    response = requests.get(f"{BACKEND_API_URL}/api/ai/upcoming_objectives?days={days}", headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error getting upcoming objectives: {response.status_code}")
        print(response.text)
        return None

def update_objectives_date(objective_ids, new_date):
    """Update objectives display date"""
    # Convert date to string if it's a date object
    if isinstance(new_date, date):
        new_date = new_date.isoformat()

    data = {
        "objective_ids": objective_ids,
        "new_date": new_date
    }

    response = requests.post(f"{BACKEND_API_URL}/api/ai/update_objectives_date", headers=headers, json=data)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error updating objectives date: {response.status_code}")
        print(response.text)
        return None

def chat_with_ai(message, history=None):
    """Chat with the AI assistant"""
    if history is None:
        history = []

    data = {
        "message": message,
        "history": history
    }

    response = requests.post(f"{BACKEND_API_URL}/api/ai/chat", headers=headers, json=data)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error chatting with AI: {response.status_code}")
        print(response.text)
        return None

def print_objectives(objectives_data):
    """Print objectives in a readable format"""
    if not objectives_data or not objectives_data.get("success"):
        print("No objectives data available")
        return

    print(f"Date: {objectives_data.get('date', 'Unknown date')}")
    objectives = objectives_data.get("objectives", [])
    print(f"Total objectives: {len(objectives)}")

    for i, obj in enumerate(objectives, 1):
        print(f"{i}. {obj['text']} (ID: {obj['id']}, Chapter: {obj['chapter']['name']})")
    print()

# Main test function
def test_rescheduling():
    """Test the rescheduling functionality"""
    print("=== TESTING RESCHEDULING FUNCTIONALITY ===\n")

    # Get today's objectives
    print("Getting today's objectives...")
    today_objectives = get_today_objectives()
    if today_objectives:
        print_objectives(today_objectives)

    # Get tomorrow's objectives
    tomorrow = (date.today() + timedelta(days=1)).isoformat()
    print(f"Getting objectives for tomorrow ({tomorrow})...")
    upcoming = get_upcoming_objectives(1)
    if upcoming and upcoming.get("success"):
        tomorrow_objectives = upcoming.get("objectives_by_date", {}).get(tomorrow, [])
        print(f"Tomorrow's objectives: {len(tomorrow_objectives)}")
        for i, obj in enumerate(tomorrow_objectives, 1):
            print(f"{i}. {obj['text']} (ID: {obj['id']}, Chapter: {obj['chapter']['name']})")
        print()

    # Test direct API update
    if today_objectives and today_objectives.get("objectives"):
        objective_to_move = today_objectives["objectives"][0]
        objective_id = objective_to_move["id"]

        print(f"Moving objective ID {objective_id} from today to tomorrow via API...")
        result = update_objectives_date([objective_id], tomorrow)

        if result and result.get("success"):
            print("Update successful!")
            print(f"Updated objectives: {json.dumps(result.get('updated_objectives', []), indent=2)}")
        else:
            print("Update failed!")

        # Verify the update
        print("\nVerifying update...")
        updated_today = get_today_objectives()
        if updated_today:
            print("Updated today's objectives:")
            print_objectives(updated_today)

        updated_upcoming = get_upcoming_objectives(1)
        if updated_upcoming and updated_upcoming.get("success"):
            tomorrow_objectives = updated_upcoming.get("objectives_by_date", {}).get(tomorrow, [])
            print(f"Updated tomorrow's objectives: {len(tomorrow_objectives)}")
            for i, obj in enumerate(tomorrow_objectives, 1):
                print(f"{i}. {obj['text']} (ID: {obj['id']}, Chapter: {obj['chapter']['name']})")
            print()

    # Test chat-based rescheduling
    print("\n=== TESTING CHAT-BASED RESCHEDULING ===\n")

    # Get current state
    today_objectives = get_today_objectives()
    if today_objectives:
        current_count = len(today_objectives.get("objectives", []))
        print(f"Current number of objectives for today: {current_count}")

        # Request fewer objectives
        if current_count > 1:
            target_count = current_count - 1
            print(f"Requesting {target_count} objectives via chat...")

            chat_response = chat_with_ai(f"I want {target_count} objectives for today")

            if chat_response and chat_response.get("success"):
                print("Chat response:")
                print(chat_response.get("response", "No response"))

                # Verify the update
                print("\nVerifying chat-based update...")
                updated_today = get_today_objectives()
                if updated_today:
                    new_count = len(updated_today.get("objectives", []))
                    print(f"New number of objectives for today: {new_count}")
                    print_objectives(updated_today)

                    if new_count == target_count:
                        print("SUCCESS: Chat-based rescheduling worked correctly!")
                    else:
                        print(f"FAILURE: Expected {target_count} objectives, but got {new_count}")
            else:
                print("Chat request failed!")

if __name__ == "__main__":
    test_rescheduling()
