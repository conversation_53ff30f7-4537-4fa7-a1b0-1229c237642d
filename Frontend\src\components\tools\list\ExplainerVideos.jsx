import { useState, useEffect } from 'react';

const ExplainerVideos = ({ tool, subject }) => {
    const [videos, setVideos] = useState([]);
    const [newVideo, setNewVideo] = useState({
        title: '',
        url: '',
        description: '',
        duration: ''
    });
    const [currentVideo, setCurrentVideo] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        // Load saved videos from localStorage
        const savedVideos = JSON.parse(localStorage.getItem('explainerVideos') || []);
        setVideos(savedVideos);
    }, []);

    useEffect(() => {
        // Save videos to localStorage when they change
        localStorage.setItem('explainerVideos', JSON.stringify(videos));
    }, [videos]);

    const addVideo = () => {
        if (newVideo.title.trim() && newVideo.url.trim()) {
            const video = {
                ...newVideo,
                id: Date.now(),
                subject,
                createdAt: new Date().toISOString()
            };

            setVideos(prev => [...prev, video]);
            setNewVideo({ title: '', url: '', description: '', duration: '' });
        }
    };

    const extractVideoId = (url) => {
        // Extract YouTube video ID from various URL formats
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[2].length === 11) ? match[2] : null;
    };

    const removeVideo = (id) => {
        setVideos(prev => prev.filter(video => video.id !== id));
        if (currentVideo && currentVideo.id === id) {
            setCurrentVideo(null);
        }
    };

    const filteredVideos = videos.filter(video => {
        const matchesSubject = video.subject === subject;
        const matchesSearch = video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            video.description.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesSubject && (searchTerm === '' || matchesSearch);
    });

    return (
        <div className="explainer-videos">
            <h2>Explainer Videos: {subject}</h2>

            <div className="search-bar">
                <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search videos..."
                />
            </div>

            <div className="videos-container">
                <div className="videos-list">
                    <h3>Available Videos</h3>
                    {filteredVideos.length > 0 ? (
                        <ul>
                            {filteredVideos.map(video => (
                                <li
                                    key={video.id}
                                    className={currentVideo?.id === video.id ? 'active' : ''}
                                    onClick={() => setCurrentVideo(video)}
                                >
                                    <div className="video-info">
                                        <h4>{video.title}</h4>
                                        <p>{video.duration} • {new Date(video.createdAt).toLocaleDateString()}</p>
                                    </div>
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            removeVideo(video.id);
                                        }}
                                    >
                                        Delete
                                    </button>
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <p>No videos yet. Add one below!</p>
                    )}
                </div>

                <div className="video-player">
                    {currentVideo ? (
                        <>
                            <div className="video-wrapper">
                                {extractVideoId(currentVideo.url) ? (
                                    <iframe
                                        width="100%"
                                        height="400"
                                        src={`https://www.youtube.com/embed/${extractVideoId(currentVideo.url)}`}
                                        frameBorder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                        allowFullScreen
                                    ></iframe>
                                ) : (
                                    <p>Invalid video URL. Please use a YouTube URL.</p>
                                )}
                            </div>

                            <div className="video-details">
                                <h3>{currentVideo.title}</h3>
                                <p className="meta">
                                    Duration: {currentVideo.duration} • Added: {new Date(currentVideo.createdAt).toLocaleDateString()}
                                </p>
                                <p className="description">{currentVideo.description}</p>
                            </div>
                        </>
                    ) : (
                        <div className="no-video">
                            <p>Select a video to watch</p>
                        </div>
                    )}
                </div>
            </div>

            <div className="add-video">
                <h3>Add New Video</h3>

                <div className="form-group">
                    <label>Title:</label>
                    <input
                        type="text"
                        value={newVideo.title}
                        onChange={(e) => setNewVideo({ ...newVideo, title: e.target.value })}
                        placeholder="Enter video title..."
                    />
                </div>

                <div className="form-group">
                    <label>YouTube URL:</label>
                    <input
                        type="text"
                        value={newVideo.url}
                        onChange={(e) => setNewVideo({ ...newVideo, url: e.target.value })}
                        placeholder="Enter YouTube URL..."
                    />
                </div>

                <div className="form-group">
                    <label>Duration:</label>
                    <input
                        type="text"
                        value={newVideo.duration}
                        onChange={(e) => setNewVideo({ ...newVideo, duration: e.target.value })}
                        placeholder="e.g., 5:30"
                    />
                </div>

                <div className="form-group">
                    <label>Description:</label>
                    <textarea
                        value={newVideo.description}
                        onChange={(e) => setNewVideo({ ...newVideo, description: e.target.value })}
                        placeholder="Enter video description..."
                    />
                </div>

                <button onClick={addVideo}>Add Video</button>
            </div>
        </div>
    );
};

export default ExplainerVideos;