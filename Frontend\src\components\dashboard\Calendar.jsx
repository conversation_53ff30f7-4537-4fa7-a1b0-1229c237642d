import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../../authContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';
import axios from 'axios';
import '../../styles/calender_dashboard_light.scss';

const Calendar = () => {
    const { user, token } = useAuth();
    const { darkMode } = useTheme();
    const [currentDate, setCurrentDate] = useState(new Date());
    const [selectedDate, setSelectedDate] = useState(null);
    const [eventForm, setEventForm] = useState({
        title: '',
        description: '',
        event_time: '',
        event_type: 'exam'
    });
    const [editingEvent, setEditingEvent] = useState(null);
    const [events, setEvents] = useState({});
    const [streakData, setStreakData] = useState([]);
    const [subjects, setSubjects] = useState([]);
    const [loading, setLoading] = useState(true);
    const modalRef = useRef(null);

    const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5001";

    // Get month/year for display
    const monthYear = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getDay();
    const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();

    // Fetch calendar events and streak data
    useEffect(() => {
        if (!user || !token) return;

        const fetchData = async () => {
            setLoading(true);
            try {
                // Fetch calendar events
                const month = currentDate.getMonth() + 1;
                const year = currentDate.getFullYear();

                const eventsResponse = await axios.get(`${API_URL}/api/calendar-events`, {
                    params: { month, year },
                    headers: { Authorization: `Bearer ${token}` }
                });

                // Organize events by date
                const eventsByDate = {};
                if (eventsResponse.data.success) {
                    eventsResponse.data.events.forEach(event => {
                        const dateKey = event.event_date.split('T')[0];
                        if (!eventsByDate[dateKey]) {
                            eventsByDate[dateKey] = [];
                        }
                        eventsByDate[dateKey].push(event);
                    });
                }
                setEvents(eventsByDate);

                // Fetch streak data
                const streakResponse = await axios.get(`${API_URL}/api/streak`, {
                    headers: { Authorization: `Bearer ${token}` }
                });

                if (streakResponse.data.success) {
                    setStreakData(streakResponse.data.streak.streak_history || []);
                }

                // Fetch subjects for the dropdown
                const subjectsResponse = await axios.get(`${API_URL}/api/subjects`, {
                    headers: { Authorization: `Bearer ${token}` }
                });

                if (subjectsResponse.data.success) {
                    setSubjects(subjectsResponse.data.subjects || []);
                }

            } catch (error) {
                console.error('Error fetching calendar data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [user, token, currentDate, API_URL]);

    // Generate calendar days
    const calendarDays = [];
    for (let i = 0; i < firstDayOfMonth; i++) {
        calendarDays.push({ key: `empty-${i}`, empty: true });
    }

    for (let day = 1; day <= daysInMonth; day++) {
        const date = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const dateEvents = events[date] || [];

        // Find streak data for this date
        const streakInfo = streakData.find(d => d.date === date);
        const isStreakDay = streakInfo?.is_streak_day || false;
        const completion = streakInfo?.completion || 0;

        // Count event types
        const examEvents = dateEvents.filter(e => e.event_type === 'exam').length;
        const quizEvents = dateEvents.filter(e => e.event_type === 'quiz').length;

        calendarDays.push({
            key: day,
            day,
            date,
            events: dateEvents,
            isStreakDay,
            completion,
            hasExams: examEvents > 0,
            hasQuizzes: quizEvents > 0
        });
    }

    // Handle month navigation
    const changeMonth = (delta) => {
        setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + delta, 1));
    };

    // Handle day click
    const handleDayClick = (date) => {
        setSelectedDate({
            date,
            events: events[date] || []
        });
        setEventForm({
            title: '',
            description: '',
            event_time: '',
            event_type: 'exam',
            subject_id: subjects.length > 0 ? subjects[0].id : null
        });
        setEditingEvent(null);
    };

    // Calculate days remaining
    const getDaysRemaining = (eventDate) => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const targetDate = new Date(eventDate);
        targetDate.setHours(0, 0, 0, 0);

        const diffTime = targetDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Tomorrow';
        if (diffDays < 0) return `${Math.abs(diffDays)} days ago`;
        return `${diffDays} days remaining`;
    };

    // Handle event form submission
    const handleAddOrUpdateEvent = async () => {
        if (!eventForm.title.trim()) return;

        try {
            if (editingEvent) {
                // Update existing event
                await axios.put(
                    `${API_URL}/api/calendar-events/${editingEvent.id}`,
                    {
                        ...eventForm,
                        event_date: selectedDate.date
                    },
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );
            } else {
                // Create new event
                await axios.post(
                    `${API_URL}/api/calendar-events`,
                    {
                        ...eventForm,
                        event_date: selectedDate.date
                    },
                    {
                        headers: { Authorization: `Bearer ${token}` }
                    }
                );
            }

            // Refresh events for the selected date
            const response = await axios.get(
                `${API_URL}/api/calendar-events`,
                {
                    params: {
                        month: currentDate.getMonth() + 1,
                        year: currentDate.getFullYear()
                    },
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            if (response.data.success) {
                const eventsByDate = {};
                response.data.events.forEach(event => {
                    const dateKey = event.event_date.split('T')[0];
                    if (!eventsByDate[dateKey]) {
                        eventsByDate[dateKey] = [];
                    }
                    eventsByDate[dateKey].push(event);
                });
                setEvents(eventsByDate);

                // Update selected date events
                setSelectedDate({
                    ...selectedDate,
                    events: eventsByDate[selectedDate.date] || []
                });
            }

            // Reset form
            setEventForm({
                title: '',
                description: '',
                event_time: '',
                event_type: 'exam',
                subject_id: subjects.length > 0 ? subjects[0].id : null
            });
            setEditingEvent(null);

        } catch (error) {
            console.error('Error saving event:', error);
        }
    };

    // Handle event deletion
    const handleDeleteEvent = async (eventId) => {
        try {
            await axios.delete(
                `${API_URL}/api/calendar-events/${eventId}`,
                {
                    headers: { Authorization: `Bearer ${token}` }
                }
            );

            // Update events after deletion
            setSelectedDate({
                ...selectedDate,
                events: selectedDate.events.filter(e => e.id !== eventId)
            });

            // Also update the events state
            const updatedEvents = { ...events };
            if (updatedEvents[selectedDate.date]) {
                updatedEvents[selectedDate.date] = updatedEvents[selectedDate.date].filter(e => e.id !== eventId);
                if (updatedEvents[selectedDate.date].length === 0) {
                    delete updatedEvents[selectedDate.date];
                }
                setEvents(updatedEvents);
            }

        } catch (error) {
            console.error('Error deleting event:', error);
        }
    };

    // Handle event edit
    const handleEditEvent = (event) => {
        setEditingEvent(event);
        setEventForm({
            title: event.title,
            description: event.description || '',
            event_time: event.event_time || '',
            event_type: event.event_type,
            subject_id: event.subject_id
        });
    };

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setSelectedDate(null);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setSelectedDate(null);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    if (loading) {
        return <div className="loading">Loading calendar...</div>;
    }

    return (
        <>
            <div
                className={`calendar-card ${darkMode ? 'theme-dark' : ''}`}
                role="button"
                tabIndex={0}
                aria-label="Calendar"
            >
                <div className="calendar-header">
                    <button className="nav-button" onClick={() => changeMonth(-1)} aria-label="Previous month">
                        ←
                    </button>
                    <span className="month-year">{monthYear}</span>
                    <button className="nav-button" onClick={() => changeMonth(1)} aria-label="Next month">
                        →
                    </button>
                </div>
                <div className="calendar-grid">
                    {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, i) => (
                        <div key={i} className="day-header">
                            {day}
                        </div>
                    ))}
                    {calendarDays.map((cell) =>
                        cell.empty ? (
                            <div key={cell.key} className="day empty"></div>
                        ) : (
                            <div
                                key={cell.key}
                                className={`day ${cell.hasExams ? 'has-exams' : ''} ${cell.hasQuizzes ? 'has-quizzes' : ''} ${cell.isStreakDay ? 'streak-day' : ''}`}
                                onClick={() => handleDayClick(cell.date)}
                                role="button"
                                tabIndex={0}
                                onKeyDown={(e) => e.key === 'Enter' && handleDayClick(cell.date)}
                                aria-label={`${cell.date}: ${cell.events.length} events, ${cell.completion}% completed`}
                            >
                                <span className="day-number">{cell.day}</span>
                                {cell.hasExams && (
                                    <span className="exam-indicator" aria-hidden="true"></span>
                                )}
                                {cell.hasQuizzes && (
                                    <span className="quiz-indicator" aria-hidden="true"></span>
                                )}
                                {cell.isStreakDay && (
                                    <span className="streak-mark" aria-hidden="true">✓</span>
                                )}
                                <div className="tooltip">
                                    <div style={{ fontWeight: 'bold', marginBottom: '4px', fontSize: '12px' }}>
                                        {new Date(cell.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
                                    </div>

                                    {cell.isStreakDay && (
                                        <div style={{
                                            background: 'rgba(52, 199, 89, 0.15)',
                                            padding: '3px 6px',
                                            borderRadius: '4px',
                                            marginBottom: '6px',
                                            color: '#34C759',
                                            fontWeight: '500',
                                            fontSize: '10px'
                                        }}>
                                            ✓ Streak day: {cell.completion}% completed
                                        </div>
                                    )}

                                    {cell.events.length > 0 ? (
                                        <>
                                            <div style={{ fontSize: '10px', marginBottom: '4px', opacity: 0.7 }}>
                                                {cell.events.length} event{cell.events.length > 1 ? 's' : ''}:
                                            </div>
                                            {cell.events.map((event, i) => (
                                                <div key={i} style={{
                                                    marginBottom: i < cell.events.length - 1 ? '6px' : '0',
                                                    borderLeft: `2px solid ${event.event_type === 'exam' ? '#FF3B30' : '#FF9500'}`,
                                                    paddingLeft: '6px'
                                                }}>
                                                    <div style={{ fontWeight: '600', fontSize: '11px' }}>
                                                        {event.event_type === 'exam' ? '📝 ' : '📋 '}
                                                        {event.title}
                                                    </div>
                                                    {event.event_time && (
                                                        <div style={{ fontSize: '10px', opacity: 0.8 }}>
                                                            ⏰ {event.event_time}
                                                        </div>
                                                    )}
                                                    <div style={{
                                                        fontSize: '10px',
                                                        marginTop: '2px',
                                                        color: getDaysRemaining(cell.date).includes('remaining') ? '#FF9500' :
                                                            getDaysRemaining(cell.date) === 'Today' ? '#34C759' : '#FF3B30'
                                                    }}>
                                                        {getDaysRemaining(cell.date)}
                                                    </div>
                                                </div>
                                            ))}
                                        </>
                                    ) : (
                                        <div style={{ fontSize: '10px', opacity: 0.7 }}>
                                            No events scheduled
                                        </div>
                                    )}

                                    <div style={{
                                        fontSize: '9px',
                                        marginTop: '6px',
                                        opacity: 0.6,
                                        textAlign: 'center',
                                        borderTop: '1px solid rgba(0,0,0,0.1)',
                                        paddingTop: '4px'
                                    }}>
                                        Click to add/edit events
                                    </div>
                                </div>
                            </div>
                        )
                    )}
                </div>
            </div>
            {/* Event Modal */}
            {selectedDate && (
                <div className={`event-modal-overlay ${darkMode ? 'theme-dark' : ''}`}>
                    <div className={`event-modal ${darkMode ? 'theme-dark' : ''}`} ref={modalRef}>
                        <div className="modal-header">
                            <h3>{selectedDate.date}</h3>
                            <button className="close-button" onClick={() => setSelectedDate(null)}>×</button>
                        </div>

                        <div className="modal-content">
                            {/* Event Form */}
                            <div className="event-form">
                                <h4>{editingEvent ? 'Edit Event' : 'Add New Event'}</h4>

                                <div className="form-group">
                                    <label htmlFor="event-title">Title:</label>
                                    <input
                                        id="event-title"
                                        type="text"
                                        value={eventForm.title}
                                        onChange={(e) => setEventForm({ ...eventForm, title: e.target.value })}
                                        placeholder="Event title"
                                        required
                                    />
                                </div>

                                <div className="form-group">
                                    <label htmlFor="event-description">Description:</label>
                                    <textarea
                                        id="event-description"
                                        value={eventForm.description}
                                        onChange={(e) => setEventForm({ ...eventForm, description: e.target.value })}
                                        placeholder="Event description"
                                        rows="3"
                                    />
                                </div>

                                <div className="form-group">
                                    <label htmlFor="event-time">Time:</label>
                                    <input
                                        id="event-time"
                                        type="time"
                                        value={eventForm.event_time}
                                        onChange={(e) => setEventForm({ ...eventForm, event_time: e.target.value })}
                                    />
                                </div>

                                <div className="form-group">
                                    <label htmlFor="event-type">Type:</label>
                                    <select
                                        id="event-type"
                                        value={eventForm.event_type}
                                        onChange={(e) => setEventForm({ ...eventForm, event_type: e.target.value })}
                                    >
                                        <option value="exam">Exam</option>
                                        <option value="quiz">Quiz</option>
                                    </select>
                                </div>

                                <div className="form-group">
                                    <label htmlFor="subject-id">Subject:</label>
                                    <select
                                        id="subject-id"
                                        value={eventForm.subject_id || ''}
                                        onChange={(e) => setEventForm({ ...eventForm, subject_id: e.target.value ? parseInt(e.target.value) : null })}
                                    >
                                        <option value="">-- No Subject --</option>
                                        {subjects.map(subject => (
                                            <option key={subject.id} value={subject.id}>
                                                {subject.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="form-actions">
                                    <button
                                        className="save-button"
                                        onClick={handleAddOrUpdateEvent}
                                        disabled={!eventForm.title.trim()}
                                    >
                                        {editingEvent ? 'Update' : 'Add'} Event
                                    </button>

                                    {editingEvent && (
                                        <button
                                            className="cancel-button"
                                            onClick={() => {
                                                setEditingEvent(null);
                                                setEventForm({
                                                    title: '',
                                                    description: '',
                                                    event_time: '',
                                                    event_type: 'exam',
                                                    subject_id: subjects.length > 0 ? subjects[0].id : null
                                                });
                                            }}
                                        >
                                            Cancel
                                        </button>
                                    )}
                                </div>
                            </div>

                            {/* Events List */}
                            {selectedDate.events.length > 0 && (
                                <div className="events-list">
                                    <h4>Events on this day:</h4>
                                    {selectedDate.events.map((event) => (
                                        <div key={event.id} className="event-item">
                                            <div className="event-details">
                                                <h5>
                                                    {event.event_type === 'exam' ? '📝 ' : '📋 '}
                                                    {event.title}
                                                </h5>
                                                {event.description && <p>{event.description}</p>}
                                                {event.event_time && <p>Time: {event.event_time}</p>}
                                                <p className="countdown">{getDaysRemaining(selectedDate.date)}</p>
                                            </div>
                                            <div className="event-actions">
                                                <button
                                                    className="edit-button"
                                                    onClick={() => handleEditEvent(event)}
                                                >
                                                    Edit
                                                </button>
                                                <button
                                                    className="delete-button"
                                                    onClick={() => handleDeleteEvent(event.id)}
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Calendar;