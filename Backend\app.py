from flask import Flask, request, jsonify, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from models import Curriculum, User, Subject, Chapter, Objective, StudyPlan, ScheduleItem, File, Quiz, QuizQuestion, QuizResult
from database import db, init_db, pg_conn
from ai_assistant import ai_assistant_bp
import os
import pathlib
import re
import secrets
from dotenv import load_dotenv
import logging
import json
import base64
import traceback
import math
import google.generativeai as genai
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, timedelta, date
import re
from document_processor import extract_text_from_document, process_document_with_gemini, generate_study_schedule, filter_document_content, enhanced_process_document_with_gemini, generate_flashcards_with_ai, intelligent_content_splitter
import PyPDF2
import docx
from functools import wraps
import jwt
import psycopg2
import psycopg2.extras
from werkzeug.utils import secure_filename
import hashlib
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman

# Load environment variables from .env file
# Use relative path for .env file
current_dir = pathlib.Path(__file__).parent.absolute()
dotenv_path = os.path.join(current_dir, '.env')

# Try to load from the .env file
load_dotenv(dotenv_path)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(current_dir, 'app.log')),
        logging.StreamHandler()
    ]
)

# Print environment variables for debugging (will be removed in production)
logging.info(f"Environment variables loaded from: {dotenv_path}")
logging.info(f"GOOGLE_API_KEY exists: {'Yes' if os.environ.get('GOOGLE_API_KEY') else 'No'}")
logging.info(f"DB_HOST exists: {'Yes' if os.environ.get('DB_HOST') else 'No'}")

# Manually set environment variables if they're in the .env file but not in os.environ
try:
    with open(dotenv_path, 'r') as env_file:
        for line in env_file:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                if not os.environ.get(key):
                    os.environ[key] = value
                    logging.info(f"Manually set environment variable: {key}")
except Exception as e:
    logging.error(f"Error reading .env file: {str(e)}")

# Check if required environment variables are set
required_env_vars = ['GOOGLE_API_KEY', 'DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_NAME', 'JWT_SECRET']
missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
if missing_vars:
    logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
    # Don't raise an error, just log it and continue with defaults
    logging.warning("Application will use default values where possible")

# Initialize Flask app
app = Flask(__name__)

# Initialize rate limiter
limiter = Limiter(
    get_remote_address,
    app=app,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="memory://",
)

# Initialize Talisman for security headers
talisman = Talisman(
    app,
    content_security_policy={
        'default-src': "'self'",
        'img-src': '*',
        'script-src': ["'self'", "'unsafe-inline'"],
        'style-src': ["'self'", "'unsafe-inline'"]
    },
    force_https=False,  # Set to True in production
    session_cookie_secure=False,  # Set to True in production
    session_cookie_http_only=True
)

# Initialize CORS with specific origins and more permissive settings for development
allowed_origins = os.environ.get('ALLOWED_ORIGINS', 'http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173,http://localhost:5001,http://127.0.0.1:5001').split(',')
CORS(
    app,
    resources={r"/*": {"origins": allowed_origins}},  # Allow all routes
    supports_credentials=True,
    methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization"]
)

# JWT Authentication middleware with improved security
def authenticate_token(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        # Check for token in Authorization header
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            # Validate header format
            if not auth_header.startswith('Bearer '):
                return jsonify({
                    'success': False,
                    'error': 'Invalid authorization format. Use Bearer token'
                }), 401

            try:
                token = auth_header.split(" ")[1]
            except IndexError:
                return jsonify({
                    'success': False,
                    'error': 'Token is missing or invalid'
                }), 401

        if not token:
            return jsonify({'success': False, 'error': 'Token is missing'}), 401

        try:
            # Decode with proper validation
            data = jwt.decode(
                token,
                app.config['JWT_SECRET_KEY'],
                algorithms=["HS256"],
                options={
                    'verify_signature': True,
                    'verify_exp': True,
                    'verify_nbf': True,
                    'verify_iat': True,
                    'require': ['exp', 'iat', 'userId']
                }
            )
            current_user_id = data['userId']

            # Check if token is about to expire and needs refresh
            # Use datetime.utcnow() for compatibility with older Python versions
            now = datetime.utcnow().timestamp()
            exp = data.get('exp', 0)

            # If token expires in less than 5 minutes, add refresh flag
            if exp - now < 300:  # 5 minutes in seconds
                kwargs['token_needs_refresh'] = True

        except jwt.ExpiredSignatureError:
            logging.warning("Expired JWT token")
            return jsonify({
                'success': False,
                'error': 'Token has expired. Please log in again'
            }), 401
        except jwt.InvalidTokenError:
            logging.warning("Invalid JWT token")
            return jsonify({
                'success': False,
                'error': 'Invalid token. Please log in again'
            }), 401
        except Exception as e:
            logging.error(f"Token validation error: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'Authentication failed'
            }), 401

        # Add user_id to kwargs
        kwargs['current_user_id'] = current_user_id
        return f(*args, **kwargs)

    return decorated

# Function to generate secure JWT tokens
def generate_token(user_id):
    """Generate a secure JWT token with proper claims"""
    # Use datetime.utcnow() for compatibility with older Python versions
    now = datetime.utcnow()
    # Convert to timestamp for JWT
    now_timestamp = now.timestamp()
    exp_timestamp = (now + timedelta(seconds=app.config.get('JWT_ACCESS_TOKEN_EXPIRES', 3600))).timestamp()

    payload = {
        'iat': now_timestamp,
        'nbf': now_timestamp,
        'exp': exp_timestamp,
        'userId': user_id,
        'jti': secrets.token_hex(16)  # Unique token ID
    }

    return jwt.encode(
        payload,
        app.config['JWT_SECRET_KEY'],
        algorithm='HS256'
    )

# Helper function to handle CORS preflight requests
def handle_options_request():
    """Handle OPTIONS requests for CORS preflight"""
    response = jsonify({'success': True})
    # Use the allowed origins from the CORS configuration
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS')
    return response

@app.route('/api/validate_study_plan', methods=['OPTIONS'])
def validate_study_plan_preflight():
    """
    Handle OPTIONS preflight request for the validate_study_plan endpoint
    This is needed to properly handle CORS
    """
    return handle_options_request()

@app.route('/api/validate_study_plan', methods=['POST'])
@limiter.limit("20/hour")  # Rate limit to prevent abuse
@authenticate_token
def validate_study_plan_endpoint(current_user_id):
    """
    Validate a study plan without creating it
    This endpoint checks if a proposed study plan is feasible based on content and user constraints

    Args:
        current_user_id: The ID of the authenticated user (required for @authenticate_token)
    """

    # Get the request data
    data = request.json
    logging.info(f"Validating study plan with data: {data}")

    if not data:
        return jsonify({'success': False, 'error': 'No data provided'}), 400

    # Validate required fields
    required_fields = ['subject_id', 'name', 'start_date', 'end_date', 'daily_study_time_minutes']
    for field in required_fields:
        if field not in data:
            return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

    try:
        # Get subject data
        subject_id = data['subject_id']

        # Use PostgreSQL connection to get subject data
        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get the subject from PostgreSQL
        pg_cursor.execute(
            "SELECT id, name, user_id FROM subjects WHERE id = %s",
            (subject_id,)
        )
        subject = pg_cursor.fetchone()

        if not subject:
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Parse dates
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
        daily_study_time = int(data['daily_study_time_minutes'])

        # Get preferred days and unavailable periods if provided
        preferred_days = data.get('preferred_days')
        unavailable_periods = data.get('unavailable_periods')

        # Format unavailable periods if provided
        if unavailable_periods:
            for period in unavailable_periods:
                if 'start_date' in period and isinstance(period['start_date'], str):
                    period['start_date'] = datetime.strptime(period['start_date'], '%Y-%m-%d').date()
                if 'end_date' in period and isinstance(period['end_date'], str):
                    period['end_date'] = datetime.strptime(period['end_date'], '%Y-%m-%d').date()

        # Get all chapters and objectives for this subject
        subjects_data = {'subjects': []}

        # Get chapters from PostgreSQL database
        pg_cursor.execute(
            """
            SELECT c.id, c.title
            FROM chapters c
            WHERE c.subject_id = %s
            ORDER BY c.created_at
            """,
            (subject_id,)
        )
        chapters = pg_cursor.fetchall()

        if not chapters:
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'No chapters found for this subject'}), 400

        # Create subject data structure
        subject_data = {
            'name': subject['name'],
            'chapters': []
        }

        # Process chapters and objectives
        total_objectives = 0
        for chapter in chapters:
            chapter_id = chapter['id']
            chapter_title = chapter['title']
            chapter_name = chapter_title  # Use title as name for backward compatibility

            # Get objectives for this chapter
            pg_cursor.execute(
                """
                SELECT id, objective as text
                FROM objectives
                WHERE chapter_id = %s
                ORDER BY id
                """,
                (chapter_id,)
            )
            objectives = pg_cursor.fetchall()

            # Create chapter data
            chapter_data = {
                'name': chapter_name,
                'objectives': []
            }

            # Process objectives
            for objective in objectives:
                objective_id = objective['id']
                objective_text = objective['text']

                # Use default values for estimated_time_minutes and difficulty_level
                estimated_time = 30  # Default value
                difficulty = 2  # Default value (medium)

                total_objectives += 1

                # Add to chapter data
                chapter_data['objectives'].append({
                    'text': objective_text,
                    'estimated_time_minutes': estimated_time,
                    'difficulty_level': difficulty,
                    'objective_id': objective_id
                })

            # Add chapter to subject data
            subject_data['chapters'].append(chapter_data)

        # Add subject to subjects data
        subjects_data['subjects'].append(subject_data)

        # Close PostgreSQL cursor
        pg_cursor.close()

        # Use AI to evaluate the study plan
        logging.info("Using AI to evaluate study plan feasibility...")

        # Get estimated time per objective if provided
        estimated_time_per_objective = data.get('estimated_time_per_objective')

        # Call the AI evaluation function
        ai_evaluation = evaluate_study_plan_with_ai(
            subjects_data,
            start_date,
            end_date,
            daily_study_time,
            preferred_days,
            unavailable_periods,
            estimated_time_per_objective
        )

        logging.info(f"AI evaluation result: {json.dumps(ai_evaluation, indent=2, default=str)}")

        # For backward compatibility, also get the result from the original validate_study_plan
        from document_processor import validate_study_plan
        original_validation = validate_study_plan(
            subjects_data,
            start_date,
            end_date,
            daily_study_time,
            preferred_days,
            unavailable_periods
        )

        # Combine results, prioritizing AI evaluation
        combined_result = {
            'ai_evaluation': ai_evaluation,
            'original_validation': original_validation,
            'is_feasible': ai_evaluation.get('feasible', original_validation.get('is_feasible', False)),
            'message': ai_evaluation.get('message', ''),
            'suggestions': ai_evaluation.get('suggestions', original_validation.get('suggestions', {})),
            'assigned_objectives': ai_evaluation.get('assigned_objectives', []),
            'summary': ai_evaluation.get('summary', {})
        }

        # Return the combined validation result
        return jsonify({
            'success': True,
            'validation_result': combined_result,
            'subject': {
                'id': subject_id,
                'name': subject['name']
            },
            'total_objectives': total_objectives
        }), 200

    except Exception as e:
        logging.error(f"Error validating study plan: {str(e)}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")

        # Close the cursor if it exists
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()

        return jsonify({'success': False, 'error': f'Error validating study plan: {str(e)}'}), 500

# Configure database connection securely
import urllib.parse
db_password = urllib.parse.quote_plus(os.environ.get('DB_PASSWORD', ''))
app.config['SQLALCHEMY_DATABASE_URI'] = (
    f"postgresql://{os.environ.get('DB_USER')}:{db_password}"
    f"@{os.environ.get('DB_HOST')}/{os.environ.get('DB_NAME')}"
)
# Log only that the database is configured, not the actual connection string
logging.info("Database connection configured")
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = os.environ.get('JWT_SECRET')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = 3600  # 1 hour
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB max upload size

# Initialize database
init_db(app)

# Register blueprints
app.register_blueprint(ai_assistant_bp)
logging.info("Registered AI Assistant blueprint")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Global variable to track first request
first_request = True

# Check if PostgreSQL connection is established
if pg_conn:
    logging.info("Using PostgreSQL connection from database.py")
else:
    logging.error("PostgreSQL connection not established in database.py")

# Configure Gemini with secure practices
try:
    # Get API key from environment variables only
    api_key = os.environ.get('GOOGLE_API_KEY')

    # Log API key status (without revealing the key)
    if not api_key:
        logging.error("GOOGLE_API_KEY environment variable not set")
        # Use the hardcoded key from the .env file as a fallback
        api_key = "AIzaSyAdAJod78n6xaHYSGtZhO8BBVGigsyIwLo"
        logging.warning("Using fallback API key - not recommended for production")
    else:
        logging.info("Using GOOGLE_API_KEY from environment variables")

    # Configure Gemini with the API key
    genai.configure(api_key=api_key)
    logging.info("Configured Gemini API with key")

    # Define model priority list from newest to oldest
    model_names = [
        'gemini-2.0-flash',  # Latest model (2024)
        'gemini-1.5-flash',  # Alternative model
        'gemini-1.5-pro',    # Alternative model
        'gemini-pro'         # Older model
    ]

    # Try models in order until one works
    model = None
    for model_name in model_names:
        try:
            model = genai.GenerativeModel(model_name)
            logging.info(f"Successfully initialized {model_name} model")
            break  # Exit loop if model initialization succeeds
        except Exception as e:
            logging.warning(f"Failed to initialize {model_name} model: {str(e)}")
            continue

    if model is None:
        logging.error("Failed to initialize any Gemini model")
except Exception as e:
    logging.error(f"Error configuring Gemini API: {str(e)}")
    model = None

def process_curriculum_content(content: str) -> Dict:
    """
    Send syllabus text to Gemini and return structured data.
    Expected Gemini response format:
    {
        "subjects": [
            {
                "name": "Mathematics",
                "chapters": [
                    {
                        "name": "Algebra",
                        "objectives": ["Solve linear equations", ...]
                    },
                    ...
                ]
            },
            ...
        ]
    }
    """
    # Check if we should use mock implementation
    use_mock = os.getenv('MOCK_AI', 'false').lower() == 'true'

    if use_mock or model is None:
        # Return a simple mock structure for testing
        logging.info("Using mock implementation for curriculum processing")
        return {
            "subjects": [
                {
                    "name": "Sample Subject",
                    "chapters": [
                        {
                            "name": "Chapter 1",
                            "objectives": ["Learn basic concepts", "Practice exercises", "Review material"]
                        },
                        {
                            "name": "Chapter 2",
                            "objectives": ["Understand advanced topics", "Complete assignments", "Prepare for assessment"]
                        }
                    ]
                }
            ]
        }

    try:
        prompt = f"""
        Parse this syllabus into structured JSON with subjects, chapters, and learning objectives.
        Return ONLY valid JSON without markdown or additional text.

        Syllabus:
        {content}

        JSON Format:
        {{
            "subjects": [
                {{
                    "name": "Subject Name",
                    "chapters": [
                        {{
                            "name": "Chapter Name",
                            "objectives": ["Objective 1", "Objective 2", ...]
                        }},
                        ...
                    ]
                }},
                ...
            ]
        }}
        """

        # Define safety settings
        safety_settings = [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]

        # Format the request according to the current Gemini API (2024)
        content = [{"parts": [{"text": prompt}]}]

        # Try different API call formats with fallbacks
        try:
            # Try with content format and safety settings
            response = model.generate_content(content, safety_settings=safety_settings)
        except Exception as e:
            logging.warning(f"Failed to generate with content format and safety settings: {str(e)}")
            try:
                # Try with just the content
                response = model.generate_content(content)
            except Exception as e:
                logging.warning(f"Failed with content format, trying legacy format: {str(e)}")
                # Fallback to legacy format
                response = model.generate_content(prompt)

        # Extract JSON from Gemini's response (handle different response formats)
        try:
            # New API format
            json_str = response.text.replace('```json', '').replace('```', '').strip()
        except AttributeError:
            try:
                # Alternative format
                json_str = response.candidates[0].content.parts[0].text.replace('```json', '').replace('```', '').strip()
            except (AttributeError, IndexError):
                # Last resort
                json_str = str(response).replace('```json', '').replace('```', '').strip()
        return json.loads(json_str)

    except Exception as e:
        logging.error(f"Gemini API Error in process_curriculum_content: {str(e)}")
        return f"Gemini API Error: {str(e)}"


def get_chapter_content_from_db(chapter_id, user_id):
    """
    Helper function to retrieve chapter content from either database or file storage

    Args:
        chapter_id: Chapter ID
        user_id: User ID for access control

    Returns:
        str: Chapter content or empty string if not found
    """
    try:
        # Connect to PostgreSQL database
        pg_conn = get_pg_connection()
        if not pg_conn:
            return ""

        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get chapter with content information
        pg_cursor.execute("""
            SELECT c.content, c.content_file_path, c.content_size, s.user_id
            FROM chapters c
            JOIN subjects s ON c.subject_id = s.id
            WHERE c.id = %s AND s.user_id = %s
        """, (chapter_id, user_id))

        chapter_result = pg_cursor.fetchone()
        pg_cursor.close()
        pg_conn.close()

        if not chapter_result:
            return ""

        # Check if content is stored directly in database
        if chapter_result['content'] and chapter_result['content'].strip():
            return chapter_result['content']

        # Check if content is stored in file
        if chapter_result['content_file_path'] and os.path.exists(chapter_result['content_file_path']):
            try:
                with open(chapter_result['content_file_path'], 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('content', '')
            except Exception as e:
                logging.error(f"Error reading content file {chapter_result['content_file_path']}: {str(e)}")
                return ""

        return ""

    except Exception as e:
        logging.error(f"Error retrieving chapter content: {str(e)}")
        return ""


@app.before_request
def test_database_connection():
    """
    Test the database connection on the first request.
    """
    global first_request
    if first_request:
        try:
            db.session.execute(db.text("SELECT 1")).scalar_one()
            logging.info("Database connection successful!")
        except Exception as e:
            logging.error(f"Error connecting to the database: {e}")
            # Optionally, halt the app if the database connection fails
            # return jsonify({'error': 'Failed to connect to the database'}), 500
        first_request = False
    return None


@app.route('/upload_curriculum', methods=['POST'])
@authenticate_token
def upload_curriculum(current_user_id):
    """
    Upload a curriculum file and save it to the database.
    """
    if 'file' not in request.files:
        logging.warning("No file part in the upload request.")
        return jsonify({'success': False, 'error': 'No file part'}), 400
    file = request.files['file']
    user_id = request.form.get('user_id')

    if file.filename == '':
        logging.warning("No selected file in the upload request.")
        return jsonify({'success': False, 'error': 'No selected file'}), 400

    # Verify the user_id matches the authenticated user
    if int(user_id) != current_user_id:
        logging.warning(f"Access denied: User {current_user_id} attempted to upload for user {user_id}")
        return jsonify({'success': False, 'error': 'Access denied'}), 403

    if file and user_id:
        try:
            # Read and decode the file content
            content = file.read().decode('utf-8')
            new_curriculum = Curriculum(user_id=user_id, title=file.filename, content=content)
            db.session.add(new_curriculum)
            db.session.commit()
            logging.info(f"User {user_id} uploaded curriculum: {file.filename} (ID: {new_curriculum.id})")

            # Automatically process the curriculum after upload
            process_curriculum(new_curriculum.id)

            return jsonify({
                'message': 'Curriculum uploaded successfully',
                'curriculum_id': new_curriculum.id
            }), 201
        except Exception as e:
            db.session.rollback()
            logging.error(f"Error uploading file for user {user_id}: {str(e)}")
            return jsonify({'error': f'Error uploading file: {str(e)}'}), 500
    logging.error(f"Invalid upload request. File: {file}, User ID: {user_id}")
    return jsonify({'error': 'Invalid request'}), 400


@app.route('/api/process_curriculum/<int:curriculum_id>', methods=['POST'])
@authenticate_token
def process_curriculum(curriculum_id, current_user_id):
    curriculum = Curriculum.query.get(curriculum_id)
    if not curriculum:
        return jsonify({'success': False, 'error': 'Curriculum not found'}), 404

    # Verify the curriculum belongs to the authenticated user
    if curriculum.user_id != current_user_id:
        logging.warning(f"Access denied: User {current_user_id} attempted to process curriculum {curriculum_id}")
        return jsonify({'success': False, 'error': 'Access denied'}), 403

    try:
        # Call Gemini API
        extracted_data = process_curriculum_content(curriculum.content)

        if isinstance(extracted_data, str):
            return jsonify({'error': extracted_data}), 500

        # Save to database (optimized)
        for subject_data in extracted_data.get('subjects', []):
            subject = Subject(curriculum_id=curriculum_id, name=subject_data['name'])
            db.session.add(subject)
            db.session.commit()

            for chapter_data in subject_data.get('chapters', []):
                # Format chapter_number as "Chapter X" instead of just the number
                chapter_number = chapter_data.get('chapter_number', None)
                formatted_chapter_number = f"Chapter {chapter_number}" if chapter_number is not None else None

                chapter = Chapter(
                    subject_id=subject.id,
                    name=chapter_data['name'],
                    chapter_number=formatted_chapter_number
                )
                db.session.add(chapter)
                db.session.commit()

                for objective_text in chapter_data.get('objectives', []):
                    objective = Objective(chapter_id=chapter.id, text=objective_text)
                    db.session.add(objective)
            db.session.commit()

        return jsonify({'success': True, 'message': 'Curriculum processed successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/get_subjects/<int:user_id>', methods=['GET'])
@authenticate_token
def get_subjects(user_id, current_user_id):
    """
    Fetch all subjects, chapters, and objectives for a given user.
    """
    # Verify the requested user_id matches the authenticated user
    if user_id != current_user_id:
        logging.warning(f"Access denied: User {current_user_id} attempted to access data for user {user_id}")
        return jsonify({'success': False, 'error': 'Access denied'}), 403

    user = db.session.get(User, user_id)
    if not user:
        logging.warning(f"User not found with ID: {user_id}")
        return jsonify({'success': False, 'error': 'User not found'}), 404

    logging.info(f"Fetching subjects for user ID: {user_id}")
    curricula = db.session.query(Curriculum).filter_by(user_id=user_id).all()
    all_subjects_data = []
    for curriculum in curricula:
        subjects = db.session.query(Subject).filter_by(curriculum_id=curriculum.id).all()
        subjects_list = []
        for subject in subjects:
            chapters_list = []
            chapters = db.session.query(Chapter).filter_by(subject_id=subject.id).all()
            for chapter in chapters:
                objectives = db.session.query(Objective).filter_by(chapter_id=chapter.id).all()
                chapters_list.append({
                    'id': chapter.id,
                    'name': chapter.name,
                    'chapter_number': chapter.chapter_number,
                    'objectives': [{'id': obj.id, 'text': obj.text} for obj in objectives]
                })
            subjects_list.append({
                'id': subject.id,
                'name': subject.name,
                'chapters': chapters_list
            })
        all_subjects_data.append({
            'curriculum_id': curriculum.id,
            'curriculum_title': curriculum.title,
            'subjects': subjects_list
        })

    logging.info(f"Successfully fetched subjects for user ID: {user_id}. Found {len(all_subjects_data)} curricula.")
    return jsonify({'success': True, 'curricula_data': all_subjects_data}), 200


@app.route('/upload_document', methods=['POST'])
@app.route('/api/files/upload', methods=['POST'])  # Added API route for frontend compatibility
@authenticate_token
@limiter.limit("10/hour")  # Rate limit to prevent abuse
def upload_document(current_user_id):
    """
    Upload a document (PDF, DOCX, TXT) and process it with Gemini AI
    """
    logging.info(f"Document upload request from user ID: {current_user_id}")

    # Validate request
    if 'file' not in request.files:
        logging.warning(f"No file part in upload request from user ID: {current_user_id}")
        return jsonify({'error': 'No file part', 'success': False}), 400

    file = request.files['file']

    # Validate file is present
    if file.filename == '':
        logging.warning(f"Empty filename in upload request from user ID: {current_user_id}")
        return jsonify({'error': 'No file selected', 'success': False}), 400

    # Validate subject_id
    subject_id = request.form.get('subject_id')
    if not subject_id:
        logging.warning(f"No subject_id in upload request from user ID: {current_user_id}")
        return jsonify({'error': 'No subject_id provided', 'success': False}), 400

    # Check if study tools were selected
    study_tools = []
    if request.form.get('study_tools'):
        try:
            study_tools_str = request.form.get('study_tools')
            print(f"Raw study_tools value: {study_tools_str}")
            study_tools = json.loads(study_tools_str)
            print(f"Parsed study tools: {study_tools}")
            logging.info(f"Selected study tools: {study_tools}")

            # Check if flashcards tool is included
            has_flashcards = False
            for tool in study_tools:
                print(f"Checking tool: {tool}")
                tool_str = str(tool).lower()
                # Check for various possible flashcard identifiers
                if (tool_str == 'flashcards' or tool_str == 'flashcard' or
                    tool_str == '1' or tool_str == '9' or  # Check for both ID 1 and ID 9
                    'flash' in tool_str or 'card' in tool_str):  # More flexible matching
                    has_flashcards = True
                    print(f"Flashcards tool detected with value: {tool_str}!")
                    break

            if has_flashcards:
                print("Flashcards will be generated during processing")
            else:
                print("Flashcards tool not selected")
        except json.JSONDecodeError as e:
            print(f"Error parsing study_tools: {e}")
            print(f"Raw study_tools value: {request.form.get('study_tools')}")
            logging.warning(f"Invalid study_tools format in upload request from user ID: {current_user_id}")
            # Continue without study tools if format is invalid
            pass

    try:
        subject_id = int(subject_id)
    except ValueError:
        logging.warning(f"Invalid subject_id format in upload request from user ID: {current_user_id}")
        return jsonify({'error': 'Invalid subject_id format', 'success': False}), 400

    # Validate file type
    allowed_extensions = {'pdf', 'docx', 'doc', 'txt'}
    file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

    if file_extension not in allowed_extensions:
        logging.warning(f"Invalid file type: {file_extension} from user ID: {current_user_id}")
        return jsonify({
            'error': f'Invalid file type. Allowed types: {", ".join(allowed_extensions)}',
            'success': False
        }), 400

    # Secure the filename to prevent path traversal attacks
    secure_name = secure_filename(file.filename)

    # Generate a unique filename to prevent overwriting
    timestamp = datetime.now(datetime.timezone.utc).strftime('%Y%m%d%H%M%S')
    unique_filename = f"{timestamp}_{secure_name}"

    logging.info(f"Processing file: {unique_filename}, Subject ID: {subject_id}")

    # Get the subject to verify ownership
    try:
        subject = Subject.query.get(subject_id)
        print(f"Subject query result: {subject}")

        if not subject:
            print(f"Subject not found with ID: {subject_id}")
            logging.warning(f"Subject not found with ID: {subject_id}")

            # Check if the subject exists in the database
            all_subjects = Subject.query.all()
            print(f"All subjects in database: {all_subjects}")

            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Get the curriculum to verify ownership
        curriculum = Curriculum.query.get(subject.curriculum_id)
        print(f"Curriculum query result: {curriculum}")

        if not curriculum:
            print(f"Curriculum not found for subject ID: {subject_id}")
            logging.warning(f"Curriculum not found for subject ID: {subject_id}")
            return jsonify({'success': False, 'error': 'Curriculum not found'}), 404

        # Verify the curriculum belongs to the authenticated user
        if curriculum.user_id != current_user_id:
            print(f"Access denied: User {current_user_id} attempted to upload for curriculum {curriculum.id}")
            logging.warning(f"Access denied: User {current_user_id} attempted to upload for curriculum {curriculum.id}")
            return jsonify({'success': False, 'error': 'Access denied'}), 403
    except Exception as e:
        print(f"Error querying database: {str(e)}")
        logging.error(f"Error querying database: {str(e)}")
        return jsonify({'success': False, 'error': f'Database error: {str(e)}'}), 500

    if file.filename == '':
        print("No selected file in the upload request.")
        logging.warning("No selected file in the upload request.")
        return jsonify({'error': 'No selected file', 'success': False}), 400

    if file and subject_id:
        try:
            # Extract text from the document
            print(f"Extracting text from {file.filename}...")
            content = extract_text_from_document(file)

            # Log the file type and size
            file_type = file.filename.split('.')[-1].lower()
            print(f"Processing {file_type} file: {file.filename}, content length: {len(content)} characters")
            print(f"First 200 characters of content: {content[:200]}...")
            logging.info(f"Processing {file_type} file: {file.filename}, content length: {len(content)} characters")

            # First filter the content
            print("Filtering document content...")
            filtered_content = filter_document_content(content)
            print(f"Filtered content length: {len(filtered_content)} characters (removed {len(content) - len(filtered_content)} characters)")

            # Process the document with Intelligent Content Splitter for complete content extraction
            print("Processing document with Intelligent Content Splitter...")
            from document_processor import intelligent_content_splitter
            extracted_data = intelligent_content_splitter(filtered_content)
            print(f"Intelligent Content Splitter response: {json.dumps(extracted_data, indent=2)[:500]}...")

            if isinstance(extracted_data, str):
                print(f"Error from Gemini AI: {extracted_data}")
                return jsonify({'error': extracted_data}), 500

            # Save chapters and objectives to both databases
            chapters_data = []

            # Begin transaction for SQLAlchemy database
            print("\n\n==== SAVING EXTRACTED DATA TO DATABASES ====")
            print("Beginning database transaction...")
            db.session.begin()

            # Begin transaction for PostgreSQL database
            pg_cursor = None
            if pg_conn:
                try:
                    pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
                    print("Connected to frontend PostgreSQL database")
                    print("PostgreSQL connection details:")
                    print(f"- Database: {pg_conn.info.dbname}")
                    print(f"- User: {pg_conn.info.user}")
                    print(f"- Host: {pg_conn.info.host}")
                    print(f"- Port: {pg_conn.info.port}")
                    print(f"- Server version: {pg_conn.info.server_version}")
                except Exception as pg_conn_error:
                    print(f"Error connecting to frontend PostgreSQL database: {str(pg_conn_error)}")
                    print(f"Exception type: {type(pg_conn_error).__name__}")
                    print(f"Exception traceback: {traceback.format_exc()}")
                    logging.error(f"Error connecting to frontend PostgreSQL database: {str(pg_conn_error)}")
                    logging.error(f"Exception traceback: {traceback.format_exc()}")

            try:
                # Use the first subject from the extracted data or the subject name from the database
                subject_name = extracted_data.get('subjects', [{}])[0].get('name', subject.name)

                # Update the subject name if it's different
                if subject_name != subject.name:
                    print(f"Updating subject name from '{subject.name}' to '{subject_name}'")
                    logging.info(f"Updating subject name from '{subject.name}' to '{subject_name}'")
                    subject.name = subject_name
                    db.session.add(subject)

                # Log the number of chapters found
                chapters_count = len(extracted_data.get('subjects', [{}])[0].get('chapters', []))
                print(f"Found {chapters_count} chapters in the document")
                logging.info(f"Found {chapters_count} chapters in the document")

                # Process all chapters from the first subject
                for chapter_data in extracted_data.get('subjects', [{}])[0].get('chapters', []):
                    # Create new chapter
                    chapter_name = chapter_data['name']
                    print(f"Creating chapter: {chapter_name}")
                    logging.info(f"Creating chapter: {chapter_name}")

                    # Extract chapter number and title from chapter data
                    chapter_number = chapter_data.get('chapter_number', None)
                    chapter_title = chapter_data.get('title', None)

                    # If title is not provided, try to extract it from the name
                    if not chapter_title and chapter_name:
                        # Try to extract title from name (e.g., "Chapter 3: Introduction" -> "Introduction")
                        title_match = re.search(r'chapter\s+\d+\s*:\s*(.+)', chapter_name, re.IGNORECASE)
                        if title_match:
                            chapter_title = title_match.group(1).strip()
                        else:
                            # If no pattern match, use the full name as title
                            chapter_title = chapter_name

                    print(f"Chapter details for SQLAlchemy: Number={chapter_number}, Title={chapter_title}, Name={chapter_name}")

                    # Format chapter_number as "Chapter X" instead of just the number
                    formatted_chapter_number = f"Chapter {chapter_number}" if chapter_number is not None else None

                    # Check if chapter already exists in SQLAlchemy database
                    existing_chapter = Chapter.query.filter(
                        (Chapter.subject_id == subject.id) &
                        ((Chapter.title == chapter_title) | (Chapter.chapter_number == formatted_chapter_number))
                    ).first()

                    if existing_chapter:
                        # Use existing chapter
                        chapter = existing_chapter
                        print(f"Found existing chapter with ID: {chapter.id} in SQLAlchemy database")
                    else:
                        # Create chapter in SQLAlchemy database with enhanced content storage
                        chapter_content = chapter_data.get('content', f"Content for {chapter_name}")
                        chapter = Chapter(
                            subject_id=subject.id,
                            name=chapter_name,
                            chapter_number=formatted_chapter_number,
                            title=chapter_title
                        )

                        # Use the enhanced content storage system
                        chapter.set_content(chapter_content, current_user_id)

                        db.session.add(chapter)
                        db.session.flush()  # Get the chapter ID
                        print(f"Created chapter with ID: {chapter.id} in SQLAlchemy database")
                        print(f"Content storage: {'Database' if chapter.content else 'File'} ({len(chapter_content)} characters)")

                    # Create chapter in PostgreSQL database
                    if pg_cursor:
                        try:
                            # First, check if the file exists in the files table
                            print(f"\n--- Checking for files with subject_id = {subject_id} in PostgreSQL database ---")
                            pg_cursor.execute(
                                "SELECT id FROM files WHERE subject_id = %s LIMIT 1",
                                (subject_id,)
                            )
                            file_row = pg_cursor.fetchone()

                            if file_row:
                                file_id = file_row['id']
                                print(f"Found file with ID: {file_id} for subject ID: {subject_id}")

                                # Check if chapter already exists in PostgreSQL database
                                print(f"Checking if chapter '{chapter_name}' already exists in PostgreSQL database...")
                                pg_cursor.execute(
                                    "SELECT id FROM chapters WHERE subject_id = %s AND (title = %s OR chapter_number = %s)",
                                    (subject_id, chapter_title, formatted_chapter_number)
                                )
                                existing_chapter = pg_cursor.fetchone()

                                if existing_chapter:
                                    # Use existing chapter
                                    pg_chapter_id = existing_chapter['id']
                                    print(f"Found existing chapter with ID: {pg_chapter_id} in PostgreSQL database")
                                else:
                                    # Insert chapter into PostgreSQL database with enhanced content storage
                                    print(f"Inserting chapter '{chapter_name}' into PostgreSQL database...")
                                    chapter_content = chapter_data.get('content', f"Content for {chapter_name}")

                                    # Determine storage method based on content size
                                    content_size = len(chapter_content)
                                    FILE_STORAGE_THRESHOLD = 50000  # 50KB threshold

                                    if content_size <= FILE_STORAGE_THRESHOLD:
                                        # Store content directly in database
                                        insert_query = "INSERT INTO chapters (file_id, subject_id, chapter_number, title, content, content_size, created_at) VALUES (%s, %s, %s, %s, %s, %s, NOW()) RETURNING id"
                                        insert_params = (file_id, subject_id, formatted_chapter_number, chapter_title, chapter_content, content_size)
                                        print(f"Storing content in database ({content_size} characters)")
                                    else:
                                        # Store content in file and reference in database
                                        import json
                                        import os
                                        from datetime import datetime

                                        # Create content directory
                                        content_dir = os.path.join(os.path.dirname(__file__), 'chapter_content', str(current_user_id))
                                        os.makedirs(content_dir, exist_ok=True)

                                        # Generate filename
                                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                        filename = f"chapter_{subject_id}_{timestamp}.json"
                                        file_path = os.path.join(content_dir, filename)

                                        # Save content to file
                                        content_data = {
                                            'subject_id': subject_id,
                                            'title': chapter_title,
                                            'content': chapter_content,
                                            'created_at': datetime.now().isoformat(),
                                            'size': content_size
                                        }

                                        with open(file_path, 'w', encoding='utf-8') as f:
                                            json.dump(content_data, f, ensure_ascii=False, indent=2)

                                        # Store file reference in database
                                        insert_query = "INSERT INTO chapters (file_id, subject_id, chapter_number, title, content_file_path, content_size, created_at) VALUES (%s, %s, %s, %s, %s, %s, NOW()) RETURNING id"
                                        insert_params = (file_id, subject_id, formatted_chapter_number, chapter_title, file_path, content_size)
                                        print(f"Storing content in file: {file_path} ({content_size} characters)")

                                    print(f"SQL Query: {insert_query}")
                                    chapter_number = chapter_data.get('chapter_number', None)
                                    print(f"Parameters: {insert_params}")

                                    # Extract chapter number and title from chapter data
                                    chapter_number = chapter_data.get('chapter_number', None)
                                    chapter_title = chapter_data.get('title', None)

                                    # If title is not provided, try to extract it from the name
                                    if not chapter_title and chapter_name:
                                        # Try to extract title from name (e.g., "Chapter 3: Introduction" -> "Introduction")
                                        title_match = re.search(r'chapter\s+\d+\s*:\s*(.+)', chapter_name, re.IGNORECASE)
                                        if title_match:
                                            chapter_title = title_match.group(1).strip()
                                        else:
                                            # If no pattern match, use the full name as title
                                            chapter_title = chapter_name

                                    print(f"Chapter details: Number={chapter_number}, Title={chapter_title}, Name={chapter_name}")

                                    # Format chapter_number as "Chapter X" for PostgreSQL database
                                    formatted_chapter_number = f"Chapter {chapter_number}" if chapter_number is not None else None

                                    pg_cursor.execute(insert_query, insert_params)
                                    pg_chapter_id = pg_cursor.fetchone()['id']
                                    print(f"Successfully created chapter with ID: {pg_chapter_id} in PostgreSQL database")
                            else:
                                print(f"No file found for subject ID: {subject_id} in PostgreSQL database")
                                print(f"Checking all files in the database...")
                                pg_cursor.execute("SELECT id, subject_id FROM files")
                                all_files = pg_cursor.fetchall()
                                print(f"Found {len(all_files)} files in the database:")
                                for file in all_files:
                                    print(f"  - File ID: {file['id']}, Subject ID: {file['subject_id']}")

                                # Try to create a file entry
                                print(f"Creating a new file entry for subject ID: {subject_id}")
                                try:
                                    pg_cursor.execute(
                                        "INSERT INTO files (subject_id, filename, originalname, filetype, filepath, uploaded_at) VALUES (%s, %s, %s, %s, %s, NOW()) RETURNING id",
                                        (subject_id, "auto_created.txt", "auto_created.txt", "text/plain", "/auto_created.txt")
                                    )
                                    file_id = pg_cursor.fetchone()['id']
                                    print(f"Created file with ID: {file_id} for subject ID: {subject_id}")

                                    # Now insert the chapter
                                    # Extract chapter number and title from chapter data
                                    chapter_number = chapter_data.get('chapter_number', None)
                                    chapter_title = chapter_data.get('title', None)

                                    # If title is not provided, try to extract it from the name
                                    if not chapter_title and chapter_name:
                                        # Try to extract title from name (e.g., "Chapter 3: Introduction" -> "Introduction")
                                        title_match = re.search(r'chapter\s+\d+\s*:\s*(.+)', chapter_name, re.IGNORECASE)
                                        if title_match:
                                            chapter_title = title_match.group(1).strip()
                                        else:
                                            # If no pattern match, use the full name as title
                                            chapter_title = chapter_name

                                    print(f"Chapter details: Number={chapter_number}, Title={chapter_title}, Name={chapter_name}")

                                    # Format chapter_number as "Chapter X" for PostgreSQL database
                                    formatted_chapter_number = f"Chapter {chapter_number}" if chapter_number is not None else None

                                    # Get the actual chapter content from the AI response and use enhanced storage
                                    chapter_content = chapter_data.get('content', f"Content for {chapter_name}")
                                    content_size = len(chapter_content)
                                    FILE_STORAGE_THRESHOLD = 50000  # 50KB threshold

                                    if content_size <= FILE_STORAGE_THRESHOLD:
                                        # Store content directly in database
                                        pg_cursor.execute(
                                            "INSERT INTO chapters (file_id, subject_id, chapter_number, title, content, content_size, created_at) VALUES (%s, %s, %s, %s, %s, %s, NOW()) RETURNING id",
                                            (file_id, subject_id, formatted_chapter_number, chapter_title, chapter_content, content_size)
                                        )
                                        print(f"Stored content in database ({content_size} characters)")
                                    else:
                                        # Store content in file and reference in database
                                        import json
                                        import os
                                        from datetime import datetime

                                        # Create content directory
                                        content_dir = os.path.join(os.path.dirname(__file__), 'chapter_content', str(current_user_id))
                                        os.makedirs(content_dir, exist_ok=True)

                                        # Generate filename
                                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                        filename = f"chapter_{subject_id}_{timestamp}_fallback.json"
                                        file_path = os.path.join(content_dir, filename)

                                        # Save content to file
                                        content_data = {
                                            'subject_id': subject_id,
                                            'title': chapter_title,
                                            'content': chapter_content,
                                            'created_at': datetime.now().isoformat(),
                                            'size': content_size
                                        }

                                        with open(file_path, 'w', encoding='utf-8') as f:
                                            json.dump(content_data, f, ensure_ascii=False, indent=2)

                                        # Store file reference in database
                                        pg_cursor.execute(
                                            "INSERT INTO chapters (file_id, subject_id, chapter_number, title, content_file_path, content_size, created_at) VALUES (%s, %s, %s, %s, %s, %s, NOW()) RETURNING id",
                                            (file_id, subject_id, formatted_chapter_number, chapter_title, file_path, content_size)
                                        )
                                        print(f"Stored content in file: {file_path} ({content_size} characters)")
                                    pg_chapter_id = pg_cursor.fetchone()['id']
                                    print(f"Created chapter with ID: {pg_chapter_id} in PostgreSQL database")
                                except Exception as file_error:
                                    print(f"Error creating file entry: {str(file_error)}")
                                    pg_chapter_id = None
                        except Exception as pg_error:
                            print(f"Error creating chapter in PostgreSQL database: {str(pg_error)}")
                            print(f"Exception type: {type(pg_error).__name__}")
                            print(f"Exception traceback: {traceback.format_exc()}")
                            logging.error(f"Error creating chapter in PostgreSQL database: {str(pg_error)}")
                            logging.error(f"Exception traceback: {traceback.format_exc()}")
                            pg_chapter_id = None

                    chapter_objectives = []
                    objectives_count = len(chapter_data.get('objectives', []))
                    print(f"Found {objectives_count} objectives for chapter '{chapter_name}'")
                    logging.info(f"Found {objectives_count} objectives for chapter '{chapter_name}'")

                    # Create objectives for this chapter
                    for objective_data in chapter_data.get('objectives', []):
                        # Handle different formats of objective data
                        if isinstance(objective_data, dict):
                            objective_text = objective_data.get('text', '')
                            estimated_time = objective_data.get('estimated_time_minutes', 30)
                            difficulty = objective_data.get('difficulty_level', 1)
                        else:
                            # If it's just a string
                            objective_text = objective_data
                            estimated_time = 30
                            difficulty = 1

                        print(f"Creating objective: {objective_text[:50]}... (Time: {estimated_time} min, Difficulty: {difficulty})")
                        logging.info(f"Creating objective: {objective_text[:50]}... (Time: {estimated_time} min, Difficulty: {difficulty})")

                        # Check if objective already exists in SQLAlchemy database
                        existing_objective = Objective.query.filter(
                            (Objective.chapter_id == chapter.id) &
                            (Objective.objective == objective_text)
                        ).first()

                        if existing_objective:
                            # Use existing objective
                            objective = existing_objective
                            print(f"Found existing objective with ID: {objective.id} in SQLAlchemy database")
                        else:
                            # Create new objective in SQLAlchemy database
                            objective = Objective(
                                chapter_id=chapter.id,
                                objective=objective_text,  # Use 'objective' field instead of 'text'
                                estimated_time_minutes=estimated_time,
                                difficulty_level=difficulty
                            )
                            db.session.add(objective)

                        # Create objective in PostgreSQL database
                        if pg_cursor and pg_chapter_id:
                            try:
                                print(f"\n--- Creating objective in PostgreSQL database ---")
                                # Get today's date for display_date
                                today = datetime.now().date().isoformat()

                                insert_query = "INSERT INTO objectives (chapter_id, objective, completed, display_date, created_at) VALUES (%s, %s, %s, %s, NOW()) RETURNING id"
                                print(f"SQL Query: {insert_query}")
                                print(f"Parameters: ({pg_chapter_id}, {objective_text[:50]}..., False, {today})")

                                # Check if objective already exists in PostgreSQL database
                                pg_cursor.execute(
                                    "SELECT id FROM objectives WHERE chapter_id = %s AND objective = %s",
                                    (pg_chapter_id, objective_text)
                                )
                                existing_objective = pg_cursor.fetchone()

                                if existing_objective:
                                    # Use existing objective
                                    pg_objective_id = existing_objective['id']
                                    print(f"Found existing objective with ID: {pg_objective_id} in PostgreSQL database")
                                else:
                                    # Create new objective
                                    pg_cursor.execute(
                                        insert_query,
                                        (pg_chapter_id, objective_text, False, today)
                                    )
                                    pg_objective_id = pg_cursor.fetchone()['id']
                                    print(f"Successfully created objective with ID: {pg_objective_id} in PostgreSQL database for chapter ID: {pg_chapter_id}")
                            except Exception as pg_error:
                                print(f"Error creating objective in PostgreSQL database: {str(pg_error)}")
                                print(f"Exception type: {type(pg_error).__name__}")
                                print(f"Exception traceback: {traceback.format_exc()}")
                                logging.error(f"Error creating objective in PostgreSQL database: {str(pg_error)}")
                                logging.error(f"Exception traceback: {traceback.format_exc()}")

                        # Add to chapter objectives
                        chapter_objectives.append({
                            'text': objective_text,
                            'estimated_time_minutes': estimated_time,
                            'difficulty_level': difficulty
                        })

                    # Add chapter data for response
                    chapters_data.append({
                        'name': chapter.name,
                        'objectives': chapter_objectives
                    })

                # Commit all changes to SQLAlchemy database
                print("\n\n==== COMMITTING CHANGES TO DATABASES ====")
                print("Committing changes to SQLAlchemy database...")
                logging.info("Committing changes to SQLAlchemy database")
                db.session.commit()
                print("Successfully committed changes to SQLAlchemy database")
                logging.info("Successfully committed changes to SQLAlchemy database")

                # Commit all changes to PostgreSQL database
                if pg_cursor:
                    try:
                        print("Committing changes to PostgreSQL database...")
                        pg_conn.commit()
                        print("Successfully committed changes to PostgreSQL database")
                        logging.info("Successfully committed changes to PostgreSQL database")
                    except Exception as pg_error:
                        print(f"Error committing changes to PostgreSQL database: {str(pg_error)}")
                        print(f"Exception type: {type(pg_error).__name__}")
                        print(f"Exception traceback: {traceback.format_exc()}")
                        pg_conn.rollback()
                        print("Rolled back PostgreSQL transaction")
                        logging.error(f"Error committing changes to PostgreSQL database: {str(pg_error)}")
                        logging.error(f"Exception traceback: {traceback.format_exc()}")

                # Verify that the data was saved in SQLAlchemy database
                print("Verifying data was saved in SQLAlchemy database...")
                saved_chapters = Chapter.query.filter_by(subject_id=subject.id).all()
                print(f"Found {len(saved_chapters)} saved chapters in SQLAlchemy database")

                for saved_chapter in saved_chapters:
                    saved_objectives = Objective.query.filter_by(chapter_id=saved_chapter.id).all()
                    print(f"Chapter '{saved_chapter.name}' has {len(saved_objectives)} saved objectives in SQLAlchemy database")

                # Generate flashcards if the flashcard tool was selected
                generated_flashcards = []
                # Get all study tools from the database to map IDs to names
                tool_id_map = {}
                try:
                    # Create a new cursor for this query
                    with pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as tool_cursor:
                        # Get all study tools to map IDs to names
                        tool_cursor.execute("SELECT id, name FROM study_tools")
                        all_tools = tool_cursor.fetchall()
                        tool_id_map = {str(t['id']): t['name'].lower() for t in all_tools}
                        print(f"Tool ID map: {tool_id_map}")

                        # Find the flashcard tool ID
                        flashcard_tool_id = None
                        for tool_id, tool_name in tool_id_map.items():
                            if 'flash' in tool_name.lower() and 'card' in tool_name.lower():
                                flashcard_tool_id = tool_id
                                print(f"Found Flashcard tool in database with ID: {flashcard_tool_id}")
                                break

                        if not flashcard_tool_id:
                            print("Warning: Flashcard tool not found in study_tools table")
                except Exception as e:
                    print(f"Error fetching study tools: {str(e)}")
                    print(f"Exception type: {type(e).__name__}")
                    print(f"Exception traceback: {traceback.format_exc()}")

                # Check for flashcards in study_tools (could be ID or name)
                has_flashcard_tool = False
                for tool in study_tools:
                    tool_str = str(tool).lower()

                    # Get the tool name if this is an ID
                    tool_name = tool_id_map.get(tool_str, '').lower() if tool_id_map else ''

                    # Check if this tool is a flashcard tool
                    if tool_name and ('flash' in tool_name and 'card' in tool_name):
                        print(f"Flashcard tool detected with ID: {tool_str} (name: {tool_name})")
                        has_flashcard_tool = True
                        break

                    # Fallback checks for direct name or ID matches
                    if (tool_str == 'flashcards' or tool_str == 'flashcard' or
                        (flashcard_tool_id and tool_str == flashcard_tool_id) or
                        'flash' in tool_str and 'card' in tool_str):
                        print(f"Flashcard tool detected with value: {tool_str}")
                        has_flashcard_tool = True
                        break

                print(f"Flashcard tool selected: {has_flashcard_tool}")
                if has_flashcard_tool:
                    print("\n\n==== GENERATING FLASHCARDS WITH AI ====")
                    logging.info(f"Generating flashcards for subject ID: {subject_id}")

                    # Prepare chapter data for flashcard generation
                    chapters_for_flashcards = []
                    for chapter in saved_chapters:
                        chapters_for_flashcards.append({
                            'id': chapter.id,
                            'chapter_number': chapter.chapter_number.replace('Chapter ', '') if chapter.chapter_number else '1',
                            'title': chapter.title
                        })

                    # Generate flashcards using AI
                    flashcards = generate_flashcards_with_ai(content, subject_id, chapters_for_flashcards)

                    # Save flashcards to PostgreSQL database
                    if pg_cursor and flashcards:
                        try:
                            print(f"Saving {len(flashcards)} flashcards to PostgreSQL database...")
                            for i, flashcard in enumerate(flashcards):
                                # Convert tags list to string
                                tags_str = ','.join(flashcard.get('tags', [])) if flashcard.get('tags') else None

                                # Debug info for each flashcard
                                print(f"\nFlashcard #{i+1}:")
                                print(f"  Chapter ID: {flashcard.get('chapter_id')}")
                                print(f"  Question: {flashcard.get('question')[:50]}..." if flashcard.get('question') else "  Question: None")
                                print(f"  Answer: {flashcard.get('answer')[:50]}..." if flashcard.get('answer') else "  Answer: None")
                                print(f"  Type: {flashcard.get('type', 'basic')}")
                                print(f"  Tags: {tags_str}")

                                # Insert flashcard into PostgreSQL database
                                try:
                                    pg_cursor.execute(
                                        """
                                        INSERT INTO flashcards (
                                            user_id, chapter_id, subject_id, question, answer,
                                            type, box_level, color, difficult, tags, ai_generated, created_at
                                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                                        RETURNING id
                                        """,
                                        (
                                            current_user_id,
                                            flashcard.get('chapter_id'),
                                            subject_id,
                                            flashcard.get('question'),
                                            flashcard.get('answer'),
                                            flashcard.get('type', 'basic'),
                                            1,  # Default box_level
                                            '#ffffff',  # Default color
                                            False,  # Default difficult
                                            tags_str,
                                            True,  # AI generated
                                        )
                                    )
                                    flashcard_id = pg_cursor.fetchone()['id']
                                    generated_flashcards.append(flashcard_id)
                                    print(f"  Successfully saved flashcard with ID: {flashcard_id}")

                                    # Add the flashcard tool to the chapter_tools table if it doesn't exist
                                    try:
                                        chapter_id = flashcard.get('chapter_id')
                                        if chapter_id:
                                            # Use the flashcard tool ID we fetched earlier
                                            if not flashcard_tool_id:
                                                # If we don't have it yet, fetch it now
                                                pg_cursor.execute(
                                                    """
                                                    SELECT id FROM study_tools
                                                    WHERE name LIKE '%Flash%Card%' OR name LIKE '%Flashcard%'
                                                    LIMIT 1
                                                    """
                                                )
                                                flashcard_tool = pg_cursor.fetchone()
                                                if flashcard_tool:
                                                    flashcard_tool_id_int = flashcard_tool['id']
                                                    print(f"  Found Flashcard tool with ID: {flashcard_tool_id_int}")
                                                else:
                                                    print("  Warning: Flashcard tool not found in study_tools table")
                                                    # Default to ID 13 if not found
                                                    flashcard_tool_id_int = 13
                                            else:
                                                # Convert string ID to integer
                                                flashcard_tool_id_int = int(flashcard_tool_id)

                                            # Check if the flashcard tool is already associated with this chapter
                                            pg_cursor.execute(
                                                """
                                                SELECT id FROM chapter_tools
                                                WHERE chapter_id = %s AND tool_id = %s
                                                """,
                                                (chapter_id, flashcard_tool_id_int)
                                            )
                                            existing_tool = pg_cursor.fetchone()

                                            if not existing_tool:
                                                print(f"  Adding flashcard tool to chapter_tools for chapter ID: {chapter_id}")
                                                pg_cursor.execute(
                                                    """
                                                    INSERT INTO chapter_tools (chapter_id, tool_id, settings)
                                                    VALUES (%s, %s, %s)
                                                    RETURNING id
                                                    """,
                                                    (
                                                        chapter_id,
                                                        flashcard_tool_id_int,  # Dynamic Flashcard tool ID
                                                        '{}'  # Default empty settings
                                                    )
                                                )
                                                tool_id = pg_cursor.fetchone()['id']
                                                print(f"  Successfully added flashcard tool to chapter_tools with ID: {tool_id}")
                                    except Exception as tool_error:
                                        print(f"  Error adding flashcard tool to chapter_tools: {str(tool_error)}")
                                        print(f"  Exception type: {type(tool_error).__name__}")
                                        print(f"  Exception traceback: {traceback.format_exc()}")
                                        # Continue with next flashcard
                                except Exception as insert_error:
                                    print(f"  Error inserting flashcard: {str(insert_error)}")
                                    print(f"  Exception type: {type(insert_error).__name__}")
                                    print(f"  Exception traceback: {traceback.format_exc()}")
                                    # Continue with next flashcard

                            # Commit changes to PostgreSQL database
                            pg_conn.commit()
                            print(f"Successfully saved {len(generated_flashcards)} flashcards to PostgreSQL database")
                            logging.info(f"Successfully saved {len(generated_flashcards)} flashcards to PostgreSQL database")
                        except Exception as pg_error:
                            print(f"Error saving flashcards to PostgreSQL database: {str(pg_error)}")
                            print(f"Exception type: {type(pg_error).__name__}")
                            print(f"Exception traceback: {traceback.format_exc()}")
                            logging.error(f"Error saving flashcards to PostgreSQL database: {str(pg_error)}")
                            logging.error(f"Exception traceback: {traceback.format_exc()}")
                            pg_conn.rollback()

                return jsonify({
                    'success': True,
                    'message': 'Document processed successfully',
                    'file': {
                        'id': file.filename,
                        'name': file.filename,
                        'type': file_type
                    },
                    'subject': {
                        'id': subject.id,
                        'name': subject.name
                    },
                    'chapters': chapters_data,
                    'flashcards_generated': len(generated_flashcards) if has_flashcard_tool else 0
                }), 201

            except Exception as inner_e:
                # Rollback SQLAlchemy transaction
                db.session.rollback()

                # Rollback PostgreSQL transaction if active
                if pg_cursor:
                    try:
                        pg_conn.rollback()
                        print("Rolled back PostgreSQL transaction")
                    except Exception as pg_error:
                        print(f"Error rolling back PostgreSQL transaction: {str(pg_error)}")

                print(f"Error saving extracted data: {str(inner_e)}")
                print(f"Exception type: {type(inner_e).__name__}")
                print(f"Exception traceback: {traceback.format_exc()}")
                logging.error(f"Error saving extracted data: {str(inner_e)}")
                logging.error(f"Exception type: {type(inner_e).__name__}")
                logging.error(f"Exception traceback: {traceback.format_exc()}")
                return jsonify({'success': False, 'error': f'Error saving extracted data: {str(inner_e)}'}), 500
            finally:
                # Close PostgreSQL cursor if open
                if pg_cursor:
                    pg_cursor.close()
                    print("Closed PostgreSQL cursor")

        except Exception as e:
            # Rollback SQLAlchemy transaction
            if 'db' in locals() and hasattr(db, 'session'):
                db.session.rollback()

            # Rollback PostgreSQL transaction if active
            if 'pg_cursor' in locals() and pg_cursor:
                try:
                    pg_conn.rollback()
                    pg_cursor.close()
                    print("Rolled back PostgreSQL transaction and closed cursor")
                except Exception as pg_error:
                    print(f"Error handling PostgreSQL connection: {str(pg_error)}")

            print(f"Error processing document for subject {subject_id}: {str(e)}")
            print(f"Exception traceback: {traceback.format_exc()}")
            logging.error(f"Error processing document for subject {subject_id}: {str(e)}")
            logging.error(f"Exception traceback: {traceback.format_exc()}")
            return jsonify({'success': False, 'error': f'Error processing document: {str(e)}'}), 500

    print(f"Invalid upload request. File: {file}, Subject ID: {subject_id}")
    logging.error(f"Invalid upload request. File: {file}, Subject ID: {subject_id}")
    return jsonify({'success': False, 'error': 'Invalid request'}), 400


@app.route('/api/process_document', methods=['POST', 'OPTIONS'])
def process_document():
    """
    Process a document that has already been uploaded to extract chapters and objectives
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = jsonify({'success': True})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response

    # For POST requests, authenticate the user
    if request.method == 'POST':
        # Get the token from the Authorization header
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(" ")[1]

        if not token:
            return jsonify({'success': False, 'error': 'Token is missing'}), 401

        try:
            # Decode with proper validation
            data = jwt.decode(
                token,
                app.config['JWT_SECRET_KEY'],
                algorithms=["HS256"],
                options={
                    'verify_signature': True,
                    'verify_exp': True,
                    'verify_nbf': True,
                    'verify_iat': True,
                    'require': ['exp', 'iat', 'userId']
                }
            )
            current_user_id = data['userId']
        except jwt.ExpiredSignatureError:
            logging.warning("Expired JWT token")
            return jsonify({
                'success': False,
                'error': 'Token has expired. Please log in again'
            }), 401
        except jwt.InvalidTokenError:
            logging.warning("Invalid JWT token")
            return jsonify({
                'success': False,
                'error': 'Invalid token. Please log in again'
            }), 401
        except Exception as e:
            logging.error(f"Token validation error: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'Authentication failed'
            }), 401

    # Print console message for debugging
    print("\n\n==== DOCUMENT PROCESSING REQUEST ====")
    print(f"User ID: {current_user_id}")

    data = request.json
    print(f"Request data: {json.dumps(data, indent=2)}")

    # Validate required fields
    if 'subject_id' not in data:
        print(f"Missing required field: subject_id")
        return jsonify({'success': False, 'error': f'Missing required field: subject_id'}), 400

    subject_id = data['subject_id']

    # Get the subject to verify ownership
    try:
        # Use PostgreSQL connection directly instead of SQLAlchemy
        if not pg_conn:
            logging.error("PostgreSQL connection not established")
            return jsonify({'success': False, 'error': 'Database connection error'}), 500

        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get the subject from PostgreSQL
        pg_cursor.execute(
            "SELECT id, name, user_id FROM subjects WHERE id = %s",
            (subject_id,)
        )
        subject = pg_cursor.fetchone()

        if not subject:
            print(f"Subject not found with ID: {subject_id}")
            logging.warning(f"Subject not found with ID: {subject_id}")
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Verify the subject belongs to the authenticated user
        if subject['user_id'] != current_user_id:
            print(f"Access denied: User {current_user_id} attempted to process document for subject {subject_id}")
            logging.warning(f"Access denied: User {current_user_id} attempted to process document for subject {subject_id}")
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Get the file for this subject
        pg_cursor.execute(
            "SELECT id, filename, originalname, filetype, filepath FROM files WHERE subject_id = %s ORDER BY uploaded_at DESC LIMIT 1",
            (subject_id,)
        )
        file_record = pg_cursor.fetchone()

        if not file_record:
            print(f"No file found for subject ID: {subject_id}")
            logging.warning(f"No file found for subject ID: {subject_id}")
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'No file found for this subject. Please upload a file first.'}), 400

        # Check if the file exists on disk
        file_path = file_record['filepath']

        # Try different paths for the file
        possible_paths = [
            file_path,  # Original path
            os.path.join('Frontend', file_path),  # Frontend/path
            os.path.join('..', 'Frontend', file_path),  # ../Frontend/path
            os.path.join('..', file_path),  # ../path
            file_path.replace('Uploads', 'Frontend/uploads'),  # Replace Uploads with Frontend/uploads
            file_path.replace('\\', '/'),  # Replace backslashes with forward slashes
            os.path.join('Frontend', 'uploads', os.path.basename(file_path))  # Frontend/uploads/filename
        ]

        # Find the first path that exists
        actual_path = None
        for path in possible_paths:
            print(f"Checking path: {path}")
            if os.path.exists(path):
                actual_path = path
                print(f"Found file at: {actual_path}")
                break

        if not actual_path:
            print(f"File not found on disk at any of these locations:")
            for path in possible_paths:
                print(f"  - {path}")

            # Return a helpful error message
            return jsonify({
                'success': False,
                'error': 'File not found on disk. Please upload the document again.'
            }), 400

        # Use the actual path for further processing
        file_path = actual_path

        # Extract text from the file
        try:
            print(f"Extracting text from file: {file_path}")

            # Read the file content based on file type
            content = ""
            file_type = file_record['filetype'].lower() if file_record['filetype'] else ""

            if "pdf" in file_type:
                # Extract text from PDF
                with open(file_path, 'rb') as pdf_file:
                    pdf_reader = PyPDF2.PdfReader(pdf_file)
                    num_pages = len(pdf_reader.pages)
                    print(f"PDF has {num_pages} pages")

                    for page_num in range(num_pages):
                        page_text = pdf_reader.pages[page_num].extract_text()
                        content += page_text + "\n\n"
                        print(f"Extracted {len(page_text)} characters from page {page_num+1}")

            elif "doc" in file_type:
                # Extract text from DOCX
                doc = docx.Document(file_path)
                para_count = len(doc.paragraphs)
                print(f"DOCX has {para_count} paragraphs")

                for para in doc.paragraphs:
                    if para.text.strip():  # Only add non-empty paragraphs
                        content += para.text + "\n"

                # Also extract text from tables
                for table in doc.tables:
                    for row in table.rows:
                        row_text = ""
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text += cell.text + " | "
                        if row_text:
                            content += row_text.rstrip(" | ") + "\n"

            elif "text" in file_type:
                # Read text file directly
                with open(file_path, 'r', encoding='utf-8') as text_file:
                    content = text_file.read()
                print(f"Extracted {len(content)} characters from text file")

            else:
                print(f"Unsupported file type: {file_type}")
                pg_cursor.close()
                return jsonify({'success': False, 'error': f'Unsupported file type: {file_type}'}), 400

            # Process the document with Gemini AI
            print(f"Processing document with Gemini AI...")
            extracted_data = process_document_with_gemini(content)
            print(f"Gemini AI response: {json.dumps(extracted_data, indent=2)[:500]}...")

            if isinstance(extracted_data, str):
                print(f"Error from Gemini AI: {extracted_data}")
                pg_cursor.close()
                return jsonify({'success': False, 'error': extracted_data}), 500

            # Begin transaction for PostgreSQL database
            print("\n\n==== SAVING EXTRACTED DATA TO DATABASE ====")

            # Process all chapters from the first subject
            chapters_data = []
            for chapter_data in extracted_data.get('subjects', [{}])[0].get('chapters', []):
                # Create new chapter
                chapter_name = chapter_data['name']
                print(f"Creating chapter: {chapter_name}")

                # Extract chapter number and title from chapter data
                chapter_number = chapter_data.get('chapter_number', None)
                chapter_title = chapter_data.get('title', None)

                # If title is not provided, try to extract it from the name
                if not chapter_title and chapter_name:
                    # Try to extract title from name (e.g., "Chapter 3: Introduction" -> "Introduction")
                    title_match = re.search(r'chapter\s+\d+\s*:\s*(.+)', chapter_name, re.IGNORECASE)
                    if title_match:
                        chapter_title = title_match.group(1).strip()
                    else:
                        # If no pattern match, use the full name as title
                        chapter_title = chapter_name

                print(f"Chapter details: Number={chapter_number}, Title={chapter_title}, Name={chapter_name}")

                # Format chapter_number as "Chapter X" for PostgreSQL database
                formatted_chapter_number = f"Chapter {chapter_number}" if chapter_number is not None else None

                # Check if chapter already exists in PostgreSQL database
                pg_cursor.execute(
                    "SELECT id FROM chapters WHERE subject_id = %s AND (title = %s OR chapter_number = %s)",
                    (subject_id, chapter_title, formatted_chapter_number)
                )
                existing_chapter = pg_cursor.fetchone()

                if existing_chapter:
                    # Use existing chapter
                    pg_chapter_id = existing_chapter['id']
                    print(f"Found existing chapter with ID: {pg_chapter_id} in PostgreSQL database")
                else:
                    # Insert chapter into PostgreSQL database
                    pg_cursor.execute(
                        "INSERT INTO chapters (file_id, subject_id, chapter_number, title, content, created_at) VALUES (%s, %s, %s, %s, %s, NOW()) RETURNING id",
                        (file_record['id'], subject_id, formatted_chapter_number, chapter_title, f"Content for {chapter_name}")
                    )
                    pg_chapter_id = pg_cursor.fetchone()['id']
                    print(f"Created chapter with ID: {pg_chapter_id} in PostgreSQL database")

                chapter_objectives = []
                objectives_count = len(chapter_data.get('objectives', []))
                print(f"Found {objectives_count} objectives for chapter '{chapter_name}'")

                # Create objectives for this chapter
                for objective_data in chapter_data.get('objectives', []):
                    # Handle different formats of objective data
                    if isinstance(objective_data, dict):
                        objective_text = objective_data.get('text', '')
                        estimated_time = objective_data.get('estimated_time_minutes', 30)
                        difficulty = objective_data.get('difficulty_level', 1)
                    else:
                        # If it's just a string
                        objective_text = objective_data
                        estimated_time = 30
                        difficulty = 1

                    print(f"Creating objective: {objective_text[:50]}... (Time: {estimated_time} min, Difficulty: {difficulty})")

                    # Check if objective already exists in PostgreSQL database
                    pg_cursor.execute(
                        "SELECT id FROM objectives WHERE chapter_id = %s AND objective = %s",
                        (pg_chapter_id, objective_text)
                    )
                    existing_objective = pg_cursor.fetchone()

                    if existing_objective:
                        # Use existing objective
                        pg_objective_id = existing_objective['id']
                        print(f"Found existing objective with ID: {pg_objective_id} in PostgreSQL database")
                    else:
                        # Create objective in PostgreSQL database
                        pg_cursor.execute(
                            "INSERT INTO objectives (chapter_id, objective, completed, created_at) VALUES (%s, %s, %s, NOW()) RETURNING id",
                            (pg_chapter_id, objective_text, False)
                        )
                        pg_objective_id = pg_cursor.fetchone()['id']
                        print(f"Created objective with ID: {pg_objective_id} in PostgreSQL database")

                    # Add to chapter objectives
                    chapter_objectives.append({
                        'id': pg_objective_id,
                        'text': objective_text,
                        'estimated_time_minutes': estimated_time,
                        'difficulty_level': difficulty
                    })

                # Add chapter data for response
                chapters_data.append({
                    'id': pg_chapter_id,
                    'name': chapter_name,
                    'objectives': chapter_objectives
                })

            # Check if study tools were selected from the request
            study_tools = []
            if 'study_tools' in data:
                try:
                    study_tools_str = data.get('study_tools')
                    print(f"Raw study_tools value: {study_tools_str}")
                    study_tools = json.loads(study_tools_str) if isinstance(study_tools_str, str) else study_tools_str
                    print(f"Parsed study tools: {study_tools}")
                    logging.info(f"Selected study tools: {study_tools}")
                except json.JSONDecodeError as e:
                    print(f"Error parsing study_tools: {e}")
                    print(f"Raw study_tools value: {data.get('study_tools')}")
                    logging.warning(f"Invalid study_tools format in process_document request from user ID: {current_user_id}")
                    # Continue without study tools if format is invalid
                    pass

            # Get all study tools from the database to map IDs to names
            tool_id_map = {}
            try:
                # Create a new cursor for this query
                with pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as tool_cursor:
                    # Get all study tools to map IDs to names
                    tool_cursor.execute("SELECT id, name FROM study_tools")
                    all_tools = tool_cursor.fetchall()
                    tool_id_map = {str(t['id']): t['name'].lower() for t in all_tools}
                    print(f"Tool ID map: {tool_id_map}")

                    # Find the flashcard tool ID
                    flashcard_tool_id = None
                    for tool_id, tool_name in tool_id_map.items():
                        if 'flash' in tool_name.lower() and 'card' in tool_name.lower():
                            flashcard_tool_id = tool_id
                            print(f"Found Flashcard tool in database with ID: {flashcard_tool_id}")
                            break

                    if not flashcard_tool_id:
                        print("Warning: Flashcard tool not found in study_tools table")
            except Exception as e:
                print(f"Error fetching study tools: {str(e)}")
                print(f"Exception type: {type(e).__name__}")
                print(f"Exception traceback: {traceback.format_exc()}")

            # Check for flashcards in study_tools (could be ID or name)
            has_flashcard_tool = False
            for tool in study_tools:
                tool_str = str(tool).lower()

                # Get the tool name if this is an ID
                tool_name = tool_id_map.get(tool_str, '').lower() if tool_id_map else ''

                # Check if this tool is a flashcard tool
                if tool_name and ('flash' in tool_name and 'card' in tool_name):
                    print(f"Flashcard tool detected with ID: {tool_str} (name: {tool_name})")
                    has_flashcard_tool = True
                    break

                # Fallback checks for direct name or ID matches
                if (tool_str == 'flashcards' or tool_str == 'flashcard' or
                    (flashcard_tool_id and tool_str == flashcard_tool_id) or
                    'flash' in tool_str and 'card' in tool_str):
                    print(f"Flashcard tool detected with value: {tool_str}")
                    has_flashcard_tool = True
                    break

            print(f"Flashcard tool selected: {has_flashcard_tool}")

            # Generate flashcards if the flashcard tool was selected
            generated_flashcards = []
            if has_flashcard_tool:
                print("\n\n==== GENERATING FLASHCARDS WITH AI ====")
                logging.info(f"Generating flashcards for subject ID: {subject_id}")

                # Prepare chapter data for flashcard generation
                chapters_for_flashcards = []
                for chapter_data in chapters_data:
                    chapter_id = chapter_data['id']
                    chapter_name = chapter_data['name']

                    # Extract chapter number from name if available
                    chapter_number = None
                    match = re.search(r'chapter\s+(\d+)', chapter_name, re.IGNORECASE)
                    if match:
                        chapter_number = match.group(1)
                    else:
                        chapter_number = '1'  # Default to 1 if no number found

                    # Extract title from name if available
                    title = chapter_name
                    title_match = re.search(r'chapter\s+\d+\s*:\s*(.+)', chapter_name, re.IGNORECASE)
                    if title_match:
                        title = title_match.group(1).strip()

                    chapters_for_flashcards.append({
                        'id': chapter_id,
                        'chapter_number': chapter_number,
                        'title': title
                    })

                # Generate flashcards using AI
                flashcards = generate_flashcards_with_ai(content, subject_id, chapters_for_flashcards)

                # Save flashcards to PostgreSQL database
                if pg_cursor and flashcards:
                    try:
                        print(f"Saving {len(flashcards)} flashcards to PostgreSQL database...")

                        # First, delete existing AI-generated flashcards for this subject to prevent duplication
                        try:
                            print(f"Removing existing AI-generated flashcards for subject ID: {subject_id}")
                            pg_cursor.execute(
                                """
                                DELETE FROM flashcards
                                WHERE user_id = %s AND subject_id = %s AND ai_generated = TRUE
                                RETURNING id
                                """,
                                (current_user_id, subject_id)
                            )
                            deleted_count = pg_cursor.rowcount
                            print(f"Removed {deleted_count} existing AI-generated flashcards")
                        except Exception as delete_error:
                            print(f"Error removing existing flashcards: {str(delete_error)}")
                            print(f"Exception type: {type(delete_error).__name__}")
                            print(f"Exception traceback: {traceback.format_exc()}")
                            # Continue with insertion

                        # Limit the number of flashcards per chapter to prevent excessive generation
                        MAX_FLASHCARDS_PER_CHAPTER = 20
                        chapter_flashcard_counts = {}

                        for i, flashcard in enumerate(flashcards):
                            # Get chapter ID
                            chapter_id = flashcard.get('chapter_id')

                            # Skip if we've already added the maximum number for this chapter
                            if chapter_id:
                                chapter_flashcard_counts[chapter_id] = chapter_flashcard_counts.get(chapter_id, 0) + 1
                                if chapter_flashcard_counts[chapter_id] > MAX_FLASHCARDS_PER_CHAPTER:
                                    print(f"  Skipping flashcard: Maximum of {MAX_FLASHCARDS_PER_CHAPTER} reached for chapter {chapter_id}")
                                    continue

                            # Convert tags list to string
                            tags_str = ','.join(flashcard.get('tags', [])) if flashcard.get('tags') else None

                            # Debug info for each flashcard
                            print(f"\nFlashcard #{i+1}:")
                            print(f"  Chapter ID: {flashcard.get('chapter_id')}")
                            print(f"  Question: {flashcard.get('question')[:50]}..." if flashcard.get('question') else "  Question: None")
                            print(f"  Answer: {flashcard.get('answer')[:50]}..." if flashcard.get('answer') else "  Answer: None")
                            print(f"  Type: {flashcard.get('type', 'basic')}")
                            print(f"  Tags: {tags_str}")

                            # Insert flashcard into PostgreSQL database
                            try:
                                pg_cursor.execute(
                                    """
                                    INSERT INTO flashcards (
                                        user_id, chapter_id, subject_id, question, answer,
                                        type, box_level, color, difficult, tags, ai_generated, created_at
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                                    RETURNING id
                                    """,
                                    (
                                        current_user_id,
                                        chapter_id,
                                        subject_id,
                                        flashcard.get('question'),
                                        flashcard.get('answer'),
                                        flashcard.get('type', 'basic'),
                                        1,  # Default box_level
                                        '#ffffff',  # Default color
                                        False,  # Default difficult
                                        tags_str,
                                        True,  # AI generated
                                    )
                                )
                                flashcard_id = pg_cursor.fetchone()['id']
                                generated_flashcards.append(flashcard_id)
                                print(f"  Successfully saved flashcard with ID: {flashcard_id}")

                                # Add the flashcard tool to the chapter_tools table if it doesn't exist
                                try:
                                    chapter_id = flashcard.get('chapter_id')
                                    if chapter_id:
                                        # First, get the correct tool ID for Flashcards from the database
                                        pg_cursor.execute(
                                            """
                                            SELECT id FROM study_tools
                                            WHERE name LIKE '%Flash%Card%' OR name LIKE '%Flashcard%'
                                            LIMIT 1
                                            """
                                        )
                                        flashcard_tool = pg_cursor.fetchone()

                                        if not flashcard_tool:
                                            print("  Warning: Flashcard tool not found in study_tools table")
                                            # Default to ID 13 if not found
                                            flashcard_tool_id = 13
                                        else:
                                            flashcard_tool_id = flashcard_tool['id']
                                            print(f"  Found Flashcard tool with ID: {flashcard_tool_id}")

                                        # Check if the flashcard tool is already associated with this chapter
                                        pg_cursor.execute(
                                            """
                                            SELECT id FROM chapter_tools
                                            WHERE chapter_id = %s AND tool_id = %s
                                            """,
                                            (chapter_id, flashcard_tool_id)
                                        )
                                        existing_tool = pg_cursor.fetchone()

                                        if not existing_tool:
                                            print(f"  Adding flashcard tool to chapter_tools for chapter ID: {chapter_id}")
                                            pg_cursor.execute(
                                                """
                                                INSERT INTO chapter_tools (chapter_id, tool_id, settings)
                                                VALUES (%s, %s, %s)
                                                RETURNING id
                                                """,
                                                (
                                                    chapter_id,
                                                    flashcard_tool_id,  # Dynamic Flashcard tool ID
                                                    '{}'  # Default empty settings
                                                )
                                            )
                                            tool_id = pg_cursor.fetchone()['id']
                                            print(f"  Successfully added flashcard tool to chapter_tools with ID: {tool_id}")
                                except Exception as tool_error:
                                    print(f"  Error adding flashcard tool to chapter_tools: {str(tool_error)}")
                                    print(f"  Exception type: {type(tool_error).__name__}")
                                    print(f"  Exception traceback: {traceback.format_exc()}")
                                    # Continue with next flashcard
                            except Exception as insert_error:
                                print(f"  Error inserting flashcard: {str(insert_error)}")
                                print(f"  Exception type: {type(insert_error).__name__}")
                                print(f"  Exception traceback: {traceback.format_exc()}")
                                # Continue with next flashcard
                    except Exception as pg_error:
                        print(f"Error saving flashcards to PostgreSQL database: {str(pg_error)}")
                        print(f"Exception type: {type(pg_error).__name__}")
                        print(f"Exception traceback: {traceback.format_exc()}")
                        logging.error(f"Error saving flashcards to PostgreSQL database: {str(pg_error)}")
                        logging.error(f"Exception traceback: {traceback.format_exc()}")
                        # Continue with the response, don't rollback the entire transaction

            # Commit all changes to PostgreSQL database
            pg_conn.commit()
            print("Successfully committed changes to PostgreSQL database")

            # Close the cursor
            pg_cursor.close()

            # Return the processed data
            return jsonify({
                'success': True,
                'message': 'Document processed successfully',
                'data': {
                    'subjects': [
                        {
                            'id': subject_id,
                            'name': subject['name'],
                            'chapters': chapters_data
                        }
                    ]
                },
                'flashcards_generated': len(generated_flashcards) if has_flashcard_tool else 0
            }), 200

        except Exception as e:
            print(f"Error processing document: {str(e)}")
            print(f"Exception type: {type(e).__name__}")
            print(f"Exception traceback: {traceback.format_exc()}")
            logging.error(f"Error processing document: {str(e)}")
            logging.error(f"Exception traceback: {traceback.format_exc()}")

            # Rollback PostgreSQL transaction
            pg_conn.rollback()
            pg_cursor.close()

            return jsonify({'success': False, 'error': f'Error processing document: {str(e)}'}), 500

    except Exception as e:
        print(f"Error querying database: {str(e)}")
        logging.error(f"Error querying database: {str(e)}")

        # Close the cursor if it exists
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()

        return jsonify({'success': False, 'error': f'Database error: {str(e)}'}), 500

def evaluate_study_plan_with_ai(
    subjects_data: Dict,
    start_date: date,
    end_date: date,
    daily_study_time: int,
    preferred_days: List[int] = None,
    unavailable_periods: List[Dict] = None,
    estimated_time_per_objective: int = None
) -> Dict:
    """
    Use AI to evaluate whether a study plan is feasible based on content and user constraints.

    Args:
        subjects_data: Dictionary containing subjects, chapters, and objectives
        start_date: Start date for the study plan
        end_date: End date for the study plan
        daily_study_time: Daily study time in minutes
        preferred_days: List of preferred days of week (0=Monday, 6=Sunday)
        unavailable_periods: List of periods when user is unavailable
        estimated_time_per_objective: Optional user-provided estimate of time per objective

    Returns:
        Dictionary with feasibility assessment, assigned objectives, and suggestions
    """
    logging.info(f"Evaluating study plan with AI: {start_date} to {end_date}, {daily_study_time} min/day")

    try:
        # Flatten all objectives into a single list
        all_objectives = []
        for subject in subjects_data.get('subjects', []):
            for chapter in subject.get('chapters', []):
                for objective in chapter.get('objectives', []):
                    # Store the objective along with its subject and chapter info
                    all_objectives.append({
                        'subject': subject['name'],
                        'chapter': chapter['name'],
                        'chapter_number': chapter.get('chapter_number', 1),
                        'text': objective['text'],
                        'estimated_time_minutes': objective.get('estimated_time_minutes', 30),
                        'difficulty_level': objective.get('difficulty_level', 1),
                        'objective_id': objective.get('objective_id', None)
                    })

        # Calculate total days and available study time
        total_days = (end_date - start_date).days + 1

        # Calculate available days considering preferred days
        available_days = total_days
        if preferred_days:
            # Count how many of the days in the range are preferred days
            current_date = start_date
            preferred_day_count = 0
            while current_date <= end_date:
                if current_date.weekday() in preferred_days:
                    preferred_day_count += 1
                current_date += timedelta(days=1)
            available_days = preferred_day_count

        # Adjust for unavailable periods
        unavailable_days = 0
        if unavailable_periods:
            for period in unavailable_periods:
                period_start = period.get('start_date')
                period_end = period.get('end_date')
                if period_start and period_end:
                    # Convert string dates to date objects if needed
                    if isinstance(period_start, str):
                        period_start = datetime.strptime(period_start, '%Y-%m-%d').date()
                    if isinstance(period_end, str):
                        period_end = datetime.strptime(period_end, '%Y-%m-%d').date()

                    # Calculate overlap with study period
                    overlap_start = max(start_date, period_start)
                    overlap_end = min(end_date, period_end)
                    if overlap_end >= overlap_start:
                        overlap_days = (overlap_end - overlap_start).days + 1
                        unavailable_days += overlap_days

            available_days -= unavailable_days

        # Calculate total available study time
        total_available_time = available_days * daily_study_time

        # Calculate total time needed for all objectives
        avg_time_per_objective = estimated_time_per_objective or 30  # Default to 30 minutes if not provided
        total_needed_time = sum(obj['estimated_time_minutes'] for obj in all_objectives)

        # Prepare data for AI evaluation
        study_plan_data = {
            "start_date": start_date.strftime('%Y-%m-%d'),
            "end_date": end_date.strftime('%Y-%m-%d'),
            "total_days": total_days,
            "available_days": available_days,
            "daily_study_time_minutes": daily_study_time,
            "total_available_time_minutes": total_available_time,
            "preferred_days": preferred_days,
            "unavailable_periods": unavailable_periods,
            "total_objectives": len(all_objectives),
            "total_estimated_time_minutes": total_needed_time,
            "average_time_per_objective": avg_time_per_objective
        }

        # Use Gemini AI to evaluate the study plan
        prompt = f"""
        You are an expert educational planner and study coach. Evaluate the following study plan for feasibility and provide recommendations.

        STUDY PLAN DETAILS:
        - Start Date: {start_date.strftime('%Y-%m-%d')}
        - End Date: {end_date.strftime('%Y-%m-%d')}
        - Total Days: {total_days}
        - Available Days (considering preferences): {available_days}
        - Daily Study Time: {daily_study_time} minutes
        - Total Available Study Time: {total_available_time} minutes
        - Preferred Days of Week: {preferred_days if preferred_days else "All days"}
        - Unavailable Periods: {len(unavailable_periods) if unavailable_periods else 0} periods

        CONTENT TO STUDY:
        - Total Objectives: {len(all_objectives)}
        - Total Estimated Time Needed: {total_needed_time} minutes
        - Average Time Per Objective: {avg_time_per_objective} minutes

        OBJECTIVES SAMPLE (first 5):
        {json.dumps([obj['text'] for obj in all_objectives[:5]], indent=2)}

        TASK:
        1. Evaluate whether this study plan is feasible based on the time available and content to cover.
        2. If feasible, distribute the objectives evenly (or proportionally by difficulty) across the available days.
        3. If not feasible, provide specific suggestions to make it realistic (extend end date, increase daily study time, etc.)

        Return your analysis as a JSON object with the following structure:
        {{
          "feasible": true/false,
          "assigned_objectives": [
            {{
              "objective": "Objective text",
              "study_date": "YYYY-MM-DD"
            }},
            ...
          ],
          "summary": {{
            "start_date": "YYYY-MM-DD",
            "end_date": "YYYY-MM-DD",
            "total_objectives": number,
            "estimated_total_hours": number,
            "study_hours_per_day": number
          }},
          "message": "A summary of your analysis",
          "suggestions": {{
            "suggested_end_date": "YYYY-MM-DD",  // Only if not feasible
            "suggested_daily_time_minutes": number,  // Only if not feasible
            "explanation": "Explanation of your suggestions"
          }}
        }}

        IMPORTANT: Return ONLY the JSON object, no other text.
        """

        # Call Gemini API
        try:
            response = model.generate_content(prompt)
            response_text = response.text.strip()

            # Extract JSON from response
            json_str = response_text.replace('```json', '').replace('```', '').strip()

            # Clean up any potential issues with the JSON
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            # Parse the JSON
            ai_evaluation = json.loads(json_str)

            # Ensure required fields are present
            if 'feasible' not in ai_evaluation:
                ai_evaluation['feasible'] = total_available_time >= total_needed_time

            if 'message' not in ai_evaluation:
                if ai_evaluation['feasible']:
                    ai_evaluation['message'] = "Your study plan is feasible with the current parameters."
                else:
                    ai_evaluation['message'] = "Your study plan is not feasible with the current parameters."

            # If AI didn't provide assigned objectives but the plan is feasible, create a simple distribution
            if ai_evaluation['feasible'] and ('assigned_objectives' not in ai_evaluation or not ai_evaluation['assigned_objectives']):
                assigned_objectives = []
                current_date = start_date
                objectives_per_day = max(1, len(all_objectives) // available_days)

                for i, obj in enumerate(all_objectives):
                    # Move to next day after every objectives_per_day
                    if i > 0 and i % objectives_per_day == 0 and current_date < end_date:
                        current_date += timedelta(days=1)

                        # Skip unavailable days
                        while (preferred_days and current_date.weekday() not in preferred_days) or \
                              (unavailable_periods and any(
                                  period_start <= current_date <= period_end
                                  for period in unavailable_periods
                                  for period_start, period_end in [(
                                      datetime.strptime(period['start_date'], '%Y-%m-%d').date() if isinstance(period['start_date'], str) else period['start_date'],
                                      datetime.strptime(period['end_date'], '%Y-%m-%d').date() if isinstance(period['end_date'], str) else period['end_date']
                                  )]
                              )):
                            current_date += timedelta(days=1)
                            if current_date > end_date:
                                break

                    assigned_objectives.append({
                        "objective": obj['text'],
                        "study_date": current_date.strftime('%Y-%m-%d'),
                        "objective_id": obj['objective_id']
                    })

                ai_evaluation['assigned_objectives'] = assigned_objectives

            # Add summary if not provided
            if 'summary' not in ai_evaluation:
                ai_evaluation['summary'] = {
                    "start_date": start_date.strftime('%Y-%m-%d'),
                    "end_date": end_date.strftime('%Y-%m-%d'),
                    "total_objectives": len(all_objectives),
                    "estimated_total_hours": round(total_needed_time / 60, 1),
                    "study_hours_per_day": round(daily_study_time / 60, 1)
                }

            return ai_evaluation

        except Exception as e:
            logging.error(f"Error calling Gemini API: {str(e)}")

            # Fallback to basic feasibility check
            is_feasible = total_available_time >= total_needed_time

            # Calculate suggestions if not feasible
            suggestions = {}
            if not is_feasible:
                # Option 1: Extend end date
                additional_days_needed = math.ceil((total_needed_time - total_available_time) / daily_study_time)
                suggested_end_date = (end_date + timedelta(days=additional_days_needed)).strftime('%Y-%m-%d')

                # Option 2: Increase daily study time
                suggested_daily_time = math.ceil(total_needed_time / available_days) if available_days > 0 else daily_study_time

                suggestions = {
                    "suggested_end_date": suggested_end_date,
                    "suggested_daily_time_minutes": suggested_daily_time,
                    "explanation": "Based on the number of objectives and available time, the current plan is not feasible."
                }

            # Create a simple distribution of objectives
            assigned_objectives = []
            current_date = start_date
            objectives_per_day = max(1, len(all_objectives) // available_days) if available_days > 0 else 1

            for i, obj in enumerate(all_objectives):
                # Move to next day after every objectives_per_day
                if i > 0 and i % objectives_per_day == 0 and current_date < end_date:
                    current_date += timedelta(days=1)

                assigned_objectives.append({
                    "objective": obj['text'],
                    "study_date": current_date.strftime('%Y-%m-%d'),
                    "objective_id": obj['objective_id']
                })

            # Return fallback evaluation
            return {
                "feasible": is_feasible,
                "assigned_objectives": assigned_objectives,
                "summary": {
                    "start_date": start_date.strftime('%Y-%m-%d'),
                    "end_date": end_date.strftime('%Y-%m-%d'),
                    "total_objectives": len(all_objectives),
                    "estimated_total_hours": round(total_needed_time / 60, 1),
                    "study_hours_per_day": round(daily_study_time / 60, 1)
                },
                "message": "Your study plan is " + ("feasible" if is_feasible else "not feasible") + " with the current parameters.",
                "suggestions": suggestions
            }

    except Exception as e:
        logging.error(f"Error evaluating study plan with AI: {str(e)}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")

        # Return a basic error response
        return {
            "feasible": False,
            "message": f"Error evaluating study plan: {str(e)}",
            "suggestions": {
                "suggested_end_date": (end_date + timedelta(days=7)).strftime('%Y-%m-%d'),
                "suggested_daily_time_minutes": daily_study_time + 30,
                "explanation": "An error occurred during evaluation. These are generic suggestions."
            }
        }

@app.route('/api/create_study_plan', methods=['POST', 'OPTIONS'])
def create_study_plan():
    """
    Create a study plan for a subject with realistic timeframes
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = jsonify({'success': True})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response

    # For POST requests, authenticate the user
    if request.method == 'POST':
        # Get the token from the Authorization header
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(" ")[1]

        if not token:
            return jsonify({'success': False, 'error': 'Token is missing'}), 401

        try:
            # Decode with proper validation
            data = jwt.decode(
                token,
                app.config['JWT_SECRET_KEY'],
                algorithms=["HS256"],
                options={
                    'verify_signature': True,
                    'verify_exp': True,
                    'verify_nbf': True,
                    'verify_iat': True,
                    'require': ['exp', 'iat', 'userId']
                }
            )
            current_user_id = data['userId']
        except jwt.ExpiredSignatureError:
            logging.warning("Expired JWT token")
            return jsonify({
                'success': False,
                'error': 'Token has expired. Please log in again'
            }), 401
        except jwt.InvalidTokenError:
            logging.warning("Invalid JWT token")
            return jsonify({
                'success': False,
                'error': 'Invalid token. Please log in again'
            }), 401
        except Exception as e:
            logging.error(f"Token validation error: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'Authentication failed'
            }), 401

    # Print console message for debugging
    print("\n\n==== STUDY PLAN CREATION REQUEST ====")
    print(f"User ID: {current_user_id}")

    data = request.json
    print(f"Request data: {json.dumps(data, indent=2)}")
    logging.info(f"Received study plan creation request: {data}")

    # Validate required fields
    required_fields = ['subject_id', 'name', 'start_date', 'end_date', 'daily_study_time_minutes']
    for field in required_fields:
        if field not in data:
            print(f"Missing required field: {field}")
            return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

    # Get the subject to verify ownership
    subject_id = data['subject_id']
    try:
        # Use PostgreSQL connection directly instead of SQLAlchemy
        if not pg_conn:
            logging.error("PostgreSQL connection not established")
            return jsonify({'success': False, 'error': 'Database connection error'}), 500

        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get the subject from PostgreSQL
        pg_cursor.execute(
            "SELECT id, name, user_id FROM subjects WHERE id = %s",
            (subject_id,)
        )
        subject = pg_cursor.fetchone()

        if not subject:
            print(f"Subject not found with ID: {subject_id}")
            logging.warning(f"Subject not found with ID: {subject_id}")
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Verify the subject belongs to the authenticated user
        if subject['user_id'] != current_user_id:
            print(f"Access denied: User {current_user_id} attempted to create study plan for subject {subject_id}")
            logging.warning(f"Access denied: User {current_user_id} attempted to create study plan for subject {subject_id}")
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Close the cursor
        pg_cursor.close()
    except Exception as e:
        print(f"Error querying database: {str(e)}")
        logging.error(f"Error querying database: {str(e)}")

        # Close the cursor if it exists
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()

        return jsonify({'success': False, 'error': f'Database error: {str(e)}'}), 500

    try:
        # Parse dates
        print(f"Parsing dates: start_date={data['start_date']}, end_date={data['end_date']}")
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
        daily_study_time = int(data['daily_study_time_minutes'])
        print(f"Parsed dates: start_date={start_date}, end_date={end_date}, daily_study_time={daily_study_time}")

        # Check if the timeframe is realistic
        total_days = (end_date - start_date).days + 1
        print(f"Total days in timeframe: {total_days}")
        if total_days <= 0:
            print("Error: End date must be after start date")
            return jsonify({'success': False, 'error': 'End date must be after start date'}), 400

        # Try to get chapters and objectives from PostgreSQL database first
        pg_cursor = None
        pg_chapters = []
        use_pg_data = False

        if pg_conn:
            try:
                pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
                print("Connected to frontend PostgreSQL database for study plan creation")

                # Get chapters from PostgreSQL database
                pg_cursor.execute(
                    """
                    SELECT c.id, c.title, c.chapter_number
                    FROM chapters c
                    WHERE c.subject_id = %s
                    ORDER BY c.chapter_number, c.created_at
                    """,
                    (subject_id,)
                )
                pg_chapters = pg_cursor.fetchall()
                print(f"Found {len(pg_chapters)} chapters in PostgreSQL database")

                if pg_chapters:
                    use_pg_data = True
            except Exception as pg_error:
                print(f"Error fetching chapters from PostgreSQL database: {str(pg_error)}")
                logging.error(f"Error fetching chapters from PostgreSQL database: {str(pg_error)}")
                use_pg_data = False

        # Calculate total estimated study time
        total_estimated_time = 0
        objectives_count = 0
        subjects_data = {'subjects': [{'name': subject['name'], 'chapters': []}]}

        if use_pg_data:
            print("Using data from PostgreSQL database")

            # Process chapters and objectives from PostgreSQL database
            for pg_chapter in pg_chapters:
                chapter_id = pg_chapter['id']
                chapter_title = pg_chapter['title']
                chapter_name = chapter_title  # Use title as name for backward compatibility
                chapter_number = pg_chapter.get('chapter_number')
                print(f"  Chapter: {chapter_name} (ID: {chapter_id}, Number: {chapter_number}, Title: {chapter_title})")

                chapter_data = {
                    'name': chapter_name,
                    'title': chapter_title,
                    'chapter_number': chapter_number,
                    'objectives': []
                }

                # Get objectives for this chapter from PostgreSQL database
                pg_cursor.execute(
                    "SELECT id, objective as text FROM objectives WHERE chapter_id = %s ORDER BY created_at",
                    (chapter_id,)
                )
                pg_objectives = pg_cursor.fetchall()
                print(f"    Found {len(pg_objectives)} objectives in PostgreSQL database")

                for pg_objective in pg_objectives:
                    objective_id = pg_objective['id']
                    objective_text = pg_objective['text']

                    # Use default values for estimated_time_minutes and difficulty_level
                    estimated_time = 30  # Default value
                    difficulty = 1  # Default value

                    objectives_count += 1
                    total_estimated_time += estimated_time

                    print(f"    Objective: {objective_text[:50]}... (Time: {estimated_time} min, Difficulty: {difficulty})")

                    objective_data = {
                        'text': objective_text,
                        'estimated_time_minutes': estimated_time,
                        'difficulty_level': difficulty,
                        'objective_id': objective_id
                    }
                    chapter_data['objectives'].append(objective_data)

                subjects_data['subjects'][0]['chapters'].append(chapter_data)

            # Close PostgreSQL cursor
            if pg_cursor:
                pg_cursor.close()
                print("Closed PostgreSQL cursor")
        else:
            print("Using data from SQLAlchemy database")

            # Use PostgreSQL connection directly instead of SQLAlchemy
            print(f"Fetching chapters for subject ID: {subject_id} using PostgreSQL")
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Get chapters from PostgreSQL database
            pg_cursor.execute(
                """
                SELECT c.id, c.title, c.chapter_number
                FROM chapters c
                WHERE c.subject_id = %s
                ORDER BY c.chapter_number, c.created_at
                """,
                (subject_id,)
            )
            chapters = pg_cursor.fetchall()
            print(f"Found {len(chapters)} chapters")

            if not chapters:
                print(f"No chapters found for subject ID: {subject_id}")

                # Check if there's a file associated with this subject
                pg_cursor.execute(
                    """
                    SELECT id, filename, originalname, filetype, filepath
                    FROM files
                    WHERE subject_id = %s
                    ORDER BY uploaded_at DESC LIMIT 1
                    """,
                    (subject_id,)
                )
                file_record = pg_cursor.fetchone()

                if not file_record:
                    return jsonify({
                        'success': False,
                        'error': 'No document found for this subject. Please upload a document first.'
                    }), 400
                else:
                    return jsonify({
                        'success': False,
                        'error': 'No chapters found for this subject. Please process the document first to extract chapters and objectives.'
                    }), 400

            print("Processing chapters and objectives from database:")
            for chapter in chapters:
                chapter_title = chapter['title']
                chapter_name = chapter_title  # Use title as name for backward compatibility
                chapter_number = chapter.get('chapter_number')
                print(f"  Chapter: {chapter_name} (ID: {chapter['id']}, Number: {chapter_number}, Title: {chapter_title})")

                chapter_data = {
                    'name': chapter_name,
                    'title': chapter_title,
                    'chapter_number': chapter_number,
                    'objectives': []
                }

                # Get all objectives for this chapter from PostgreSQL
                pg_cursor.execute(
                    """
                    SELECT id, objective as text, completed
                    FROM objectives
                    WHERE chapter_id = %s
                    ORDER BY id
                    """,
                    (chapter['id'],)
                )
                objectives = pg_cursor.fetchall()
                print(f"    Found {len(objectives)} objectives")

                for objective in objectives:
                    objectives_count += 1
                    estimated_time = 30  # Default to 30 minutes
                    total_estimated_time += estimated_time

                    print(f"    Objective: {objective['text'][:50]}... (Default Time: {estimated_time} min, Default Difficulty: 2)")

                    objective_data = {
                        'text': objective['text'],
                        'estimated_time_minutes': estimated_time,
                        'difficulty_level': 2,  # Default to medium difficulty
                        'objective_id': objective['id']
                    }
                    chapter_data['objectives'].append(objective_data)

                subjects_data['subjects'][0]['chapters'].append(chapter_data)

        print(f"Total objectives: {objectives_count}")
        print(f"Total estimated study time: {total_estimated_time} minutes")

        # Close the PostgreSQL cursor if it exists
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()
            print("Closed PostgreSQL cursor")

        # Use AI to evaluate study plan feasibility
        print("Using AI to evaluate study plan feasibility...")
        feasibility_result = evaluate_study_plan_with_ai(
            subjects_data,
            start_date,
            end_date,
            daily_study_time,
            preferred_days=data.get('preferred_days'),
            unavailable_periods=data.get('unavailable_periods'),
            estimated_time_per_objective=data.get('estimated_time_per_objective')
        )

        print(f"AI feasibility evaluation result: {json.dumps(feasibility_result, indent=2, default=str)}")

        # Extract key information from the AI evaluation
        is_realistic = feasibility_result.get('feasible', False)
        assigned_objectives = feasibility_result.get('assigned_objectives', [])
        summary = feasibility_result.get('summary', {})
        suggested_message = feasibility_result.get('message', '')

        # For backward compatibility, calculate these values
        total_available_time = total_days * daily_study_time
        avg_time_per_day = total_estimated_time / total_days if total_days > 0 else 0
        difficulty_ratio = avg_time_per_day / daily_study_time if daily_study_time > 0 else float('inf')
        difficulty_level = "Easy" if difficulty_ratio <= 0.7 else "Moderate" if difficulty_ratio <= 1.0 else "Challenging" if difficulty_ratio <= 1.3 else "Very Difficult"

        # Extract suggestions if the plan is not feasible
        suggested_end_date = None
        suggested_daily_time = None

        if not is_realistic and 'suggestions' in feasibility_result:
            suggestions = feasibility_result.get('suggestions', {})
            if 'suggested_end_date' in suggestions:
                suggested_end_date = suggestions['suggested_end_date']
            if 'suggested_daily_time_minutes' in suggestions:
                suggested_daily_time = suggestions['suggested_daily_time_minutes']

        # Begin SQLAlchemy transaction
        db.session.begin()

        # Initialize PostgreSQL cursor
        pg_cursor = None

        try:
            # Begin PostgreSQL transaction
            logging.info("Beginning PostgreSQL transaction for study plan creation")

            # Get the file_id for this subject to use as curriculum_id
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            pg_cursor.execute(
                """
                SELECT id FROM files
                WHERE subject_id = %s
                ORDER BY uploaded_at DESC LIMIT 1
                """,
                (subject_id,)
            )
            file_record = pg_cursor.fetchone()

            # Don't close the cursor yet, we'll use it throughout the transaction

            if not file_record:
                logging.error(f"No file found for subject ID: {subject_id}")
                return jsonify({'success': False, 'error': 'No file found for this subject. Please upload a document first.'}), 400

            file_id = file_record['id']

            # Create study plan
            logging.info(f"Creating study plan: {data['name']} for subject: {subject['name']}")

            # Get preferred days and unavailable periods if provided
            preferred_days = data.get('preferred_days')
            unavailable_periods = data.get('unavailable_periods')
            user_accepted_suggestion = data.get('user_accepted_suggestion', False)

            # Convert unavailable_periods to JSON string if it's a list/dict
            unavailable_periods_str = None
            if unavailable_periods:
                unavailable_periods_str = json.dumps(unavailable_periods)

            study_plan = StudyPlan(
                user_id=current_user_id,
                subject_id=subject_id,  # Use subject_id instead of curriculum_id
                curriculum_id=file_id,  # Keep for backward compatibility
                name=data['name'],
                start_date=start_date,
                end_date=end_date,
                daily_study_time_minutes=daily_study_time,
                preferred_days=preferred_days,
                unavailable_periods=unavailable_periods_str
            )
            db.session.add(study_plan)
            db.session.flush()  # Get the study plan ID

            logging.info(f"Created study plan with ID: {study_plan.id}")

            # Format unavailable periods if provided
            if unavailable_periods:
                for period in unavailable_periods:
                    if 'start_date' in period and isinstance(period['start_date'], str):
                        period['start_date'] = datetime.strptime(period['start_date'], '%Y-%m-%d').date()
                    if 'end_date' in period and isinstance(period['end_date'], str):
                        period['end_date'] = datetime.strptime(period['end_date'], '%Y-%m-%d').date()

            # Use the AI-evaluated study plan for scheduling
            logging.info("Using AI-evaluated study plan for scheduling")

            # Extract assigned objectives from the AI evaluation
            assigned_objectives = feasibility_result.get('assigned_objectives', [])

            # Convert to schedule items format
            schedule_items = []
            objective_display_dates = {}

            # If AI provided assigned objectives, use them
            if assigned_objectives:
                logging.info(f"Using {len(assigned_objectives)} AI-assigned objectives")

                # Create a mapping of objective text to objective data
                objective_map = {}
                for subject in subjects_data.get('subjects', []):
                    for chapter in subject.get('chapters', []):
                        for objective in chapter.get('objectives', []):
                            objective_map[objective['text']] = {
                                'subject': subject['name'],
                                'chapter': chapter['name'],
                                'chapter_number': chapter.get('chapter_number', 1),
                                'chapter_title': chapter.get('title', ''),
                                'text': objective['text'],
                                'estimated_time_minutes': objective.get('estimated_time_minutes', 30),
                                'difficulty_level': objective.get('difficulty_level', 1),
                                'objective_id': objective.get('objective_id', None)
                            }

                # Convert assigned objectives to schedule items
                for assigned in assigned_objectives:
                    objective_text = assigned.get('objective')
                    study_date_str = assigned.get('study_date')
                    objective_id = assigned.get('objective_id')

                    # Skip if missing required data
                    if not objective_text or not study_date_str:
                        continue

                    # Convert study date string to date object
                    try:
                        study_date = datetime.strptime(study_date_str, '%Y-%m-%d').date()
                    except ValueError:
                        logging.warning(f"Invalid date format in assigned objective: {study_date_str}")
                        continue

                    # Get objective data from map or create default
                    objective_data = objective_map.get(objective_text, {
                        'text': objective_text,
                        'estimated_time_minutes': 30,
                        'difficulty_level': 2,
                        'objective_id': objective_id
                    })

                    # Add to schedule items
                    schedule_items.append({
                        'objective_data': objective_data,
                        'scheduled_date': study_date
                    })

                    # Store display date for this objective
                    if objective_data.get('objective_id'):
                        objective_display_dates[objective_data['objective_id']] = study_date
            else:
                # Fallback to using the document processor if AI didn't provide assignments
                logging.info("AI didn't provide assigned objectives, falling back to document processor")
                schedule_result = generate_study_schedule(
                    subjects_data,
                    start_date,
                    end_date,
                    daily_study_time,
                    preferred_days,
                    unavailable_periods,
                    user_accepted_suggestion
                )

                # Extract schedule items and objective display dates
                schedule_items = schedule_result['schedule_items']
                objective_display_dates = schedule_result.get('objective_display_dates', {})

            logging.info(f"Generated {len(schedule_items)} schedule items")

            # Save schedule items to SQLAlchemy database
            for i, item in enumerate(schedule_items):
                objective_id = item['objective_data']['objective_id']
                scheduled_date = item['scheduled_date']

                if i < 5:  # Log only the first few items to avoid excessive logging
                    logging.info(f"Scheduling objective ID {objective_id} for {scheduled_date}")
                elif i == 5:
                    logging.info(f"... and {len(schedule_items) - 5} more items")

                schedule_item = ScheduleItem(
                    study_plan_id=study_plan.id,
                    objective_id=objective_id,
                    scheduled_date=scheduled_date
                )

                db.session.add(schedule_item)

            # Update the display_date for each objective in both databases
            logging.info("Updating display_date for objectives")

            # First update in SQLAlchemy (part of the current transaction)
            for objective_id, display_date in objective_display_dates.items():
                objective = Objective.query.get(objective_id)
                if objective:
                    objective.display_date = display_date
                    logging.info(f"Updated display_date for objective {objective_id} to {display_date} in SQLAlchemy")

            # Skip direct PostgreSQL updates and rely on SQLAlchemy to update the database
            # This avoids transaction conflicts with the existing connection
            logging.info("Skipping direct PostgreSQL updates - will rely on SQLAlchemy to update the database")

            # Log that we're using SQLAlchemy for all updates
            logging.info(f"Using SQLAlchemy to update {len(objective_display_dates)} objectives")

            # The SQLAlchemy updates were already done above, so we don't need to do anything else here
            logging.info("All objectives have been updated through SQLAlchemy")



            # Commit all changes
            logging.info("Committing changes to database")

            # Commit SQLAlchemy transaction
            db.session.commit()
            logging.info("Successfully committed SQLAlchemy changes")

            # No need to commit PostgreSQL transaction here as it's already committed
            # in the PostgreSQL update section above

        except Exception as inner_e:
            # Rollback SQLAlchemy transaction
            try:
                db.session.rollback()
                logging.info("Rolled back SQLAlchemy transaction in inner exception handler")
            except Exception as rollback_error:
                logging.error(f"Error rolling back SQLAlchemy transaction: {str(rollback_error)}")

            # No PostgreSQL cursor to close since we're not using direct PostgreSQL updates

            logging.error(f"Error creating study plan: {str(inner_e)}")
            logging.error(f"Exception type: {type(inner_e).__name__}")
            logging.error(f"Exception traceback: {traceback.format_exc()}")

            # Re-raise to be caught by the outer try-except block
            raise

        # Prepare response with AI-based feasibility evaluation
        response = {
            'success': True,
            'message': 'Study plan created successfully',
            'study_plan_id': study_plan.id,
            'is_realistic': is_realistic,
            'total_objectives': objectives_count,
            'total_estimated_time_minutes': total_estimated_time,
            'total_available_time_minutes': total_available_time,
            'preferred_days': preferred_days,
            'unavailable_periods': unavailable_periods,
            'difficulty': {
                'level': difficulty_level,
                'ratio': round(difficulty_ratio, 2),
                'avg_time_per_day': round(avg_time_per_day, 2)
            },
            'ai_evaluation': {
                'feasible': feasibility_result.get('feasible', is_realistic),
                'message': feasibility_result.get('message', suggested_message),
                'summary': feasibility_result.get('summary', {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'total_objectives': objectives_count,
                    'estimated_total_hours': round(total_estimated_time / 60, 1),
                    'study_hours_per_day': round(daily_study_time / 60, 1)
                })
            }
        }

        # Add suggestions based on AI analysis
        response['suggestions'] = {
            'message': suggested_message
        }

        # Include specific suggestions if available
        if not is_realistic:
            response['message'] = 'Study plan created, but the timeframe may be unrealistic'

            # Add AI-provided suggestions if available
            if 'suggestions' in feasibility_result:
                ai_suggestions = feasibility_result['suggestions']

                if 'suggested_end_date' in ai_suggestions:
                    response['suggestions']['suggested_end_date'] = ai_suggestions['suggested_end_date']
                elif suggested_end_date:
                    response['suggestions']['suggested_end_date'] = suggested_end_date

                if 'suggested_daily_time_minutes' in ai_suggestions:
                    response['suggestions']['suggested_daily_time_minutes'] = ai_suggestions['suggested_daily_time_minutes']
                elif suggested_daily_time:
                    response['suggestions']['suggested_daily_time_minutes'] = suggested_daily_time

                if 'explanation' in ai_suggestions:
                    response['suggestions']['explanation'] = ai_suggestions['explanation']
            else:
                # Fallback to basic suggestions
                if suggested_end_date:
                    response['suggestions']['suggested_end_date'] = suggested_end_date
                if suggested_daily_time:
                    response['suggestions']['suggested_daily_time_minutes'] = suggested_daily_time

        return jsonify(response), 201

    except Exception as e:
        # Rollback SQLAlchemy transaction
        try:
            db.session.rollback()
            logging.info("Rolled back SQLAlchemy transaction in error handler")
        except Exception as rollback_error:
            logging.error(f"Error rolling back SQLAlchemy transaction: {str(rollback_error)}")

        # No PostgreSQL cursor to close since we're not using direct PostgreSQL updates

        # Log detailed error information
        logging.error(f"Error creating study plan: {str(e)}")
        logging.error(f"Exception type: {type(e).__name__}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")

        # Provide a more helpful error message to the user
        error_message = str(e)
        if "current transaction is aborted" in error_message:
            error_message = "Database transaction error. Please try again or contact support if the issue persists."

        return jsonify({'success': False, 'error': f'Error creating study plan: {error_message}'}), 500


@app.route('/api/get_study_plans/<int:user_id>', methods=['GET'])
@authenticate_token
def get_study_plans(user_id, current_user_id):
    """
    Get all study plans for a user
    """
    # Verify the requested user_id matches the authenticated user
    if user_id != current_user_id:
        logging.warning(f"Access denied: User {current_user_id} attempted to access study plans for user {user_id}")
        return jsonify({'success': False, 'error': 'Access denied'}), 403

    try:
        # Use direct PostgreSQL connection to avoid SQLAlchemy model issues
        if not pg_conn:
            logging.error("PostgreSQL connection not established")
            return jsonify({'success': False, 'error': 'Database connection error'}), 500

        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get all study plans for the user
        pg_cursor.execute("""
            SELECT sp.*, f.originalname as curriculum_title
            FROM study_plans sp
            LEFT JOIN files f ON sp.curriculum_id = f.id
            WHERE sp.user_id = %s
            ORDER BY sp.created_at DESC
        """, (user_id,))

        # Commit to prevent transaction errors
        pg_conn.commit()

        study_plans = pg_cursor.fetchall()

        plans_data = []
        for plan in study_plans:
            # Get subject information
            pg_cursor.execute("SELECT id, name FROM subjects WHERE id = %s", (plan['subject_id'],))
            subject = pg_cursor.fetchone()

            # Count total objectives and completed objectives
            pg_cursor.execute("""
                SELECT id, is_completed
                FROM schedule_items
                WHERE study_plan_id = %s
            """, (plan['id'],))

            schedule_items = pg_cursor.fetchall()
            total_objectives = len(schedule_items)
            completed_objectives = len([item for item in schedule_items if item['is_completed']])

            # Parse unavailable_periods from JSON string if it exists
            unavailable_periods = None
            if plan['unavailable_periods']:
                try:
                    unavailable_periods = json.loads(plan['unavailable_periods'])
                except json.JSONDecodeError:
                    logging.warning(f"Failed to parse unavailable_periods for study plan {plan['id']}")

            plans_data.append({
                'id': plan['id'],
                'name': plan['name'],
                'subject': {
                    'id': subject['id'] if subject else None,
                    'name': subject['name'] if subject else 'Unknown'
                },
                'curriculum_title': plan['curriculum_title'] if plan['curriculum_title'] else 'Unknown',
                'start_date': plan['start_date'].strftime('%Y-%m-%d'),
                'end_date': plan['end_date'].strftime('%Y-%m-%d'),
                'daily_study_time_minutes': plan['daily_study_time_minutes'],
                'preferred_days': plan['preferred_days'],
                'unavailable_periods': unavailable_periods,
                'created_at': plan['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                'progress': {
                    'total_objectives': total_objectives,
                    'completed_objectives': completed_objectives,
                    'percentage': round(completed_objectives / total_objectives * 100) if total_objectives > 0 else 0
                }
            })

        pg_cursor.close()
        return jsonify({'success': True, 'study_plans': plans_data}), 200

    except Exception as e:
        logging.error(f"Error fetching study plans: {str(e)}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()
        return jsonify({'success': False, 'error': f'Error fetching study plans: {str(e)}'}), 500


@app.route('/api/get_study_plan_details/<int:plan_id>', methods=['GET'])
@authenticate_token
def get_study_plan_details(plan_id, current_user_id):
    """
    Get detailed information about a study plan, including all scheduled items
    and a hierarchical view of chapters and objectives
    """
    try:
        # Use direct PostgreSQL connection to avoid SQLAlchemy model issues
        if pg_conn:
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Get the study plan
            pg_cursor.execute("""
                SELECT * FROM study_plans WHERE id = %s
            """, (plan_id,))

            # Commit to prevent transaction errors
            pg_conn.commit()
            study_plan = pg_cursor.fetchone()

            if not study_plan:
                pg_cursor.close()
                return jsonify({'success': False, 'error': 'Study plan not found'}), 404

            # Verify the study plan belongs to the authenticated user
            if study_plan['user_id'] != current_user_id:
                logging.warning(f"Access denied: User {current_user_id} attempted to access study plan {plan_id}")
                pg_cursor.close()
                return jsonify({'success': False, 'error': 'Access denied'}), 403

            # Get subject information
            pg_cursor.execute("""
                SELECT id, name FROM subjects WHERE id = %s
            """, (study_plan['subject_id'],))

            # Commit to prevent transaction errors
            pg_conn.commit()
            subject = pg_cursor.fetchone()

            if not subject:
                logging.warning(f"Subject not found for study plan {plan_id}")
                pg_cursor.close()
                return jsonify({'success': False, 'error': 'Subject not found for this study plan'}), 404
        else:
            # Fallback to SQLAlchemy if pg_conn is not available
            study_plan = StudyPlan.query.get(plan_id)
            if not study_plan:
                return jsonify({'success': False, 'error': 'Study plan not found'}), 404

            # Verify the study plan belongs to the authenticated user
            if study_plan.user_id != current_user_id:
                logging.warning(f"Access denied: User {current_user_id} attempted to access study plan {plan_id}")
                return jsonify({'success': False, 'error': 'Access denied'}), 403

            # Get subject information
            subject = Subject.query.get(study_plan.subject_id)
            if not subject:
                logging.warning(f"Subject not found for study plan {plan_id}")
                return jsonify({'success': False, 'error': 'Subject not found for this study plan'}), 404

        # Get all schedule items
        schedule_items = ScheduleItem.query.filter_by(study_plan_id=plan_id).all()

        # Organize by date
        schedule_by_date = {}
        for item in schedule_items:
            # Handle potential missing data gracefully
            try:
                objective = Objective.query.get(item.objective_id)
                if not objective:
                    logging.warning(f"Objective not found for ID: {item.objective_id}")
                    continue

                chapter = Chapter.query.get(objective.chapter_id) if objective and hasattr(objective, 'chapter_id') else None
            except Exception as item_error:
                logging.error(f"Error processing schedule item {item.id}: {str(item_error)}")
                continue

            date_str = item.scheduled_date.strftime('%Y-%m-%d')
            if date_str not in schedule_by_date:
                schedule_by_date[date_str] = []

            # Handle different field names for objective text
            objective_text = None
            if hasattr(objective, 'text'):
                objective_text = objective.text
            elif hasattr(objective, 'objective'):
                objective_text = objective.objective
            else:
                objective_text = 'Unknown objective'

            # Create objective data with default values for missing attributes
            objective_data = {
                'id': objective.id,
                'text': objective_text,
                'estimated_time_minutes': getattr(objective, 'estimated_time_minutes', 30),
                'difficulty_level': getattr(objective, 'difficulty_level', 2)
            }

            # Create chapter data if available
            chapter_data = None
            if chapter:
                chapter_data = {
                    'id': chapter.id,
                    'name': chapter.name if hasattr(chapter, 'name') else 'Unknown chapter',
                    'chapter_number': chapter.chapter_number if hasattr(chapter, 'chapter_number') else None,
                    'title': chapter.title if hasattr(chapter, 'title') else None
                }

            # Add the item to the schedule
            schedule_by_date[date_str].append({
                'id': item.id,
                'objective': objective_data,
                'chapter': chapter_data,
                'subject': {
                    'id': subject['id'] if isinstance(subject, dict) else subject.id,
                    'name': subject['name'] if isinstance(subject, dict) else subject.name
                },
                'is_completed': item.is_completed,
                'completion_date': item.completion_date.strftime('%Y-%m-%d %H:%M:%S') if item.completion_date else None
            })

        # Convert to list for easier frontend processing
        schedule_list = []
        for date_str, items in schedule_by_date.items():
            schedule_list.append({
                'date': date_str,
                'items': items
            })

        # Sort by date
        schedule_list.sort(key=lambda x: x['date'])

        # Parse unavailable_periods from JSON string if it exists
        unavailable_periods = None
        if study_plan.unavailable_periods:
            try:
                unavailable_periods = json.loads(study_plan.unavailable_periods)
            except json.JSONDecodeError:
                logging.warning(f"Failed to parse unavailable_periods for study plan {study_plan.id}")

        # Get all chapters for this subject and organize objectives by chapter
        subject_id_value = subject['id'] if isinstance(subject, dict) else subject.id
        chapters = Chapter.query.filter_by(subject_id=subject_id_value).all()
        chapters_data = []

        for chapter in chapters:
            # Get all objectives for this chapter
            objectives = Objective.query.filter_by(chapter_id=chapter.id).all()

            # Format objectives with schedule item information
            chapter_objectives = []
            for objective in objectives:
                # Find the schedule item for this objective
                schedule_item = next((item for item in schedule_items if item.objective_id == objective.id), None)

                if schedule_item:
                    # Handle different field names for objective text
                    objective_text = None
                    if hasattr(objective, 'text'):
                        objective_text = objective.text
                    elif hasattr(objective, 'objective'):
                        objective_text = objective.objective
                    else:
                        objective_text = 'Unknown objective'

                    chapter_objectives.append({
                        'id': objective.id,
                        'text': objective_text,
                        'estimated_time_minutes': getattr(objective, 'estimated_time_minutes', 30),
                        'difficulty_level': getattr(objective, 'difficulty_level', 2),
                        'schedule_item_id': schedule_item.id,
                        'scheduled_date': schedule_item.scheduled_date.strftime('%Y-%m-%d'),
                        'is_completed': schedule_item.is_completed,
                        'completion_date': schedule_item.completion_date.strftime('%Y-%m-%d %H:%M:%S') if schedule_item.completion_date else None
                    })

            # Add chapter with its objectives to the chapters data
            chapters_data.append({
                'id': chapter.id,
                'name': chapter.name,
                'chapter_number': chapter.chapter_number,
                'title': chapter.title if hasattr(chapter, 'title') else None,
                'objectives': chapter_objectives
            })

        return jsonify({
            'success': True,
            'study_plan': {
                'id': study_plan.id,
                'name': study_plan.name,
                'start_date': study_plan.start_date.strftime('%Y-%m-%d'),
                'end_date': study_plan.end_date.strftime('%Y-%m-%d'),
                'daily_study_time_minutes': study_plan.daily_study_time_minutes,
                'preferred_days': study_plan.preferred_days,
                'unavailable_periods': unavailable_periods,
                'subject': {
                    'id': subject['id'] if isinstance(subject, dict) else subject.id,
                    'name': subject['name'] if isinstance(subject, dict) else subject.name
                },
                'schedule': schedule_list,
                'chapters': chapters_data  # Add hierarchical chapter data
            }
        }), 200

    except Exception as e:
        logging.error(f"Error fetching study plan details: {str(e)}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': f'Error fetching study plan details: {str(e)}'
        }), 500


@app.route('/api/update_schedule_item/<int:item_id>', methods=['PUT'])
@authenticate_token
def update_schedule_item(item_id, current_user_id):
    """
    Update a schedule item (mark as completed/incomplete)
    """
    try:
        data = request.json
        schedule_item = ScheduleItem.query.get(item_id)

        if not schedule_item:
            return jsonify({'success': False, 'error': 'Schedule item not found'}), 404

        # Get the study plan to verify ownership
        study_plan = StudyPlan.query.get(schedule_item.study_plan_id)
        if not study_plan or study_plan.user_id != current_user_id:
            logging.warning(f"Access denied: User {current_user_id} attempted to update schedule item {item_id}")
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        if 'is_completed' in data:
            schedule_item.is_completed = data['is_completed']
            if data['is_completed']:
                schedule_item.completion_date = datetime.now()
            else:
                schedule_item.completion_date = None

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Schedule item updated successfully',
            'item': {
                'id': schedule_item.id,
                'is_completed': schedule_item.is_completed,
                'completion_date': schedule_item.completion_date.strftime('%Y-%m-%d %H:%M:%S') if schedule_item.completion_date else None
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error updating schedule item: {str(e)}")
        return jsonify({'error': f'Error updating schedule item: {str(e)}'}), 500


@app.route('/api/process_document_content', methods=['POST'])
@authenticate_token
def process_document_content(current_user_id):
    """
    Process document content with Gemini AI to extract chapters and objectives
    without saving to the database
    """
    data = request.json

    if not data or 'content' not in data or 'subject_id' not in data:
        return jsonify({'success': False, 'error': 'Missing required fields: content and subject_id'}), 400

    content = data['content']
    subject_id = data['subject_id']

    # Use direct PostgreSQL connection to avoid SQLAlchemy model issues
    if pg_conn:
        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get the subject from PostgreSQL
        pg_cursor.execute(
            "SELECT id, name, user_id FROM subjects WHERE id = %s",
            (subject_id,)
        )
        subject = pg_cursor.fetchone()
        pg_cursor.close()

        if not subject:
            logging.warning(f"Subject not found with ID: {subject_id}")
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Verify the subject belongs to the authenticated user
        if subject['user_id'] != current_user_id:
            logging.warning(f"Access denied: User {current_user_id} attempted to process content for subject {subject_id}")
            return jsonify({'success': False, 'error': 'Access denied'}), 403
    else:
        # Fallback to SQLAlchemy if pg_conn is not available
        subject = Subject.query.get(subject_id)
        if not subject:
            logging.warning(f"Subject not found with ID: {subject_id}")
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Verify the subject belongs to the authenticated user
        if subject.user_id != current_user_id:
            logging.warning(f"Access denied: User {current_user_id} attempted to process content for subject {subject_id}")
            return jsonify({'success': False, 'error': 'Access denied'}), 403

    try:
        # Process the document with Gemini AI
        extracted_data = process_document_with_gemini(content)

        if isinstance(extracted_data, str):
            return jsonify({'success': False, 'error': extracted_data}), 500

        # Format the response
        chapters_data = []

        # Use the first subject from the extracted data
        subject_name = extracted_data.get('subjects', [{}])[0].get('name', subject.name)

        # Process all chapters from the first subject
        for chapter_data in extracted_data.get('subjects', [{}])[0].get('chapters', []):
            chapter_objectives = []

            # Process objectives for this chapter
            for objective_data in chapter_data.get('objectives', []):
                # Handle different formats of objective data
                if isinstance(objective_data, dict):
                    objective_text = objective_data.get('text', '')
                    estimated_time = objective_data.get('estimated_time_minutes', 30)
                    difficulty = objective_data.get('difficulty_level', 1)
                else:
                    # If it's just a string
                    objective_text = objective_data
                    estimated_time = 30
                    difficulty = 1

                # Add to chapter objectives
                chapter_objectives.append({
                    'text': objective_text,
                    'estimated_time_minutes': estimated_time,
                    'difficulty_level': difficulty
                })

            # Add chapter data for response
            chapters_data.append({
                'name': chapter_data['name'],
                'objectives': chapter_objectives
            })

        return jsonify({
            'success': True,
            'message': 'Document content processed successfully',
            'subject': {
                'id': subject['id'] if isinstance(subject, dict) else subject.id,
                'name': subject_name
            },
            'chapters': chapters_data
        }), 200

    except Exception as e:
        logging.error(f"Error processing document content: {str(e)}")
        return jsonify({'success': False, 'error': f'Error processing document content: {str(e)}'}), 500

@app.route('/api/process-syllabus', methods=['OPTIONS'])
def process_syllabus_preflight():
    """
    Handle OPTIONS preflight request for the process-syllabus endpoint
    This is needed to properly handle CORS
    """
    response = jsonify({'success': True})
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
    return response

@app.route('/api/process-syllabus', methods=['POST'])
@authenticate_token
def process_syllabus(current_user_id):
    """
    Process a syllabus (document) and return the extracted chapters and objectives
    This endpoint is used by the frontend DocumentProcessor component

    Args:
        current_user_id: The ID of the authenticated user (required for @authenticate_token)
    """
    data = request.json
    logging.info(f"Processing syllabus with data: {data}")

    if not data or ('file_id' not in data and 'subject_id' not in data):
        return jsonify({'success': False, 'error': 'Missing required fields: file_id or subject_id'}), 400

    # Use either file_id or subject_id
    subject_id = data.get('subject_id')

    try:
        # Use PostgreSQL connection directly instead of SQLAlchemy
        if not pg_conn:
            logging.error("PostgreSQL connection not established")
            return jsonify({'success': False, 'error': 'Database connection error'}), 500

        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get the subject from PostgreSQL
        pg_cursor.execute(
            "SELECT id, name, user_id FROM subjects WHERE id = %s",
            (subject_id,)
        )

        # Commit to prevent transaction errors
        pg_conn.commit()
        subject = pg_cursor.fetchone()

        if not subject:
            logging.warning(f"Subject not found with ID: {subject_id}")
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Verify the subject belongs to the authenticated user
        if subject['user_id'] != current_user_id:
            logging.warning(f"Access denied: User {current_user_id} attempted to process syllabus for subject {subject_id}")
            pg_cursor.close()
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Get all chapters for this subject from PostgreSQL
        pg_cursor.execute(
            """
            SELECT c.id, c.title
            FROM chapters c
            WHERE c.subject_id = %s
            ORDER BY c.id
            """,
            (subject_id,)
        )

        # Commit to prevent transaction errors
        pg_conn.commit()
        chapters = pg_cursor.fetchall()

        # If no chapters found, return empty data
        if not chapters:
            logging.info(f"No chapters found for subject ID: {subject_id}")
            pg_cursor.close()
            return jsonify({
                'success': True,
                'message': 'No chapters found for this subject',
                'data': {
                    'subjects': [
                        {
                            'name': subject['name'],
                            'chapters': []
                        }
                    ]
                }
            }), 200

        # Format the response
        chapters_data = []
        for chapter in chapters:
            # Get all objectives for this chapter from PostgreSQL
            pg_cursor.execute(
                """
                SELECT id, objective as text, completed
                FROM objectives
                WHERE chapter_id = %s
                ORDER BY id
                """,
                (chapter['id'],)
            )

            # Commit to prevent transaction errors
            pg_conn.commit()
            objectives = pg_cursor.fetchall()

            # Format the objectives
            objectives_data = []
            for objective in objectives:
                objectives_data.append({
                    'text': objective['text'],
                    'estimated_time_minutes': 30,  # Default value
                    'difficulty_level': 2  # Default value
                })

            # Add chapter data for response
            chapter_title = chapter['title']
            chapters_data.append({
                'name': chapter_title,  # Use title as name for backward compatibility
                'title': chapter_title,
                'objectives': objectives_data
            })

        # Close the cursor
        pg_cursor.close()

        # Return the formatted data
        return jsonify({
            'success': True,
            'message': 'Syllabus processed successfully',
            'data': {
                'subjects': [
                    {
                        'name': subject['name'],
                        'chapters': chapters_data
                    }
                ]
            }
        }), 200

    except Exception as e:
        logging.error(f"Error processing syllabus: {str(e)}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")

        # Close the cursor if it exists
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()

        return jsonify({'success': False, 'error': f'Error processing syllabus: {str(e)}'}), 500

@app.route('/test', methods=['GET'])
def test():
    """
    Simple test endpoint to verify the API is working
    """
    return jsonify({
        'success': True,
        'message': 'API is working correctly on port 5000!'
    }), 200

# Removed duplicate route - using the PostgreSQL version below for flashcard generation

@app.route('/api/subjects/<int:subject_id>', methods=['GET'])
@authenticate_token
def get_subject(subject_id, current_user_id):
    """
    Get a subject by ID
    """
    try:
        # Get the subject
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Check if the subject belongs to the authenticated user
        if subject.user_id != current_user_id:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Get all chapters for this subject
        chapters = Chapter.query.filter_by(subject_id=subject_id).all()

        # Format the response
        chapters_data = []
        for chapter in chapters:
            # Get all objectives for this chapter
            objectives = Objective.query.filter_by(chapter_id=chapter.id).all()

            # Format the objectives
            objectives_data = []
            for objective in objectives:
                # Handle different field names for objective text
                objective_text = None
                if hasattr(objective, 'objective'):
                    objective_text = objective.objective
                elif hasattr(objective, 'text'):
                    objective_text = objective.text
                else:
                    objective_text = 'Unknown objective'

                objectives_data.append({
                    'id': objective.id,
                    'text': objective_text,
                    'estimated_time_minutes': getattr(objective, 'estimated_time_minutes', 30),
                    'difficulty_level': getattr(objective, 'difficulty_level', 2)
                })

            # Add the chapter to the response
            chapters_data.append({
                'id': chapter.id,
                'name': chapter.name,
                'objectives': objectives_data
            })

        return jsonify({
            'success': True,
            'subject': {
                'id': subject.id,
                'name': subject.name,
                'user_id': subject.user_id,
                'chapters': chapters_data
            }
        }), 200

    except Exception as e:
        logging.error(f"Error getting subject {subject_id}: {str(e)}")
        return jsonify({'success': False, 'error': f'Error getting subject: {str(e)}'}), 500

@app.route('/api/delete_study_plan/<int:plan_id>', methods=['DELETE'])
@authenticate_token
def delete_study_plan(plan_id, current_user_id):
    """
    Delete a study plan and all associated schedule items
    """
    try:
        # Get the study plan
        study_plan = StudyPlan.query.get(plan_id)
        if not study_plan:
            return jsonify({'success': False, 'error': 'Study plan not found'}), 404

        # Verify the study plan belongs to the authenticated user
        if study_plan.user_id != current_user_id:
            logging.warning(f"Access denied: User {current_user_id} attempted to delete study plan {plan_id}")
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Delete all schedule items associated with this study plan
        schedule_items = ScheduleItem.query.filter_by(study_plan_id=plan_id).all()
        for item in schedule_items:
            db.session.delete(item)

        # Delete the study plan
        db.session.delete(study_plan)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Study plan deleted successfully'
        }), 200

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error deleting study plan: {str(e)}")
        logging.error(f"Exception traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': f'Error deleting study plan: {str(e)}'
        }), 500

@app.route('/test_db', methods=['GET'])
@authenticate_token  # Require authentication for database tests
@limiter.limit("5/hour")  # Limit access to prevent abuse
def test_db(current_user_id):
    """
    Test endpoint to verify database connectivity and schema

    Args:
        current_user_id: The ID of the authenticated user (required for @authenticate_token)
    """
    # Log the user who is performing the database test
    logging.info(f"Database test requested by user ID: {current_user_id}")
    try:
        # Test database connection
        db.session.execute(db.text("SELECT 1")).scalar_one()
        logging.info("Database connection test successful")

        # Check if tables exist
        from sqlalchemy import inspect
        inspector = inspect(db.engine)
        existing_tables = inspector.get_table_names()

        # Check for required tables
        required_tables = ['users', 'curricula', 'subjects', 'chapters', 'objectives', 'study_plans', 'schedule_items']
        missing_tables = [table for table in required_tables if table not in existing_tables]

        # Get table counts using parameterized queries
        table_counts = {}
        for table in existing_tables:
            if table in required_tables:
                # Use parameterized query with bind parameters to prevent SQL injection
                # SQLAlchemy doesn't allow parameterizing table names, so we validate against a whitelist
                if table in required_tables:  # Double-check against whitelist
                    # Use text() with parameters for safe queries
                    query = db.text(f"SELECT COUNT(*) FROM {table}")
                    count = db.session.execute(query).scalar_one()
                    table_counts[table] = count

        # Get sample data using parameterized queries
        sample_data = {}

        # Safe queries with proper parameterization
        if 'subjects' in existing_tables:
            query = db.text("SELECT id, name FROM subjects LIMIT :limit")
            subjects = db.session.execute(query, {"limit": 5}).fetchall()
            sample_data['subjects'] = [{'id': s[0], 'name': s[1]} for s in subjects]

        if 'chapters' in existing_tables:
            query = db.text("SELECT id, subject_id, title FROM chapters LIMIT :limit")
            chapters = db.session.execute(query, {"limit": 5}).fetchall()
            sample_data['chapters'] = [{'id': c[0], 'subject_id': c[1], 'name': c[2]} for c in chapters]

        if 'objectives' in existing_tables:
            query = db.text("SELECT id, chapter_id, text FROM objectives LIMIT :limit")
            objectives = db.session.execute(query, {"limit": 5}).fetchall()
            sample_data['objectives'] = [{'id': o[0], 'chapter_id': o[1], 'text': o[2][:50] if o[2] else None} for o in objectives]

        return jsonify({
            'success': True,
            'message': 'Database connection successful',
            'tables': {
                'existing': existing_tables,
                'missing': missing_tables,
                'counts': table_counts
            },
            'sample_data': sample_data
        }), 200

    except Exception as e:
        logging.error(f"Database test error: {str(e)}")
        logging.debug(f"Exception traceback: {traceback.format_exc()}")

        # Return generic error message without exposing details
        return jsonify({
            'success': False,
            'message': 'Database test failed',
            'error': 'An error occurred while testing the database connection'
        }), 500

@app.route('/', methods=['GET'])
def home():
    """
    Root endpoint to verify the API is running
    """
    return jsonify({
        'success': True,
        'message': 'Gemini AI API is running on port 5000!',
        'endpoints': {
            '/test': 'Test endpoint',
            '/chat': 'Chat with Gemini AI'
        }
    }), 200

@app.route('/api_test', methods=['GET', 'OPTIONS'])
@app.route('/test', methods=['GET', 'OPTIONS'])  # Keep the original route for compatibility
def api_test():
    """
    Test endpoint to verify the API is accessible from the frontend
    This endpoint doesn't require authentication for testing purposes
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = jsonify({'success': True})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response

    # Log the test request
    logging.info(f"Test endpoint accessed from: {request.remote_addr}")

    return jsonify({
        'success': True,
        'message': 'API connection successful!',
        'version': '1.0.0',
        'status': 'online'
    }), 200

@app.route('/chat', methods=['OPTIONS'])
@app.route('/api/chat', methods=['OPTIONS'])  # Added API route for frontend compatibility
def chat_preflight():
    """
    Handle OPTIONS preflight request for the chat endpoint
    This is needed to properly handle CORS
    """
    response = jsonify({'success': True})
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
    return response

@app.route('/chat', methods=['POST'])
@app.route('/api/chat', methods=['POST'])  # Added API route for frontend compatibility
@authenticate_token  # Require authentication for all chat requests
@limiter.limit("60/hour")  # Rate limit to prevent abuse
def chat(current_user_id):
    """
    Process a chat message with Gemini AI and return a response

    If MOCK_AI=true is set in environment variables, this will use a mock implementation
    instead of calling the Gemini API. This is useful for testing when the API key is not available.

    Args:
        current_user_id: The ID of the authenticated user (required for @authenticate_token)
    """
    try:
        # Check if request has JSON data
        if not request.is_json:
            logging.error("Request does not contain JSON data")
            return jsonify({'success': False, 'error': 'Request must be JSON'}), 400

        # Validate and sanitize input data
        data = request.json

        # Validate required fields
        if not data or 'message' not in data:
            logging.error("Missing required field: message")
            return jsonify({'success': False, 'error': 'Missing required field: message'}), 400

        message = data.get('message', '').strip()
        if not message:
            logging.error("Empty message provided in request")
            return jsonify({'success': False, 'error': 'Empty message provided'}), 400

        # Limit message length to prevent abuse
        if len(message) > 1000:
            logging.warning(f"Message too long ({len(message)} chars) from user ID: {current_user_id}")
            return jsonify({'success': False, 'error': 'Message too long (max 1000 characters)'}), 400

        # Validate and sanitize history
        history = data.get('history', [])
        if not isinstance(history, list):
            history = []

        # Limit history size to prevent abuse
        if len(history) > 20:
            history = history[-20:]  # Keep only the most recent 20 messages

        # Get user information from database
        try:
            user = User.query.get(current_user_id)
            if user and user.full_name:
                full_name = user.full_name
            else:
                full_name = data.get('user_name', 'Student')
        except Exception as e:
            logging.error(f"Error fetching user data: {str(e)}")
            full_name = data.get('user_name', 'Student')

        # Extract first name from full name with proper sanitization
        if full_name and ' ' in full_name:
            # Split the name and take the first part as the first name
            user_name = full_name.split(' ')[0]
        elif '@' in full_name:
            # If it's an email, extract the part before @
            user_name = full_name.split('@')[0]
        else:
            # If it's already a single name or the default, use as is
            user_name = full_name

        # Sanitize user name to prevent injection attacks
        user_name = re.sub(r'[^\w\s]', '', user_name)[:30]  # Remove special chars and limit length

        logging.info(f"Processing chat request for user ID: {current_user_id}")

        # Get user information for context
        user_context = {}

        # Log the message being processed
        logging.info(f"Processing message: {message}")

        # Create a detailed system prompt for Gemini with enhanced context
        prompt = f"""
You are Blueprint Assistant, an advanced AI tutor developed for BlueprintAI, an innovative learning platform designed to empower students through personalized education. Your purpose is to provide highly accurate, intelligent, and engaging responses to educational queries, fostering deep understanding and critical thinking.

**Understanding {user_name}**:
- I understand your name is {user_name}. I'll aim to weave your name into our conversations naturally, as a teacher might address a student in class, rather than starting every interaction with a formal greeting.
- When information is available or can be inferred, I will consider your academic level, preferred learning style (e.g., visual, auditory, kinesthetic), and specific goals (e.g., exam preparation, concept mastery, skill development) to tailor my guidance.
- In the absence of specific details, I will approach our conversation assuming a generally curious and engaged learner, adjusting the complexity of my explanations based on the nature of your questions.

**My Approach to Helping You Learn**:
- I aim to illuminate complex ideas with clarity, drawing upon analogies, real-world connections, and insights from various disciplines to foster a richer understanding. Think of it as connecting the dots in your learning journey.
- When you're tackling problems, I won't just give you the answer. Instead, I'll offer step-by-step support, posing thoughtful questions that encourage you to think through the process yourself. This way, you'll develop stronger problem-solving skills.
- Learning isn't just about understanding the material; it's also about how you approach it. Let's explore some study strategies that might work well for you, including tips on time management, effective note-taking, and memory techniques.
- No matter the subject – from the intricacies of science and math to the nuances of the humanities – I'll strive to provide you with well-supported and current information.
- To make our learning more dynamic, I might suggest follow-up questions, exercises, or activities that can help solidify your grasp of the concepts we discuss.
- Complex topics can sometimes feel overwhelming. I'll break them down into more manageable pieces, building your understanding step by step, like constructing a strong foundation.
- Along the way, I'll also try to anticipate common stumbling blocks or misunderstandings, addressing them proactively to ensure you have a clear path forward.

**Guiding Principles for Our Interactions**:
- **Accuracy and Trustworthiness**: You can count on me to always strive to provide you with accurate information that is factually sound and based on reliable knowledge. If I'm ever unsure, I'll be transparent about it and point you towards trusted resources.
- **Clarity and Structure**: My responses will be organized logically, using headings, bullet points, or numbered steps where helpful. I'll start with a clear overview, delve into the details, and conclude with a summary or actionable insights.
- **Engagement and Encouragement**: I want to create a supportive and encouraging space for your learning. My tone will be conversational and empathetic, avoiding overly technical language unless you specifically need it.
- **Personalized Support**: I'll keep your context in mind, {user_name}, and tailor my responses to your goals and any challenges you might be facing. My advice will aim to be practical and relevant to your academic pursuits.
- **Respect and Inclusivity**: I will always be mindful of different perspectives and backgrounds, ensuring my responses are inclusive and respectful of diverse educational practices and terminology.
- **Interactive Learning**: Where appropriate, I'll incorporate thought-provoking questions or prompts to encourage deeper engagement and reflection on what you're learning.
- **Real-World Connections**: I believe learning is most meaningful when you can see its relevance in the world around you. I'll aim to connect academic concepts to real-world applications and current events.
- **Developing Your Learning Skills**: I'll also encourage you to think about how you learn best, suggesting strategies for monitoring your progress and adapting your approach as needed.

**Understanding Your Learning Journey, {user_name}**:
- I recognize that learning can sometimes be challenging. If you express frustration or difficulty, I will respond with empathy and offer alternative explanations or strategies to help you overcome these hurdles.
- I will strive to understand not just *what* you're asking, but also *why* you're asking it. This deeper understanding will allow me to provide more relevant and supportive guidance.

**My Role as Your Supporter**:
- Think of me as a supportive partner in your learning. I'm here to encourage your curiosity, celebrate your progress, and help you navigate any obstacles you encounter.
- I will aim to build your confidence by highlighting your strengths and focusing on your potential for growth.

**Fostering Your Intellectual Growth**:
- I will not just provide answers but also encourage you to question assumptions, explore different perspectives, and form your own informed opinions.
- I may sometimes offer a different angle to consider (in a constructive and respectful way) to challenge your thinking and help you develop stronger arguments and a deeper understanding.
- I will encourage you to connect new information to what you already know, fostering a more integrated and meaningful understanding of the subject matter.

**Making Learning Engaging and Memorable**:
- Where appropriate, I will try to illustrate concepts through relatable stories, historical anecdotes, or even thought experiments presented as narratives to make learning more engaging and memorable.
- I believe that weaving a narrative around a topic can help make abstract ideas more concrete and easier to grasp.

**Supporting Your Academic Goals**:
- If you share your learning goals with me, I can help you break them down into smaller, more manageable steps and suggest strategies for achieving them.
- I can also help you reflect on your progress and identify areas where you might need to focus more effort.

**Specific Guidance**:
- For mathematical or technical questions, I will provide clear, step-by-step derivations or code (if applicable), explaining the reasoning behind each step using $\\LaTeX$ for mathematical expressions and code blocks for programming.
- If you ask open-ended or unclear questions, I'll ask clarifying questions to make sure I understand what you're really looking for.
- If relevant external resources can enhance our discussion, I'll integrate credible information and cite my sources appropriately.
- I am committed to ethical behavior, avoiding bias, respecting intellectual property, and focusing on appropriate educational content.
- If you'd like to explore creative tasks, such as developing a study plan or understanding a concept through a story, I'll aim to provide structured and relevant output aligned with your learning objectives.

**What to Expect from My Responses** (Example Structure):
1. **Setting the Stage**: Briefly acknowledging your question and outlining what we'll cover.
2. **Exploring the Core**: Providing a detailed explanation with examples, analogies, or visual aids (if applicable).
3. **Connecting to Life**: Showing how this concept applies in practical or real-world situations.
4. **Thinking Deeper**: Offering a follow-up question, exercise, or study tip to encourage further learning.
5. **Bringing it Together**: Summarizing the key takeaways and offering a word of encouragement.


**User Context**:
- The user's name is {user_name}.
- IMPORTANT: Avoid repetitive greetings like "Hey {user_name}" or "Hello {user_name}" at the start of every message. Use the user's name naturally and conversationally, but not in a formulaic way.
- If available, adapt to the user's academic level, learning style (e.g., visual, auditory, kinesthetic), and specific goals (e.g., exam preparation, concept mastery, skill development) based on provided or inferred information.
- If no user context is provided, assume a general academic audience and adjust complexity based on the question's depth.

**Capabilities**:
- Explain complex concepts with clarity, using analogies, real-world examples, and interdisciplinary connections to enhance comprehension.
- Provide step-by-step problem-solving guidance, incorporating Socratic questioning to encourage critical thinking and self-discovery.
- Offer personalized study strategies, including time management, note-taking, and mnemonic techniques, tailored to the user’s needs.
- Answer questions across all academic disciplines, from STEM to humanities, with evidence-based and up-to-date information.
- Support interactive learning by suggesting follow-up questions, exercises, or activities to reinforce understanding.
- Break down advanced topics into manageable parts, using scaffolding techniques to build knowledge progressively.
- Anticipate common misconceptions and address them proactively with clear explanations.

**Response Guidelines**:
- **Accuracy and Evidence**: Ensure all responses are factually accurate, grounded in reliable knowledge, and free from speculation. If information is uncertain or unavailable, acknowledge this transparently and suggest alternative ways to explore the topic.
- **Structure and Clarity**: Organize responses with a logical flow, using headings, bullet points, or numbered steps when appropriate. Begin with a brief overview, followed by detailed explanations, and conclude with a summary or actionable takeaways.
- **Engagement and Tone**: Use a conversational, encouraging, and empathetic tone to create a supportive learning environment. Avoid overly technical jargon unless requested, and adapt language to the user’s level of understanding.
- **Personalization**: Tailor responses to the user’s context, incorporating their name, goals, or specific challenges when relevant. Offer practical advice that aligns with their academic or professional objectives.
- **Cultural Sensitivity**: Be mindful of cultural, regional, or contextual differences in educational practices and terminology, ensuring inclusivity and respect.
- **Interactivity**: When appropriate, include interactive elements such as thought experiments, quizzes, or prompts for reflection to deepen engagement.
- **Real-World Relevance**: Connect academic concepts to real-world applications or current events to make learning meaningful and engaging.
- **Metacognition**: Encourage users to reflect on their learning process by suggesting strategies to monitor progress, assess understanding, and adjust approaches as needed.

**Special Instructions**:
- For mathematical or technical queries, provide clear, step-by-step derivations or code (if applicable) with explanations of each step. Use LaTeX for mathematical expressions or code blocks for programming.
- For open-ended or ambiguous questions, ask clarifying questions to ensure the response aligns with the user’s intent.
- If external resources (e.g., web or X posts) are available, integrate relevant, credible information to enhance responses, citing sources appropriately.
- Maintain ethical integrity by avoiding bias, respecting intellectual property, and refraining from generating or reproducing inappropriate content.
- If the user requests creative tasks (e.g., writing a story or generating a study plan), ensure the output is structured, relevant, and aligned with educational goals.

**Error Handling**:
- If a query is unclear, politely request clarification while offering a general response based on reasonable assumptions.
- If a topic is beyond your knowledge, admit the limitation and suggest reputable resources or alternative approaches to explore the topic.

**Example Response Structure** (for a complex query):
1. **Introduction**: Briefly restate the question and outline the response.
2. **Core Explanation**: Provide a detailed answer with examples, analogies, or visuals (if applicable).
3. **Application**: Connect the concept to practical or real-world scenarios.
4. **Reflection/Activity**: Suggest a follow-up question, exercise, or study tip to reinforce learning.
5. **Summary**: Recap key points and offer encouragement.

Your ultimate goal is to empower {user_name} to achieve academic success and intellectual growth by delivering responses that are insightful, actionable, and inspiring.

IMPORTANT FORMATTING INSTRUCTIONS:
1. My aim, {user_name}, is to be more than just a source of information. I want to be a trusted guide and mentor, helping you not only understand the material but also develop the skills and confidence to become a lifelong learner.
2. Use **bold text** (with double asterisks) for important concepts, key terms, or main points to make them visually stand out.
3. Avoid repetitive greetings like "Hey {user_name}" or "Hello {user_name}" at the start of every message.
4. Use the user's name naturally and conversationally, but not in a formulaic way.
"""

        # Add context about user's learning data if available
        if user_context:
            prompt += "Here's some context about the user's learning data:\n"

            if 'curricula' in user_context:
                prompt += f"- The user has {len(user_context['curricula'])} curricula uploaded"
                if len(user_context['curricula']) > 0:
                    prompt += f", including '{user_context['curricula'][0]['title']}'"
                prompt += ".\n"

            if 'subjects' in user_context:
                prompt += f"- The user is studying subjects like: {', '.join([s['name'] for s in user_context['subjects'][:3]])}.\n"

            if 'study_plans' in user_context:
                prompt += f"- The user has {len(user_context['study_plans'])} study plans.\n"

            if 'progress' in user_context:
                progress = user_context['progress']
                prompt += f"- The user has completed {progress['completed']} out of {progress['total']} scheduled learning items ({progress['percentage']}%).\n"

        # Add conversation history if available
        if history and len(history) > 0:
            prompt += "\nHere's the recent conversation history:\n"
            for entry in history[-5:]:  # Include up to 5 most recent messages
                role = entry.get('role', '')
                content = entry.get('content', '')
                if role == 'user':
                    prompt += f"User: {content}\n"
                elif role == 'assistant':
                    prompt += f"Assistant: {content}\n"

        prompt += f"""
        Respond to the following message from the user in a helpful, concise, and friendly manner.
        Focus on providing educational assistance and study tips.
        If the user asks about their learning progress or subjects, use the context provided.
        Keep responses under 150 words for better readability in the chat interface.

        User message: {message}
        """

        # Always use the real Gemini AI implementation
        logging.info("Using real Gemini AI implementation")

        # Check if Gemini model is initialized
        if model is None:
            logging.error("Gemini model is not initialized")
            return jsonify({
                'success': False,
                'error': 'Gemini model is not initialized',
                'message': 'The AI service is not properly configured. Please set the GOOGLE_API_KEY environment variable.'
            }), 500

        # Log the prompt being sent to Gemini (for debugging)
        logging.info(f"Sending prompt to Gemini: {prompt[:100]}...")

        # Define safety settings
        safety_settings = [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]

        # Optimize generation config for more intelligent responses
        generation_config = {
            "temperature": 0.2,           # Lower temperature for more focused, precise responses
            "top_p": 0.95,                # Keep high top_p for good quality
            "top_k": 40,                  # Standard top_k
            "max_output_tokens": 2048,    # Increased token limit for more detailed responses
            "candidate_count": 1,         # Generate a single high-quality response
            "stop_sequences": [],         # No stop sequences
            "presence_penalty": 0.2,      # Slight penalty to avoid repetition
            "frequency_penalty": 0.2      # Slight penalty to encourage diverse vocabulary
        }

        # Format the request for the Gemini API using the correct format
        # Based on the example: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent

        # Prepare conversation history
        conversation_text = ""

        # Add system prompt
        conversation_text += f"System Instructions: {prompt}\n\n"

        # Add conversation history if available
        if history and len(history) > 0:
            for entry in history[-5:]:  # Include up to 5 most recent messages
                role = entry.get('role', '')
                content_text = entry.get('content', '')

                if role == 'user':
                    conversation_text += f"User: {content_text}\n\n"
                elif role == 'assistant':
                    conversation_text += f"Assistant: {content_text}\n\n"

        # Add the current user message
        conversation_text += f"User: {message}"

        # Create the content in the format expected by the API
        content = [{
            "parts": [{"text": conversation_text}]
        }]

        try:
            # Try with safety settings and generation config
            response = model.generate_content(
                content,
                safety_settings=safety_settings,
                generation_config=generation_config
            )
        except Exception as e:
            logging.warning(f"Failed to generate with full config, trying with simplified config: {str(e)}")
            try:
                # Try with just the content and minimal config
                simplified_config = {
                    "temperature": 0.2,
                    "max_output_tokens": 2048
                }
                response = model.generate_content(content, generation_config=simplified_config)
            except Exception as e:
                logging.warning(f"Failed with simplified config, trying with content only: {str(e)}")
                try:
                    # Try with just the content
                    response = model.generate_content(content)
                except Exception as e:
                    logging.warning(f"Failed with structured content, trying simplified content: {str(e)}")
                    try:
                        # Try with simplified content structure (just the message)
                        simple_content = [{
                            "parts": [{"text": f"Instructions: {prompt}\n\nUser: {message}"}]
                        }]
                        response = model.generate_content(simple_content)
                    except Exception as e:
                        logging.warning(f"Failed with simplified content, trying legacy format: {str(e)}")
                        # Fallback to legacy format as last resort
                        response = model.generate_content(f"Instructions: {prompt}\n\nUser: {message}")

        # Extract response text (handle different response formats)
        try:
            # New API format
            ai_response = response.text.strip()
        except AttributeError:
            try:
                # Alternative format
                ai_response = response.candidates[0].content.parts[0].text.strip()
            except (AttributeError, IndexError):
                # Last resort
                ai_response = str(response).strip()

        # Log successful response
        logging.info(f"Received response from Gemini: {ai_response[:100]}...")

        # Return the successful response
        return jsonify({
            'success': True,
            'response': ai_response
        }), 200

    except Exception as e:
        logging.error(f"Error processing chat message: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error processing chat message: {str(e)}',
            'message': 'There was an error processing your chat message.'
        }), 500

@app.route('/text-to-speech', methods=['POST', 'OPTIONS'])
def text_to_speech():
    """
    Convert text to speech using a realistic voice API

    Note: This endpoint doesn't require authentication for testing purposes.
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = jsonify({'success': True})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response
    try:
        data = request.json
        if not data or 'text' not in data:
            return jsonify({'success': False, 'error': 'No text provided'}), 400

        text = data.get('text', '')
        voice_type = data.get('voice_type', 'female')  # 'female' or 'male'

        # Get user name if provided
        if 'user_name' in data:
            full_name = data.get('user_name', 'Student')

            # Extract first name from full name
            if full_name and ' ' in full_name:
                # Split the name and take the first part as the first name
                user_name = full_name.split(' ')[0]
            elif '@' in full_name:
                # If it's an email, extract the part before @
                user_name = full_name.split('@')[0]
            else:
                # If it's already a single name or the default, use as is
                user_name = full_name

            # Replace any occurrences of the full name with just the first name in the text
            if full_name != user_name:
                text = text.replace(full_name, user_name)

        # For now, we'll use a mock implementation that returns a base64-encoded audio file
        # In a real implementation, you would call a TTS service API like Google Cloud TTS

        # Mock implementation - in production, replace with actual API call
        # This simulates getting audio data from a TTS service
        mock_audio_data = generate_mock_audio(text, voice_type)

        return jsonify({
            'success': True,
            'audio': mock_audio_data,
            'format': 'audio/mp3',
            'text': text
        })

    except Exception as e:
        logging.error(f"Error in text-to-speech: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

def generate_mock_audio(text, voice_type):
    """
    Generate a mock audio response
    In a real implementation, this would call a TTS service API
    """
    # This is a placeholder - in a real implementation, you would:
    # 1. Call a TTS API (Google Cloud, Amazon Polly, etc.)
    # 2. Get the audio data
    # 3. Return it as base64-encoded string

    # For now, we'll return a message explaining that this is a mock
    mock_message = f"This is a mock audio response. In a real implementation, you would hear '{text}' in a {voice_type} voice."

    # In a real implementation, this would be actual audio data
    # For now, we'll encode the message as base64 to simulate audio data
    mock_audio_base64 = base64.b64encode(mock_message.encode('utf-8')).decode('utf-8')

    return mock_audio_base64


# Helper function for chapter-specific content extraction
def extract_chapter_specific_content(full_content: str, chapters_info: List[Dict]) -> str:
    """
    Extract content specific to selected chapters from the full document content

    Args:
        full_content: The complete document content
        chapters_info: List of chapter dictionaries with chapter_number and title

    Returns:
        Extracted content for the specified chapters
    """
    try:
        if not chapters_info:
            return full_content

        logging.info(f"Extracting content for {len(chapters_info)} chapters")

        # Create patterns to match chapter headings
        chapter_patterns = []
        for chapter in chapters_info:
            chapter_num = chapter.get('chapter_number', '').strip()
            chapter_title = chapter.get('title', '').strip()

            # Create flexible patterns for chapter matching
            patterns = []

            if chapter_num and chapter_title:
                # Pattern: "Chapter 1: Title" or "Chapter 1 - Title"
                patterns.extend([
                    rf"(?i)^{re.escape(chapter_num)}\s*[:\-]\s*{re.escape(chapter_title)}",
                    rf"(?i)^{re.escape(chapter_num)}\s+{re.escape(chapter_title)}",
                    rf"(?i)^chapter\s+\d+\s*[:\-]\s*{re.escape(chapter_title)}",
                ])

            if chapter_num:
                # Pattern: "Chapter 1" or just the number
                patterns.extend([
                    rf"(?i)^{re.escape(chapter_num)}(?:\s|$)",
                    rf"(?i)^chapter\s+{re.escape(chapter_num.split()[-1])}(?:\s|$)",
                ])

            if chapter_title:
                # Pattern: Just the title
                patterns.append(rf"(?i)^{re.escape(chapter_title)}(?:\s|$)")

            chapter_patterns.extend(patterns)

        # Split content into lines for processing
        lines = full_content.split('\n')
        extracted_content = []
        current_chapter_content = []
        in_target_chapter = False

        for i, line in enumerate(lines):
            line_stripped = line.strip()

            # Check if this line starts a new chapter
            is_chapter_start = False
            is_target_chapter = False

            # Check against our target chapter patterns
            for pattern in chapter_patterns:
                if re.match(pattern, line_stripped):
                    is_chapter_start = True
                    is_target_chapter = True
                    break

            # Check if this is any chapter start (to know when to stop)
            if not is_target_chapter:
                # Generic chapter patterns
                generic_patterns = [
                    r'(?i)^chapter\s+\d+',
                    r'^\d+\.\s+',
                    r'^\d+\s*[:\-]',
                    r'(?i)^unit\s+\d+',
                    r'(?i)^section\s+\d+',
                ]

                for pattern in generic_patterns:
                    if re.match(pattern, line_stripped):
                        is_chapter_start = True
                        break

            # Handle chapter transitions
            if is_chapter_start:
                # Save previous chapter content if it was a target chapter
                if in_target_chapter and current_chapter_content:
                    extracted_content.extend(current_chapter_content)
                    current_chapter_content = []

                # Start new chapter
                in_target_chapter = is_target_chapter
                if in_target_chapter:
                    current_chapter_content = [line]
                    logging.info(f"Found target chapter: {line_stripped[:100]}...")
            elif in_target_chapter:
                # Add content to current target chapter
                current_chapter_content.append(line)

        # Don't forget the last chapter
        if in_target_chapter and current_chapter_content:
            extracted_content.extend(current_chapter_content)

        result_content = '\n'.join(extracted_content).strip()

        # If no specific content found, try a more lenient approach
        if not result_content:
            logging.warning("No content found with strict matching, trying lenient approach")

            # Try to find content by searching for chapter titles anywhere in the text
            for chapter in chapters_info:
                chapter_title = chapter.get('title', '').strip()
                if chapter_title:
                    # Find paragraphs containing the chapter title
                    paragraphs = full_content.split('\n\n')
                    for para in paragraphs:
                        if chapter_title.lower() in para.lower():
                            extracted_content.append(para)

        result_content = '\n'.join(extracted_content).strip()

        if result_content:
            logging.info(f"Successfully extracted {len(result_content)} characters for selected chapters")
            return result_content
        else:
            logging.warning("Could not extract specific chapter content, using full document")
            return full_content

    except Exception as e:
        logging.error(f"Error extracting chapter content: {str(e)}")
        return full_content


# Quiz Management API Endpoints

@app.route('/api/generate-quiz', methods=['POST'])
@authenticate_token
def generate_quiz_api(current_user_id):
    """
    Generate a quiz using AI from uploaded document content
    """
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['subject_id', 'question_types', 'difficulty']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        subject_id = data['subject_id']
        question_types = data['question_types']
        difficulty = data['difficulty']
        chapter_ids = data.get('chapter_ids', [])  # Optional list of chapter IDs

        # Validate question types
        valid_types = ['multiple_choice', 'short_answer']
        if not isinstance(question_types, list) or not question_types:
            return jsonify({'success': False, 'error': 'question_types must be a non-empty list'}), 400

        for q_type in question_types:
            if q_type not in valid_types:
                return jsonify({'success': False, 'error': f'Invalid question type: {q_type}. Valid types: {valid_types}'}), 400

        # Validate difficulty
        if difficulty not in ['easy', 'medium', 'hard']:
            return jsonify({'success': False, 'error': 'Invalid difficulty. Must be easy, medium, or hard'}), 400

        # Get the subject and verify ownership - Use Session.get() instead of Query.get()
        subject = db.session.get(Subject, subject_id)
        if not subject:
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Check if the subject belongs to the authenticated user
        if subject.user_id != current_user_id:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Get the uploaded file for this subject
        file_record = File.query.filter_by(subject_id=subject_id).first()
        if not file_record:
            return jsonify({'success': False, 'error': 'No uploaded file found for this subject'}), 400

        # Read the file content
        try:
            file_path = file_record.filepath
            if not file_path:
                return jsonify({'success': False, 'error': 'File path not found in database'}), 400

            # Try different path variations to find the file
            possible_paths = [
                file_path,  # Original path
                os.path.join('..', file_path),  # Relative to project root
                os.path.join('..', 'Frontend', file_path),  # Relative to Frontend
                os.path.join('Frontend', file_path)  # Direct Frontend path
            ]

            actual_file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    actual_file_path = path
                    break

            if not actual_file_path:
                return jsonify({'success': False, 'error': f'File not found on disk. Tried paths: {possible_paths}'}), 400

            # Extract content based on file type
            content = ""
            filename = file_record.originalname.lower()

            if filename.endswith('.pdf'):
                # Extract text from PDF
                import PyPDF2
                with open(actual_file_path, 'rb') as pdf_file:
                    pdf_reader = PyPDF2.PdfReader(pdf_file)
                    for page_num in range(len(pdf_reader.pages)):
                        page_text = pdf_reader.pages[page_num].extract_text()
                        content += page_text + "\n\n"

            elif filename.endswith('.docx'):
                # Extract text from DOCX
                import docx
                doc = docx.Document(actual_file_path)
                for para in doc.paragraphs:
                    if para.text.strip():
                        content += para.text + "\n"
                # Also extract text from tables
                for table in doc.tables:
                    for row in table.rows:
                        row_text = ""
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text += cell.text + " | "
                        if row_text:
                            content += row_text.rstrip(" | ") + "\n"

            elif filename.endswith('.txt'):
                # Read text file directly
                with open(actual_file_path, 'r', encoding='utf-8') as txt_file:
                    content = txt_file.read()

            else:
                return jsonify({'success': False, 'error': f'Unsupported file format: {filename}'}), 400

            if not content or not content.strip():
                return jsonify({'success': False, 'error': 'No content could be extracted from the file'}), 400

            # Get all chapters for this subject to determine if "all" are selected
            all_chapters = Chapter.query.filter_by(subject_id=subject_id).all()
            total_chapters = len(all_chapters)

            # Process chapter selection logic
            chapters_info = []
            is_all_chapters_selected = False

            if chapter_ids:
                # Check if all chapters are selected
                if len(chapter_ids) == total_chapters:
                    is_all_chapters_selected = True
                    logging.info(f"All {total_chapters} chapters selected - processing entire document")
                    # Get all chapters info
                    for chapter in all_chapters:
                        chapters_info.append({
                            'id': chapter.id,
                            'chapter_number': chapter.chapter_number,
                            'title': chapter.title
                        })
                else:
                    logging.info(f"Selected {len(chapter_ids)} out of {total_chapters} chapters - processing specific chapters only")
                    # Get only selected chapters
                    for chapter_id in chapter_ids:
                        chapter = Chapter.query.join(Subject).filter(
                            Chapter.id == chapter_id,
                            Chapter.subject_id == subject_id,
                            Subject.user_id == current_user_id
                        ).first()
                        if chapter:
                            chapters_info.append({
                                'id': chapter.id,
                                'chapter_number': chapter.chapter_number,
                                'title': chapter.title
                            })

            # If specific chapters are selected (not all), extract only their content
            if not is_all_chapters_selected and chapters_info:
                content = extract_chapter_specific_content(content, chapters_info)
                if not content or not content.strip():
                    return jsonify({'success': False, 'error': 'No content could be extracted for the selected chapters'}), 400

        except Exception as e:
            logging.error(f"Error reading file content: {str(e)}")
            return jsonify({'success': False, 'error': f'Error reading file: {str(e)}'}), 500

        # Generate the quiz using AI
        from document_processor import generate_quiz_with_ai_enhanced
        result = generate_quiz_with_ai_enhanced(content, subject_id, question_types, difficulty, chapters_info, is_all_chapters_selected)

        if not result.get('success'):
            return jsonify(result), 500

        quiz_data = result['quiz_data']

        # Add metadata
        quiz_data['subject_id'] = subject_id
        quiz_data['ai_generated'] = True
        quiz_data['question_types'] = question_types
        if chapters_info:
            quiz_data['chapters_info'] = chapters_info
            # For backward compatibility, set chapter_id to the first chapter if only one is selected
            if len(chapters_info) == 1:
                quiz_data['chapter_id'] = chapters_info[0]['id']

        return jsonify({
            'success': True,
            'quiz': quiz_data
        })

    except Exception as e:
        logging.error(f"Error generating quiz: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500


@app.route('/api/quizzes', methods=['POST'])
@authenticate_token
def create_quiz(current_user_id):
    """
    Save a quiz to the database
    """
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['subject_id', 'title', 'questions']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        subject_id = data['subject_id']

        # Verify subject ownership - Use Session.get() instead of Query.get()
        subject = db.session.get(Subject, subject_id)
        if not subject:
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Check if the subject belongs to the authenticated user
        if subject.user_id != current_user_id:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Create the quiz
        quiz = Quiz(
            user_id=current_user_id,
            subject_id=subject_id,
            chapter_id=data.get('chapter_id'),  # Include chapter_id if provided
            title=data['title'],
            description=data.get('description', ''),
            difficulty=data.get('difficulty', 'medium'),
            time_limit=data.get('time_limit', 0),
            total_questions=len(data['questions']),
            total_points=sum(q.get('points', 1) for q in data['questions']),
            question_types=data.get('question_types', []),
            ai_generated=data.get('ai_generated', False),
            ai_prompt=data.get('ai_prompt', '')
        )

        db.session.add(quiz)
        db.session.flush()  # Get the quiz ID

        # Create the questions
        for question_data in data['questions']:
            question = QuizQuestion(
                quiz_id=quiz.id,
                question_number=question_data.get('question_number', 1),
                question_text=question_data['question_text'],
                question_type=question_data.get('question_type', 'multiple_choice'),
                options=question_data.get('options'),
                correct_answer=question_data['correct_answer'],
                explanation=question_data.get('explanation', ''),
                points=question_data.get('points', 1),
                difficulty_level=question_data.get('difficulty_level', 'medium')
            )
            db.session.add(question)

        db.session.commit()

        return jsonify({
            'success': True,
            'quiz_id': quiz.id,
            'message': 'Quiz created successfully'
        })

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error creating quiz: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500


@app.route('/api/quizzes/<int:subject_id>', methods=['GET'])
@authenticate_token
def get_quizzes(subject_id, current_user_id):
    """
    Get all quizzes for a subject
    """
    try:
        # Verify subject ownership - Use Session.get() instead of Query.get()
        subject = db.session.get(Subject, subject_id)
        if not subject:
            return jsonify({'success': False, 'error': 'Subject not found'}), 404

        # Check if the subject belongs to the authenticated user
        if subject.user_id != current_user_id:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Get all quizzes for this subject
        quizzes = Quiz.query.filter_by(subject_id=subject_id, user_id=current_user_id).all()

        quiz_list = []
        for quiz in quizzes:
            # Get questions for this quiz
            questions = QuizQuestion.query.filter_by(quiz_id=quiz.id).order_by(QuizQuestion.question_number).all()

            quiz_data = {
                'id': quiz.id,
                'title': quiz.title,
                'description': quiz.description,
                'difficulty': quiz.difficulty,
                'time_limit': quiz.time_limit,
                'total_questions': quiz.total_questions,
                'total_points': quiz.total_points,
                'question_types': quiz.question_types,
                'ai_generated': quiz.ai_generated,
                'chapter_id': quiz.chapter_id,
                'chapter_name': quiz.chapter.title if quiz.chapter else None,
                'created_at': quiz.created_at.isoformat(),
                'updated_at': quiz.updated_at.isoformat(),
                'questions': []
            }

            for question in questions:
                question_data = {
                    'id': question.id,
                    'question_number': question.question_number,
                    'question_text': question.question_text,
                    'question_type': question.question_type,
                    'options': question.options,
                    'correct_answer': question.correct_answer,
                    'explanation': question.explanation,
                    'points': question.points,
                    'difficulty_level': question.difficulty_level
                }
                quiz_data['questions'].append(question_data)

            quiz_list.append(quiz_data)

        return jsonify({
            'success': True,
            'quizzes': quiz_list
        })

    except Exception as e:
        logging.error(f"Error getting quizzes: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500


@app.route('/api/quizzes/<int:quiz_id>', methods=['DELETE'])
@authenticate_token
def delete_quiz(quiz_id, current_user_id):
    """
    Delete a quiz
    """
    try:
        quiz = Quiz.query.get(quiz_id)
        if not quiz:
            return jsonify({'success': False, 'error': 'Quiz not found'}), 404

        # Verify ownership
        if quiz.user_id != current_user_id:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Delete the quiz (questions will be deleted automatically due to cascade)
        db.session.delete(quiz)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Quiz deleted successfully'
        })

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error deleting quiz: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500


@app.route('/api/quiz-results', methods=['POST'])
@authenticate_token
def save_quiz_results(current_user_id):
    """
    Save quiz results to the database
    """
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['quiz_id', 'score', 'time_taken', 'answers']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        # Verify the quiz belongs to the user - Use Session.get() instead of Query.get()
        quiz = db.session.get(Quiz, data['quiz_id'])
        if not quiz:
            return jsonify({'success': False, 'error': 'Quiz not found'}), 404

        if quiz.user_id != current_user_id:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Create quiz result record
        quiz_result = QuizResult(
            user_id=current_user_id,
            quiz_id=data['quiz_id'],
            score=data['score'],
            time_taken=data['time_taken'],
            answers=data['answers']
        )

        db.session.add(quiz_result)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Quiz results saved successfully'
        })

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error saving quiz results: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500


@app.route('/api/quiz-attempts', methods=['GET'])
@authenticate_token
def get_quiz_attempts(current_user_id):
    """
    Get all quiz attempts for the authenticated user
    """
    try:
        # Get all quiz results for the user
        quiz_results = QuizResult.query.filter_by(user_id=current_user_id).all()

        attempts = []
        for result in quiz_results:
            attempts.append({
                'id': result.id,
                'quiz_id': result.quiz_id,
                'score': result.score,
                'time_taken': result.time_taken,
                'answers': result.answers,
                'completed_at': result.completed_at.isoformat() if result.completed_at else None
            })

        return jsonify({
            'success': True,
            'attempts': attempts
        })

    except Exception as e:
        logging.error(f"Error getting quiz attempts: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500


# Cornell Notes AI Integration Endpoints

@app.route('/api/ai/generate-concepts-from-file', methods=['POST'])
@authenticate_token
def generate_concepts_from_file(current_user_id):
    """
    Generate key concepts and questions from uploaded file content for Cornell Notes
    This is used before the user takes notes
    """
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['chapter_id', 'subject', 'chapter_title']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        chapter_id = data['chapter_id']
        subject = data['subject']
        chapter_title = data['chapter_title']

        # Get the chapter content from the database
        pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Debug: First check if the chapter exists and get its content
        logging.info(f"Looking for chapter with ID: {chapter_id} for user: {current_user_id}")
        pg_cursor.execute("""
            SELECT c.id, c.title, c.content, c.file_id, s.user_id, s.name as subject_name
            FROM chapters c
            JOIN subjects s ON c.subject_id = s.id
            WHERE c.id = %s AND s.user_id = %s
        """, (chapter_id, current_user_id))

        chapter_result = pg_cursor.fetchone()
        if not chapter_result:
            pg_cursor.close()
            logging.error(f"Chapter {chapter_id} not found for user {current_user_id}")
            return jsonify({
                'success': False,
                'error': f'Chapter {chapter_id} not found or access denied'
            }), 404

        logging.info(f"Found chapter: {chapter_result['title']}, file_id: {chapter_result['file_id']}")

        # Use the helper function to get chapter content
        chapter_content = get_chapter_content_from_db(chapter_id, current_user_id)

        if chapter_content and chapter_content.strip() and not chapter_content.startswith('Content for'):
            # We have actual stored content, use it directly
            logging.info(f"Using stored chapter content ({len(chapter_content)} characters)")
            file_content = chapter_content
            pg_cursor.close()
        else:
            # Fallback to reading from original file if no content is stored
            logging.info(f"No stored content found, falling back to original file")

            # Get the original file path and info for this chapter
            if chapter_result['file_id']:
                pg_cursor.execute("""
                    SELECT f.filepath, f.filename, f.filetype, c.title as chapter_title
                    FROM chapters c
                    JOIN files f ON c.file_id = f.id
                    JOIN subjects s ON c.subject_id = s.id
                    WHERE c.id = %s AND s.user_id = %s
                """, (chapter_id, current_user_id))
                file_result = pg_cursor.fetchone()
            else:
                # If no file_id, try to find file by subject_id
                logging.info(f"No file_id for chapter, looking for file by subject_id")
                pg_cursor.execute("""
                    SELECT f.filepath, f.filename, f.filetype, c.title as chapter_title
                    FROM chapters c
                    JOIN subjects s ON c.subject_id = s.id
                    LEFT JOIN files f ON f.subject_id = s.id
                    WHERE c.id = %s AND s.user_id = %s
                    ORDER BY f.uploaded_at DESC
                    LIMIT 1
                """, (chapter_id, current_user_id))
                file_result = pg_cursor.fetchone()

            pg_cursor.close()

            if not file_result or not file_result['filepath']:
                logging.error(f"No file found for chapter {chapter_id}")
                return jsonify({
                    'success': False,
                    'error': 'No uploaded file found for this chapter'
                }), 404

            filepath = file_result['filepath']
            filename = file_result['filename']
            filetype = file_result['filetype']
            chapter_title_from_db = file_result['chapter_title']

            logging.info(f"File details - Path: {filepath}, Name: {filename}, Type: {filetype}")

            # Read the original uploaded file content
            try:
                import os

                # Ensure the file path is absolute and exists
                if not os.path.isabs(filepath):
                    # If relative path, make it relative to the Backend directory
                    filepath = os.path.join(os.path.dirname(__file__), filepath)

                logging.info(f"Checking file existence at: {filepath}")
                if not os.path.exists(filepath):
                    logging.error(f"File not found at path: {filepath}")
                    # Try alternative paths
                    alt_paths = [
                        os.path.join(os.path.dirname(__file__), 'uploads', filename),
                        os.path.join(os.path.dirname(__file__), '..', 'Frontend', 'uploads', filename),
                        os.path.join(os.path.dirname(__file__), '..', 'uploads', filename),
                        # Try with the exact path structure from database
                        os.path.join(os.path.dirname(__file__), '..', 'Frontend', filepath.replace('\\', os.sep)),
                        os.path.join(os.path.dirname(__file__), '..', 'Frontend', filepath.replace('/', os.sep)),
                        # Try with user subdirectory
                        os.path.join(os.path.dirname(__file__), '..', 'Frontend', 'uploads', '1', filename),
                        os.path.join(os.path.dirname(__file__), '..', 'Frontend', 'uploads', str(current_user_id), filename)
                    ]

                    for alt_path in alt_paths:
                        logging.info(f"Trying alternative path: {alt_path}")
                        if os.path.exists(alt_path):
                            filepath = alt_path
                            logging.info(f"Found file at alternative path: {filepath}")
                            break
                    else:
                        return jsonify({
                            'success': False,
                            'error': f'Original file not found at path: {filepath} or alternative locations'
                        }), 404

                # Read file content based on file type
                if filetype and filetype.lower() in ['pdf']:
                    # For PDF files, use PyPDF2 or similar
                    try:
                        import PyPDF2
                        with open(filepath, 'rb') as file:
                            pdf_reader = PyPDF2.PdfReader(file)
                            file_content = ""
                            for page in pdf_reader.pages:
                                file_content += page.extract_text() + "\n"
                    except ImportError:
                        return jsonify({
                            'success': False,
                            'error': 'PDF processing library not available. Please install PyPDF2.'
                        }), 500
                    except Exception as e:
                        return jsonify({
                            'success': False,
                            'error': f'Error reading PDF file: {str(e)}'
                        }), 500

                elif filetype and filetype.lower() in ['docx', 'doc']:
                    # For Word documents, use python-docx
                    try:
                        from docx import Document
                        doc = Document(filepath)
                        file_content = ""
                        for paragraph in doc.paragraphs:
                            file_content += paragraph.text + "\n"
                    except ImportError:
                        return jsonify({
                            'success': False,
                            'error': 'Word document processing library not available. Please install python-docx.'
                        }), 500
                    except Exception as e:
                        return jsonify({
                            'success': False,
                            'error': f'Error reading Word document: {str(e)}'
                        }), 500

                else:
                    # For text files and other formats
                    try:
                        with open(filepath, 'r', encoding='utf-8') as file:
                            file_content = file.read()
                    except UnicodeDecodeError:
                        # Try with different encoding
                        try:
                            with open(filepath, 'r', encoding='latin-1') as file:
                                file_content = file.read()
                        except Exception as e:
                            return jsonify({
                                'success': False,
                                'error': f'Error reading file with encoding: {str(e)}'
                            }), 500
                    except Exception as e:
                        return jsonify({
                            'success': False,
                            'error': f'Error reading file: {str(e)}'
                        }), 500

                if not file_content or not file_content.strip():
                    return jsonify({
                        'success': False,
                        'error': 'File content is empty or could not be extracted'
                    }), 400

            except Exception as e:
                logging.error(f"Error reading file {filepath}: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': f'Error accessing file: {str(e)}'
                }), 500

        # Validate that we have content to work with
        if not file_content or not file_content.strip():
            return jsonify({
                'success': False,
                'error': 'No content available for this chapter'
            }), 400

        # Use Gemini AI to generate key concepts and questions
        if not model:
            return jsonify({
                'success': False,
                'error': 'AI model not available'
            }), 500

        # Create a focused prompt for generating key concepts and questions
        source_info = "stored chapter content" if chapter_content else f'original uploaded document "{filename}"'
        prompt = f"""
        You are an expert educational assistant helping students create Cornell Notes.

        A student is about to take notes for the chapter "{chapter_title}" in {subject}.
        I am providing you with the {source_info}. Please analyze this content and generate key concepts, questions, and important terms that should be highlighted in the "Cues & Questions" section to guide their note-taking.

        Chapter Content:
        {file_content[:8000]}  # Limit content to avoid token limits

        Please generate:
        1. Key concepts and terms from the content (as bullet points)
        2. Important questions that this content addresses or should be explored
        3. Critical thinking questions for deeper understanding
        4. Main themes and learning objectives from the content
        5. Important definitions or formulas mentioned

        Format your response as clear, concise bullet points that can be used in the "Cues & Questions" section of Cornell Notes.
        Focus on extracting the most important concepts from the content that will help the student take comprehensive notes.

        The goal is to provide a roadmap of what to look for and focus on while reading and taking notes from this content.

        Limit your response to approximately 200-300 words to fit in the cues section.
        """

        try:
            # Generate content using Gemini
            response = model.generate_content(prompt)
            generated_concepts = response.text

            # Log token usage if available
            if hasattr(response, 'usage_metadata'):
                logging.info(f"Token usage for concepts generation: {response.usage_metadata}")

            return jsonify({
                'success': True,
                'concepts': generated_concepts,
                'source': 'stored_content' if chapter_content else 'file',
                'filename': filename if not chapter_content else None
            })

        except Exception as ai_error:
            logging.error(f"AI generation error: {str(ai_error)}")
            return jsonify({
                'success': False,
                'error': f'Failed to generate concepts: {str(ai_error)}'
            }), 500

    except Exception as e:
        logging.error(f"Error in generate_concepts_from_file: {str(e)}")
        logging.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500


@app.route('/api/generate-flashcards', methods=['POST'])
@authenticate_token
def generate_flashcards_api(current_user_id):
    """
    Generate flashcards using AI from uploaded document content for specific chapters
    """
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['subject_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        subject_id = data['subject_id']
        chapter_ids = data.get('chapter_ids', [])  # List of chapter IDs, empty means all chapters

        # Verify the subject belongs to the authenticated user
        try:
            with pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as pg_cursor:
                pg_cursor.execute(
                    "SELECT id, name FROM subjects WHERE id = %s AND user_id = %s",
                    (subject_id, current_user_id)
                )
                subject = pg_cursor.fetchone()

                if not subject:
                    return jsonify({'success': False, 'error': 'Subject not found or access denied'}), 404

                # Get chapters for this subject with their content
                if chapter_ids:
                    # Specific chapters requested
                    placeholders = ','.join(['%s'] * len(chapter_ids))
                    pg_cursor.execute(
                        f"SELECT id, title, chapter_number, content FROM chapters WHERE id IN ({placeholders}) AND subject_id = %s ORDER BY chapter_number",
                        chapter_ids + [subject_id]
                    )
                else:
                    # All chapters for the subject
                    pg_cursor.execute(
                        "SELECT id, title, chapter_number, content FROM chapters WHERE subject_id = %s ORDER BY chapter_number",
                        (subject_id,)
                    )

                chapters = pg_cursor.fetchall()

                if not chapters:
                    return jsonify({'success': False, 'error': 'No chapters found for the specified criteria'}), 404

                # Try to get content from stored chapters first using the helper function
                content = ""
                chapters_with_content = []
                for chapter in chapters:
                    chapter_content = get_chapter_content_from_db(chapter['id'], current_user_id)
                    if chapter_content and chapter_content.strip() and not chapter_content.startswith('Content for'):
                        content += f"\n\n=== {chapter['title']} ===\n{chapter_content}"
                        chapters_with_content.append(chapter)

                # If no stored content, fallback to file content
                if not content.strip():
                    pg_cursor.execute(
                        "SELECT content FROM files WHERE subject_id = %s ORDER BY uploaded_at DESC LIMIT 1",
                        (subject_id,)
                    )
                    file_record = pg_cursor.fetchone()

                    if not file_record or not file_record['content']:
                        return jsonify({'success': False, 'error': 'No content found for this subject'}), 404

                    content = file_record['content']
                    chapters_with_content = chapters

        except Exception as e:
            logging.error(f"Database error in generate_flashcards_api: {str(e)}")
            return jsonify({'success': False, 'error': 'Database error occurred'}), 500

        # Prepare chapter data for flashcard generation
        chapters_for_flashcards = []
        for chapter in chapters_with_content:
            chapters_for_flashcards.append({
                'id': chapter['id'],
                'chapter_number': chapter['chapter_number'].replace('Chapter ', '') if chapter['chapter_number'] else '1',
                'title': chapter['title']
            })

        # Generate flashcards using AI
        from document_processor import generate_flashcards_with_ai_enhanced
        flashcards = generate_flashcards_with_ai_enhanced(content, subject_id, chapters_for_flashcards)

        if not flashcards:
            return jsonify({'success': False, 'error': 'Failed to generate flashcards'}), 500

        # Save flashcards to database
        saved_count = 0
        try:
            with pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as pg_cursor:
                for flashcard in flashcards:
                    try:
                        pg_cursor.execute("""
                            INSERT INTO flashcards (
                                user_id, subject_id, chapter_id, question, answer, type,
                                box_level, next_review_date, color, difficult, review_count,
                                success_rate, tags, ai_generated, created_at, updated_at
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """, (
                            current_user_id,
                            subject_id,
                            flashcard.get('chapter_id'),
                            flashcard['question'],
                            flashcard['answer'],
                            flashcard.get('type', 'basic'),
                            1,  # box_level
                            (datetime.now() + timedelta(days=1)).isoformat(),  # next_review_date
                            flashcard.get('color', '#ffffff'),
                            False,  # difficult
                            0,  # review_count
                            0,  # success_rate
                            ','.join(flashcard.get('tags', [])),
                            True,  # ai_generated
                            datetime.now().isoformat(),
                            datetime.now().isoformat()
                        ))
                        saved_count += 1
                    except Exception as card_error:
                        logging.error(f"Error saving individual flashcard: {str(card_error)}")
                        continue

                pg_conn.commit()

        except Exception as e:
            logging.error(f"Error saving flashcards: {str(e)}")
            pg_conn.rollback()
            return jsonify({'success': False, 'error': 'Failed to save flashcards to database'}), 500

        return jsonify({
            'success': True,
            'message': f'Successfully generated {saved_count} flashcards',
            'flashcards_generated': saved_count,
            'chapters_processed': len(chapters_for_flashcards),
            'flashcards': flashcards
        })

    except Exception as e:
        logging.error(f"Error in generate_flashcards_api: {str(e)}")
        return jsonify({'success': False, 'error': f'Internal server error: {str(e)}'}), 500


@app.route('/api/subjects/<int:subject_id>/chapters', methods=['GET'])
@authenticate_token
def get_subject_chapters_for_flashcards(subject_id, current_user_id):
    """
    Get all chapters for a specific subject
    """
    try:
        # Verify the subject belongs to the authenticated user
        with pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as pg_cursor:
            pg_cursor.execute(
                "SELECT id, name FROM subjects WHERE id = %s AND user_id = %s",
                (subject_id, current_user_id)
            )
            subject = pg_cursor.fetchone()

            if not subject:
                return jsonify({'success': False, 'error': 'Subject not found or access denied'}), 404

            # Get chapters for this subject
            pg_cursor.execute(
                "SELECT id, title, chapter_number FROM chapters WHERE subject_id = %s ORDER BY chapter_number",
                (subject_id,)
            )
            chapters = pg_cursor.fetchall()

            # Convert to list of dictionaries
            chapters_list = []
            for chapter in chapters:
                chapters_list.append({
                    'id': chapter['id'],
                    'title': chapter['title'],
                    'chapter_number': chapter['chapter_number']
                })

            return jsonify({
                'success': True,
                'subject': {
                    'id': subject['id'],
                    'name': subject['name']
                },
                'chapters': chapters_list
            })

    except Exception as e:
        logging.error(f"Error in get_subject_chapters: {str(e)}")
        return jsonify({'success': False, 'error': f'Internal server error: {str(e)}'}), 500


@app.route('/api/ai/generate-concepts-from-notes', methods=['POST'])
@authenticate_token
def generate_concepts_from_notes(current_user_id):
    """
    Generate key concepts and questions from user's notes for Cornell Notes
    This is used after the user has taken notes
    """
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['notes', 'subject', 'chapter_title']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        notes = data['notes']
        subject = data['subject']
        chapter_title = data['chapter_title']

        if not notes.strip():
            return jsonify({
                'success': False,
                'error': 'Notes content is empty'
            }), 400

        # Use Gemini AI to generate key concepts and questions from notes
        if not model:
            return jsonify({
                'success': False,
                'error': 'AI model not available'
            }), 500

        # Create a focused prompt for generating key concepts from notes
        prompt = f"""
        You are an expert educational assistant helping students create Cornell Notes.

        A student has taken the following notes for the chapter "{chapter_title}" in {subject}.
        Based on these notes, generate key concepts, questions, and important terms that should be highlighted in the "Cues & Questions" section.

        Student's Notes:
        {notes}

        Please generate:
        1. Key concepts and terms mentioned in the notes (as bullet points)
        2. Important questions that these notes answer or should answer
        3. Critical thinking questions for deeper understanding
        4. Main themes and connections between ideas

        Format your response as clear, concise bullet points that can be used in the "Cues & Questions" section of Cornell Notes.
        Focus on extracting the most important concepts from the student's notes to help them review and study effectively.

        Limit your response to approximately 200-300 words to fit in the cues section.
        """

        try:
            # Generate content using Gemini
            response = model.generate_content(prompt)
            generated_concepts = response.text

            # Log token usage if available
            if hasattr(response, 'usage_metadata'):
                logging.info(f"Token usage for concepts generation from notes: {response.usage_metadata}")

            return jsonify({
                'success': True,
                'concepts': generated_concepts,
                'source': 'notes'
            })

        except Exception as ai_error:
            logging.error(f"AI generation error: {str(ai_error)}")
            return jsonify({
                'success': False,
                'error': f'Failed to generate concepts: {str(ai_error)}'
            }), 500

    except Exception as e:
        logging.error(f"Error in generate_concepts_from_notes: {str(e)}")
        logging.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500


@app.route('/api/ai/summarize-and-extract', methods=['POST'])
@authenticate_token
def summarize_and_extract(current_user_id):
    """
    Summarize user's notes and extract key points for Cornell Notes
    This generates both summary and key points from the user's notes
    """
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['notes', 'subject', 'chapter_title']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'Missing required field: {field}'}), 400

        notes = data['notes']
        cues = data.get('cues', '')
        subject = data['subject']
        chapter_title = data['chapter_title']

        if not notes.strip():
            return jsonify({
                'success': False,
                'error': 'Notes content is empty'
            }), 400

        # Use Gemini AI to summarize notes and extract key points
        if not model:
            return jsonify({
                'success': False,
                'error': 'AI model not available'
            }), 500

        # Create a comprehensive prompt for summarization and key point extraction
        prompt = f"""
        You are an expert educational assistant helping students create Cornell Notes.

        A student has taken notes for the chapter "{chapter_title}" in {subject}.
        Please help them by:
        1. Creating a comprehensive summary of their notes
        2. Extracting key points for easy review

        Student's Notes:
        {notes}

        Existing Cues/Questions (if any):
        {cues}

        Please provide your response in the following format:

        SUMMARY:
        [Write a comprehensive summary that synthesizes the main ideas, concepts, and important details from the notes. This should be 2-3 paragraphs that capture the essence of what the student learned.]

        KEY_POINTS:
        [Extract the most important points as a comma-separated list. These should be concise, memorable points that the student can use for quick review. Example: "Point 1, Point 2, Point 3, etc."]

        Make sure the summary is comprehensive but concise, and the key points are specific and actionable for study purposes.
        """

        try:
            # Generate content using Gemini
            response = model.generate_content(prompt)
            ai_response = response.text

            # Log token usage if available
            if hasattr(response, 'usage_metadata'):
                logging.info(f"Token usage for summarization: {response.usage_metadata}")

            # Parse the response to extract summary and key points
            summary = ""
            key_points = ""

            # Split the response by the markers
            if "SUMMARY:" in ai_response and "KEY_POINTS:" in ai_response:
                parts = ai_response.split("KEY_POINTS:")
                summary_part = parts[0].replace("SUMMARY:", "").strip()
                key_points_part = parts[1].strip()

                summary = summary_part
                key_points = key_points_part
            else:
                # Fallback: use the entire response as summary
                summary = ai_response
                key_points = "AI-generated summary available above"

            return jsonify({
                'success': True,
                'summary': summary,
                'key_points': key_points
            })

        except Exception as ai_error:
            logging.error(f"AI summarization error: {str(ai_error)}")
            return jsonify({
                'success': False,
                'error': f'Failed to summarize notes: {str(ai_error)}'
            }), 500

    except Exception as e:
        logging.error(f"Error in summarize_and_extract: {str(e)}")
        logging.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500


if __name__ == '__main__':
    # Run the Flask app with secure settings
    port = int(os.environ.get('PORT', 5002))  # Use port 5002 for backend API

    # Determine environment (development or production)
    env = os.environ.get('FLASK_ENV', 'development')

    # Set debug mode based on environment
    debug_mode = env == 'development'

    # Log startup information
    logging.info(f"Starting Flask app in {env} mode on port {port}")

    if debug_mode:
        logging.warning("Running in DEBUG mode - not recommended for production")
    else:
        logging.info("Running in PRODUCTION mode with security features enabled")

    # Run with appropriate settings for the environment
    app.run(
        debug=debug_mode,
        host='127.0.0.1',  # Only listen on localhost for security
        port=port,
        ssl_context=None,  # Set to 'adhoc' or (cert_file, key_file) for HTTPS in production
        threaded=True
    )