import { useState, useEffect, useRef } from 'react';
import '../../styles/eisenhower_dashboard.scss';
import axios from 'axios';
import { useAuth } from '../../../authContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';


const Eisenhower = ({ subjectId }) => {
    const [showModal, setShowModal] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const modalRef = useRef(null);
    const { token } = useAuth(); // Get the token from auth context
    const { darkMode } = useTheme(); // Get the current theme
    const [tasks, setTasks] = useState({
        do: {
            count: 0,
            label: 'Do',
            description: 'Urgent & Important',
            items: [],
        },
        schedule: {
            count: 0,
            label: 'Schedule',
            description: 'Not Urgent & Important',
            items: [],
        },
        delegate: {
            count: 0,
            label: 'Delegate',
            description: 'Urgent & Not Important',
            items: [],
        },
        delete: {
            count: 0,
            label: 'Delete',
            description: 'Not Urgent & Not Important',
            items: [],
        },
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Fetch tasks from the database
    useEffect(() => {
        const fetchTasks = async () => {
            if (!token) {
                setError('Authentication required');
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                console.log(`Fetching eisenhower tasks for subject: ${subjectId || 'all'}`);

                // Use query parameters instead of path parameters
                const url = subjectId ? `/api/eisenhower?subjectId=${subjectId}` : '/api/eisenhower';
                const response = await axios.get(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                console.log('Eisenhower API response:', response.data);

                // Check if the response is HTML instead of JSON
                if (typeof response.data === 'string' && response.data.includes('<!doctype html>')) {
                    console.error('Received HTML response instead of JSON. API endpoint may be incorrect or server not running.');
                    setError('API connection error. Please check server.');
                    setLoading(false);
                    return;
                }

                if (response.data.success) {
                    // Map database tasks to our quadrants
                    const tasksByQuadrant = {
                        do: [],
                        schedule: [],
                        delegate: [],
                        delete: []
                    };

                    response.data.tasks.forEach(task => {
                        let quadrant;
                        switch (task.quadrant) {
                            case 'Urgent-Important':
                                quadrant = 'do';
                                break;
                            case 'Not Urgent-Important':
                                quadrant = 'schedule';
                                break;
                            case 'Urgent-Not Important':
                                quadrant = 'delegate';
                                break;
                            case 'Not Urgent-Not Important':
                                quadrant = 'delete';
                                break;
                            default:
                                quadrant = 'do';
                        }

                        tasksByQuadrant[quadrant].push({
                            id: task.id,
                            title: task.task,
                            due: task.created_at ? new Date(task.created_at).toLocaleDateString() : 'No date'
                        });
                    });

                    console.log('Processed eisenhower tasks:', tasksByQuadrant);

                    setTasks({
                        do: {
                            ...tasks.do,
                            count: tasksByQuadrant.do.length,
                            items: tasksByQuadrant.do
                        },
                        schedule: {
                            ...tasks.schedule,
                            count: tasksByQuadrant.schedule.length,
                            items: tasksByQuadrant.schedule
                        },
                        delegate: {
                            ...tasks.delegate,
                            count: tasksByQuadrant.delegate.length,
                            items: tasksByQuadrant.delegate
                        },
                        delete: {
                            ...tasks.delete,
                            count: tasksByQuadrant.delete.length,
                            items: tasksByQuadrant.delete
                        }
                    });
                }
                setLoading(false);
            } catch (err) {
                console.error('Error fetching Eisenhower tasks:', err);
                if (err.response?.status === 401) {
                    setError('Authentication required. Please log in again.');
                } else {
                    setError('Failed to load tasks');
                }
                setLoading(false);
            }
        };

        // Always fetch tasks when component mounts or when showModal changes
        fetchTasks();
    }, [showModal, subjectId, token, tasks.do.items.length]);

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowModal(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowModal(false);
            }
        };

        if (showModal) {
            document.addEventListener('mousedown', handleClickOutside);
            document.addEventListener('keydown', handleKeyDown);
            // Prevent scrolling on the body when modal is open
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
            // Restore scrolling when modal is closed
            document.body.style.overflow = 'auto';
        };
    }, [showModal]);

    return (
        <>
            <div
                className={`eisenhower-card ${darkMode ? 'theme-dark' : ''}`}
                onClick={() => {
                    console.log('Eisenhower card clicked, setting showModal to true');
                    setShowModal(true);
                }}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
                aria-label="Open Eisenhower Matrix"
            >
                <div className="widget-header">
                    <h3 className="widget-title">Eisenhower Matrix</h3>
                    <div className="total-count">{tasks.do.count + tasks.schedule.count + tasks.delegate.count + tasks.delete.count}</div>
                </div>

                <div className="matrix-grid">
                    <div className="quadrant do-quadrant">
                        <div className="quadrant-content">
                            <div className="quadrant-info">
                                <span className="quadrant-label">Do</span>
                                <span className="quadrant-desc">Urgent</span>
                            </div>
                            <div className="task-count">{tasks.do.count}</div>
                        </div>
                    </div>

                    <div className="quadrant schedule-quadrant">
                        <div className="quadrant-content">
                            <div className="quadrant-info">
                                <span className="quadrant-label">Schedule</span>
                                <span className="quadrant-desc">Plan</span>
                            </div>
                            <div className="task-count">{tasks.schedule.count}</div>
                        </div>
                    </div>

                    <div className="quadrant delegate-quadrant">
                        <div className="quadrant-content">
                            <div className="quadrant-info">
                                <span className="quadrant-label">Delegate</span>
                                <span className="quadrant-desc">Assign</span>
                            </div>
                            <div className="task-count">{tasks.delegate.count}</div>
                        </div>
                    </div>

                    <div className="quadrant delete-quadrant">
                        <div className="quadrant-content">
                            <div className="quadrant-info">
                                <span className="quadrant-label">Delete</span>
                                <span className="quadrant-desc">Eliminate</span>
                            </div>
                            <div className="task-count">{tasks.delete.count}</div>
                        </div>
                    </div>
                </div>
            </div>
            {console.log('showModal state:', showModal)}
            {showModal && (
                <div className="modal-overlay" style={{ position: 'fixed', zIndex: 999999 }}>
                    <div
                        className={`modal-window eisenhower-modal ${isFullscreen ? 'fullscreen' : ''} ${darkMode ? 'theme-dark' : ''}`}
                        ref={modalRef}
                        role="dialog"
                        aria-labelledby="modal-title"
                        style={{ position: 'relative', zIndex: 1000000 }}
                    >
                        <div className="modal-header">
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowModal(false)}
                                    aria-label="Close"
                                ></button>
                                <button
                                    className="traffic-light yellow"
                                    aria-label="Minimize"
                                    onClick={() => setIsFullscreen(false)}
                                ></button>
                                <button
                                    className="traffic-light green"
                                    aria-label="Maximize"
                                    onClick={() => setIsFullscreen(true)}
                                ></button>
                            </div>
                            <h2 id="modal-title" className="modal-title">
                                Eisenhower Matrix
                            </h2>
                            <div className="view-controls">
                                <button
                                    className={`view-button ${isFullscreen ? '' : 'active'}`}
                                    onClick={() => setIsFullscreen(false)}
                                    aria-label="Window View"
                                >
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="2" y="2" width="12" height="12" rx="2" stroke="currentColor" strokeWidth="1.5" />
                                    </svg>
                                </button>
                                <button
                                    className={`view-button ${isFullscreen ? 'active' : ''}`}
                                    onClick={() => setIsFullscreen(true)}
                                    aria-label="Fullscreen View"
                                >
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M2 6V2H6M10 2H14V6M14 10V14H10M6 14H2V10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div className="modal-content">
                            {loading ? (
                                <div className="loading-container">
                                    <div className="spinner"></div>
                                    <p>Loading tasks...</p>
                                </div>
                            ) : error ? (
                                <div className="error-container">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                        <path d="M12 7V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                                        <circle cx="12" cy="17" r="1" fill="currentColor" />
                                    </svg>
                                    <p className="error-message">{error}</p>
                                </div>
                            ) : (
                                <div className="matrix-container">
                                    <div className="matrix-axes">
                                        <div className="y-axis">
                                            <span className="axis-label top">Important</span>
                                            <span className="axis-label bottom">Not Important</span>
                                        </div>
                                        <div className="x-axis">
                                            <span className="axis-label left">Urgent</span>
                                            <span className="axis-label right">Not Urgent</span>
                                        </div>
                                    </div>
                                    <div className="matrix-grid">
                                        <div className="matrix-quadrant do">
                                            <div className="quadrant-header">
                                                <h3>{tasks.do.label}</h3>
                                                <div className="task-count">{tasks.do.count}</div>
                                            </div>
                                            <p className="quadrant-description">{tasks.do.description}</p>
                                            <ul className="task-list">
                                                {tasks.do.items.map((task) => (
                                                    <li key={task.id} className="task-item">
                                                        <span className="task-title">{task.title}</span>
                                                        <span className="due-date">Due: {task.due}</span>
                                                    </li>
                                                ))}
                                                {tasks.do.items.length === 0 &&
                                                    <li className="empty-task-list">No tasks</li>
                                                }
                                            </ul>
                                        </div>
                                        <div className="matrix-quadrant schedule">
                                            <div className="quadrant-header">
                                                <h3>{tasks.schedule.label}</h3>
                                                <div className="task-count">{tasks.schedule.count}</div>
                                            </div>
                                            <p className="quadrant-description">{tasks.schedule.description}</p>
                                            <ul className="task-list">
                                                {tasks.schedule.items.map((task) => (
                                                    <li key={task.id} className="task-item">
                                                        <span className="task-title">{task.title}</span>
                                                        <span className="due-date">Due: {task.due}</span>
                                                    </li>
                                                ))}
                                                {tasks.schedule.items.length === 0 &&
                                                    <li className="empty-task-list">No tasks</li>
                                                }
                                            </ul>
                                        </div>
                                        <div className="matrix-quadrant delegate">
                                            <div className="quadrant-header">
                                                <h3>{tasks.delegate.label}</h3>
                                                <div className="task-count">{tasks.delegate.count}</div>
                                            </div>
                                            <p className="quadrant-description">{tasks.delegate.description}</p>
                                            <ul className="task-list">
                                                {tasks.delegate.items.map((task) => (
                                                    <li key={task.id} className="task-item">
                                                        <span className="task-title">{task.title}</span>
                                                        <span className="due-date">Due: {task.due}</span>
                                                    </li>
                                                ))}
                                                {tasks.delegate.items.length === 0 &&
                                                    <li className="empty-task-list">No tasks</li>
                                                }
                                            </ul>
                                        </div>
                                        <div className="matrix-quadrant delete">
                                            <div className="quadrant-header">
                                                <h3>{tasks.delete.label}</h3>
                                                <div className="task-count">{tasks.delete.count}</div>
                                            </div>
                                            <p className="quadrant-description">{tasks.delete.description}</p>
                                            <ul className="task-list">
                                                {tasks.delete.items.map((task) => (
                                                    <li key={task.id} className="task-item">
                                                        <span className="task-title">{task.title}</span>
                                                        <span className="due-date">Due: {task.due}</span>
                                                    </li>
                                                ))}
                                                {tasks.delete.items.length === 0 &&
                                                    <li className="empty-task-list">No tasks</li>
                                                }
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="modal-footer">
                            <button className="close-button" onClick={() => setShowModal(false)}>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Eisenhower;
