import React, { useState, useRef, useEffect } from 'react';
import { usePomodoroContext } from '../../contexts/pomodoroContext.jsx';
import '../../styles/Dashboard.scss';

const Pomodoro = () => {
    const [editingSettings, setEditingSettings] = useState(false);
    const [settingsForm, setSettingsForm] = useState({
        duration_minutes: 25,
        break_duration_minutes: 5
    });

    const {
        isRunning,
        isWorkPhase,
        workDuration,
        breakDuration,
        timeLeft,
        sessionCount,
        history,
        showMiniTimer,
        showPomodoroPopup,
        subjects,
        selectedSubject,
        formatTime,
        toggleTimer,
        toggleMiniTimer,
        showMiniTimerAndClosePopup,
        setShowPomodoroPopup,
        updateWorkDuration,
        updateBreakDuration,
        resetTimer,
        saveSettings,
        setSelectedSubject
    } = usePomodoroContext();

    const modalRef = useRef(null);

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowPomodoroPopup(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowPomodoroPopup(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    // Handle settings form changes
    const handleSettingsChange = (e) => {
        const { name, value } = e.target;
        setSettingsForm(prev => ({
            ...prev,
            [name]: parseInt(value) || 1
        }));
    };

    // Save settings form
    const handleSaveSettings = async () => {
        const { duration_minutes, break_duration_minutes } = settingsForm;

        // Update local state
        updateWorkDuration(duration_minutes);
        updateBreakDuration(break_duration_minutes);

        // Save to API
        await saveSettings(duration_minutes, break_duration_minutes);

        // Exit editing mode
        setEditingSettings(false);
    };

    return (
        <>
            <div
                className="pomodoro-card"
                onClick={() => setShowPomodoroPopup(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowPomodoroPopup(true)}
                aria-label="Open Pomodoro Timer"
            >
                <h2 className="card-title">Pomodoro</h2>
                <div className="timer-display">
                    <div className="icon">
                        <span className="timer-icon" aria-hidden="true">⏰</span>
                    </div>
                    <div className="timer">
                        <div className="work-time">
                            <div className="time">Timer</div>
                            <span className="timer-time">{formatTime(timeLeft)} {isWorkPhase ? 'Study' : 'Break'}</span>
                        </div>
                        <div className="break-time">
                            <span>Break</span>
                            <span>{Math.floor(breakDuration / 60)}min</span>
                        </div>
                    </div>
                    <div className="play-button">
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                toggleTimer();
                                if (!isRunning) {
                                    showMiniTimerAndClosePopup();
                                }
                            }}
                            className={isRunning ? 'pause' : 'play'}
                        >
                            {isRunning ? '⏸️' : '▶️'}
                        </button>
                    </div>
                </div>

            </div>

            {/* Mini Timer is now handled by the global MiniTimer component */}
            {showPomodoroPopup && (
                <div className="modal-overlay">
                    <div className="modal-window" ref={modalRef} role="dialog" aria-labelledby="modal-title">
                        <div className="modal-header">
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowPomodoroPopup(false)}
                                    aria-label="Close"
                                ></button>
                                <button className="traffic-light yellow" disabled aria-label="Minimize"></button>
                                <button className="traffic-light green" disabled aria-label="Maximize"></button>
                            </div>
                            <h2 id="modal-title" className="modal-title">
                                Pomodoro Timer
                            </h2>
                        </div>
                        <div className="modal-content">
                            <div className="timer-section">
                                <div className="timer-circle">
                                    <svg className="progress-ring" width="200" height="200">
                                        <circle
                                            className="progress-ring-bg"
                                            cx="100"
                                            cy="100"
                                            r="90"
                                            strokeWidth="10"
                                        />
                                        <circle
                                            className="progress-ring-fill"
                                            cx="100"
                                            cy="100"
                                            r="90"
                                            strokeWidth="10"
                                            strokeDasharray="565.48"
                                            strokeDashoffset={
                                                565.48 * (1 - timeLeft / (isWorkPhase ? workDuration : breakDuration))
                                            }
                                        />
                                    </svg>
                                    <div className="timer-text">
                                        <span className="timer-time">{formatTime(timeLeft)}</span>
                                        <span className="timer-phase">{isWorkPhase ? 'Work' : 'Break'}</span>
                                    </div>
                                </div>
                                <div className="timer-controls">
                                    <button
                                        className="control-button"
                                        onClick={toggleTimer}
                                        aria-label={isRunning ? 'Pause timer' : 'Start timer'}
                                    >
                                        {isRunning ? 'Pause' : 'Start'}
                                    </button>
                                    <button
                                        className="control-button reset"
                                        onClick={resetTimer}
                                        aria-label="Reset timer"
                                    >
                                        Reset
                                    </button>
                                </div>
                            </div>
                            <div className="settings-section">
                                <div className="settings-header">
                                    <h3>Settings</h3>
                                    <button
                                        className="edit-settings-button"
                                        onClick={() => {
                                            if (editingSettings) {
                                                handleSaveSettings();
                                            } else {
                                                setSettingsForm({
                                                    duration_minutes: Math.floor(workDuration / 60),
                                                    break_duration_minutes: Math.floor(breakDuration / 60)
                                                });
                                                setEditingSettings(true);
                                            }
                                        }}
                                        disabled={isRunning}
                                    >
                                        {editingSettings ? 'Save' : 'Edit'}
                                    </button>
                                </div>

                                {editingSettings ? (
                                    // Settings Form
                                    <>
                                        <div className="duration-setting">
                                            <label htmlFor="duration_minutes">Work Duration (minutes):</label>
                                            <input
                                                id="duration_minutes"
                                                name="duration_minutes"
                                                type="number"
                                                min="1"
                                                value={settingsForm.duration_minutes}
                                                onChange={handleSettingsChange}
                                                aria-label="Work duration in minutes"
                                            />
                                        </div>
                                        <div className="duration-setting">
                                            <label htmlFor="break_duration_minutes">Break Duration (minutes):</label>
                                            <input
                                                id="break_duration_minutes"
                                                name="break_duration_minutes"
                                                type="number"
                                                min="1"
                                                value={settingsForm.break_duration_minutes}
                                                onChange={handleSettingsChange}
                                                aria-label="Break duration in minutes"
                                            />
                                        </div>
                                        <div className="settings-actions">
                                            <button
                                                className="cancel-button"
                                                onClick={() => setEditingSettings(false)}
                                            >
                                                Cancel
                                            </button>
                                        </div>
                                    </>
                                ) : (
                                    // Display Current Settings
                                    <>
                                        <div className="duration-setting">
                                            <label>Work Duration:</label>
                                            <span>{Math.floor(workDuration / 60)} minutes</span>
                                        </div>
                                        <div className="duration-setting">
                                            <label>Break Duration:</label>
                                            <span>{Math.floor(breakDuration / 60)} minutes</span>
                                        </div>
                                        <div className="subject-setting">
                                            <label htmlFor="subject-select">Subject (optional):</label>
                                            <select
                                                id="subject-select"
                                                value={selectedSubject}
                                                onChange={(e) => setSelectedSubject(e.target.value)}
                                                disabled={isRunning}
                                            >
                                                <option value="">-- No Subject --</option>
                                                {subjects.map(subject => (
                                                    <option key={subject.id} value={subject.id}>
                                                        {subject.name}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>
                                    </>
                                )}

                                <div className="mini-timer-toggle">
                                    <button
                                        className="toggle-button"
                                        onClick={toggleMiniTimer}
                                    >
                                        {showMiniTimer ? 'Hide Mini Timer' : 'Show Mini Timer'}
                                    </button>
                                </div>
                            </div>
                            <div className="history-section">
                                <h3>Session History</h3>
                                {history.length === 0 ? (
                                    <p>No sessions completed yet.</p>
                                ) : (
                                    <ul className="history-list">
                                        {history.map((session, index) => (
                                            <li key={index}>
                                                {session.type} ({formatTime(session.duration)}): Completed at{' '}
                                                {session.completedAt}
                                            </li>
                                        ))}
                                    </ul>
                                )}
                            </div>
                        </div>
                        <div className="modal-footer">
                            <div className="footer-actions">
                                <button
                                    className="mini-timer-button"
                                    onClick={toggleMiniTimer}
                                    disabled={!isRunning}
                                >
                                    {showMiniTimer ? 'Hide Mini Timer' : 'Show Mini Timer'}
                                </button>
                                <button className="close-button" onClick={() => setShowPomodoroPopup(false)}>
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Pomodoro;