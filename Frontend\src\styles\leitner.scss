@use "sass:color";

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;
$system-gold: rgb(188, 149, 49);

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Leitner System specific colors
$box-1-color: $system-red;
$box-2-color: $system-orange;
$box-3-color: $system-green;

// Transition speed
$transition-speed: 0.3s;

// Leitner System Container
.leitner-system {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  border-radius: 16px;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);

  // Light theme
  background-color: $window-background;
  color: $primary-text;

  // Dark theme
  &.theme-dark {
    background-color: $dark-window-background;
    color: $dark-primary-text;
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.25);
  }

  // Header
  .leitner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid $separator-color;

    h1 {
      font-size: 2rem;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;

      svg {
        margin-right: 0.75rem;
        color: $system-blue;
      }
    }

    // Dark theme header border
    .theme-dark & {
      border-bottom: 1px solid $dark-separator-color;
    }

    .leitner-controls {
      display: flex;
      gap: 0.75rem;

      button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        border: none;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all $transition-speed;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        // Light theme
        background-color: $secondary-background;
        color: $primary-text;

        &:hover {
          background-color: darken($secondary-background, 5%);
          transform: translateY(-1px);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        &.primary {
          background-color: $system-blue;
          color: white;
          box-shadow: 0 2px 5px rgba(0, 122, 255, 0.3);

          &:hover {
            background-color: darken($system-blue, 5%);
            box-shadow: 0 3px 7px rgba(0, 122, 255, 0.4);
          }

          &:active {
            box-shadow: 0 2px 5px rgba(0, 122, 255, 0.3);
          }
        }

        // Dark theme
        .theme-dark & {
          background-color: $dark-secondary-background;
          color: $dark-primary-text;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

          &:hover {
            background-color: lighten($dark-secondary-background, 5%);
            box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
          }

          &:active {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
          }

          &.primary {
            background-color: $system-blue;
            color: white;
            box-shadow: 0 2px 5px rgba(0, 122, 255, 0.3);

            &:hover {
              background-color: lighten($system-blue, 5%);
              box-shadow: 0 3px 7px rgba(0, 122, 255, 0.4);
            }

            &:active {
              box-shadow: 0 2px 5px rgba(0, 122, 255, 0.3);
            }
          }
        }

        .icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  // Mastery Goal Indicator
  .leitner-goal-indicator {
    background-color: $secondary-background;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    .theme-dark & {
      background-color: $dark-secondary-background;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
      display: flex;
      align-items: center;

      &::before {
        content: '🎯';
        margin-right: 0.75rem;
        font-size: 1.5rem;
      }
    }

    p {
      margin: 0 0 1.25rem 0;
      font-size: 1rem;
      opacity: 0.8;
    }

    .progress-container {
      height: 12px;
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 0.75rem;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

      .theme-dark & {
        background-color: rgba(0, 0, 0, 0.2);
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
      }

      .progress-bar {
        height: 100%;
        border-radius: 6px;
        transition: width 0.5s ease-out;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    .progress-stats {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.9rem;
      font-weight: 500;

      .mastery-complete {
        display: flex;
        align-items: center;
        color: $system-green;
        font-weight: 600;

        svg {
          margin-right: 0.5rem;
        }
      }
    }
  }

  // Leitner Boxes Container
  .leitner-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 2rem;
    perspective: 1000px;
    padding: 1rem;
  }

  // Individual Box
  .leitner-box {
    flex: 1;
    min-width: 300px;
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.1),
      0 1px 8px rgba(0, 0, 0, 0.07),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transform: perspective(1000px) rotateX(5deg) translateZ(0);

    // Light theme
    background-color: $window-background;
    border: none;

    // Dark theme
    .theme-dark & {
      background-color: $dark-secondary-background;
      box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.25),
        0 1px 8px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    // 3D hover effect
    &:hover {
      transform: perspective(1000px) rotateX(10deg) translateY(-10px) translateZ(20px);
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 15px 12px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(0, 0, 0, 0.05);

      .theme-dark & {
        box-shadow:
          0 20px 40px rgba(0, 0, 0, 0.4),
          0 15px 12px rgba(0, 0, 0, 0.2),
          0 0 0 1px rgba(255, 255, 255, 0.07);
      }

      .box-preview {
        transform: translateZ(15px);
      }

      .box-actions {
        transform: translateZ(10px);
      }
    }

    // 3D inner elements
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 8px;
      background: linear-gradient(to right, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1));
      border-radius: 20px 20px 0 0;
      opacity: 0.5;

      .theme-dark & {
        opacity: 0.2;
      }
    }

    .box-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      transform: translateZ(10px);
      transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

      h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        .theme-dark & {
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .box-icon {
          margin-right: 0.75rem;
          font-size: 2rem;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
          transition: transform 0.3s ease;
        }
      }

      .card-count {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 42px;
        height: 42px;
        border-radius: 50%;
        font-size: 1.1rem;
        font-weight: 700;
        box-shadow:
          0 4px 8px rgba(0, 0, 0, 0.1),
          0 1px 3px rgba(0, 0, 0, 0.08),
          inset 0 1px 1px rgba(255, 255, 255, 0.3);
        transform: translateZ(5px);

        // Light theme
        background-color: $secondary-background;
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          background-color: $dark-window-background;
          color: $dark-primary-text;
          box-shadow:
            0 4px 8px rgba(0, 0, 0, 0.25),
            0 1px 3px rgba(0, 0, 0, 0.15),
            inset 0 1px 1px rgba(255, 255, 255, 0.1);
        }
      }
    }

    .box-description {
      margin-bottom: 1.75rem;
      transform: translateZ(5px);
      transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

      p {
        margin: 0.5rem 0;
        font-size: 1rem;
        line-height: 1.6;

        &.review-schedule {
          font-size: 0.9rem;
          opacity: 0.8;
          margin-top: 0.75rem;
          display: flex;
          align-items: center;

          &::before {
            content: '•';
            margin-right: 0.5rem;
            font-size: 1.2rem;
            opacity: 0.7;
          }
        }
      }
    }

    // Box styles
    &.box-1 {
      background: linear-gradient(145deg,
          rgba($box-1-color, 0.08) 0%,
          rgba($box-1-color, 0.03) 50%,
          rgba($box-1-color, 0.01) 100%);

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 8px;
        height: 100%;
        background: $box-1-color;
        border-radius: 20px 0 0 20px;
        box-shadow:
          2px 0 10px rgba($box-1-color, 0.3),
          inset 0 1px 1px rgba(255, 255, 255, 0.5);
      }

      .box-header h3 .box-icon {
        color: $box-1-color;
        filter: drop-shadow(0 3px 6px rgba($box-1-color, 0.4));
      }

      .card-count {
        background: linear-gradient(145deg, rgba($box-1-color, 0.15), rgba($box-1-color, 0.05));
        color: $box-1-color;
        box-shadow:
          0 4px 10px rgba($box-1-color, 0.3),
          0 1px 3px rgba($box-1-color, 0.2),
          inset 0 1px 1px rgba(255, 255, 255, 0.4);
      }

      .theme-dark & {
        background: linear-gradient(145deg,
            rgba($box-1-color, 0.18) 0%,
            rgba($box-1-color, 0.08) 50%,
            rgba($box-1-color, 0.04) 100%);

        &::after {
          box-shadow:
            2px 0 15px rgba($box-1-color, 0.4),
            inset 0 1px 1px rgba(255, 255, 255, 0.2);
        }
      }
    }

    &.box-2 {
      background: linear-gradient(145deg,
          rgba($box-2-color, 0.08) 0%,
          rgba($box-2-color, 0.03) 50%,
          rgba($box-2-color, 0.01) 100%);

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 8px;
        height: 100%;
        background: $box-2-color;
        border-radius: 20px 0 0 20px;
        box-shadow:
          2px 0 10px rgba($box-2-color, 0.3),
          inset 0 1px 1px rgba(255, 255, 255, 0.5);
      }

      .box-header h3 .box-icon {
        color: $box-2-color;
        filter: drop-shadow(0 3px 6px rgba($box-2-color, 0.4));
      }

      .card-count {
        background: linear-gradient(145deg, rgba($box-2-color, 0.15), rgba($box-2-color, 0.05));
        color: $box-2-color;
        box-shadow:
          0 4px 10px rgba($box-2-color, 0.3),
          0 1px 3px rgba($box-2-color, 0.2),
          inset 0 1px 1px rgba(255, 255, 255, 0.4);
      }

      .theme-dark & {
        background: linear-gradient(145deg,
            rgba($box-2-color, 0.18) 0%,
            rgba($box-2-color, 0.08) 50%,
            rgba($box-2-color, 0.04) 100%);

        &::after {
          box-shadow:
            2px 0 15px rgba($box-2-color, 0.4),
            inset 0 1px 1px rgba(255, 255, 255, 0.2);
        }
      }
    }

    &.box-3 {
      background: linear-gradient(145deg,
          rgba($box-3-color, 0.08) 0%,
          rgba($box-3-color, 0.03) 50%,
          rgba($box-3-color, 0.01) 100%);

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 8px;
        height: 100%;
        background: $box-3-color;
        border-radius: 20px 0 0 20px;
        box-shadow:
          2px 0 10px rgba($box-3-color, 0.3),
          inset 0 1px 1px rgba(255, 255, 255, 0.5);
      }

      .box-header h3 .box-icon {
        color: $box-3-color;
        filter: drop-shadow(0 3px 6px rgba($box-3-color, 0.4));
      }

      .card-count {
        background: linear-gradient(145deg, rgba($box-3-color, 0.15), rgba($box-3-color, 0.05));
        color: $box-3-color;
        box-shadow:
          0 4px 10px rgba($box-3-color, 0.3),
          0 1px 3px rgba($box-3-color, 0.2),
          inset 0 1px 1px rgba(255, 255, 255, 0.4);
      }

      .theme-dark & {
        background: linear-gradient(145deg,
            rgba($box-3-color, 0.18) 0%,
            rgba($box-3-color, 0.08) 50%,
            rgba($box-3-color, 0.04) 100%);

        &::after {
          box-shadow:
            2px 0 15px rgba($box-3-color, 0.4),
            inset 0 1px 1px rgba(255, 255, 255, 0.2);
        }
      }
    }

    // Box preview
    .box-preview {
      position: relative;
      height: 120px;
      margin-bottom: 1.75rem;
      transform: translateZ(5px);
      transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      perspective: 800px;

      .preview-card {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 90px;
        padding: 1rem;
        border-radius: 12px;
        background-color: $window-background;
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.08),
          0 1px 3px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transform-style: preserve-3d;
        transform: translateZ(0) rotateX(0);
        transition: all 0.3s ease;

        &:nth-child(1) {
          z-index: 3;
        }

        &:nth-child(2) {
          z-index: 2;
          transform: translateY(8px) scale(0.98) rotateX(-2deg);
          opacity: 0.9;
          filter: brightness(0.97);
        }

        &:nth-child(3) {
          z-index: 1;
          transform: translateY(16px) scale(0.96) rotateX(-4deg);
          opacity: 0.8;
          filter: brightness(0.94);
        }

        .theme-dark & {
          background-color: $dark-window-background;
          box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.15),
            0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .preview-content {
          font-size: 0.9rem;
          line-height: 1.5;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }

      .more-cards {
        position: absolute;
        bottom: 5px;
        right: 5px;
        font-size: 0.85rem;
        font-weight: 600;
        padding: 0.3rem 0.6rem;
        border-radius: 20px;
        background: linear-gradient(145deg, rgba($system-blue, 0.15), rgba($system-blue, 0.05));
        color: $system-blue;
        box-shadow:
          0 2px 6px rgba($system-blue, 0.2),
          0 1px 2px rgba($system-blue, 0.1);
        transform: translateZ(10px);
        z-index: 10000;

        .theme-dark & {
          background: linear-gradient(145deg, rgba($system-blue, 0.25), rgba($system-blue, 0.1));
          box-shadow:
            0 2px 6px rgba($system-blue, 0.3),
            0 1px 2px rgba($system-blue, 0.2);
        }
      }
    }

    // Empty box
    .empty-box {
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.75rem;
      border-radius: 12px;
      background: linear-gradient(145deg,
          rgba($secondary-background, 0.7),
          rgba($secondary-background, 0.3));
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
      transform: translateZ(5px);

      .theme-dark & {
        background: linear-gradient(145deg,
            rgba($dark-window-background, 0.7),
            rgba($dark-window-background, 0.3));
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15);
      }

      p {
        font-size: 0.95rem;
        color: $tertiary-text;
        text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);

        .theme-dark & {
          color: $dark-tertiary-text;
          text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
        }
      }
    }

    // Box actions
    .box-actions {
      display: flex;
      gap: 1rem;
      transform: translateZ(10px);
      transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

      button {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.9rem;
        border-radius: 12px;
        border: none;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        transform-style: preserve-3d;

        &.study-button {
          background: linear-gradient(145deg, lighten($system-blue, 5%), $system-blue);
          color: white;
          box-shadow:
            0 6px 12px rgba($system-blue, 0.3),
            0 2px 4px rgba($system-blue, 0.2),
            inset 0 1px 1px rgba(255, 255, 255, 0.3);
          text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);

          &:hover:not(:disabled) {
            transform: translateY(-4px) translateZ(5px);
            box-shadow:
              0 10px 20px rgba($system-blue, 0.4),
              0 4px 8px rgba($system-blue, 0.3),
              inset 0 1px 1px rgba(255, 255, 255, 0.4);
          }

          &:active:not(:disabled) {
            transform: translateY(-2px) translateZ(2px);
            box-shadow:
              0 6px 10px rgba($system-blue, 0.3),
              0 2px 4px rgba($system-blue, 0.2),
              inset 0 1px 1px rgba(255, 255, 255, 0.3);
          }
        }

        &.preview-button {
          background: linear-gradient(145deg,
              lighten($secondary-background, 5%),
              $secondary-background );
          color: $secondary-text;
          box-shadow:
            0 4px 8px rgba(0, 0, 0, 0.08),
            0 1px 3px rgba(0, 0, 0, 0.05),
            inset 0 1px 1px rgba(255, 255, 255, 0.5);

          &:hover:not(:disabled) {
            transform: translateY(-4px) translateZ(5px);
            box-shadow:
              0 8px 16px rgba(0, 0, 0, 0.1),
              0 2px 4px rgba(0, 0, 0, 0.08),
              inset 0 1px 1px rgba(255, 255, 255, 0.6);
          }

          &:active:not(:disabled) {
            transform: translateY(-2px) translateZ(2px);
            box-shadow:
              0 4px 8px rgba(0, 0, 0, 0.08),
              0 1px 3px rgba(0, 0, 0, 0.05),
              inset 0 1px 1px rgba(255, 255, 255, 0.5);
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none !important;
          box-shadow: none !important;
        }

        // Dark theme
        .theme-dark & {
          &.study-button {
            background: linear-gradient(145deg, lighten($system-blue, 8%), $system-blue);
            box-shadow:
              0 6px 12px rgba($system-blue, 0.4),
              0 2px 4px rgba($system-blue, 0.3),
              inset 0 1px 1px rgba(255, 255, 255, 0.2);

            &:hover:not(:disabled) {
              background: linear-gradient(145deg, lighten($system-blue, 10%), lighten($system-blue, 2%));
              box-shadow:
                0 10px 20px rgba($system-blue, 0.5),
                0 4px 8px rgba($system-blue, 0.4),
                inset 0 1px 1px rgba(255, 255, 255, 0.3);
            }
          }

          &.preview-button {
            background: linear-gradient(145deg,
                lighten($dark-secondary-background, 5%),
                $dark-secondary-background );
            color: $dark-secondary-text;
            box-shadow:
              0 4px 8px rgba(0, 0, 0, 0.15),
              0 1px 3px rgba(0, 0, 0, 0.1),
              inset 0 1px 1px rgba(255, 255, 255, 0.1);

            &:hover:not(:disabled) {
              background: linear-gradient(145deg,
                  lighten($dark-secondary-background, 8%),
                  lighten($dark-secondary-background, 3%));
              box-shadow:
                0 8px 16px rgba(0, 0, 0, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.15),
                inset 0 1px 1px rgba(255, 255, 255, 0.15);
            }
          }
        }

        svg {
          margin-right: 0.75rem;
          font-size: 1.25rem;
          filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
        }
      }
    }
  }

  // Streak counter
  .streak-counter {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    margin-top: 1rem;
    border-radius: 20px;
    background: linear-gradient(to right, rgba($system-orange, 0.1), rgba($system-red, 0.1));
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .theme-dark & {
      background: linear-gradient(to right, rgba($system-orange, 0.2), rgba($system-red, 0.2));
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .streak-icon {
      color: $system-orange;
      margin-right: 0.5rem;
      filter: drop-shadow(0 1px 2px rgba($system-orange, 0.5));
    }

    span {
      font-weight: 600;
      color: $system-red;
    }
  }

  // Leitner Study Session
  .leitner-study-session {
    padding: 1.5rem;
    border-radius: 16px;
    background-color: $window-background;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    }

    // Session header
    .session-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid $separator-color;

      .theme-dark & {
        border-bottom: 1px solid $dark-separator-color;
      }

      .session-info {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .box-indicator {
          display: flex;
          align-items: center;
          font-size: 1.25rem;
          font-weight: 600;

          svg {
            margin-right: 0.5rem;
            font-size: 1.5rem;

            // Box colors
            &.box-1-icon {
              color: $box-1-color;
            }

            &.box-2-icon {
              color: $box-2-color;
            }

            &.box-3-icon {
              color: $box-3-color;
            }
          }
        }

        .session-progress {
          display: flex;
          align-items: center;
          gap: 1rem;

          .progress-bar {
            flex: 1;
            height: 8px;
            background-color: $secondary-background;
            border-radius: 4px;
            overflow: hidden;

            .theme-dark & {
              background-color: $dark-secondary-background;
            }

            .progress-fill {
              height: 100%;
              border-radius: 4px;
              transition: width 0.3s ease;
            }
          }

          .progress-text {
            font-size: 0.9rem;
            font-weight: 500;
            color: $secondary-text;

            .theme-dark & {
              color: $dark-secondary-text;
            }
          }
        }
      }

      .session-stats {
        display: flex;
        gap: 1.5rem;

        .stat {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .stat-icon {
            font-size: 1.25rem;

            &.correct {
              color: $system-green;
            }

            &.incorrect {
              color: $system-red;
            }
          }

          span {
            font-size: 0.9rem;
            font-weight: 600;
          }
        }
      }
    }

    // Card container
    .card-container {
      position: relative;
      perspective: 1000px;
      margin-bottom: 2rem;
      min-height: 300px;

      .study-card {
        width: 100%;
        height: 300px;
        position: relative;
        transform-style: preserve-3d;
        transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
        border-radius: 16px;
        cursor: pointer;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

        .theme-dark & {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }

        &.flipped {
          transform: rotateY(180deg);
        }

        .card-face {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          overflow: hidden;
          border-radius: 16px;
          display: flex;
          flex-direction: column;
          padding: 1.5rem;

          &.card-front {
            background-color: $window-background;
            border: 1px solid $border-color;
            z-index: 2;
            transform: rotateY(0deg);

            .theme-dark & {
              background-color: $dark-window-background;
              border-color: $dark-border-color;
            }
          }

          &.card-back {
            background-color: $window-background;
            border: 1px solid $border-color;
            transform: rotateY(180deg);

            .theme-dark & {
              background-color: $dark-window-background;
              border-color: $dark-border-color;
            }
          }

          .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;

            h3 {
              font-size: 1.25rem;
              font-weight: 600;
              margin: 0 0 1.5rem 0;
              display: flex;
              align-items: center;

              .card-icon {
                margin-right: 0.75rem;
                color: $system-blue;
              }
            }

            .card-text {
              flex: 1;
              font-size: 1.1rem;
              line-height: 1.6;
              overflow-y: auto;
              padding-right: 0.5rem;
            }

            .card-hint {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-top: 1.5rem;
              font-size: 0.9rem;
              color: $tertiary-text;

              .theme-dark & {
                color: $dark-tertiary-text;
              }

              .flip-icon {
                margin-right: 0.5rem;
                font-size: 1.1rem;
              }
            }
          }
        }
      }
    }

    // Answer controls
    .answer-controls {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .answer-button {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        border-radius: 12px;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &.correct {
          background-color: $system-green;
          color: white;
          box-shadow: 0 4px 12px rgba($system-green, 0.3);

          &:hover:not(:disabled) {
            background-color: darken($system-green, 5%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba($system-green, 0.4);
          }

          &:active:not(:disabled) {
            transform: translateY(0);
          }
        }

        &.incorrect {
          background-color: $system-red;
          color: white;
          box-shadow: 0 4px 12px rgba($system-red, 0.3);

          &:hover:not(:disabled) {
            background-color: darken($system-red, 5%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba($system-red, 0.4);
          }

          &:active:not(:disabled) {
            transform: translateY(0);
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        svg {
          margin-right: 0.75rem;
          font-size: 1.25rem;
        }
      }
    }

    // Navigation controls
    .navigation-controls {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1.5rem;

      .nav-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        border-radius: 24px;
        border: none;
        background-color: $secondary-background;
        color: $primary-text;
        cursor: pointer;
        transition: all 0.3s ease;

        .theme-dark & {
          background-color: $dark-secondary-background;
          color: $dark-primary-text;
        }

        &:hover:not(:disabled) {
          background-color: $system-blue;
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($system-blue, 0.3);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        svg {
          font-size: 1.5rem;
        }
      }

      .card-counter {
        font-size: 1rem;
        font-weight: 500;
      }
    }

    // Empty session
    .empty-session {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      text-align: center;

      h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
      }

      p {
        font-size: 1rem;
        color: $tertiary-text;
        margin-bottom: 2rem;

        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }

      button {
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        border: none;
        background-color: $system-blue;
        color: white;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba($system-blue, 0.3);

        &:hover {
          background-color: darken($system-blue, 5%);
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba($system-blue, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  // Leitner Stats
  .leitner-stats {
    padding: 1.5rem;
    border-radius: 16px;
    background-color: $window-background;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    }

    // Stats header
    .stats-header {
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid $separator-color;

      .theme-dark & {
        border-bottom: 1px solid $dark-separator-color;
      }

      h2 {
        font-size: 1.75rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;

        svg {
          margin-right: 0.75rem;
          color: $system-blue;
        }
      }
    }

    // Stats overview
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2.5rem;

      .stat-card {
        padding: 1.5rem;
        border-radius: 12px;
        background-color: $secondary-background;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;

        .theme-dark & {
          background-color: $dark-secondary-background;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          border-radius: 24px;
          margin-right: 1rem;
          background-color: rgba($system-blue, 0.1);
          color: $system-blue;

          .theme-dark & {
            background-color: rgba($system-blue, 0.2);
          }

          svg {
            font-size: 1.5rem;
          }
        }

        .stat-content {
          flex: 1;

          h3 {
            font-size: 0.9rem;
            font-weight: 500;
            margin: 0 0 0.5rem 0;
            color: $secondary-text;

            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .stat-value {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }

          .stat-secondary {
            font-size: 0.85rem;
            color: $tertiary-text;

            .theme-dark & {
              color: $dark-tertiary-text;
            }
          }

          .progress-bar {
            height: 6px;
            background-color: rgba($system-blue, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 0.5rem;

            .progress-fill {
              height: 100%;
              border-radius: 3px;
            }
          }
        }

        &.mastery {
          .stat-icon {
            background-color: rgba($system-green, 0.1);
            color: $system-green;

            .theme-dark & {
              background-color: rgba($system-green, 0.2);
            }
          }
        }

        &.reviews {
          .stat-icon {
            background-color: rgba($system-orange, 0.1);
            color: $system-orange;

            .theme-dark & {
              background-color: rgba($system-orange, 0.2);
            }
          }
        }
      }
    }

    // Box distribution
    .box-distribution {
      margin-bottom: 2.5rem;

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 1.5rem 0;
      }

      .distribution-chart {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .box-stat {
          display: flex;
          align-items: center;
          gap: 1rem;

          .box-label {
            width: 100px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;

            svg {
              font-size: 1.25rem;
            }
          }

          .box-bar-container {
            flex: 1;
            height: 24px;
            background-color: $secondary-background;
            border-radius: 12px;
            overflow: hidden;

            .theme-dark & {
              background-color: $dark-secondary-background;
            }

            .box-bar {
              height: 100%;
              min-width: 24px;
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: flex-end;
              padding-right: 0.5rem;
              transition: width 0.5s ease;
            }
          }

          .box-count {
            width: 50px;
            text-align: right;
            font-weight: 600;
            font-size: 1.1rem;
          }
        }
      }
    }

    // Stats footer
    .stats-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 1.5rem;
      border-top: 1px solid $separator-color;

      .theme-dark & {
        border-top: 1px solid $dark-separator-color;
      }

      .average-level {
        h3 {
          font-size: 1rem;
          font-weight: 600;
          margin: 0 0 0.75rem 0;
        }

        .level-indicator {
          width: 200px;
          height: 8px;
          background-color: $secondary-background;
          border-radius: 4px;
          position: relative;
          overflow: hidden;

          .theme-dark & {
            background-color: $dark-secondary-background;
          }

          .level-fill {
            height: 100%;
            background: linear-gradient(to right, $system-red, $system-orange, $system-green);
            border-radius: 4px;
          }

          .level-value {
            position: absolute;
            top: -20px;
            right: 0;
            font-size: 0.9rem;
            font-weight: 600;
          }
        }
      }

      .stats-actions {
        button {
          padding: 0.75rem 1.5rem;
          border-radius: 10px;
          border: none;
          background-color: $system-blue;
          color: white;
          font-size: 0.9rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 2px 8px rgba($system-blue, 0.3);

          &:hover {
            background-color: darken($system-blue, 5%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba($system-blue, 0.4);
          }

          &:active {
            transform: translateY(0);
          }

          .theme-dark & {
            box-shadow: 0 2px 8px rgba($system-blue, 0.4);

            &:hover {
              background-color: lighten($system-blue, 5%);
              box-shadow: 0 4px 12px rgba($system-blue, 0.5);
            }
          }
        }
      }
    }
  }

  // Leitner Preview
  .leitner-preview {
    padding: 1.5rem;
    border-radius: 16px;
    background-color: $window-background;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    }

    // Preview header
    .preview-header {
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid $separator-color;

      .theme-dark & {
        border-bottom: 1px solid $dark-separator-color;
      }

      h2 {
        font-size: 1.75rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;

        svg {
          margin-right: 0.75rem;
          color: $system-blue;
        }
      }

      p {
        font-size: 1rem;
        color: $secondary-text;
        margin: 0;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    // Preview cards
    .preview-cards {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      margin-bottom: 2rem;
      max-height: 70vh;
      overflow-y: auto;
      padding-right: 0.5rem;

      // macOS-style scrollbar
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba($tertiary-text, 0.5);
        border-radius: 4px;

        .theme-dark & {
          background-color: rgba($dark-tertiary-text, 0.5);
        }
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: rgba($tertiary-text, 0.7);

        .theme-dark & {
          background-color: rgba($dark-tertiary-text, 0.7);
        }
      }

      .preview-card-full {
        display: flex;
        background-color: $secondary-background;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

        .theme-dark & {
          background-color: $dark-secondary-background;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .card-number {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          font-size: 1.25rem;
          font-weight: 600;
          background-color: rgba($system-blue, 0.1);
          color: $system-blue;

          .theme-dark & {
            background-color: rgba($system-blue, 0.2);
          }
        }

        .card-content {
          flex: 1;
          display: flex;

          .card-question,
          .card-answer {
            flex: 1;
            padding: 1.25rem;

            h3 {
              font-size: 0.9rem;
              font-weight: 600;
              margin: 0 0 0.75rem 0;
              color: $secondary-text;

              .theme-dark & {
                color: $dark-secondary-text;
              }
            }

            p {
              font-size: 1rem;
              line-height: 1.5;
              margin: 0;
            }
          }

          .card-question {
            border-right: 1px solid $separator-color;

            .theme-dark & {
              border-right: 1px solid $dark-separator-color;
            }
          }
        }
      }
    }

    // Preview actions
    .preview-actions {
      display: flex;
      justify-content: center;
      padding-top: 1.5rem;
      border-top: 1px solid $separator-color;

      .theme-dark & {
        border-top: 1px solid $dark-separator-color;
      }

      button {
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;

        &.primary {
          background-color: $system-blue;
          color: white;
          box-shadow: 0 4px 12px rgba($system-blue, 0.3);

          &:hover {
            background-color: darken($system-blue, 5%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba($system-blue, 0.4);
          }

          &:active {
            transform: translateY(0);
          }

          .theme-dark & {
            box-shadow: 0 4px 12px rgba($system-blue, 0.4);

            &:hover {
              background-color: lighten($system-blue, 5%);
              box-shadow: 0 6px 16px rgba($system-blue, 0.5);
            }
          }
        }

        svg {
          margin-right: 0.75rem;
          font-size: 1.25rem;
        }
      }
    }
  }

  // Leitner Info
  .leitner-info {
    padding: 1.5rem;
    border-radius: 16px;
    background-color: $window-background;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

    .theme-dark & {
      background-color: $dark-window-background;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    }

    .info-header {
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid $separator-color;

      .theme-dark & {
        border-bottom: 1px solid $dark-separator-color;
      }

      h2 {
        font-size: 1.75rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;

        svg {
          margin-right: 0.75rem;
          color: $system-blue;
        }
      }
    }

    .info-section {
      margin-bottom: 2.5rem;

      &:first-of-type {
        display: flex;
        align-items: flex-start;
        gap: 1.5rem;

        .info-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64px;
          height: 64px;
          border-radius: 32px;
          background-color: rgba($system-blue, 0.1);
          color: $system-blue;

          .theme-dark & {
            background-color: rgba($system-blue, 0.2);
          }

          svg {
            font-size: 2rem;
          }
        }

        .info-content {
          flex: 1;
        }
      }

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 1rem 0;
      }

      p {
        font-size: 1rem;
        line-height: 1.6;
        margin: 0 0 1rem 0;
      }

      .boxes-explanation {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;

        .box-explanation {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          padding: 1.25rem;
          border-radius: 12px;
          background-color: $secondary-background;

          .theme-dark & {
            background-color: $dark-secondary-background;
          }

          .box-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 20px;

            &.box-1 {
              background-color: rgba($box-1-color, 0.1);
              color: $box-1-color;

              .theme-dark & {
                background-color: rgba($box-1-color, 0.2);
              }
            }

            &.box-2 {
              background-color: rgba($box-2-color, 0.1);
              color: $box-2-color;

              .theme-dark & {
                background-color: rgba($box-2-color, 0.2);
              }
            }

            &.box-3 {
              background-color: rgba($box-3-color, 0.1);
              color: $box-3-color;

              .theme-dark & {
                background-color: rgba($box-3-color, 0.2);
              }
            }

            svg {
              font-size: 1.5rem;
            }
          }

          .box-details {
            flex: 1;

            h4 {
              font-size: 1.1rem;
              font-weight: 600;
              margin: 0 0 0.5rem 0;
            }

            p {
              font-size: 0.9rem;
              margin: 0;
              color: $secondary-text;

              .theme-dark & {
                color: $dark-secondary-text;
              }
            }
          }
        }
      }

      .rules-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;

        .rule {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
          padding: 1.25rem;
          border-radius: 12px;
          background-color: $secondary-background;

          .theme-dark & {
            background-color: $dark-secondary-background;
          }

          .rule-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 20px;

            &.correct {
              background-color: rgba($system-green, 0.1);
              color: $system-green;

              .theme-dark & {
                background-color: rgba($system-green, 0.2);
              }
            }

            &.incorrect {
              background-color: rgba($system-red, 0.1);
              color: $system-red;

              .theme-dark & {
                background-color: rgba($system-red, 0.2);
              }
            }

            svg {
              font-size: 1.5rem;
            }
          }

          .rule-content {
            flex: 1;

            h4 {
              font-size: 1.1rem;
              font-weight: 600;
              margin: 0 0 0.5rem 0;
            }

            p {
              font-size: 0.9rem;
              margin: 0 0 1rem 0;
              color: $secondary-text;

              .theme-dark & {
                color: $dark-secondary-text;
              }
            }

            .rule-illustration {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 1rem;

              .box-mini {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 36px;
                height: 36px;
                border-radius: 8px;
                font-weight: 600;

                &.box-1 {
                  background-color: rgba($box-1-color, 0.1);
                  color: $box-1-color;

                  .theme-dark & {
                    background-color: rgba($box-1-color, 0.2);
                  }
                }

                &.box-2 {
                  background-color: rgba($box-2-color, 0.1);
                  color: $box-2-color;

                  .theme-dark & {
                    background-color: rgba($box-2-color, 0.2);
                  }
                }

                &.box-3 {
                  background-color: rgba($box-3-color, 0.1);
                  color: $box-3-color;

                  .theme-dark & {
                    background-color: rgba($box-3-color, 0.2);
                  }
                }
              }

              .arrow {
                color: $tertiary-text;

                .theme-dark & {
                  color: $dark-tertiary-text;
                }
              }
            }
          }
        }
      }

      // Goal illustration
      .goal-illustration {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 2rem 0;
        padding: 1.5rem;
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 12px;

        .theme-dark & {
          background-color: rgba(255, 255, 255, 0.05);
        }

        .goal-step {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;

          p {
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
            font-weight: 500;
          }

          &:last-of-type {
            .box-mini {
              animation: pulse 2s infinite;
              box-shadow: 0 0 0 rgba($box-3-color, 0.4);
            }

            p {
              font-weight: 700;
              color: $box-3-color;
            }
          }
        }

        .arrow {
          margin: 0 1.5rem;
          color: $system-blue;
          font-size: 1.5rem;

          .theme-dark & {
            color: lighten($system-blue, 10%);
          }
        }
      }

      .goal-note {
        background-color: rgba($system-blue, 0.1);
        border-left: 4px solid $system-blue;
        padding: 1rem;
        border-radius: 0 8px 8px 0;
        margin-top: 1rem;
        font-size: 0.95rem;

        .theme-dark & {
          background-color: rgba($system-blue, 0.2);
        }
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba($box-3-color, 0.7);
        }

        70% {
          box-shadow: 0 0 0 10px rgba($box-3-color, 0);
        }

        100% {
          box-shadow: 0 0 0 0 rgba($box-3-color, 0);
        }
      }

      .benefits-list {
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.75rem;
          font-size: 1rem;
          line-height: 1.6;

          strong {
            color: $system-blue;

            .theme-dark & {
              color: lighten($system-blue, 10%);
            }
          }
        }
      }
    }

    .info-footer {
      display: flex;
      justify-content: flex-end;
      padding-top: 1.5rem;
      border-top: 1px solid $separator-color;

      .theme-dark & {
        border-top: 1px solid $dark-separator-color;
      }

      button {
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        border: none;
        background-color: $system-blue;
        color: white;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba($system-blue, 0.3);

        &:hover {
          background-color: darken($system-blue, 5%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($system-blue, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        .theme-dark & {
          box-shadow: 0 2px 8px rgba($system-blue, 0.4);

          &:hover {
            background-color: lighten($system-blue, 5%);
            box-shadow: 0 4px 12px rgba($system-blue, 0.5);
          }
        }
      }
    }
  }
}