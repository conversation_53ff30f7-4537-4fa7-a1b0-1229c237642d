import React, { useState, useEffect, useRef } from 'react';
import '../../styles/progress_dashboard.scss';
import { useObjectives } from '../../contexts/ObjectivesContext.jsx';
import { useTheme } from '../../contexts/ThemeContext.jsx';

const Progress = () => {
    const [showModal, setShowModal] = useState(false);
    const modalRef = useRef(null);
    const { darkMode } = useTheme();

    // Get objectives data from context
    const {
        objectives,
        totalObjectives,
        completedObjectives,
        progressPercentage,
        toggleObjective
    } = useObjectives();

    const isStreakDay = progressPercentage >= 60; // For future streak integration

    // Handle clicking outside modal or pressing Escape
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setShowModal(false);
            }
        };

        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                setShowModal(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    return (
        <>
            <div
                className="progress-card"
                onClick={() => setShowModal(true)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && setShowModal(true)}
                aria-label="Open Daily Progress"
            >
                <h2 className="card-title">Today's Progress</h2>
                <div className="progress-circle">
                    <svg className="progress-ring" width="150" height="150">
                        {/* Outer border */}
                        <circle
                            className="progress-ring-outer-border"
                            cx="75"
                            cy="75"
                            r="72"
                            strokeWidth="2"
                        />

                        {/* Segmented background - 36 segments of 10 degrees each */}
                        {Array.from({ length: 36 }).map((_, i) => (
                            <path
                                key={i}
                                className="progress-ring-segment"
                                d={`M 75 75 L ${75 + 68 * Math.cos((i * 10) * Math.PI / 180)} ${75 + 68 * Math.sin((i * 10) * Math.PI / 180)} A 68 68 0 0 1 ${75 + 68 * Math.cos(((i + 1) * 10) * Math.PI / 180)} ${75 + 68 * Math.sin(((i + 1) * 10) * Math.PI / 180)} Z`}
                                strokeWidth="1"
                            />
                        ))}

                        {/* Main progress fill */}
                        <circle
                            className="progress-ring-fill"
                            cx="75"
                            cy="75"
                            r="68"
                            strokeWidth="12"
                            stroke="#0095ff"
                            strokeDasharray="427.26"
                            strokeDashoffset={427.26 * (1 - progressPercentage / 100)}
                            transform="rotate(-90 75 75)"
                        />

                        {/* Inner border */}
                        <circle
                            className="progress-ring-inner-border"
                            cx="75"
                            cy="75"
                            r="56"
                            strokeWidth="2"
                        />

                        {/* Clear center */}
                        <circle
                            className="progress-ring-center"
                            cx="75"
                            cy="75"
                            r="56"
                            fill="transparent"
                        />
                    </svg>
                    <span className="progress-text">{progressPercentage}%</span>
                    <span className="tooltip">{progressPercentage}% complete</span>
                </div>
            </div>
            {showModal && (
                <div className={`modal-overlay ${darkMode ? 'theme-dark' : ''}`}>
                    <div className={`modal-window ${darkMode ? 'theme-dark' : ''}`} ref={modalRef} role="dialog" aria-labelledby="modal-title">
                        <div className="modal-header">
                            <div className="traffic-lights">
                                <button
                                    className="traffic-light red"
                                    onClick={() => setShowModal(false)}
                                    aria-label="Close"
                                ></button>
                                <button className="traffic-light yellow" disabled aria-label="Minimize"></button>
                                <button className="traffic-light green" disabled aria-label="Maximize"></button>
                            </div>
                            <h2 id="modal-title" className="modal-title">
                                Daily Progress
                            </h2>
                        </div>
                        <div className="modal-content">
                            <div className="summary-section">
                                <h3>Today's Objectives</h3>
                                <p>
                                    Completed: {completedObjectives} / {totalObjectives} objectives
                                    {isStreakDay && (
                                        <span className="streak-note"> (Qualifies for streak)</span>
                                    )}
                                </p>
                                <div className="progress-bar">
                                    <div
                                        className="progress-fill"
                                        style={{
                                            width: `${progressPercentage}%`
                                        }}
                                        role="progressbar"
                                        aria-valuenow={progressPercentage}
                                        aria-valuemin="0"
                                        aria-valuemax="100"
                                    ></div>
                                    <span className="tooltip">{progressPercentage}% complete</span>
                                </div>
                            </div>
                            <div className="objectives-list">
                                <h3>Objectives</h3>
                                {objectives.length === 0 ? (
                                    <p className="no-objectives">No daily objectives found. Add objectives from the Subjects page.</p>
                                ) : (
                                    <ul className="objective-items">
                                        {objectives.map((objective) => (
                                            <li
                                                key={objective.id}
                                                className={objective.completed ? 'completed' : 'pending'}
                                                onClick={() => toggleObjective(objective.id, objective.completed)}
                                                role="button"
                                                tabIndex={0}
                                                onKeyDown={(e) =>
                                                    (e.key === 'Enter' || e.key === ' ') &&
                                                    toggleObjective(objective.id, objective.completed)
                                                }
                                                aria-label={`Toggle ${objective.objective} completion status`}
                                            >
                                                <strong>{objective.objective}</strong>
                                                <span className="objective-details">
                                                    {objective.subject_name} - {objective.chapter_title}
                                                </span>
                                                {objective.completed && (
                                                    <span className="completed-icon" aria-hidden="true">
                                                        ✅
                                                    </span>
                                                )}
                                            </li>
                                        ))}
                                    </ul>
                                )}
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button className="close-button" onClick={() => setShowModal(false)}>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Progress;