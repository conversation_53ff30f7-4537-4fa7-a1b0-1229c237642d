@use "../styles/variables" as *;
@use "sass:color";
@use "scrollbar";

.mini-timer {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 250px;
    border-radius: 12px;
    z-index: 9999;
    overflow: hidden;
    transition: all $transition-speed;

    // Dark glass effect for both themes
    background: color.adjust($dark-background, $alpha: -0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

    // Light theme specific styles
    &.theme-light {
        background: color.adjust($light-background, $alpha: -0.2);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border: 1px solid $light-accent-color-2;
    }

    .mini-timer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        font-size: 0.9rem;

        // Dark theme (default)
        background: color.adjust($dark-accent-color-2, $alpha: -0.5);
        color: $dark-primary-text;

        // Light theme
        .theme-light & {
            background: color.adjust($light-accent-color-2, $alpha: -0.5);
            color: $light-primary-text;
        }

        .mini-timer-controls {
            display: flex;
            gap: 8px;

            .mini-timer-button {
                background: none;
                border: none;
                font-size: 1rem;
                cursor: pointer;
                padding: 0;
                transition: all $transition-speed;

                // Dark theme (default)
                color: $dark-primary-text;

                // Light theme
                .theme-light & {
                    color: $light-primary-text;
                }

                &:hover {
                    opacity: 0.8;
                    transform: scale(1.1);
                }
            }
        }
    }

    .mini-timer-body {
        padding: 12px;

        .mini-timer-time {
            font-size: 2rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 8px;

            // Dark theme (default)
            color: $dark-primary-text;

            // Light theme
            .theme-light & {
                color: $light-primary-text;
            }
        }

        .mini-timer-progress {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;

            // Dark theme (default)
            background: color.adjust($dark-accent-color-2, $alpha: -0.5);

            // Light theme
            .theme-light & {
                background: color.adjust($light-accent-color-2, $alpha: -0.5);
            }

            .mini-timer-progress-bar {
                height: 100%;
                transition: width 1s linear;

                &.work {
                    background-color: $color-success;
                }

                &.break {
                    background-color: $color-primary;
                }
            }
        }
    }
}