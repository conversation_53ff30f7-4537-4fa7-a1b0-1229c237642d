# Leitner System Fix

This document explains how to fix the 500 Internal Server Error in the Leitner System component when saving flashcards.

## Problem

The Leitner System component is experiencing the same issue as the FlashCards component:

1. When a user clicks on the "Correct" or "Incorrect" button, the component tries to save all flashcards to the database, not just the modified ones.
2. This causes a 500 Internal Server Error due to unique constraint violations.
3. The streak counter is not updating properly because the save operation fails.

## Root Cause

The root cause is the same as in the FlashCards component:

1. The component doesn't track which cards were loaded from the database versus which ones were modified.
2. When saving, all cards are sent to the server, not just the modified ones.
3. This violates the unique constraint on the flashcards table.

## Solution

The solution is to modify the LeitnerSystem component to:

1. Track original cards loaded from the database
2. Only save cards that have been modified
3. Mark cards as modified when they are moved between boxes or their properties are updated
4. Process cards in smaller batches to avoid payload size issues

## Implementation

Three key functions need to be modified:

1. `loadCards` - To track original cards
2. `saveBoxes` - To only save modified cards
3. `answerCard` - To mark cards as modified
4. `moveCard` - To mark cards as modified

### Step 1: Add State to Track Original Cards

Add a new state variable to track original cards:

```jsx
// Track original cards loaded from the database to avoid re-saving them
const [originalCards, setOriginalCards] = useState([]);
```

### Step 2: Modify loadCards Function

Update the `loadCards` function to track original cards:

```jsx
// In the loadCards function, after fetching cards:
const fetchedCards = response.data.flashcards || [];
      
// Store original cards to avoid re-saving them
setOriginalCards(fetchedCards.map(card => ({
  ...card,
  isModified: false // Mark cards from database as not modified
})));
```

### Step 3: Modify saveBoxes Function

Replace the `saveBoxes` function with the version in `fix_leitner_system.jsx`. The key changes are:

1. Identify only new or modified cards that need to be saved
2. Process cards in smaller batches
3. Update originalCards after saving

### Step 4: Modify answerCard Function

Replace the `answerCard` function with the version in `fix_answer_card.jsx`. The key change is:

```jsx
// Mark the card as modified
card.isModified = true;
```

### Step 5: Modify moveCard Function

Replace the `moveCard` function with the version in `fix_move_card.jsx`. The key change is:

```jsx
// Update the card's box_level and mark it as modified
card.box_level = toBox;
card.isModified = true;
```

## How to Apply the Fix

1. Open `Frontend/src/systems/LeitnerSystem/LeitnerSystem.jsx`
2. Add the `originalCards` state variable
3. Replace the `loadCards`, `saveBoxes`, `answerCard`, and `moveCard` functions with the versions provided in the fix files
4. Test the Leitner System by moving cards between boxes

## Expected Behavior After Fix

1. When a user clicks on the "Correct" or "Incorrect" button, only the modified card will be saved to the database
2. No more 500 Internal Server Error
3. The streak counter will update properly
4. Cards will move between boxes as expected

## Technical Details

The fix uses the same approach as the FlashCards component fix:

1. Track original cards loaded from the database
2. Only save cards that have been modified
3. Mark cards as modified when they are moved or updated
4. Process cards in smaller batches

This approach minimizes database operations and prevents unique constraint violations while still ensuring all user changes are properly saved.
