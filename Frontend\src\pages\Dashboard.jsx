import React, { useState, useEffect } from 'react';
import '../styles/Dashboard.scss';
import DailyObjectives from '../components/dashboard/DailyObjectives.jsx';
import <PERSON> from '../components/dashboard/Eisenhower.jsx';
import Kanban from '../components/dashboard/Kanban.jsx';
import MasteryTracker from '../components/dashboard/MasteryTracker.jsx';
import Retrospective from '../components/dashboard/Retrospective.jsx';
//import Pomodoro from '../components/dashboard/Pomodoro.jsx';
import Progress from '../components/dashboard/Progress.jsx';
import LeitnerDashboard from '../components/dashboard/LeitnerDashboard.jsx';
import Calendar from '../components/dashboard/Calendar.jsx';

const Dashboard = () => {
    const [isDarkMode, setIsDarkMode] = useState(false);

    // Check for dark mode on component mount and when theme changes
    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    return (
        <div className={`dashboard-container ${isDarkMode ? 'theme-dark' : ''}`}>
            <div className="Objectives">
                <DailyObjectives />
            </div>
            <div className="eisenhower">
                <Eisenhower />
            </div>
            <div className="kanban">
                <Kanban />
            </div>
            <div className="retrospective">
                <Retrospective />
            </div>
            <div className="masteryTracker">
                <MasteryTracker />
            </div>
            <div className="progress">
                <Progress />
            </div>
            <div className="streak">
                <LeitnerDashboard />
            </div>
            <div className="calender">
                <Calendar />
            </div>
        </div>
    );
};

export default Dashboard;