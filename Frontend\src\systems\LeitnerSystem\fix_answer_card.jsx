/**
 * This file contains the modified answerCard function for the LeitnerSystem component
 * to fix the 500 error when saving flashcards.
 * 
 * The issue is that cards need to be marked as modified before saving.
 * 
 * Replace the answerCard function in LeitnerSystem.jsx with this version.
 */

// Modified answerCard function to mark cards as modified
const answerCard = (cardId, isCorrect) => {
  // Find which box the card is in
  let fromBox = null;
  let cardIndex = -1;
  let card = null;

  // Find the card in the boxes
  for (let boxNum = 1; boxNum <= 3; boxNum++) {
    cardIndex = boxes[boxNum].findIndex(c => c.id === cardId);
    if (cardIndex !== -1) {
      fromBox = boxNum;
      card = boxes[boxNum][cardIndex];
      break;
    }
  }

  if (fromBox === null || !card) {
    console.error(`Card with ID ${cardId} not found in any box`);
    return;
  }

  // Mark the card as modified
  card.isModified = true;
  
  // Update card's last reviewed timestamp
  card.last_reviewed = new Date().toISOString();

  // Update streak
  if (isCorrect) {
    // Increment card's streak
    card.leitner_streak = (card.leitner_streak || 0) + 1;

    // Move to next box if correct (unless already in box 3)
    if (fromBox < 3) {
      // Calculate next review date based on box level
      const nextReviewDays = fromBox === 1 ? 1 : (fromBox === 2 ? 3 : 7);
      const nextReviewDate = new Date();
      nextReviewDate.setDate(nextReviewDate.getDate() + nextReviewDays);
      card.next_review_date = nextReviewDate.toISOString();

      moveCard(cardId, fromBox, fromBox + 1);

      // Update streak and show confetti for milestones
      setStreak(prev => {
        const newStreak = prev + 1;
        if (newStreak % 3 === 0) {
          setIsExploding(true);
          setTimeout(() => setIsExploding(false), 2000);
        }
        return newStreak;
      });
    } else {
      // If already in box 3, just save the updated card
      // Calculate next review date (7 days for box 3)
      const nextReviewDate = new Date();
      nextReviewDate.setDate(nextReviewDate.getDate() + 7);
      card.next_review_date = nextReviewDate.toISOString();
      
      // Update the card in place
      const updatedBoxes = { ...boxes };
      updatedBoxes[fromBox][cardIndex] = card;
      setBoxes(updatedBoxes);
      
      // Save changes
      saveBoxes();
    }
  } else {
    // Reset card's streak on incorrect answer
    card.leitner_streak = 0;

    // Set next review date for tomorrow
    const nextReviewDate = new Date();
    nextReviewDate.setDate(nextReviewDate.getDate() + 1);
    card.next_review_date = nextReviewDate.toISOString();

    // Move back to box 1 if incorrect
    if (fromBox > 1) {
      moveCard(cardId, fromBox, 1);
    } else {
      // If already in box 1, just save the updated card
      // Update the card in place
      const updatedBoxes = { ...boxes };
      updatedBoxes[fromBox][cardIndex] = card;
      setBoxes(updatedBoxes);
      
      // Save changes
      saveBoxes();
    }

    // Reset session streak on incorrect answer
    setStreak(0);
  }
};
