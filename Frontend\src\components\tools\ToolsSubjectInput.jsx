import React, { useState } from 'react';

const ToolSubjectInput = ({ tool, onSubmit }) => {
    const [subject, setSubject] = useState('');

    const handleSubmit = (e) => {
        e.preventDefault();
        if (subject.trim()) {
            onSubmit(subject);
        }
    };

    return (
        <div className="tool-subject-input">
            <h2>{tool.name}</h2>
            <p>{tool.description}</p>
            <form onSubmit={handleSubmit}>
                <label htmlFor="subject">What subject are you working on?</label>
                <input
                    type="text"
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder="Enter subject/topic..."
                    required
                    autoFocus
                />
                <button
                    type="submit"
                    disabled={!subject.trim()}
                >
                    Continue
                </button>
            </form>
        </div>
    );
};

export default ToolSubjectInput;