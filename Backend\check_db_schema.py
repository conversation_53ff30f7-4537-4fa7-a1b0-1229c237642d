"""
<PERSON><PERSON><PERSON> to check the database schema for the flashcards table.
"""

import psycopg2
import psycopg2.extras
import os
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

# Database connection parameters
DB_HOST = os.environ.get('DB_HOST', 'localhost')
DB_PORT = os.environ.get('DB_PORT', '5432')
DB_NAME = os.environ.get('DB_NAME', 'blueprint')
DB_USER = os.environ.get('DB_USER', 'postgres')
DB_PASSWORD = os.environ.get('DB_PASSWORD', 'Lukind@1956')

def connect_to_db():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        logging.info("Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logging.error(f"Error connecting to database: {str(e)}")
        raise

def check_flashcards_table(conn):
    """Check the schema of the flashcards table"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Check if the flashcards table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'flashcards'
            )
        """)

        if not cursor.fetchone()[0]:
            logging.error("Flashcards table does not exist")
            return

        # Get column information
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'flashcards'
            ORDER BY ordinal_position
        """)

        columns = cursor.fetchall()
        logging.info(f"Flashcards table has {len(columns)} columns:")
        for col in columns:
            logging.info(f"  {col['column_name']} ({col['data_type']}, nullable: {col['is_nullable']})")

        # Check constraints
        cursor.execute("""
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'flashcards'
        """)

        constraints = cursor.fetchall()
        logging.info(f"Flashcards table has {len(constraints)} constraints:")
        for constraint in constraints:
            logging.info(f"  {constraint['constraint_name']} ({constraint['constraint_type']})")

            # If it's a unique constraint, get the columns
            if constraint['constraint_type'] == 'UNIQUE':
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.constraint_column_usage
                    WHERE constraint_name = %s
                """, (constraint['constraint_name'],))

                constraint_columns = cursor.fetchall()
                columns_str = ", ".join([col['column_name'] for col in constraint_columns])
                logging.info(f"    Columns: {columns_str}")

        # Check for system enum type
        cursor.execute("""
            SELECT typname, typtype
            FROM pg_type
            WHERE typname = 'flashcard_system'
        """)

        enum_type = cursor.fetchone()
        if enum_type:
            logging.info(f"Found enum type: {enum_type['typname']}")

            # Get enum values
            cursor.execute("""
                SELECT enumlabel
                FROM pg_enum
                WHERE enumtypid = (
                    SELECT oid
                    FROM pg_type
                    WHERE typname = 'flashcard_system'
                )
            """)

            enum_values = cursor.fetchall()
            values_str = ", ".join([val['enumlabel'] for val in enum_values])
            logging.info(f"  Values: {values_str}")
        else:
            logging.warning("flashcard_system enum type not found")

        # Count flashcards
        cursor.execute("SELECT COUNT(*) FROM flashcards")
        count = cursor.fetchone()[0]
        logging.info(f"Total flashcards: {count}")

        # Count by user
        cursor.execute("""
            SELECT user_id, COUNT(*) as count
            FROM flashcards
            GROUP BY user_id
            ORDER BY count DESC
        """)

        user_counts = cursor.fetchall()
        for user in user_counts:
            logging.info(f"  User {user['user_id']}: {user['count']} flashcards")

    except Exception as e:
        logging.error(f"Error checking flashcards table: {str(e)}")
        raise
    finally:
        cursor.close()

def main():
    """Main function"""
    try:
        conn = connect_to_db()
        check_flashcards_table(conn)
        conn.close()
    except Exception as e:
        logging.error(f"Script failed: {str(e)}")

if __name__ == "__main__":
    main()
