@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Streak Card
.streak-card {
    background-color: $window-background;
    border-radius: 12px;
    border: 1px solid $border-color;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 1.2rem;
    width: 100%;
    height: 100%;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;

    // Dark theme
    .theme-dark & {
        background-color: $dark-window-background;
        border-color: $dark-border-color;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }



    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);


    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
    }

    // We're now handling the expanded-icon-mode styles directly in the streak-count class

    .card-title {
        font-size: 18px;
        font-weight: 600;
        color: $primary-text;
        margin-bottom: 16px;
        text-align: center;

        // Dark theme
        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    .streak-count {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;

        // Default layout (when calendar is shown): horizontal with image on left, text on right
        flex-direction: row;
        justify-content: flex-start;

        // When calendar is hidden, we use vertical layout with image on top
        .streak-card.expanded-icon-mode & {
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-top: 1.5rem;
            text-align: center;
        }

        .streak-icon {
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                width: 40px;
                height: 40px;
                transition: all 0.2s ease;

                &.large-icon {
                    width: 60px;
                    height: 60px;
                }

                // Add margin below the image when in vertical layout
                .streak-card.expanded-icon-mode & {
                    margin-bottom: 0.5rem;
                }
            }
        }

        h2 {
            font-size: 20px;
            font-weight: 600;
            color: $system-blue;
            margin: 0;

            // Adjust text alignment in vertical layout
            .streak-card.expanded-icon-mode & {
                text-align: center;
                margin-top: 0.5rem;
            }
        }
    }

    .weekly-calender {
        background-color: $secondary-background;
        border-radius: 10px;
        padding: 1rem;
        margin-top: 0.5rem;
        margin-bottom: 1rem;
        width: 100%;
        clear: both;

        // Dark theme
        .theme-dark & {
            background-color: $dark-secondary-background;
        }


        .week-label {
            font-size: 14px;
            font-weight: 500;
            color: $secondary-text;
            margin-bottom: 10px;
            text-align: center;

            // Dark theme
            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .week-calendar {
            .day-labels {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;

                span {
                    width: 30px;
                    text-align: center;
                    font-size: 12px;
                    font-weight: 500;
                    color: $tertiary-text;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }
                }
            }

            .day-numbers {
                display: flex;
                justify-content: space-between;

                .day {
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    font-weight: 500;
                    color: $primary-text;
                    border-radius: 50%;
                    position: relative;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-primary-text;
                    }

                    &.streak-day {
                        background-color: $system-green;
                        color: white;
                        box-shadow: 0 2px 5px rgba($system-green, 0.3);


                    }

                    .streak-mark {
                        position: absolute;
                        top: -2px;
                        right: -2px;
                        font-size: 8px;
                    }
                }
            }
        }
    }

    .calendar-toggle-button {
        position: absolute;
        bottom: 1rem;
        left: 50%;
        transform: translateX(-50%);
        background-color: transparent;
        color: $system-blue;
        border: 1px solid $system-blue;
        border-radius: 20px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        // Dark theme
        .theme-dark & {
            color: lighten($system-blue, 10%);
            border-color: lighten($system-blue, 10%);
        }



        &:hover {
            background-color: rgba($system-blue, 0.1);

            // Dark theme
            .theme-dark & {
                background-color: rgba($system-blue, 0.2);
            }
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
        }
    }
}

// Modal content styling for streak
.modal-content {
    .summary-section {
        h3 {
            font-size: 16px;
            font-weight: 600;
            color: $primary-text;
            margin-bottom: 16px;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        p {
            font-size: 14px;
            color: $secondary-text;
            margin-bottom: 8px;
            line-height: 1.5;

            // Dark theme
            .theme-dark & {
                color: $dark-secondary-text;
            }
        }
    }

    .calendar-section,
    .history-section {
        margin-top: 24px;

        h3 {
            font-size: 16px;
            font-weight: 600;
            color: $primary-text;
            margin-bottom: 16px;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }

    .week-calendar {
        background-color: $secondary-background;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;

        // Dark theme
        .theme-dark & {
            background-color: $dark-secondary-background;
        }



        .day-labels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;

            span {
                width: 40px;
                text-align: center;
                font-size: 13px;
                font-weight: 500;
                color: $tertiary-text;

                // Dark theme
                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }
        }

        .day-numbers {
            display: flex;
            justify-content: space-between;

            .day {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: 500;
                color: $primary-text;
                border-radius: 50%;
                position: relative;

                // Dark theme
                .theme-dark & {
                    color: $dark-primary-text;
                }

                &.streak-day {
                    background-color: $system-green;
                    color: white;
                    box-shadow: 0 2px 5px rgba($system-green, 0.3);


                }

                .day-number {
                    position: relative;
                    z-index: 2;
                }

                .progress-ring {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 1;

                    svg {
                        width: 100%;
                        height: 100%;

                        .progress-ring-bg {
                            fill: none;
                            stroke: rgba($system-blue, 0.1);


                        }

                        .progress-ring-fill {
                            fill: none;
                            stroke: $system-blue;
                            stroke-linecap: round;
                            transform: rotate(-90deg);
                            transform-origin: center;
                            transition: stroke-dashoffset 0.3s ease;


                        }
                    }
                }

                .tooltip {
                    position: absolute;
                    top: -40px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: $window-background;
                    color: $primary-text;
                    padding: 6px 10px;
                    border-radius: 8px;
                    font-size: 12px;
                    white-space: nowrap;
                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                    border: 1px solid $border-color;
                    opacity: 0;
                    pointer-events: none;
                    transition: opacity 0.2s ease;
                    z-index: 10;

                    // Dark theme
                    .theme-dark & {
                        background-color: $dark-window-background;
                        color: $dark-primary-text;
                        border-color: $dark-border-color;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
                    }
                }

                &:hover .tooltip {
                    opacity: 1;
                }
            }
        }

        &.small {
            padding: 10px;

            .day-numbers {
                .day {
                    width: 30px;
                    height: 30px;
                    font-size: 12px;
                }
            }
        }
    }

    .week-history {
        background-color: $secondary-background;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;

        // Dark theme
        .theme-dark & {
            background-color: $dark-secondary-background;
        }



        h4 {
            font-size: 14px;
            font-weight: 600;
            color: $primary-text;
            margin-bottom: 8px;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        p {
            font-size: 13px;
            color: $secondary-text;
            margin-bottom: 12px;

            // Dark theme
            .theme-dark & {
                color: $dark-secondary-text;
            }
        }
    }
}