import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../authContext.jsx';
import { useLocation, NavLink } from 'react-router-dom';
import "../styles/sidebar.scss";
import ContactModal from './ContactModal';
import { useTheme } from '../contexts/ThemeContext.jsx';

import SchoolIcon from '@mui/icons-material/School';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import DashboardIcon from '@mui/icons-material/Dashboard';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import ConstructionIcon from '@mui/icons-material/Construction';
import BookmarksIcon from '@mui/icons-material/Bookmarks';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import ContactSupportIcon from '@mui/icons-material/ContactSupport';
import logo from "../assets/file.svg";
import streakIcon from "../assets/Streak.png";

import NavItem from './NavItem';

const LeftSidebar = ({ collapsed, toggleCollapse, onLogout }) => {
    const { logout, user, token } = useAuth();
    const location = useLocation();
    const { darkMode } = useTheme();
    const [hoveredText, setHoveredText] = useState('');
    const [hoverPosition, setHoverPosition] = useState(0);
    const [showProfileMenu, setShowProfileMenu] = useState(false);
    const [showContactModal, setShowContactModal] = useState(false);
    const [streakCount, setStreakCount] = useState(0);
    const profileRef = useRef(null);

    // API URL from environment or default
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

    // Fetch streak data
    useEffect(() => {
        const fetchStreakData = async () => {
            if (!user) return;

            const authToken = token || localStorage.getItem('token');
            if (!authToken) return;

            try {
                const response = await fetch(`${API_URL}/api/streak`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        setStreakCount(data.streak.current_streak);
                    }
                }
            } catch (error) {
                console.error('Error fetching streak data:', error);
            }
        };

        fetchStreakData();

        // Set up interval to refresh streak data every 5 minutes
        const intervalId = setInterval(fetchStreakData, 5 * 60 * 1000);

        return () => clearInterval(intervalId);
    }, [user, token]);

    const getInitials = () => {
        if (!user || !user.fullName) return 'U';

        const fullName = user.fullName;
        const initials = fullName.split(' ')
            .filter(word => word.length > 0)
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);

        return initials || 'U';
    };

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (profileRef.current && !profileRef.current.contains(event.target)) {
                setShowProfileMenu(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    useEffect(() => {
        console.log('LeftSidebar: Current pathname:', location.pathname);
    }, [location.pathname]);

    // Log user state for debugging
    useEffect(() => {
        console.log('LeftSidebar: User state:', user);
    }, [user]);

    const navItemsTop = [
        { icon: <DashboardIcon />, text: "Dashboard", to: "/dashboard" },
        { icon: <MenuBookIcon />, text: "My Subjects", to: "/subjects" },
        { icon: <ConstructionIcon />, text: "Study Tools", to: "/study-tools" },
        { icon: <CalendarTodayIcon />, text: "Study Plans", to: "/study-plans" },
        { icon: <BookmarksIcon />, text: "Journal", to: "/journal" },
        { icon: <EmojiEventsIcon />, text: "Achievements", to: "/achievements" }
    ];

    const profileMenuItems = [
        { icon: <SettingsIcon />, text: "Settings", to: "/settings" },
        {
            icon: <ContactSupportIcon />,
            text: "Contact Us",
            onClick: () => {
                setShowProfileMenu(false);
                setShowContactModal(true);
            }
        },
        {
            icon: <LogoutIcon />,
            text: "Log Out",
            onClick: () => {
                setShowProfileMenu(false);
                logout();
                onLogout && onLogout();
            }
        }
    ];

    const isActive = (path) => {
        return location.pathname === path;
    };

    const handleMouseEnter = (text, e) => {
        if (collapsed) {
            setHoveredText(text);
            // Get the Y position of the mouse relative to the viewport
            if (e && e.clientY) {
                setHoverPosition(e.clientY);
            }
        }
    };

    const handleMouseLeave = () => {
        setHoveredText('');
    };

    const toggleProfileMenu = () => {
        setShowProfileMenu(!showProfileMenu);
    };

    return (
        <div className={`sidebar left-sidebar ${collapsed ? 'collapsed' : ''}`}>
            <div className="sidebar-top">
                <div className="logo">
                    <img src={logo} alt="" />
                    {!collapsed && <span className="logo-text">Blueprint AI</span>}
                </div>
                <button className="toggle-btn" onClick={toggleCollapse}>
                    {collapsed ? <KeyboardDoubleArrowRightIcon /> : <KeyboardDoubleArrowLeftIcon />}
                </button>
            </div>

            <div className="sidebar-middle">


                <ul className="nav-menu">
                    {navItemsTop.map((item, index) => (
                        <div
                            key={index}
                            onMouseEnter={(e) => handleMouseEnter(item.text, e)}
                            onMouseLeave={handleMouseLeave}
                        >
                            <NavItem
                                icon={item.icon}
                                text={item.text}
                                to={item.to}
                                collapsed={collapsed}
                                isActive={isActive(item.to)}
                            />
                        </div>
                    ))}
                </ul>

                {/* Streak Component */}
                <NavLink
                    to="/dashboard"
                    className={`sidebar-streak ${collapsed ? 'collapsed' : ''}`}
                    onMouseEnter={(e) => handleMouseEnter('Current Streak', e)}
                    onMouseLeave={handleMouseLeave}
                >
                    <div className="streak-icon">
                        <img src={streakIcon} alt="Streak" />
                    </div>
                    {!collapsed && (
                        <div className="streak-info">
                            <div className="streak-count">{streakCount}</div>
                            <div className="streak-text">Days Streak</div>
                        </div>
                    )}
                </NavLink>
            </div>



            <div className="sidebar-bottom" ref={profileRef}>
                <div
                    className={`profile-section ${collapsed ? 'collapsed' : ''}`}
                    onClick={toggleProfileMenu}
                    onMouseEnter={(e) => handleMouseEnter("Profile", e)}
                    onMouseLeave={handleMouseLeave}
                >
                    <div className="profile-pic" title={user?.fullName || "User"}>
                        {user?.photoURL ? (
                            <img src={user.photoURL} alt="Profile" />
                        ) : (
                            <AccountCircleIcon style={{ fontSize: '2rem' }} />
                        )}
                    </div>
                    {!collapsed && (
                        <div className="profile-info">
                            <div className="profile-name">
                                {user?.fullName ? user.fullName : "User"}
                            </div>
                        </div>
                    )}
                </div>

                {showProfileMenu && (
                    <div className={`profile-menu ${collapsed ? 'collapsed' : ''}`}>
                        {profileMenuItems.map((item, index) => (
                            item.to ? (
                                <NavLink
                                    key={index}
                                    to={item.to}
                                    className={({ isActive: navLinkActive }) =>
                                        `profile-menu-item ${navLinkActive ? 'active' : ''}`
                                    }
                                    onClick={() => setShowProfileMenu(false)}
                                >
                                    <div className="profile-menu-icon">{item.icon}</div>
                                    <div className="profile-menu-text">{item.text}</div>
                                </NavLink>
                            ) : (
                                <div
                                    key={index}
                                    className={`profile-menu-item ${item.text === 'Log Out' ? 'logout-item' : ''}`}
                                    onClick={item.onClick}
                                >
                                    <div className="profile-menu-icon">{item.icon}</div>
                                    <div className="profile-menu-text">{item.text}</div>
                                </div>
                            )
                        ))}
                    </div>
                )}
            </div>
            {collapsed && hoveredText && (
                <div
                    className="hovered-text"
                    style={{
                        position: 'fixed',
                        left: '80px',
                        top: `${hoverPosition}px`,
                        transform: 'translateY(-50%)',
                        backgroundColor: darkMode ? '#007AFF' : '#007AFF',
                        color: darkMode ? '#FFFFFF' : '#FFFFFF',
                        padding: '8px 16px',
                        borderRadius: '10px',
                        zIndex: 1000,
                        whiteSpace: 'nowrap',
                        boxShadow: darkMode ? '0 4px 15px rgba(0, 0, 0, 0.25)' : '0 4px 15px rgba(0, 0, 0, 0.1)',
                        border: darkMode ? '1px solid #38383A' : '1px solid #C6C6C8'
                    }}
                >
                    {hoveredText}
                </div>
            )}

            {/* Contact Modal */}
            <ContactModal
                isOpen={showContactModal}
                onClose={() => setShowContactModal(false)}
                user={user}
            />
        </div>
    );
};

export default LeftSidebar;