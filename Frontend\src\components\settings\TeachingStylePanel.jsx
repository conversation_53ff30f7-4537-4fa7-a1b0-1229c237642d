import React from 'react';
import SchoolIcon from '@mui/icons-material/School';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import '../../styles/SettingsPanels.scss';

const TeachingStylePanel = ({ settings }) => {
  const { teachingStyle, setTeachingStyle, isDarkMode } = settings;
  
  const teachingStyles = [
    {
      id: 'socratic',
      label: 'Socratic',
      icon: <QuestionAnswerIcon />,
      description: 'Teaches through questions that guide you to discover answers yourself.'
    },
    {
      id: 'visual',
      label: 'Visual',
      icon: <VisibilityIcon />,
      description: 'Emphasizes diagrams, charts, and visual explanations of concepts.'
    },
    {
      id: 'drill',
      label: 'Drill-based',
      icon: <FitnessCenterIcon />,
      description: 'Focuses on repetition and practice to reinforce learning.'
    },
    {
      id: 'balanced',
      label: 'Balanced',
      icon: <SchoolIcon />,
      description: 'Combines multiple teaching approaches based on the subject matter.'
    }
  ];
  
  return (
    <div className={`detail-panel teaching-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Teaching Style</h2>
        <p className="panel-description">
          Select how you prefer the AI to teach and explain concepts to you.
        </p>
      </div>
      
      <div className="settings-group teaching-styles">
        {teachingStyles.map(style => (
          <div 
            key={style.id}
            className={`teaching-style-card ${teachingStyle === style.id ? 'active' : ''}`}
            onClick={() => setTeachingStyle(style.id)}
          >
            <div className="style-icon">{style.icon}</div>
            <div className="style-content">
              <h3>{style.label}</h3>
              <p>{style.description}</p>
            </div>
            <div className="style-selector"></div>
          </div>
        ))}
      </div>
      
      <div className="info-box">
        <p>
          Your selected teaching style will influence how the AI explains concepts 
          and structures its responses. You can change this at any time.
        </p>
      </div>
    </div>
  );
};

export default TeachingStylePanel;
