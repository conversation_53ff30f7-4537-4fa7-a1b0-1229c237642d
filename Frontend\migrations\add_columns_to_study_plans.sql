-- Add preferred_days and unavailable_periods columns to study_plans table if they don't exist
DO $$
BEGIN
    -- Check if preferred_days column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'study_plans' AND column_name = 'preferred_days'
    ) THEN
        ALTER TABLE study_plans ADD COLUMN preferred_days VARCHAR(50);
    END IF;

    -- Check if unavailable_periods column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'study_plans' AND column_name = 'unavailable_periods'
    ) THEN
        ALTER TABLE study_plans ADD COLUMN unavailable_periods TEXT;
    END IF;
END
$$;
