# Recommended Backend Structure

## Essential Files
```
Backend/
├── .env                       # Environment variables (keep this)
├── app.py                     # Main Flask application
├── ai_assistant.py            # AI assistant functionality
├── database.py                # Database connection and initialization
├── document_processor.py      # Document processing functionality
├── models.py                  # SQLAlchemy models
├── requirements.txt           # Python dependencies
└── uploads/                   # Directory for uploaded files (keep this)
```

## Files to Remove
The following files are not necessary for the core functionality and can be removed:

```
Backend/
├── alter_objectives_table.sql      # One-time migration script
├── alter_study_plans_table.sql     # One-time migration script
├── celery_config.py                # Not used in the current implementation
├── check_objectives.py             # Testing script
├── check_postgres.py               # Testing script
├── db_maintenance.py               # Maintenance script, not core functionality
├── document_workflow_example.py    # Example file, not needed for production
├── install_dependencies.bat        # One-time setup script
├── install_requirements.py         # One-time setup script
├── migration.log                   # Log file, not needed
├── migration.py                    # One-time migration script
├── package-lock.json               # Node.js related, not needed for Python backend
├── package.json                    # Node.js related, not needed for Python backend
├── README_DISPLAY_DATE.md          # Documentation, can be consolidated
├── reset_objectives.py             # Maintenance script
├── reset_sequences.py              # Maintenance script
├── SECURITY.md                     # Documentation, can be consolidated
├── tasks.py                        # Not used in the current implementation
├── test_*.py                       # All test files can be moved to a tests/ directory
└── node_modules/                   # Node.js related, not needed for Python backend
```

## Directories to Reorganize
The following directories should be reorganized:

```
Backend/
├── migrations/                     # Contains only one file, can be removed or consolidated
├── models/                         # Contains only Streak.js (Node.js), not used by Python backend
└── routes/                         # Contains only streakRoutes.js (Node.js), not used by Python backend
```

## Recommended New Structure
Here's the recommended new structure for the Backend directory:

```
Backend/
├── .env                       # Environment variables
├── app.py                     # Main Flask application
├── ai_assistant.py            # AI assistant functionality
├── database.py                # Database connection and initialization
├── document_processor.py      # Document processing functionality
├── models.py                  # SQLAlchemy models
├── requirements.txt           # Python dependencies
├── uploads/                   # Directory for uploaded files
└── tests/                     # Directory for test files
    ├── test_ai_response.py
    ├── test_db_connection.py
    └── test_rescheduling.py   # Consolidated rescheduling tests
```

## Implementation Steps

1. Create a `tests` directory
2. Move essential test files to the `tests` directory
3. Remove unnecessary files
4. Remove unused directories (migrations, models, routes)

This structure will make the codebase cleaner, more maintainable, and easier to understand for new developers.
