from flask_sqlalchemy import SQLAlchemy
import logging
import psycopg2
import psycopg2.extras

db = SQLAlchemy()
pg_conn = None  # Initialize as None, will be set below

# Initialize PostgreSQL connection to frontend database
try:
    # Connection parameters for the frontend PostgreSQL database - using exact values from Frontend/.env
    pg_conn_params = {
        'dbname': 'blueprint',
        'user': 'postgres',
        'password': 'newpassword123',
        'host': 'localhost',
        'port': '5432'
    }

    # Create a connection pool
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_conn.autocommit = False
    print("Successfully connected to frontend PostgreSQL database in database.py")
except Exception as e:
    print(f"Error connecting to frontend PostgreSQL database in database.py: {str(e)}")
    pg_conn = None

def init_db(app):
    """Initialize the database and create tables if they don't exist"""
    print("Initializing database...")
    db.init_app(app)

    # Test the database connection first
    with app.app_context():
        try:
            # Test connection
            print("Testing database connection...")
            from sqlalchemy import text
            db.session.execute(text("SELECT 1")).scalar_one()
            print("Database connection successful!")
            logging.info("Database connection successful!")
        except Exception as e:
            print(f"Database connection error: {str(e)}")
            print("Check your database credentials and connection settings.")
            logging.error(f"Database connection error: {str(e)}")
            # Don't raise here, just log the error and continue
            return

    # Create tables within application context
    with app.app_context():
        try:
            # Check if tables exist
            print("Checking if database tables exist...")
            logging.info("Checking if database tables exist...")

            # Try to create all tables
            db.create_all()
            print("Database tables created or already exist")
            logging.info("Database tables created or already exist")

            # Verify that key tables exist
            from sqlalchemy import inspect
            inspector = inspect(db.engine)

            required_tables = ['users', 'curricula', 'subjects', 'chapters', 'objectives', 'study_plans', 'schedule_items']
            existing_tables = inspector.get_table_names()

            #print(f"Existing tables: {existing_tables}")
            #logging.info(f"Existing tables: {existing_tables}")

            missing_tables = [table for table in required_tables if table not in existing_tables]
            if missing_tables:
                print(f"Missing tables: {missing_tables}")
                logging.warning(f"Missing tables: {missing_tables}")
            else:
                print("All required tables exist")
                logging.info("All required tables exist")

            # Log table columns for debugging
            '''for table in existing_tables:
                columns = [col['name'] for col in inspector.get_columns(table)]
                print(f"Table '{table}' columns: {columns}")
                logging.info(f"Table '{table}' columns: {columns}")'''

        except Exception as e:
            print(f"Error initializing database: {str(e)}")
            logging.error(f"Error initializing database: {str(e)}")
            # Don't raise here, just log the error and continue
            return