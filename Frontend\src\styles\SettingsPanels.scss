@use "variables" as *;
@use "sass:color";

// Common styles for all settings panels
.detail-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: $primary-text;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  // Dark mode
  &.theme-dark {
    color: $dark-primary-text;
  }

  .panel-header {
    margin-bottom: 1.5rem;

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
      color: $primary-text;

      // Dark mode
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .panel-description {
      font-size: 0.95rem;
      color: $secondary-text;
      margin: 0;

      // Dark mode
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  .settings-group {
    margin-bottom: 1.5rem;
  }

  .info-box {
    margin-top: auto;
    padding: 1rem;
    background-color: rgba($system-light-gray, 0.5);
    border-radius: 8px;

    p {
      font-size: 0.9rem;
      color: $secondary-text;
      margin: 0;

      // Dark mode
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    // Dark mode
    .theme-dark & {
      background-color: rgba($dark-secondary-background, 0.5);
    }
  }
}

// Slider settings
.slider-setting {
  margin-bottom: 1.5rem;

  .setting-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;

    span {
      font-size: 0.95rem;
      font-weight: 500;
    }

    .setting-value {
      color: $system-blue;
      font-weight: 600;

      // Dark mode
      .theme-dark & {
        color: $system-blue;
      }
    }
  }

  .slider-container {
    .slider-labels {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.25rem;

      span {
        font-size: 0.8rem;
        color: $tertiary-text;

        // Dark mode
        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }
    }

    .slider {
      -webkit-appearance: none;
      width: 100%;
      height: 6px;
      border-radius: 3px;
      background: $system-light-gray;
      outline: none;

      &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: $system-blue;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 2px 8px rgba($system-blue, 0.4);
        }

        &:active {
          transform: scale(0.95);
        }
      }

      // Dark mode
      .theme-dark & {
        background: $dark-border-color;
      }
    }
  }
}

// Toggle settings
.toggle-setting {
  margin-bottom: 1.5rem;

  .toggle-container {
    display: flex;
    flex-direction: column;

    .toggle-label {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;

      svg {
        color: $system-blue;
        margin-right: 0.5rem;
      }

      span {
        font-size: 1rem;
        font-weight: 500;
      }
    }

    .toggle-description {
      margin-bottom: 1rem;

      p {
        font-size: 0.9rem;
        color: $secondary-text;
        margin: 0;

        // Dark mode
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
      align-self: flex-start;

      input {
        opacity: 0;
        width: 0;
        height: 0;

        &:checked+.toggle-slider {
          background-color: $system-blue;

          &:before {
            transform: translateX(26px);
          }
        }
      }

      .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: $system-light-gray;
        transition: .4s;
        border-radius: 34px;

        &:before {
          position: absolute;
          content: "";
          height: 20px;
          width: 20px;
          left: 2px;
          bottom: 2px;
          background-color: white;
          transition: .4s;
          border-radius: 50%;
        }

        // Dark mode
        .theme-dark & {
          background-color: $dark-border-color;
        }
      }

      .toggle-status {
        position: absolute;
        left: 60px;
        top: 2px;
        font-size: 0.9rem;
        color: $secondary-text;

        // Dark mode
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }
}

// Preview button
.preview-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background-color: $system-blue;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  svg {
    margin-right: 0.5rem;
  }

  &:hover {
    background-color: color.adjust($system-blue, $lightness: -5%);
    transform: translateY(-1px);
  }

  &:active {
    background-color: color.adjust($system-blue, $lightness: -10%);
    transform: translateY(0);
  }

  &.active {
    background-color: $system-green;

    &:hover {
      background-color: color.adjust($system-green, $lightness: -5%);
    }
  }

  &:disabled {
    background-color: $system-gray;
    cursor: not-allowed;
  }
}

// Voice panel specific styles
.voice-panel {
  .preview-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 2rem 0;

    .voice-visualizer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
      width: 100%;
      margin-top: 1.5rem;

      .visualizer-bar {
        width: 4px;
        background-color: $system-blue;
        margin: 0 2px;
        border-radius: 2px;
        animation: pulse 1s infinite alternate;

        @keyframes pulse {
          from {
            height: 10px;
          }

          to {
            height: 50px;
          }
        }
      }
    }
  }
}

// Teaching style panel
.teaching-panel {
  .teaching-styles {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .teaching-style-card {
      display: flex;
      align-items: center;
      padding: 1rem;
      border-radius: 10px;
      background-color: $secondary-background;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      border: 1px solid transparent;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      &.active {
        border-color: $system-blue;
        background-color: rgba($system-blue, 0.05);

        .style-selector {
          background-color: $system-blue;
        }
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        &.active {
          border-color: $system-blue;
          background-color: rgba($system-blue, 0.1);
        }
      }

      .style-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: rgba($system-blue, 0.1);
        margin-right: 1rem;
        color: $system-blue;

        // Dark mode
        .theme-dark & {
          background-color: rgba($system-blue, 0.2);
        }
      }

      .style-content {
        flex: 1;

        h3 {
          margin: 0 0 0.25rem 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: $primary-text;

          // Dark mode
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        p {
          margin: 0;
          font-size: 0.9rem;
          color: $secondary-text;

          // Dark mode
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }

      .style-selector {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid $system-light-gray;
        margin-left: 1rem;
        transition: all 0.2s ease;

        // Dark mode
        .theme-dark & {
          border-color: $dark-border-color;
        }
      }
    }
  }
}

// Knowledge depth panel
.knowledge-panel {
  .knowledge-dial-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 2rem 0;

    .knowledge-dial {
      position: relative;
      width: 200px;
      height: 200px;
      border-radius: 50%;
      background-color: $secondary-background;
      border: 1px solid $border-color;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;
        border-color: $dark-border-color;
      }

      .dial-markers {
        position: absolute;
        width: 100%;
        height: 100%;

        .dial-marker {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100px;
          height: 2px;
          transform-origin: 0 50%;

          .marker-dot {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: $system-light-gray;
            transition: all 0.3s ease;

            // Dark mode
            .theme-dark & {
              background-color: $dark-border-color;
            }
          }

          &.active .marker-dot {
            background-color: $system-blue;
            box-shadow: 0 0 8px rgba($system-blue, 0.5);
          }
        }
      }

      .dial-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 90px;
        height: 4px;
        background-color: $system-blue;
        transform-origin: 0 50%;
        transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: 2;

        .indicator-arrow {
          position: absolute;
          right: -12px;
          top: 50%;
          transform: translateY(-50%);
          color: $system-blue;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .dial-center {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: $window-background;
        border: 1px solid $border-color;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        // Dark mode
        .theme-dark & {
          background-color: $dark-window-background;
          border-color: $dark-border-color;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .depth-level {
          font-size: 0.9rem;
          font-weight: 600;
          color: $system-blue;
        }
      }
    }

    .depth-controls {
      display: flex;
      gap: 0.5rem;

      .depth-button {
        padding: 0.5rem 0.75rem;
        border: 1px solid $border-color;
        border-radius: 6px;
        background-color: $window-background;
        color: $secondary-text;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: $system-blue;
          color: $system-blue;
        }

        &.active {
          background-color: $system-blue;
          color: white;
          border-color: $system-blue;
        }

        // Dark mode
        .theme-dark & {
          background-color: $dark-window-background;
          border-color: $dark-border-color;
          color: $dark-secondary-text;

          &:hover {
            border-color: $system-blue;
            color: $system-blue;
          }

          &.active {
            background-color: $system-blue;
            color: white;
            border-color: $system-blue;
          }
        }
      }
    }
  }

  .depth-description {
    background-color: $secondary-background;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;

    h4 {
      margin: 0 0 0.5rem 0;
      font-size: 1rem;
      color: $primary-text;

      // Dark mode
      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    p {
      margin: 0;
      font-size: 0.9rem;
      color: $secondary-text;

      // Dark mode
      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    // Dark mode
    .theme-dark & {
      background-color: $dark-secondary-background;
    }
  }
}

// Neural Processing panel
.neural-panel {
  .neural-visualization {
    margin: 2rem 0;
    display: flex;
    justify-content: center;

    .neural-network {
      position: relative;
      width: 300px;
      height: 200px;
      display: flex;
      justify-content: space-between;

      .neural-layer {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: 100%;

        .neural-node {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: $system-light-gray;
          position: relative;
          transition: all 0.3s ease;

          &.active {
            background-color: $system-blue;
            box-shadow: 0 0 12px rgba($system-blue, 0.5);

            .node-pulse {
              opacity: 1;
              animation: node-pulse 2s infinite;
            }
          }

          .node-pulse {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: $system-blue;
            opacity: 0;
            z-index: -1;
          }

          // Dark mode
          .theme-dark & {
            background-color: $dark-border-color;

            &.active {
              background-color: $system-blue;
            }
          }

          @keyframes node-pulse {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }

            70% {
              opacity: 0;
              transform: scale(2);
            }

            100% {
              opacity: 0;
              transform: scale(2);
            }
          }
        }
      }

      .neural-connections {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;

        .connection-line {
          position: absolute;
          height: 2px;
          background: linear-gradient(to right, rgba($system-blue, 0.2), rgba($system-blue, 0.8));
          top: 50%;
          left: 25%;
          width: 50%;
          opacity: 0.5;
          animation: connection-pulse 3s infinite;
          transform-origin: 0 0;

          @for $i from 1 through 12 {
            &:nth-child(#{$i}) {
              top: #{10 + ($i * 7)}%;
              transform: rotate(#{($i - 6) * 5}deg);
            }
          }
        }

        @keyframes connection-pulse {
          0% {
            opacity: 0.2;
          }

          50% {
            opacity: 0.8;
          }

          100% {
            opacity: 0.2;
          }
        }
      }
    }
  }
}

// Context Memory panel
.context-panel {
  .memory-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .memory-option {
      padding: 1rem;
      border-radius: 10px;
      background-color: $secondary-background;
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      &.active {
        border-color: $system-blue;
        background-color: rgba($system-blue, 0.05);

        .memory-selector {
          background-color: $system-blue;
        }
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        &.active {
          border-color: $system-blue;
          background-color: rgba($system-blue, 0.1);
        }
      }

      .memory-option-header {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;

        .memory-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: rgba($system-blue, 0.1);
          margin-right: 1rem;
          color: $system-blue;

          // Dark mode
          .theme-dark & {
            background-color: rgba($system-blue, 0.2);
          }
        }

        .memory-label {
          flex: 1;

          h3 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: $primary-text;

            // Dark mode
            .theme-dark & {
              color: $dark-primary-text;
            }
          }

          .memory-duration {
            font-size: 0.8rem;
            color: $tertiary-text;

            // Dark mode
            .theme-dark & {
              color: $dark-tertiary-text;
            }
          }
        }

        .memory-selector {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 2px solid $system-light-gray;
          transition: all 0.2s ease;

          // Dark mode
          .theme-dark & {
            border-color: $dark-border-color;
          }
        }
      }

      .memory-description {
        font-size: 0.9rem;
        color: $secondary-text;
        margin-left: calc(40px + 1rem);

        // Dark mode
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }

  .memory-visualization {
    margin: 2rem 0;

    .memory-timeline {
      .timeline-track {
        height: 8px;
        background-color: $system-light-gray;
        border-radius: 4px;
        position: relative;
        margin-bottom: 1rem;

        .memory-indicator {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          background-color: $system-blue;
          border-radius: 4px;
          transition: width 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        // Dark mode
        .theme-dark & {
          background-color: $dark-border-color;
        }
      }

      .timeline-markers {
        display: flex;
        justify-content: space-between;

        .timeline-marker {
          display: flex;
          flex-direction: column;
          align-items: center;

          .marker-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: $system-blue;
            margin-bottom: 0.5rem;
          }

          span {
            font-size: 0.8rem;
            color: $secondary-text;

            // Dark mode
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }
        }
      }
    }
  }
}

// Cross-Subject Linking panel
.linking-panel {
  .subject-linking-visualization {
    margin: 2rem 0;
    display: flex;
    justify-content: center;

    .subjects-network {
      position: relative;
      width: 300px;
      height: 300px;

      .subject-node {
        position: absolute;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.8rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        transform: translate(-50%, -50%);

        span {
          text-align: center;
          padding: 0.25rem;
        }

        &:hover {
          transform: translate(-50%, -50%) scale(1.1);
          z-index: 10;
        }
      }

      .subject-connections {
        .connection-line {
          position: absolute;
          height: 2px;
          background-color: rgba($system-blue, 0.3);
          transform-origin: 0 0;
          z-index: -1;

          &::after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: $system-blue;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            animation: connection-dot 3s infinite alternate;
          }

          @keyframes connection-dot {
            from {
              transform: translateY(-50%) translateX(0);
              opacity: 0;
            }

            to {
              transform: translateY(-50%) translateX(100%);
              opacity: 1;
            }
          }
        }
      }
    }
  }
}

// Response Priority panel
.priority-panel {
  .priority-matrix {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .priority-level {
      padding: 1rem;
      border-radius: 10px;
      background-color: $secondary-background;
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      &.active {
        border-color: $system-blue;
        background-color: rgba($system-blue, 0.05);

        .priority-selector {
          background-color: $system-blue;
        }
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        &.active {
          border-color: $system-blue;
          background-color: rgba($system-blue, 0.1);
        }
      }

      .priority-header {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;

        .priority-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: rgba($system-blue, 0.1);
          margin-right: 1rem;
          color: $system-blue;

          // Dark mode
          .theme-dark & {
            background-color: rgba($system-blue, 0.2);
          }
        }

        .priority-label {
          flex: 1;

          h3 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: $primary-text;

            // Dark mode
            .theme-dark & {
              color: $dark-primary-text;
            }
          }

          .response-time {
            font-size: 0.8rem;
            color: $tertiary-text;

            // Dark mode
            .theme-dark & {
              color: $dark-tertiary-text;
            }
          }
        }

        .priority-selector {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 2px solid $system-light-gray;
          transition: all 0.2s ease;

          // Dark mode
          .theme-dark & {
            border-color: $dark-border-color;
          }
        }
      }

      .priority-description {
        font-size: 0.9rem;
        color: $secondary-text;
        margin-left: calc(40px + 1rem);

        // Dark mode
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }

  .priority-visualization {
    margin: 2rem 0;
    display: flex;
    justify-content: center;

    .speedometer {
      position: relative;
      width: 200px;
      height: 100px;
      overflow: hidden;

      .speedometer-scale {
        position: absolute;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        top: 0;
        left: 0;
        overflow: hidden;

        .scale-segment {
          position: absolute;
          width: 200px;
          height: 200px;
          border-radius: 50%;
          clip: rect(0, 200px, 200px, 100px);

          &.standard {
            background-color: $system-light-gray;
            transform: rotate(-60deg);

            // Dark mode
            .theme-dark & {
              background-color: $dark-border-color;
            }
          }

          &.normal {
            background-color: $system-blue;
            transform: rotate(-30deg);
          }

          &.urgent {
            background-color: $system-red;
            transform: rotate(0deg);
          }
        }
      }

      .speedometer-needle {
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 4px;
        height: 90px;
        background-color: $primary-text;
        transform-origin: bottom center;
        transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: 2;

        // Dark mode
        .theme-dark & {
          background-color: $dark-primary-text;
        }
      }

      .speedometer-center {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: $window-background;
        border: 2px solid $primary-text;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;

        span {
          font-size: 0.7rem;
          font-weight: 600;
          color: $system-blue;
        }

        // Dark mode
        .theme-dark & {
          background-color: $dark-window-background;
          border-color: $dark-primary-text;
        }
      }
    }
  }
}

// Issue Reporting panel
.issue-panel {
  .issue-categories {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;

    .issue-category {
      display: flex;
      align-items: center;
      padding: 1rem;
      border-radius: 10px;
      background-color: $secondary-background;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      border: 1px solid transparent;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      &.active {
        border-color: $system-blue;
        background-color: rgba($system-blue, 0.05);

        .category-selector {
          background-color: $system-blue;
        }
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        &.active {
          border-color: $system-blue;
          background-color: rgba($system-blue, 0.1);
        }
      }

      .category-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: rgba($system-blue, 0.1);
        margin-right: 1rem;
        color: $system-blue;

        // Dark mode
        .theme-dark & {
          background-color: rgba($system-blue, 0.2);
        }
      }

      .category-content {
        flex: 1;

        h3 {
          margin: 0 0 0.25rem 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: $primary-text;

          // Dark mode
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        p {
          margin: 0;
          font-size: 0.9rem;
          color: $secondary-text;

          // Dark mode
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }

      .category-selector {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid $system-light-gray;
        margin-left: 1rem;
        transition: all 0.2s ease;

        // Dark mode
        .theme-dark & {
          border-color: $dark-border-color;
        }
      }
    }
  }

  .issue-form {
    .form-group {
      margin-bottom: 1rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        font-weight: 500;
        color: $secondary-text;

        // Dark mode
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }

      .form-control {
        width: 100%;
        padding: 0.75rem;
        border-radius: 8px;
        border: 1px solid $border-color;
        background-color: $window-background;
        font-size: 0.95rem;
        color: $primary-text;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

        &:focus {
          outline: none;
          border-color: $system-blue;
          box-shadow: 0 0 0 2px rgba($system-blue, 0.2);
        }

        // Dark mode
        .theme-dark & {
          background-color: $dark-window-background;
          border-color: $dark-border-color;
          color: $dark-primary-text;

          &:focus {
            border-color: $system-blue;
            box-shadow: 0 0 0 2px rgba($system-blue, 0.3);
          }
        }
      }
    }

    .submit-issue-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.75rem 1.5rem;
      background-color: $system-blue;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-top: 1.5rem;

      svg {
        margin-right: 0.5rem;
      }

      &:hover {
        background-color: color.adjust($system-blue, $lightness: -5%);
        transform: translateY(-1px);
      }

      &:active {
        background-color: color.adjust($system-blue, $lightness: -10%);
        transform: translateY(0);
      }
    }
  }
}

// Screenshot Tool panel
.screenshot-panel {
  .screenshot-tabs {
    display: flex;
    border-bottom: 1px solid $border-color;
    margin-bottom: 1.5rem;

    // Dark mode
    .theme-dark & {
      border-color: $dark-border-color;
    }

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0.75rem 1.5rem;
      cursor: pointer;
      transition: all 0.2s ease;
      color: $tertiary-text;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -1px;
        width: 100%;
        height: 2px;
        background-color: transparent;
        transition: all 0.2s ease;
      }

      &:hover {
        color: $secondary-text;
      }

      &.active {
        color: $system-blue;

        &::after {
          background-color: $system-blue;
        }
      }

      // Dark mode
      .theme-dark & {
        color: $dark-tertiary-text;

        &:hover {
          color: $dark-secondary-text;
        }

        &.active {
          color: $system-blue;
        }
      }

      .tab-icon {
        margin-bottom: 0.5rem;
      }

      .tab-label {
        font-size: 0.9rem;
        font-weight: 500;
      }
    }
  }

  .screenshot-content {
    margin-bottom: 1.5rem;

    .capture-section,
    .annotate-section,
    .gallery-section {

      .capture-preview,
      .annotation-canvas,
      .gallery-grid {
        height: 200px;
        background-color: $secondary-background;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;

        // Dark mode
        .theme-dark & {
          background-color: $dark-secondary-background;
        }

        .preview-placeholder,
        .canvas-placeholder,
        .empty-gallery {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: $tertiary-text;

          svg {
            font-size: 2.5rem;
            margin-bottom: 1rem;
          }

          p {
            font-size: 0.9rem;
            text-align: center;
            max-width: 80%;
          }

          // Dark mode
          .theme-dark & {
            color: $dark-tertiary-text;
          }
        }
      }
    }

    .capture-controls {
      .capture-button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        background-color: $system-blue;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 1rem;

        svg {
          margin-right: 0.5rem;
        }

        &:hover {
          background-color: color.adjust($system-blue, $lightness: -5%);
          transform: translateY(-1px);
        }

        &:active {
          background-color: color.adjust($system-blue, $lightness: -10%);
          transform: translateY(0);
        }
      }

      .capture-options {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;

        .option-item {
          display: flex;
          align-items: center;
          font-size: 0.9rem;
          color: $secondary-text;

          input[type="checkbox"] {
            margin-right: 0.5rem;
            accent-color: $system-blue;
          }

          // Dark mode
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }
    }

    .annotation-tools {
      .tool-group {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;

        .tool-button {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 0.5rem;
          background-color: $secondary-background;
          border: 1px solid $border-color;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba($system-blue, 0.05);
            border-color: $system-blue;
          }

          .tool-icon {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
          }

          span {
            font-size: 0.8rem;
            color: $secondary-text;
          }

          // Dark mode
          .theme-dark & {
            background-color: $dark-secondary-background;
            border-color: $dark-border-color;

            &:hover {
              background-color: rgba($system-blue, 0.1);
              border-color: $system-blue;
            }

            span {
              color: $dark-secondary-text;
            }
          }
        }
      }

      .color-picker {
        display: flex;
        gap: 0.5rem;

        .color-dot {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.2s ease;
          border: 2px solid transparent;

          &.active {
            border-color: $primary-text;
            transform: scale(1.1);

            // Dark mode
            .theme-dark & {
              border-color: $dark-primary-text;
            }
          }

          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }
  }

  .screenshot-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;

    .action-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-size: 0.95rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &.secondary {
        background-color: $secondary-background;
        color: $secondary-text;
        border: 1px solid $border-color;

        &:hover {
          background-color: $system-light-gray;
        }

        // Dark mode
        .theme-dark & {
          background-color: $dark-secondary-background;
          color: $dark-secondary-text;
          border-color: $dark-border-color;

          &:hover {
            background-color: $dark-border-color;
          }
        }
      }

      &.primary {
        background-color: $system-blue;
        color: white;
        border: none;

        svg {
          margin-right: 0.5rem;
        }

        &:hover {
          background-color: color.adjust($system-blue, $lightness: -5%);
          transform: translateY(-1px);
        }

        &:active {
          background-color: color.adjust($system-blue, $lightness: -10%);
          transform: translateY(0);
        }
      }
    }
  }
}

// Theme Settings panel
.theme-panel {
  .theme-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;

    .theme-option {
      display: flex;
      align-items: center;
      padding: 1rem;
      border-radius: 10px;
      background-color: $secondary-background;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      border: 1px solid transparent;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      &.active {
        border-color: $system-blue;
        background-color: rgba($system-blue, 0.05);

        .theme-selector {
          background-color: $system-blue;
        }
      }

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        &.active {
          border-color: $system-blue;
          background-color: rgba($system-blue, 0.1);
        }
      }

      .theme-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: rgba($system-blue, 0.1);
        margin-right: 1rem;
        color: $system-blue;

        // Dark mode
        .theme-dark & {
          background-color: rgba($system-blue, 0.2);
        }
      }

      .theme-content {
        flex: 1;

        h3 {
          margin: 0 0 0.25rem 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: $primary-text;

          // Dark mode
          .theme-dark & {
            color: $dark-primary-text;
          }
        }

        p {
          margin: 0;
          font-size: 0.9rem;
          color: $secondary-text;

          // Dark mode
          .theme-dark & {
            color: $dark-secondary-text;
          }
        }
      }

      .theme-selector {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid $system-light-gray;
        margin-left: 1rem;
        transition: all 0.2s ease;

        // Dark mode
        .theme-dark & {
          border-color: $dark-border-color;
        }
      }
    }
  }

  .theme-preview {
    margin-bottom: 1.5rem;

    .preview-header {
      margin-bottom: 1rem;

      h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: $primary-text;
        margin: 0;

        // Dark mode
        .theme-dark & {
          color: $dark-primary-text;
        }
      }
    }

    .preview-container {
      margin-bottom: 1rem;

      .preview-frame {
        width: 100%;
        height: 200px;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        border: 1px solid $border-color;
        transition: all 0.3s ease;

        &.light {
          background-color: $window-background;

          .preview-sidebar {
            background-color: $secondary-background;
            border-right: 1px solid $border-color;
          }

          .preview-header-bar {
            background-color: $window-background;
            border-bottom: 1px solid $border-color;
          }

          .preview-card {
            background-color: $window-background;
            border: 1px solid $border-color;
          }
        }

        &.dark {
          background-color: $dark-window-background;

          .preview-sidebar {
            background-color: $dark-secondary-background;
            border-right: 1px solid $dark-border-color;
          }

          .preview-header-bar {
            background-color: $dark-window-background;
            border-bottom: 1px solid $dark-border-color;
          }

          .preview-card {
            background-color: $dark-window-background;
            border: 1px solid $dark-border-color;
          }
        }

        .preview-sidebar {
          width: 60px;
          height: 100%;
          transition: all 0.3s ease;
        }

        .preview-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .preview-header-bar {
            height: 40px;
            transition: all 0.3s ease;
          }

          .preview-main {
            flex: 1;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .preview-card {
              height: 50px;
              border-radius: 8px;
              transition: all 0.3s ease;
            }
          }
        }
      }
    }

    .preview-controls {
      display: flex;
      gap: 0.5rem;

      .preview-toggle {
        display: flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        border: 1px solid $border-color;
        border-radius: 6px;
        background-color: $window-background;
        color: $secondary-text;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.2s ease;

        svg {
          margin-right: 0.5rem;
          font-size: 1.1rem;
        }

        &:hover {
          border-color: $system-blue;
          color: $system-blue;
        }

        &.active {
          background-color: $system-blue;
          color: white;
          border-color: $system-blue;
        }

        // Dark mode
        .theme-dark & {
          background-color: $dark-window-background;
          border-color: $dark-border-color;
          color: $dark-secondary-text;

          &:hover {
            border-color: $system-blue;
            color: $system-blue;
          }

          &.active {
            background-color: $system-blue;
            color: white;
            border-color: $system-blue;
          }
        }
      }
    }
  }
}

// Motion Settings panel
.motion-panel {
  .motion-preview {
    margin: 2rem 0;

    .preview-header {
      margin-bottom: 1rem;

      h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: $primary-text;
        margin: 0;

        // Dark mode
        .theme-dark & {
          color: $dark-primary-text;
        }
      }
    }

    .animation-container {
      height: 150px;
      background-color: $secondary-background;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      overflow: hidden;

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;
      }

      .animation-demo {
        .animated-elements {
          display: flex;
          align-items: center;
          gap: 1rem;

          .animated-element {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            animation: float 3s infinite alternate ease-in-out;

            @keyframes float {
              0% {
                transform: translateY(0) scale(1);
              }

              100% {
                transform: translateY(-20px) scale(1.1);
              }
            }
          }
        }

        .static-elements {
          display: flex;
          align-items: center;
          gap: 1rem;

          .static-element {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
          }
        }
      }
    }

    .preview-description {
      text-align: center;

      p {
        font-size: 0.9rem;
        color: $secondary-text;

        // Dark mode
        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }

  .motion-options {
    margin-top: 2rem;

    .option-group {
      background-color: $secondary-background;
      border-radius: 10px;
      padding: 1rem;

      // Dark mode
      .theme-dark & {
        background-color: $dark-secondary-background;
      }

      h3 {
        margin: 0 0 1rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: $primary-text;

        // Dark mode
        .theme-dark & {
          color: $dark-primary-text;
        }
      }

      .option-item {
        margin-bottom: 0.75rem;

        &:last-child {
          margin-bottom: 0;
        }

        .option-label {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            font-size: 0.9rem;
            color: $secondary-text;

            // Dark mode
            .theme-dark & {
              color: $dark-secondary-text;
            }
          }

          .option-select {
            padding: 0.5rem;
            border-radius: 6px;
            border: 1px solid $border-color;
            background-color: $window-background;
            color: $primary-text;
            font-size: 0.9rem;

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            // Dark mode
            .theme-dark & {
              background-color: $dark-window-background;
              border-color: $dark-border-color;
              color: $dark-primary-text;
            }
          }
        }
      }
    }
  }
}