import { useState, useEffect, useRef } from 'react';
import { usePomodoroContext } from '../../../contexts/pomodoroContext';
import './PomodoroTimer.scss';

const PomodoroTimer = ({ subject, onClose }) => {
    // Global Pomodoro Context
    const {
        isRunning,
        isWorkPhase,
        workDuration: globalWorkDuration,
        breakDuration: globalBreakDuration,
        timeLeft,
        formatTime,
        toggleTimer,
        resetTimer,
        updateWorkDuration,
        updateBreakDuration,
        showMiniTimerAndClosePopup
    } = usePomodoroContext();

    // Theme detection
    const [isDarkMode, setIsDarkMode] = useState(false);

    // Local timer states for advanced features
    const [sessionType, setSessionType] = useState('work');
    const [cyclesCompleted, setCyclesCompleted] = useState(0);
    const [totalFocusTime, setTotalFocusTime] = useState(0);

    // Settings states
    const [workDuration, setWorkDuration] = useState(25);
    const [shortBreakDuration, setShortBreakDuration] = useState(5);
    const [longBreakDuration, setLongBreakDuration] = useState(15);
    const [longBreakInterval, setLongBreakInterval] = useState(4);
    const [showSettings, setShowSettings] = useState(false);
    const [notes, setNotes] = useState('');
    const [autoStartBreaks, setAutoStartBreaks] = useState(false);
    const [autoStartPomodoros, setAutoStartPomodoros] = useState(false);
    const [soundEnabled, setSoundEnabled] = useState(true);
    const [tickSoundEnabled, setTickSoundEnabled] = useState(false);
    const [tickSoundType, setTickSoundType] = useState('classic');
    const [tickVolume, setTickVolume] = useState(0.3);

    // Progress and animation states
    const [progress, setProgress] = useState(0);
    const [isAnimating, setIsAnimating] = useState(false);
    const [isTickPlaying, setIsTickPlaying] = useState(false);

    // Derived state from global context
    const isActive = isRunning;

    // Audio refs for notifications
    const alarmSound = useRef(null);
    const tickSounds = useRef({});
    const audioContext = useRef(null);

    // Tick sound options
    const tickSoundOptions = [
        { id: 'classic', name: 'Classic Clock', frequency: 800 },
        { id: 'modern', name: 'Modern Tick', frequency: 1000 },
        { id: 'soft', name: 'Soft Click', frequency: 600 },
        { id: 'mechanical', name: 'Mechanical', frequency: 400 },
        { id: 'digital', name: 'Digital Beep', frequency: 1200 }
    ];

    // Initialize audio context and tick sounds
    useEffect(() => {
        const initializeAudio = async () => {
            try {
                // Initialize Web Audio API for tick sounds
                const AudioContextClass = window.AudioContext || window.webkitAudioContext || false;
                if (AudioContextClass) {
                    audioContext.current = new AudioContextClass();
                } else {
                    throw new Error('Web Audio API not supported');
                }

                // Create tick sounds for each type
                tickSoundOptions.forEach(option => {
                    tickSounds.current[option.id] = createTickSound(option.frequency);
                });
            } catch (error) {
                console.warn('Web Audio API not supported, falling back to HTML5 audio');
            }
        };

        initializeAudio();

        return () => {
            if (audioContext.current) {
                audioContext.current.close();
            }
        };
    }, []);

    // Theme detection
    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        checkDarkMode();
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    // Sync local settings with global context
    useEffect(() => {
        updateWorkDuration(workDuration);
    }, [workDuration, updateWorkDuration]);

    useEffect(() => {
        updateBreakDuration(shortBreakDuration);
    }, [shortBreakDuration, updateBreakDuration]);

    // Calculate progress based on global timer state
    useEffect(() => {
        let totalTime;
        if (isWorkPhase) {
            totalTime = workDuration * 60;
        } else {
            totalTime = shortBreakDuration * 60;
        }

        const newProgress = ((totalTime - timeLeft) / totalTime) * 100;
        setProgress(Math.max(0, Math.min(100, newProgress)));
    }, [timeLeft, isWorkPhase, workDuration, shortBreakDuration]);

    // Create tick sound function
    const createTickSound = (frequency) => {
        if (!audioContext.current) return null;

        return () => {
            try {
                const oscillator = audioContext.current.createOscillator();
                const gainNode = audioContext.current.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.current.destination);

                oscillator.frequency.setValueAtTime(frequency, audioContext.current.currentTime);
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0, audioContext.current.currentTime);
                gainNode.gain.linearRampToValueAtTime(tickVolume, audioContext.current.currentTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.current.currentTime + 0.1);

                oscillator.start(audioContext.current.currentTime);
                oscillator.stop(audioContext.current.currentTime + 0.1);
            } catch (error) {
                console.warn('Error playing tick sound:', error);
            }
        };
    };

    // Play tick sound function
    const playTickSound = () => {
        if (!tickSoundEnabled) return;

        try {
            if (audioContext.current && tickSounds.current[tickSoundType]) {
                // Resume audio context if suspended (required by some browsers)
                if (audioContext.current.state === 'suspended') {
                    audioContext.current.resume();
                }

                // Visual feedback
                setIsTickPlaying(true);
                setTimeout(() => setIsTickPlaying(false), 100);

                tickSounds.current[tickSoundType]();
            }
        } catch (error) {
            console.warn('Error playing tick sound:', error);
        }
    };

    // Test tick sound function
    const testTickSound = () => {
        playTickSound();
    };

    // Play tick sound when timer is running
    useEffect(() => {
        let interval = null;

        if (isActive && tickSoundEnabled) {
            interval = setInterval(() => {
                playTickSound();
            }, 1000);
        }

        return () => clearInterval(interval);
    }, [isActive, tickSoundEnabled, tickSoundType, tickVolume]);

    const playSound = (soundRef) => {
        if (soundEnabled && soundRef.current) {
            soundRef.current.currentTime = 0;
            soundRef.current.play().catch(() => { });
        }
    };

    const handleTimerComplete = () => {
        setIsAnimating(true);
        playSound(alarmSound);

        if (sessionType === 'work') {
            const newCycles = cyclesCompleted + 1;
            setCyclesCompleted(newCycles);
            setTotalFocusTime(prev => prev + workDuration);

            // Determine break type
            if (newCycles % longBreakInterval === 0) {
                setSessionType('longBreak');
            } else {
                setSessionType('shortBreak');
            }
        } else {
            setSessionType('work');
        }

        setTimeout(() => setIsAnimating(false), 1000);
    };

    const handleToggleTimer = () => {
        toggleTimer();
        // Show mini timer and close popup when starting the timer
        if (!isActive) {
            showMiniTimerAndClosePopup();
        }
    };

    const handleResetTimer = () => {
        resetTimer();
        setSessionType('work');
        setProgress(0);
    };

    const skipSession = () => {
        handleTimerComplete();
    };

    const handleSaveSettings = () => {
        resetTimer();
        setSessionType('work');
        setProgress(0);
        setShowSettings(false);
    };

    const handleMinimize = () => {
        showMiniTimerAndClosePopup();
    };

    const getSessionDisplayName = () => {
        switch (sessionType) {
            case 'work': return 'Focus Time';
            case 'shortBreak': return 'Short Break';
            case 'longBreak': return 'Long Break';
            default: return 'Focus Time';
        }
    };

    const getSessionColor = () => {
        switch (sessionType) {
            case 'work': return 'work';
            case 'shortBreak': return 'short-break';
            case 'longBreak': return 'long-break';
            default: return 'work';
        }
    };

    return (
        <div className={`advanced-pomodoro-timer ${isDarkMode ? 'theme-dark' : ''} ${getSessionColor()} ${isAnimating ? 'animating' : ''}`}>
            {/* Audio elements */}
            <audio ref={alarmSound} src="https://assets.mixkit.co/sfx/preview/mixkit-alarm-digital-clock-beep-989.mp3" />

            {/* Header */}
            <div className="timer-header">
                <div className="header-left">
                    <h1 className="timer-title">{subject || 'Pomodoro Timer'}</h1>
                    <div className="session-badge">
                        <span className="session-icon">
                            {sessionType === 'work' ? '🎯' : sessionType === 'shortBreak' ? '☕' : '🌟'}
                        </span>
                        <span className="session-name">{getSessionDisplayName()}</span>
                    </div>
                </div>
                <div className="header-controls">
                    <button
                        className="icon-button minimize-button"
                        onClick={handleMinimize}
                        aria-label="Minimize to mini timer"
                        title="Minimize to mini timer"
                    >
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 13H5v-2h14v2z" />
                        </svg>
                    </button>
                    <button
                        className="icon-button settings-button"
                        onClick={() => setShowSettings(!showSettings)}
                        aria-label="Settings"
                    >
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z" />
                        </svg>
                    </button>
                    {onClose && (
                        <button
                            className="icon-button close-button"
                            onClick={onClose}
                            aria-label="Close Pomodoro Timer"
                            title="Close"
                        >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                            </svg>
                        </button>
                    )}
                </div>
            </div>

            {showSettings ? (
                <div className="settings-panel">
                    <div className="settings-header">
                        <h2>Timer Settings</h2>
                        <button
                            className="close-settings"
                            onClick={() => setShowSettings(false)}
                            aria-label="Close settings"
                        >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                            </svg>
                        </button>
                    </div>

                    <div className="settings-content">
                        <div className="settings-section">
                            <h3>Duration Settings</h3>
                            <div className="settings-grid">
                                <div className="setting-item">
                                    <label>Focus Duration</label>
                                    <div className="input-group">
                                        <input
                                            type="number"
                                            min="1"
                                            max="60"
                                            value={workDuration}
                                            onChange={(e) => setWorkDuration(parseInt(e.target.value) || 25)}
                                        />
                                        <span className="input-suffix">min</span>
                                    </div>
                                </div>
                                <div className="setting-item">
                                    <label>Short Break</label>
                                    <div className="input-group">
                                        <input
                                            type="number"
                                            min="1"
                                            max="30"
                                            value={shortBreakDuration}
                                            onChange={(e) => setShortBreakDuration(parseInt(e.target.value) || 5)}
                                        />
                                        <span className="input-suffix">min</span>
                                    </div>
                                </div>
                                <div className="setting-item">
                                    <label>Long Break</label>
                                    <div className="input-group">
                                        <input
                                            type="number"
                                            min="1"
                                            max="60"
                                            value={longBreakDuration}
                                            onChange={(e) => setLongBreakDuration(parseInt(e.target.value) || 15)}
                                        />
                                        <span className="input-suffix">min</span>
                                    </div>
                                </div>
                                <div className="setting-item">
                                    <label>Long Break Interval</label>
                                    <div className="input-group">
                                        <input
                                            type="number"
                                            min="2"
                                            max="10"
                                            value={longBreakInterval}
                                            onChange={(e) => setLongBreakInterval(parseInt(e.target.value) || 4)}
                                        />
                                        <span className="input-suffix">cycles</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="settings-section">
                            <h3>Automation</h3>
                            <div className="toggle-group">
                                <div className="toggle-item">
                                    <label>Auto-start breaks</label>
                                    <button
                                        className={`toggle ${autoStartBreaks ? 'active' : ''}`}
                                        onClick={() => setAutoStartBreaks(!autoStartBreaks)}
                                    >
                                        <span className="toggle-slider"></span>
                                    </button>
                                </div>
                                <div className="toggle-item">
                                    <label>Auto-start focus sessions</label>
                                    <button
                                        className={`toggle ${autoStartPomodoros ? 'active' : ''}`}
                                        onClick={() => setAutoStartPomodoros(!autoStartPomodoros)}
                                    >
                                        <span className="toggle-slider"></span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="settings-section">
                            <h3>Audio Settings</h3>
                            <div className="toggle-group">
                                <div className="toggle-item">
                                    <label>Notification sounds</label>
                                    <button
                                        className={`toggle ${soundEnabled ? 'active' : ''}`}
                                        onClick={() => setSoundEnabled(!soundEnabled)}
                                    >
                                        <span className="toggle-slider"></span>
                                    </button>
                                </div>
                                <div className="toggle-item">
                                    <label>Tick sounds</label>
                                    <button
                                        className={`toggle ${tickSoundEnabled ? 'active' : ''}`}
                                        onClick={() => setTickSoundEnabled(!tickSoundEnabled)}
                                    >
                                        <span className="toggle-slider"></span>
                                    </button>
                                </div>
                            </div>

                            {tickSoundEnabled && (
                                <div className="audio-controls">
                                    <div className="setting-item">
                                        <label>Tick Sound Type</label>
                                        <div className="sound-selector">
                                            {tickSoundOptions.map(option => (
                                                <button
                                                    key={option.id}
                                                    className={`sound-option ${tickSoundType === option.id ? 'active' : ''}`}
                                                    onClick={() => setTickSoundType(option.id)}
                                                >
                                                    <span className="sound-name">{option.name}</span>
                                                    <button
                                                        className="test-sound-btn"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            const prevType = tickSoundType;
                                                            setTickSoundType(option.id);
                                                            setTimeout(() => {
                                                                testTickSound();
                                                                setTickSoundType(prevType);
                                                            }, 100);
                                                        }}
                                                        aria-label={`Test ${option.name} sound`}
                                                    >
                                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                                            <path d="M8 5v14l11-7z" />
                                                        </svg>
                                                    </button>
                                                </button>
                                            ))}
                                        </div>
                                    </div>

                                    <div className="setting-item">
                                        <label>Tick Volume</label>
                                        <div className="volume-control">
                                            <span className="volume-icon">🔉</span>
                                            <input
                                                type="range"
                                                min="0.1"
                                                max="1"
                                                step="0.1"
                                                value={tickVolume}
                                                onChange={(e) => setTickVolume(parseFloat(e.target.value))}
                                                className="volume-slider"
                                            />
                                            <span className="volume-icon">🔊</span>
                                            <button
                                                className="test-volume-btn"
                                                onClick={testTickSound}
                                                aria-label="Test volume"
                                            >
                                                Test
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="settings-footer">
                        <button className="save-button" onClick={handleSaveSettings}>
                            Save Settings
                        </button>
                    </div>
                </div>
            ) : (
                <div className="timer-content">
                    {/* Main Timer Display */}
                    <div className="timer-main">
                        <div className="timer-circle">
                            <svg className="progress-ring" viewBox="0 0 200 200">
                                <circle
                                    className="progress-ring-background"
                                    cx="100"
                                    cy="100"
                                    r="90"
                                />
                                <circle
                                    className="progress-ring-progress"
                                    cx="100"
                                    cy="100"
                                    r="90"
                                    style={{
                                        strokeDasharray: `${2 * Math.PI * 90}`,
                                        strokeDashoffset: `${2 * Math.PI * 90 * (1 - progress / 100)}`
                                    }}
                                />
                            </svg>
                            <div className={`timer-display ${isTickPlaying ? 'tick-pulse' : ''}`}>
                                <div className="time-remaining">
                                    {formatTime(timeLeft)}
                                    {tickSoundEnabled && isActive && (
                                        <span className={`tick-indicator ${isTickPlaying ? 'active' : ''}`}>
                                            ♪
                                        </span>
                                    )}
                                </div>
                                <div className="session-label">
                                    {isWorkPhase ? 'Focus Time' : 'Break Time'}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Timer Controls */}
                    <div className="timer-controls">
                        <button
                            className="control-button secondary"
                            onClick={handleResetTimer}
                            disabled={!isActive && timeLeft === workDuration * 60}
                        >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z" />
                            </svg>
                            Reset
                        </button>

                        <button
                            className={`control-button primary ${isActive ? 'pause' : 'play'}`}
                            onClick={handleToggleTimer}
                        >
                            {isActive ? (
                                <>
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
                                    </svg>
                                    Pause
                                </>
                            ) : (
                                <>
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z" />
                                    </svg>
                                    Start
                                </>
                            )}
                        </button>

                        <button
                            className="control-button secondary"
                            onClick={skipSession}
                            disabled={!isActive}
                        >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" />
                            </svg>
                            Skip
                        </button>
                    </div>

                    {/* Statistics */}
                    <div className="timer-stats">
                        <div className="stat-item">
                            <div className="stat-value">{cyclesCompleted}</div>
                            <div className="stat-label">Completed</div>
                        </div>
                        <div className="stat-item">
                            <div className="stat-value">{Math.floor(totalFocusTime / 60)}h {totalFocusTime % 60}m</div>
                            <div className="stat-label">Focus Time</div>
                        </div>
                        <div className="stat-item">
                            <div className="stat-value">{cyclesCompleted % longBreakInterval}/{longBreakInterval}</div>
                            <div className="stat-label">Until Long Break</div>
                        </div>
                    </div>

                    {/* Notes Section */}
                    <div className="notes-section">
                        <div className="notes-header">
                            <h3>Session Notes</h3>
                        </div>
                        <textarea
                            className="notes-textarea"
                            placeholder="What are you working on? Add your thoughts and progress here..."
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            rows={4}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default PomodoroTimer;