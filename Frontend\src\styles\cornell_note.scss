@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;
$system-gold: #fbb034;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Mixins
@mixin macOS-shadow($opacity: 0.1) {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, $opacity),
        0 0.0625rem 0.125rem rgba(0, 0, 0, $opacity * 0.5);
}

@mixin macOS-focus-ring {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.25);
}

@mixin macOS-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    cursor: pointer;
    background-color: $secondary-background;
    color: $primary-text;
    border: 1px solid $border-color;

    &:hover {
        background-color: darken($secondary-background, 5%);
    }

    &:active {
        transform: scale(0.98);
    }

    .theme-dark & {
        background-color: $dark-secondary-background;
        color: $dark-primary-text;
        border-color: $dark-border-color;

        &:hover {
            background-color: lighten($dark-secondary-background, 5%);
        }
    }
}

// iOS-like scrollbar
@mixin ios-scrollbar {
    &::-webkit-scrollbar {
        width: 0.5rem;
        height: 0.5rem;
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba($system-gray, 0.5);
        border-radius: 0.25rem;

        .theme-dark & {
            background-color: rgba($dark-tertiary-text, 0.5);
        }
    }

    &::-webkit-scrollbar-thumb:hover {
        background-color: rgba($system-gray, 0.8);

        .theme-dark & {
            background-color: rgba($dark-tertiary-text, 0.8);
        }
    }
}

.cornell-notes-container {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
    padding: .5rem;
    background-color: $window-background;
    color: $primary-text;
    border-radius: 0.75rem;
    @include macOS-shadow(0.08);
    display: flex;
    flex-direction: column;
    //min-height: 100dvh;
    height: 100%;
    overflow: auto;
    transition: background-color 0.3s ease, color 0.3s ease;
    position: relative;

    &.theme-dark {
        background-color: $dark-window-background;
        color: $dark-primary-text;
    }

    * {
        box-sizing: border-box;
    }

    // macOS-style notification
    .macos-notification {
        position: fixed;
        top: 1.5rem;
        right: 1.5rem;
        width: 320px;
        padding: 1rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        z-index: 1000;
        animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-in 2.7s forwards;
        @include macOS-shadow(0.15);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);

        &.success {
            background-color: rgba($window-background, 0.85);
            border: 1px solid rgba($system-green, 0.3);

            .theme-dark & {
                background-color: rgba($dark-window-background, 0.85);
                border: 1px solid rgba($system-green, 0.3);
            }

            .notification-icon {
                color: $system-green;
            }

            .notification-title {
                color: $system-green;
            }
        }

        &.error {
            background-color: rgba($window-background, 0.85);
            border: 1px solid rgba($system-red, 0.3);

            .theme-dark & {
                background-color: rgba($dark-window-background, 0.85);
                border: 1px solid rgba($system-red, 0.3);
            }

            .notification-icon {
                color: $system-red;

                .error-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 1.5rem;
                    height: 1.5rem;
                    border-radius: 50%;
                    background-color: $system-red;
                    color: white;
                    font-weight: bold;
                    font-size: 1rem;
                }
            }

            .notification-title {
                color: $system-red;
            }
        }

        .notification-icon {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .notification-message {
            font-size: 0.875rem;
            color: $secondary-text;
            line-height: 1.4;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }

            to {
                opacity: 0;
            }
        }
    }
}

.cornell-notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    margin-bottom: .5rem;
    padding-bottom: .5rem;
    margin-top: 0;
    padding-top: 0;
    border-bottom: 1px solid $separator-color;

    .theme-dark & {
        border-bottom-color: $dark-separator-color;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        position: relative;
    }

    .close-button {
        position: absolute;
        top: 0;
        right: 0;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        background-color: transparent;
        color: $tertiary-text;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;

        .theme-dark & {
            color: $dark-tertiary-text;
        }

        &:hover {
            background-color: rgba($system-red, 0.1);
            color: $system-red;

            .theme-dark & {
                background-color: rgba($system-red, 0.2);
            }
        }

        &:active {
            transform: scale(0.9);
        }
    }
}

.metadata {
    display: flex;
    align-items: center;
    justify-content: left;
    gap: 1rem;
    flex-wrap: wrap;
    width: fit-content;
    float: left;
}

.subject {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.9375rem;
    color: $secondary-text;
    background-color: $secondary-background;
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;

    .theme-dark & {
        color: $dark-secondary-text;
        background-color: $dark-secondary-background;
    }

    .icon {
        color: $system-blue;

        .theme-dark & {
            color: $system-blue;
        }
    }
}

.dates {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: $tertiary-text;

    .theme-dark & {
        color: $dark-tertiary-text;
    }

    .icon {
        color: $system-gray;

        .theme-dark & {
            color: $dark-tertiary-text;
        }
    }
}

.icon {
    font-size: 1.125rem;
}

.title-container {
    flex: 1;
    margin: 0 1.5rem;
    text-align: center;
}

.notes-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: $primary-text;
    cursor: pointer;
    transition: color 0.2s ease;

    .theme-dark & {
        color: $dark-primary-text;
    }

    .edit-icon {
        font-size: 1.125rem;
        opacity: 0;
        transition: opacity 0.2s ease;
        color: $system-blue;
    }

    &:hover {
        color: $system-blue;

        .edit-icon {
            opacity: 1;
        }
    }
}

.title-input {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    border: none;
    background: none;
    text-align: center;
    width: 100%;
    color: $primary-text;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;

    .theme-dark & {
        color: $dark-primary-text;
        background-color: transparent;
    }

    &:focus {
        outline: none;
        background-color: rgba($system-blue, 0.1);

        .theme-dark & {
            background-color: rgba($system-blue, 0.2);
        }
    }

    &::placeholder {
        color: $placeholder-text;

        .theme-dark & {
            color: $dark-placeholder-text;
        }
    }
}

.tags-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    max-width: 25%;
}

.tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.625rem;
    background-color: rgba($system-blue, 0.1);
    color: $system-blue;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;

    .theme-dark & {
        background-color: rgba($system-blue, 0.2);
    }

    .remove-tag {
        cursor: pointer;
        font-size: 1rem;
        line-height: 0.75;
        margin-left: 0.25rem;

        &:hover {
            color: $system-red;
        }
    }
}

.add-tag {
    display: flex;
    align-items: center;

    .tag-input {
        border: none;
        background: none;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        width: 6rem;
        color: $primary-text;
        border-bottom: 1px solid $border-color;

        .theme-dark & {
            color: $dark-primary-text;
            border-bottom-color: $dark-border-color;
        }

        &:focus {
            outline: none;
            border-bottom-color: $system-blue;
        }

        &::placeholder {
            color: $placeholder-text;

            .theme-dark & {
                color: $dark-placeholder-text;
            }
        }
    }

    .tag-button {
        background: none;
        border: none;
        color: $system-blue;
        font-size: 1rem;
        cursor: pointer;
        padding: 0.25rem;

        &:hover {
            color: darken($system-blue, 10%);
        }

        &:active {
            transform: scale(0.95);
        }
    }
}

.cornell-notes-tabs {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid $separator-color;

    .theme-dark & {
        border-bottom-color: $dark-separator-color;
    }

    .tab-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: none;
        border: none;
        font-size: 0.9375rem;
        font-weight: 500;
        color: $tertiary-text;
        cursor: pointer;
        position: relative;
        transition: color 0.2s ease;

        .theme-dark & {
            color: $dark-tertiary-text;
        }

        svg {
            font-size: 1.125rem;
        }

        &::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: transparent;
            transition: background-color 0.2s ease;
        }

        &:hover {
            color: $system-blue;
        }

        &.active {
            color: $system-blue;
            font-weight: 600;

            &::after {
                background-color: $system-blue;
            }
        }
    }
}

.formatting-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: $secondary-background;
    border-radius: 0.5rem;
    border: 1px solid $border-color;

    .theme-dark & {
        background-color: $dark-secondary-background;
        border-color: $dark-border-color;
    }

    .toolbar-left {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .toggle-formatting {
        @include macOS-button;
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
        background-color: transparent;
        border: none;
        color: $system-blue;

        &:hover {
            background-color: rgba($system-blue, 0.1);

            .theme-dark & {
                background-color: rgba($system-blue, 0.2);
            }
        }
    }

    .preview-toggle,
    .rich-text-toggle {
        @include macOS-button;
        font-size: 0.8125rem;
        padding: 0.375rem 0.75rem;
        background-color: $window-background;
        border-color: $system-blue;
        color: $system-blue;

        .theme-dark & {
            background-color: $dark-window-background;
            border-color: $system-blue;
            color: $system-blue;
        }

        &.active {
            background-color: $system-blue;
            color: white;

            &:hover {
                background-color: darken($system-blue, 10%);
            }
        }

        &:hover {
            background-color: rgba($system-blue, 0.1);

            .theme-dark & {
                background-color: rgba($system-blue, 0.2);
            }
        }
    }

    .formatting-buttons {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .formatting-group {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem;
            background-color: rgba($border-color, 0.3);
            border-radius: 0.375rem;

            .theme-dark & {
                background-color: rgba($dark-border-color, 0.5);
            }
        }

        button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            border-radius: 0.375rem;
            border: none;
            background-color: transparent;
            color: $secondary-text;
            cursor: pointer;
            transition: all 0.2s ease;

            .theme-dark & {
                color: $dark-secondary-text;
            }

            .header-icon {
                font-weight: 600;
                font-size: 0.75rem;
            }

            &:hover {
                background-color: rgba($system-blue, 0.1);
                color: $system-blue;

                .theme-dark & {
                    background-color: rgba($system-blue, 0.2);
                }
            }

            &:active {
                background-color: rgba($system-blue, 0.2);
                transform: scale(0.95);

                .theme-dark & {
                    background-color: rgba($system-blue, 0.3);
                }
            }
        }
    }

    .save-status-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .save-status {
            font-size: 0.8125rem;
            color: $tertiary-text;

            .theme-dark & {
                color: $dark-tertiary-text;
            }
        }
    }

    .save-status-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        min-width: 150px;

        .save-status {
            color: $system-green;
            font-size: 0.875rem;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            background-color: rgba($system-green, 0.1);
            border-radius: 1rem;
            animation: fadeIn 0.3s ease;

            .theme-dark & {
                color: $system-green;
                background-color: rgba($system-green, 0.2);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }
    }
}

// Formatted text display styles
.formatted-text-display {
    flex: 1;
    min-height: 12rem;
    padding: 0.875rem;
    border: 1px solid $border-color;
    border-radius: 0.5rem;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
    font-size: 0.9375rem;
    line-height: 1.6;
    color: $primary-text;
    background-color: $window-background;
    overflow-y: auto;
    @include ios-scrollbar;

    * {
        text-align: left;
    }

    .theme-dark & {
        color: $dark-primary-text;
        background-color: $dark-window-background;
        border-color: $dark-border-color;
    }

    // Markdown formatting styles
    h1,
    h2,
    h3 {
        margin: 1rem 0 0.5rem 0;
        font-weight: 600;
        color: $system-blue;
        text-align: left;
        width: 100%;

        .theme-dark & {
            color: $system-blue;
        }
    }

    h1 {
        font-size: 1.5rem;
        border-bottom: 2px solid $system-blue;
        padding-bottom: 0.25rem;
    }

    h2 {
        font-size: 1.25rem;
        border-bottom: 1px solid rgba($system-blue, 0.3);
        padding-bottom: 0.25rem;
    }

    h3 {
        font-size: 1.125rem;
    }

    strong {
        font-weight: 600;
        color: $primary-text;

        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    em {
        font-style: italic;
        color: $secondary-text;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    u {
        text-decoration: underline;
        text-decoration-color: $system-blue;
    }

    del {
        text-decoration: line-through;
        color: $tertiary-text;

        .theme-dark & {
            color: $dark-tertiary-text;
        }
    }

    code {
        background-color: rgba($system-gray, 0.1);
        color: $system-blue;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        font-size: 0.875rem;

        .theme-dark & {
            background-color: rgba($dark-border-color, 0.5);
            color: $system-blue;
        }
    }

    pre {
        background-color: $secondary-background;
        border: 1px solid $border-color;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;
        @include ios-scrollbar;

        .theme-dark & {
            background-color: $dark-secondary-background;
            border-color: $dark-border-color;
        }

        code {
            background: none;
            padding: 0;
            border-radius: 0;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }

    blockquote {
        border-left: 4px solid $system-blue;
        padding-left: 1rem;
        margin: 1rem 0;
        font-style: italic;
        color: $secondary-text;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    ul,
    ol {
        margin: 0.5rem 0;
        padding-left: 1.5rem;

        li {
            margin: 0.25rem 0;
            line-height: 1.5;
        }
    }

    ul {
        list-style-type: disc;
    }

    ol {
        list-style-type: decimal;
    }

    a {
        color: $system-blue;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    br {
        line-height: 1.6;
    }

    // Special preview styles for different sections
    &.cues-preview {
        background-color: rgba($system-orange, 0.02);
        border-color: rgba($system-orange, 0.3);

        .theme-dark & {
            background-color: rgba($system-orange, 0.05);
        }
    }

    &.notes-preview {
        background-color: rgba($system-blue, 0.02);
        border-color: rgba($system-blue, 0.3);

        .theme-dark & {
            background-color: rgba($system-blue, 0.05);
        }
    }

    &.summary-preview {
        background-color: rgba($system-green, 0.02);
        border-color: rgba($system-green, 0.3);

        .theme-dark & {
            background-color: rgba($system-green, 0.05);
        }
    }
}

// Rich Text Editor styles
.rich-text-editor {
    flex: 1;
    min-height: 12rem;
    padding: 0.875rem;
    border: 1px solid $border-color;
    border-radius: 0.5rem;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
    font-size: 0.9375rem;
    line-height: 1.6;
    color: $primary-text;
    background-color: $window-background;
    overflow-y: auto;
    @include ios-scrollbar;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;


    * {
        text-align: left !important;
        width: 100%;
    }

    .theme-dark & {
        color: $dark-primary-text;
        background-color: $dark-window-background;
        border-color: $dark-border-color;
    }

    &:focus {
        outline: none;
        border-color: $system-blue;
        box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.1);

        .theme-dark & {
            box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.2);
        }
    }

    // Placeholder styling
    &:empty:before {
        content: attr(data-placeholder);
        color: $tertiary-text;
        font-style: italic;
        pointer-events: none;

        .theme-dark & {
            color: $dark-tertiary-text;
        }
    }

    // Rich text formatting styles
    strong {
        font-weight: 600;
        color: $primary-text;
        text-align: left;
        width: 100%;

        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    em {
        font-style: italic;
        color: $secondary-text;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    u {
        text-decoration: underline;
        text-decoration-color: $system-blue;
    }

    del {
        text-decoration: line-through;
        color: $tertiary-text;

        .theme-dark & {
            color: $dark-tertiary-text;
        }
    }

    code {
        background-color: rgba($system-gray, 0.1);
        color: $system-blue;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        font-size: 0.875rem;

        .theme-dark & {
            background-color: rgba($dark-border-color, 0.5);
            color: $system-blue;
        }
    }

    h1,
    h2,
    h3 {
        margin: 1rem 0 0.5rem 0;
        font-weight: 600;
        color: $system-blue;

        .theme-dark & {
            color: $system-blue;
        }
    }

    h1 {
        font-size: 1.5rem;
        border-bottom: 2px solid $system-blue;
        padding-bottom: 0.25rem;
    }

    h2 {
        font-size: 1.25rem;
        border-bottom: 1px solid rgba($system-blue, 0.3);
        padding-bottom: 0.25rem;
    }

    h3 {
        font-size: 1.125rem;
    }

    blockquote {
        border-left: 4px solid $system-blue;
        padding-left: 1rem;
        margin: 1rem 0;
        font-style: italic;
        color: $secondary-text;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    // Special editor variants
    &.cues-editor {
        background-color: rgba($system-orange, 0.02);
        border-color: rgba($system-orange, 0.3);

        .theme-dark & {
            background-color: rgba($system-orange, 0.05);
        }

        &:focus {
            border-color: $system-orange;
            box-shadow: 0 0 0 0.25rem rgba($system-orange, 0.1);

            .theme-dark & {
                box-shadow: 0 0 0 0.25rem rgba($system-orange, 0.2);
            }
        }
    }

    &.notes-editor {
        background-color: rgba($system-blue, 0.02);
        border-color: rgba($system-blue, 0.3);

        .theme-dark & {
            background-color: rgba($system-blue, 0.05);
        }
    }

    &.summary-editor {
        background-color: rgba($system-green, 0.02);
        border-color: rgba($system-green, 0.3);

        .theme-dark & {
            background-color: rgba($system-green, 0.05);
        }

        &:focus {
            border-color: $system-green;
            box-shadow: 0 0 0 0.25rem rgba($system-green, 0.1);

            .theme-dark & {
                box-shadow: 0 0 0 0.25rem rgba($system-green, 0.2);
            }
        }
    }
}

.cornell-notes-grid {
    //overflow-y: auto;
    display: grid;
    grid-template-columns: 35% 65%;
    grid-template-rows: auto 1fr;
    grid-template-areas:
        "cues notes"
        "summary summary";
    gap: .5rem;
    margin-bottom: .5rem;

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        grid-template-areas:
            "cues"
            "notes"
            "summary";
    }
}

.section-header {
    margin-bottom: 0.5rem;

    h3 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        margin: 0 0 0.375rem 0;
        color: $secondary-text;

        .theme-dark & {
            color: $dark-secondary-text;
        }

        .section-icon {
            color: $system-blue;
        }
    }

    .instructions {
        font-size: 0.8125rem;
        color: $tertiary-text;

        .theme-dark & {
            color: $dark-tertiary-text;
        }

        p {
            margin: 0;
        }
    }
}

.cues-column {
    grid-area: cues;
    display: flex;
    flex-direction: column;
}

.notes-column {
    grid-area: notes;
    display: flex;
    flex-direction: column;
}

.summary-section {
    grid-area: summary;
    display: flex;
    flex-direction: column;
}

.cues-textarea,
.notes-textarea,
.summary-textarea {
    flex: 1;
    min-height: 12rem;
    padding: 0.875rem;
    border: 1px solid $border-color;
    border-radius: 0.5rem;
    resize: vertical;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
    font-size: 0.9375rem;
    line-height: 1.5;
    color: $primary-text;
    background-color: $window-background;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    @include ios-scrollbar;

    .theme-dark & {
        color: $dark-primary-text;
        background-color: $dark-window-background;
        border-color: $dark-border-color;
    }

    &:focus {
        outline: none;
        border-color: $system-blue;
        box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.1);

        .theme-dark & {
            box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.2);
        }
    }

    &::placeholder {
        color: $placeholder-text;

        .theme-dark & {
            color: $dark-placeholder-text;
        }
    }
}

.key-points-section {
    margin-bottom: 1.5rem;
    padding: .5rem;
    background-color: $secondary-background;
    border-radius: 0.75rem;

    .theme-dark & {
        background-color: $dark-secondary-background;
    }
}

.key-points-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .key-point-input {
        flex: 1;
        padding: 0.625rem 0.875rem;
        border: 1px solid $border-color;
        border-radius: 0.5rem;
        font-size: 0.9375rem;
        color: $primary-text;
        background-color: $window-background;

        .theme-dark & {
            color: $dark-primary-text;
            background-color: $dark-window-background;
            border-color: $dark-border-color;
        }

        &:focus {
            outline: none;
            border-color: $system-blue;
            box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.1);

            .theme-dark & {
                box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.2);
            }
        }

        &::placeholder {
            color: $placeholder-text;

            .theme-dark & {
                color: $dark-placeholder-text;
            }
        }
    }

    .add-key-point-button {
        @include macOS-button;
        padding: 0.625rem 1rem;
        background-color: $system-blue;
        color: white;
        border: none;

        &:hover {
            background-color: darken($system-blue, 5%);
        }

        .theme-dark & {
            background-color: $system-blue;
            color: white;

            &:hover {
                background-color: darken($system-blue, 5%);
            }
        }
    }
}

.key-points-list {
    list-style: none;
    padding: 0;
    margin: 0;

    .key-point-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        background-color: $window-background;
        border-radius: 0.5rem;
        border-left: 3px solid $system-green;
        @include macOS-shadow(0.05);

        .theme-dark & {
            background-color: $dark-window-background;
        }

        span {
            flex: 1;
            font-size: 0.9375rem;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }
        }

        .remove-key-point {
            background: none;
            border: none;
            color: $tertiary-text;
            cursor: pointer;
            padding: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;

            .theme-dark & {
                color: $dark-tertiary-text;
            }

            &:hover {
                background-color: rgba($system-red, 0.1);
                color: $system-red;

                .theme-dark & {
                    background-color: rgba($system-red, 0.2);
                }
            }
        }
    }
}

.study-tips {
    padding: 1.5rem;

    h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 1.5rem 0;
        color: $primary-text;
        text-align: center;

        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    .tips-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.25rem;
    }

    .tip-card {
        background-color: $window-background;
        border-radius: 0.75rem;
        padding: 1.25rem;
        @include macOS-shadow(0.08);
        border-top: 3px solid $system-blue;

        .theme-dark & {
            background-color: $dark-window-background;
        }

        &:nth-child(2) {
            border-top-color: $system-green;
        }

        &:nth-child(3) {
            border-top-color: $system-orange;
        }

        &:nth-child(4) {
            border-top-color: $system-gold;
        }

        h4 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            color: $secondary-text;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        ul {
            margin: 0;
            padding: 0 0 0 1.25rem;

            li {
                margin-bottom: 0.5rem;
                color: $primary-text;
                font-size: 0.9375rem;
                line-height: 1.5;

                .theme-dark & {
                    color: $dark-primary-text;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}

.export-options {
    padding: 1.5rem;
    text-align: center;

    h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 0.75rem 0;
        color: $primary-text;

        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    p {
        margin: 0 0 1.5rem 0;
        color: $secondary-text;
        font-size: 0.9375rem;

        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    .export-buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;

        .export-button {
            @include macOS-button;
            padding: 0.75rem 1.25rem;
            min-width: 200px;

            &:nth-child(1) {
                background-color: $system-blue;
                color: white;
                border: none;

                &:hover {
                    background-color: darken($system-blue, 5%);
                }
            }

            &:nth-child(2) {
                background-color: $secondary-background;
                color: $secondary-text;

                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $dark-secondary-text;
                }

                &:hover {
                    background-color: darken($secondary-background, 5%);

                    .theme-dark & {
                        background-color: lighten($dark-secondary-background, 5%);
                    }
                }
            }

            &:nth-child(3) {
                background-color: $secondary-background;
                color: $secondary-text;

                .theme-dark & {
                    background-color: $dark-secondary-background;
                    color: $dark-secondary-text;
                }

                &:hover {
                    background-color: darken($secondary-background, 5%);

                    .theme-dark & {
                        background-color: lighten($dark-secondary-background, 5%);
                    }
                }
            }
        }
    }
}

// New styles for enhanced Cornell Notes features
.ai-buttons-group {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.ai-generate-button,
.ai-summarize-button {
    @include macOS-button;
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
    background-color: rgba($system-blue, 0.1);
    color: $system-blue;
    border: none;
    min-width: 100px;
    flex: 1;

    .theme-dark & {
        background-color: rgba($system-blue, 0.2);
    }

    &:hover:not(:disabled) {
        background-color: rgba($system-blue, 0.2);

        .theme-dark & {
            background-color: rgba($system-blue, 0.3);
        }
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    svg {
        margin-right: 0.25rem;
        font-size: 1rem;
    }
}

.ai-generate-from-file {
    background-color: rgba($system-orange, 0.1);
    color: $system-orange;

    .theme-dark & {
        background-color: rgba($system-orange, 0.2);
    }

    &:hover:not(:disabled) {
        background-color: rgba($system-orange, 0.2);

        .theme-dark & {
            background-color: rgba($system-orange, 0.3);
        }
    }
}

.ai-generate-from-notes {
    background-color: rgba($system-blue, 0.1);
    color: $system-blue;

    .theme-dark & {
        background-color: rgba($system-blue, 0.2);
    }

    &:hover:not(:disabled) {
        background-color: rgba($system-blue, 0.2);

        .theme-dark & {
            background-color: rgba($system-blue, 0.3);
        }
    }
}

.ai-summarize-button {
    background-color: rgba($system-green, 0.1);
    color: $system-green;

    .theme-dark & {
        background-color: rgba($system-green, 0.2);
    }

    &:hover:not(:disabled) {
        background-color: rgba($system-green, 0.2);

        .theme-dark & {
            background-color: rgba($system-green, 0.3);
        }
    }
}

.notes-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.image-upload-button {
    @include macOS-button;
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
    background-color: rgba($system-orange, 0.1);
    color: $system-orange;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;

    .theme-dark & {
        background-color: rgba($system-orange, 0.2);
    }

    &:hover {
        background-color: rgba($system-orange, 0.2);

        .theme-dark & {
            background-color: rgba($system-orange, 0.3);
        }
    }

    svg {
        font-size: 1rem;
    }
}

.image-preview-container {
    margin: 0.75rem 0;
    padding: 0.75rem;
    background-color: $secondary-background;
    border-radius: 0.5rem;
    border: 1px solid $border-color;

    .theme-dark & {
        background-color: $dark-secondary-background;
        border-color: $dark-border-color;
    }
}

.image-preview {
    position: relative;
    display: inline-block;
    max-width: 100%;

    img {
        max-width: 100%;
        max-height: 200px;
        border-radius: 0.375rem;
        @include macOS-shadow(0.05);
    }

    .remove-image-button {
        position: absolute;
        top: -0.5rem;
        right: -0.5rem;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        background-color: $system-red;
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        @include macOS-shadow(0.1);
        transition: all 0.2s ease;

        &:hover {
            background-color: darken($system-red, 10%);
            transform: scale(1.1);
        }

        svg {
            font-size: 1rem;
        }
    }
}

.key-points-textarea {
    width: 100%;
    min-height: 4rem;
    padding: 0.75rem;
    border: 1px solid $border-color;
    border-radius: 0.5rem;
    resize: vertical;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
    font-size: 0.9375rem;
    line-height: 1.5;
    color: $primary-text;
    background-color: $window-background;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    @include ios-scrollbar;

    .theme-dark & {
        color: $dark-primary-text;
        background-color: $dark-window-background;
        border-color: $dark-border-color;
    }

    &:focus {
        outline: none;
        border-color: $system-blue;
        box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.1);

        .theme-dark & {
            box-shadow: 0 0 0 0.25rem rgba($system-blue, 0.2);
        }
    }

    &::placeholder {
        color: $placeholder-text;

        .theme-dark & {
            color: $dark-placeholder-text;
        }
    }
}

.key-points-preview {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: rgba($system-green, 0.05);
    border-radius: 0.5rem;
    border-left: 3px solid $system-green;

    .theme-dark & {
        background-color: rgba($system-green, 0.1);
    }

    h4 {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: $system-green;
    }

    .key-points-list {
        margin: 0;
        padding: 0;
        list-style: none;

        .key-point-item {
            display: flex;
            align-items: center;
            padding: 0.375rem 0;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }

            &:before {
                content: '•';
                color: $system-green;
                margin-right: 0.5rem;
                font-weight: bold;
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.cornell-notes-footer {
    margin-top: auto;
    padding-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .chapter-info {
        display: flex;
        align-items: center;

        .chapter-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background-color: rgba($system-blue, 0.1);
            color: $system-blue;
            border-radius: 0.5rem;
            font-size: 0.9375rem;
            font-weight: 500;

            .theme-dark & {
                background-color: rgba($system-blue, 0.2);
            }

            .icon {
                font-size: 1.25rem;
            }
        }
    }

    .notes-actions {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        position: relative;

        .view-notes-button {
            @include macOS-button;
            padding: 0.625rem 1rem;
            background-color: rgba($system-blue, 0.1);
            color: $system-blue;
            border: none;

            .theme-dark & {
                background-color: rgba($system-blue, 0.2);
            }

            &:hover {
                background-color: rgba($system-blue, 0.2);

                .theme-dark & {
                    background-color: rgba($system-blue, 0.3);
                }
            }
        }

        .pdf-view-button {
            @include macOS-button;
            padding: 0.625rem 1rem;
            background-color: rgba($system-orange, 0.1);
            color: $system-orange;
            border: none;

            .theme-dark & {
                background-color: rgba($system-orange, 0.2);
            }

            &:hover {
                background-color: rgba($system-orange, 0.2);

                .theme-dark & {
                    background-color: rgba($system-orange, 0.3);
                }
            }
        }

        .save-status-popup {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.9375rem;
            font-weight: 500;
            animation: fadeInOut 3s ease;

            &.success {
                background-color: rgba($system-green, 0.1);
                color: $system-green;

                .theme-dark & {
                    background-color: rgba($system-green, 0.2);
                }
            }

            &.error {
                background-color: rgba($system-red, 0.1);
                color: $system-red;

                .theme-dark & {
                    background-color: rgba($system-red, 0.2);
                }
            }
        }

        @keyframes fadeInOut {
            0% {
                opacity: 0;
            }

            15% {
                opacity: 1;
            }

            85% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }
    }

    .save-button {
        @include macOS-button;
        padding: 0.75rem 1.5rem;
        background-color: $system-green;
        color: white;
        border: none;
        font-size: 1rem;
        min-width: 150px;

        &:hover:not(:disabled) {
            background-color: darken($system-green, 5%);
        }

        &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .theme-dark & {
            background-color: $system-green;
            color: white;

            &:hover:not(:disabled) {
                background-color: darken($system-green, 5%);
            }
        }
    }
}

// Notes Modal Styles
.cornell-notes-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
        width: 90%;
        max-width: 900px;
        max-height: 90vh;
        background-color: $window-background;
        border-radius: 0.75rem;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        @include macOS-shadow(0.2);

        .theme-dark & {
            background-color: $dark-window-background;
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid $separator-color;

        .theme-dark & {
            border-bottom-color: $dark-separator-color;
        }

        h3 {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }

            .modal-icon {
                color: $system-blue;
                font-size: 1.5rem;
            }
        }

        .close-modal-button {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background-color: transparent;
            color: $tertiary-text;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;

            .theme-dark & {
                color: $dark-tertiary-text;
            }

            &:hover {
                background-color: rgba($system-red, 0.1);
                color: $system-red;

                .theme-dark & {
                    background-color: rgba($system-red, 0.2);
                }
            }
        }
    }

    .modal-body {
        padding: 1.5rem;
        overflow-y: auto;
        flex: 1;
        @include ios-scrollbar;
    }

    .notes-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h4 {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 500;
            color: $secondary-text;

            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .new-note-button {
            @include macOS-button;
            padding: 0.5rem 1rem;
            background-color: $system-green;
            color: white;
            border: none;

            &:hover {
                background-color: darken($system-green, 5%);
            }
        }
    }

    .notes-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
    }

    .note-item {
        background-color: $secondary-background;
        border-radius: 0.75rem;
        padding: 1.25rem;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;
        @include macOS-shadow(0.05);

        .theme-dark & {
            background-color: $dark-secondary-background;
        }

        &:hover {
            transform: translateY(-2px);
            @include macOS-shadow(0.1);
        }

        &.active {
            border-color: $system-blue;

            .theme-dark & {
                border-color: $system-blue;
            }
        }

        .note-item-header {
            margin-bottom: 0.75rem;

            h4 {
                margin: 0 0 0.5rem 0;
                font-size: 1.125rem;
                font-weight: 600;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .note-date {
                font-size: 0.8125rem;
                color: $tertiary-text;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }
        }

        .note-item-preview {
            margin-bottom: 1rem;

            p {
                margin: 0;
                font-size: 0.9375rem;
                color: $secondary-text;
                line-height: 1.5;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;

                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }
        }

        .note-item-actions {
            display: flex;
            gap: 0.5rem;

            button {
                flex: 1;
                @include macOS-button;
                padding: 0.5rem;
                font-size: 0.8125rem;

                &.view-note-button {
                    background-color: rgba($system-orange, 0.1);
                    color: $system-orange;
                    border: none;

                    .theme-dark & {
                        background-color: rgba($system-orange, 0.2);
                    }

                    &:hover {
                        background-color: rgba($system-orange, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-orange, 0.3);
                        }
                    }
                }

                &.edit-note-button {
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;
                    border: none;

                    .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                    }

                    &:hover {
                        background-color: rgba($system-blue, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-blue, 0.3);
                        }
                    }
                }

                &.delete-note-button {
                    background-color: rgba($system-red, 0.1);
                    color: $system-red;
                    border: none;

                    .theme-dark & {
                        background-color: rgba($system-red, 0.2);
                    }

                    &:hover {
                        background-color: rgba($system-red, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-red, 0.3);
                        }
                    }
                }
            }
        }
    }

    .no-notes-message {
        text-align: center;
        padding: 3rem 1rem;
        background-color: $secondary-background;
        border-radius: 0.75rem;

        .theme-dark & {
            background-color: $dark-secondary-background;
        }

        p {
            margin: 0;
            font-size: 1.125rem;
            color: $tertiary-text;

            .theme-dark & {
                color: $dark-tertiary-text;
            }
        }
    }
}

// PDF Viewer Styles
.cornell-notes-pdf-viewer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .pdf-viewer-content {
        width: 95%;
        max-width: 1100px;
        height: 95vh;
        background-color: white;
        border-radius: 0.75rem;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        @include macOS-shadow(0.3);

        .theme-dark & {
            background-color: $dark-window-background;
        }
    }

    .pdf-viewer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        background-color: $secondary-background;
        border-bottom: 1px solid $separator-color;

        .theme-dark & {
            background-color: $dark-secondary-background;
            border-bottom-color: $dark-separator-color;
        }

        h3 {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: $primary-text;

            .theme-dark & {
                color: $dark-primary-text;
            }

            .pdf-icon {
                color: $system-orange;
                font-size: 1.5rem;
            }
        }

        .pdf-viewer-actions {
            display: flex;
            gap: 0.75rem;

            button {
                @include macOS-button;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;

                &.edit-in-pdf-button {
                    background-color: rgba($system-blue, 0.1);
                    color: $system-blue;
                    border: none;

                    .theme-dark & {
                        background-color: rgba($system-blue, 0.2);
                    }

                    &:hover {
                        background-color: rgba($system-blue, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-blue, 0.3);
                        }
                    }
                }

                &.save-as-new-button {
                    background-color: rgba($system-green, 0.1);
                    color: $system-green;
                    border: none;

                    .theme-dark & {
                        background-color: rgba($system-green, 0.2);
                    }

                    &:hover {
                        background-color: rgba($system-green, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-green, 0.3);
                        }
                    }
                }

                &.print-pdf-button {
                    background-color: rgba($system-gray, 0.1);
                    color: $system-dark-gray;
                    border: none;

                    .theme-dark & {
                        background-color: rgba($system-gray, 0.2);
                        color: $dark-tertiary-text;
                    }

                    &:hover {
                        background-color: rgba($system-gray, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-gray, 0.3);
                        }
                    }
                }

                &.delete-pdf-button {
                    background-color: rgba($system-red, 0.1);
                    color: $system-red;
                    border: none;

                    .theme-dark & {
                        background-color: rgba($system-red, 0.2);
                    }

                    &:hover {
                        background-color: rgba($system-red, 0.2);

                        .theme-dark & {
                            background-color: rgba($system-red, 0.3);
                        }
                    }
                }

                &.close-pdf-button {
                    width: 2rem;
                    height: 2rem;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: none;
                    background-color: transparent;
                    color: $tertiary-text;
                    border-radius: 50%;

                    .theme-dark & {
                        color: $dark-tertiary-text;
                    }

                    &:hover {
                        background-color: rgba($system-red, 0.1);
                        color: $system-red;

                        .theme-dark & {
                            background-color: rgba($system-red, 0.2);
                        }
                    }
                }
            }
        }
    }

    .pdf-viewer-body {
        flex: 1;
        overflow-y: auto;
        padding: 2rem;
        background-color: #f5f5f5;

        .theme-dark & {
            background-color: #1a1a1a;
        }

        @include ios-scrollbar;
    }

    .cornell-pdf-layout {
        max-width: 1000px;
        margin: 0 auto;
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .theme-dark & {
            background-color: $dark-window-background;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .pdf-metadata {
            padding: 1.5rem;
            border-bottom: 2px solid $system-blue;
            background-color: rgba($system-blue, 0.05);

            .theme-dark & {
                background-color: rgba($system-blue, 0.1);
            }

            .pdf-subject {
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: $primary-text;

                .theme-dark & {
                    color: $dark-primary-text;
                }
            }

            .pdf-chapter {
                font-size: 1rem;
                color: $secondary-text;
                margin-bottom: 1rem;

                .theme-dark & {
                    color: $dark-secondary-text;
                }
            }

            .pdf-date {
                font-size: 0.875rem;
                color: $tertiary-text;

                .theme-dark & {
                    color: $dark-tertiary-text;
                }
            }
        }

        .pdf-main-content {
            display: grid;
            grid-template-columns: 35% 65%;
            grid-template-rows: auto auto auto;
            grid-template-areas:
                "cues notes"
                "summary summary"
                "keypoints keypoints";

            h4 {
                margin: 0 0 0.75rem 0;
                font-size: 1.125rem;
                font-weight: 600;
                color: $system-blue;

                .theme-dark & {
                    color: $system-blue;
                }
            }

            .pdf-cues-column {
                grid-area: cues;
                padding: 1.5rem;
                border-right: 1px solid $separator-color;
                min-height: 300px;

                .theme-dark & {
                    border-right-color: $dark-separator-color;
                }

                .pdf-cues-content {
                    white-space: pre-wrap;
                    line-height: 1.6;
                    color: $primary-text;

                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }
            }

            .pdf-notes-column {
                grid-area: notes;
                padding: 1.5rem;
                min-height: 300px;

                .pdf-notes-content {
                    white-space: pre-wrap;
                    line-height: 1.6;
                    color: $primary-text;

                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }
            }

            .pdf-summary-section {
                grid-area: summary;
                padding: 1.5rem;
                border-top: 1px solid $separator-color;

                .theme-dark & {
                    border-top-color: $dark-separator-color;
                }

                .pdf-summary-content {
                    white-space: pre-wrap;
                    line-height: 1.6;
                    color: $primary-text;

                    .theme-dark & {
                        color: $dark-primary-text;
                    }
                }
            }

            .pdf-key-points {
                grid-area: keypoints;
                padding: 1.5rem;
                border-top: 1px solid $separator-color;
                background-color: rgba($system-green, 0.05);

                .theme-dark & {
                    border-top-color: $dark-separator-color;
                    background-color: rgba($system-green, 0.1);
                }

                ul {
                    margin: 0;
                    padding-left: 1.5rem;

                    li {
                        margin-bottom: 0.5rem;
                        color: $primary-text;

                        .theme-dark & {
                            color: $dark-primary-text;
                        }

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }
}