@use "variables" as *;
@use "sass:color";

.pomodoro-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 1rem;

    .pomodoro-popup-content {
        background: $dark-background;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        max-width: 90vw;
        max-height: 90vh;
        width: 100%;
        max-width: 800px;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        // Light theme
        .theme-light & {
            background: $light-background;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pomodoro-popup-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid $dark-accent-color-2;
            background: color.adjust($dark-background, $lightness: 2%);

            // Light theme
            .theme-light & {
                border-bottom: 1px solid $light-accent-color-2;
                background: color.adjust($light-background, $lightness: -2%);
            }

            h2 {
                margin: 0;
                font-size: 1.5rem;
                font-weight: 600;
                color: $dark-primary-text;

                // Light theme
                .theme-light & {
                    color: $light-primary-text;
                }
            }

            .close-button {
                background: none;
                border: none;
                padding: 0.5rem;
                border-radius: 8px;
                cursor: pointer;
                color: $dark-secondary-text;
                transition: all $transition-speed;
                display: flex;
                align-items: center;
                justify-content: center;

                // Light theme
                .theme-light & {
                    color: $light-secondary-text;
                }

                svg {
                    width: 1.25rem;
                    height: 1.25rem;
                }

                &:hover {
                    background: $dark-accent-color-2;
                    color: $dark-primary-text;

                    // Light theme
                    .theme-light & {
                        background: $light-accent-color-2;
                        color: $light-primary-text;
                    }
                }

                &:focus {
                    outline: 2px solid $color-primary;
                    outline-offset: 2px;
                }
            }
        }

        .pomodoro-popup-body {
            flex: 1;
            overflow: auto;
            padding: 0;

            // Remove any extra padding from the PomodoroTimer component when in popup
            .advanced-pomodoro-timer {
                border-radius: 0;
                box-shadow: none;
                margin: 0;
                height: 100%;
                min-height: auto;
            }
        }
    }

    // Responsive design
    @media (max-width: 768px) {
        padding: 0.5rem;

        .pomodoro-popup-content {
            max-width: 100%;
            max-height: 100%;
            border-radius: 12px;

            .pomodoro-popup-header {
                padding: 1rem 1.5rem;

                h2 {
                    font-size: 1.25rem;
                }
            }
        }
    }

    @media (max-width: 480px) {
        padding: 0;

        .pomodoro-popup-content {
            border-radius: 0;
            max-width: 100%;
            max-height: 100%;
            height: 100vh;
            width: 100vw;

            .pomodoro-popup-header {
                padding: 1rem;
            }
        }
    }
}
