import React from 'react';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import '../../styles/SettingsPanels.scss';

const KnowledgeDepthPanel = ({ settings }) => {
  const { knowledgeDepth, setKnowledgeDepth, isDarkMode } = settings;
  
  const depthLevels = [
    { value: 1, label: 'Novice', description: 'Basic explanations with simplified concepts' },
    { value: 2, label: 'Beginner', description: 'Foundational knowledge with some details' },
    { value: 3, label: 'Intermediate', description: 'Balanced depth suitable for most students' },
    { value: 4, label: 'Advanced', description: 'Detailed explanations with technical terms' },
    { value: 5, label: 'Graduate', description: 'Expert-level depth with academic precision' }
  ];
  
  return (
    <div className={`detail-panel knowledge-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Knowledge Depth</h2>
        <p className="panel-description">
          Set how detailed and complex you want explanations to be.
        </p>
      </div>
      
      <div className="settings-group">
        <div className="knowledge-dial-container">
          <div className="knowledge-dial">
            <div className="dial-markers">
              {depthLevels.map((level, index) => (
                <div 
                  key={level.value}
                  className={`dial-marker ${knowledgeDepth >= level.value ? 'active' : ''}`}
                  style={{ 
                    transform: `rotate(${(index * 45) - 90}deg)`,
                  }}
                >
                  <div className="marker-dot"></div>
                </div>
              ))}
            </div>
            
            <div 
              className="dial-indicator" 
              style={{ 
                transform: `rotate(${((knowledgeDepth - 1) * 45) - 90}deg)`
              }}
            >
              <div className="indicator-arrow">
                <AutoAwesomeIcon />
              </div>
            </div>
            
            <div className="dial-center">
              <span className="depth-level">{depthLevels[knowledgeDepth - 1].label}</span>
            </div>
          </div>
          
          <div className="depth-controls">
            {depthLevels.map(level => (
              <button
                key={level.value}
                className={`depth-button ${knowledgeDepth === level.value ? 'active' : ''}`}
                onClick={() => setKnowledgeDepth(level.value)}
              >
                {level.label}
              </button>
            ))}
          </div>
        </div>
        
        <div className="depth-description">
          <h4>Current Setting: {depthLevels[knowledgeDepth - 1].label}</h4>
          <p>{depthLevels[knowledgeDepth - 1].description}</p>
        </div>
      </div>
      
      <div className="info-box">
        <p>
          Knowledge depth affects how detailed the AI's explanations will be. 
          Higher levels include more technical terms and complex concepts.
        </p>
      </div>
    </div>
  );
};

export default KnowledgeDepthPanel;
