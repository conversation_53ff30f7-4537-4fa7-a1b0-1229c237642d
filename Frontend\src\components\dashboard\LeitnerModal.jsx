
import { createPortal } from 'react-dom';
import { useTheme } from '../../contexts/ThemeContext.jsx';
import '../../styles/leitner.scss';

// Material UI Icons
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import FlipIcon from '@mui/icons-material/Flip';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import RefreshIcon from '@mui/icons-material/Refresh';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';

const LeitnerModal = ({
  isOpen,
  onClose,
  loading,
  error,
  dueTodayCards,
  currentCardIndex,
  isFlipped,
  answered,
  sessionStats,
  onFlip,
  onAnswer,
  onNext,
  onRetry
}) => {
  const { darkMode } = useTheme();

  // Get box icon
  const getBoxIcon = (boxNum) => {
    switch (boxNum) {
      case 1: return <LooksOneIcon />;
      case 2: return <LooksTwoIcon />;
      case 3: return <Looks3Icon />;
      default: return <LooksOneIcon />;
    }
  };

  // Current card in study session
  const currentCard = dueTodayCards[currentCardIndex] || {};

  console.log('🎯 [LEITNER MODAL] Render called with isOpen:', isOpen);
  console.log('🎯 [LEITNER MODAL] dueTodayCards:', dueTodayCards);
  console.log('🎯 [LEITNER MODAL] loading:', loading);
  console.log('🎯 [LEITNER MODAL] error:', error);

  if (!isOpen) {
    console.log('🎯 [LEITNER MODAL] Modal is closed, returning null');
    return null;
  }

  console.log('🎯 [LEITNER MODAL] Modal should be visible!');

  // Check if modal-root exists
  const modalRoot = document.getElementById('modal-root');
  console.log('🎯 [LEITNER MODAL] modal-root element:', modalRoot);

  // Add a test to see if the modal is actually being rendered
  setTimeout(() => {
    const modalOverlay = document.querySelector('.modal-overlay');
    console.log('🎯 [LEITNER MODAL] Modal overlay in DOM:', modalOverlay);
    if (modalOverlay) {
      const styles = window.getComputedStyle(modalOverlay);
      console.log('🎯 [LEITNER MODAL] Modal overlay z-index:', styles.zIndex);
      console.log('🎯 [LEITNER MODAL] Modal overlay display:', styles.display);
      console.log('🎯 [LEITNER MODAL] Modal overlay position:', styles.position);
      console.log('🎯 [LEITNER MODAL] Modal overlay visibility:', styles.visibility);
      console.log('🎯 [LEITNER MODAL] Modal overlay opacity:', styles.opacity);

      // Check for elements with higher z-index
      const allElements = document.querySelectorAll('*');
      const highZIndexElements = [];
      allElements.forEach(el => {
        const zIndex = window.getComputedStyle(el).zIndex;
        if (zIndex !== 'auto' && parseInt(zIndex) > 2147483640) {
          highZIndexElements.push({ element: el, zIndex: zIndex });
        }
      });
      console.log('🎯 [LEITNER MODAL] Elements with higher z-index:', highZIndexElements);
    }
  }, 100);

  // Create portal to render modal at the root level
  return createPortal(
    <div
      className={`modal-overlay ${darkMode ? 'theme-dark' : ''}`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 0, 0, 0.8)',
        backdropFilter: 'blur(5px)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 2147483647
      }}
    >
      <div
        className={`modal-window ${darkMode ? 'theme-dark' : ''}`}
        role="dialog"
        style={{
          width: '90%',
          maxWidth: '800px',
          maxHeight: '90vh',
          backgroundColor: '#00FF00',
          borderRadius: '12px',
          boxShadow: '0 12px 30px rgba(0, 0, 0, 0.3)',
          overflow: 'hidden',
          zIndex: 2147483647
        }}
      >
        <div
          className="modal-header"
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '1rem 1.5rem',
            borderBottom: `1px solid ${darkMode ? '#38383A' : '#E5E5EA'}`,
            backgroundColor: darkMode ? '#2C2C2E' : '#F2F2F7'
          }}
        >
          <div className="traffic-lights" style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              className="traffic-light red"
              onClick={onClose}
              aria-label="Close"
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                backgroundColor: '#FF5F57',
                cursor: 'pointer'
              }}
            ></button>
            <button
              className="traffic-light yellow"
              disabled
              aria-label="Minimize"
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                backgroundColor: '#FFBD2E',
                opacity: 0.5
              }}
            ></button>
            <button
              className="traffic-light green"
              disabled
              aria-label="Maximize"
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                backgroundColor: '#28CA42',
                opacity: 0.5
              }}
            ></button>
          </div>
          <h2
            className="modal-title"
            style={{
              margin: 0,
              fontSize: '1.1rem',
              fontWeight: 600,
              color: darkMode ? '#FFFFFF' : '#000000'
            }}
          >
            Review Cards Due Today
          </h2>
        </div>

        <div
          className="modal-content"
          style={{
            padding: '1.5rem',
            maxHeight: 'calc(90vh - 80px)',
            overflowY: 'auto'
          }}
        >
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading flashcards...</p>
            </div>
          ) : error ? (
            <div className="error-message">
              <p>{error}</p>
              <button className="retry-button" onClick={onRetry}>
                <RefreshIcon /> Retry
              </button>
            </div>
          ) : dueTodayCards.length > 0 ? (
            <>
              <div className="session-progress">
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{
                      width: `${((currentCardIndex) / dueTodayCards.length) * 100}%`,
                    }}
                  ></div>
                </div>
                <div className="progress-text">
                  {currentCardIndex + 1} / {dueTodayCards.length}
                </div>
              </div>

              <div className="card-container">
                <div className={`study-card ${isFlipped ? 'flipped' : ''}`} onClick={() => !answered && onFlip()}>
                  <div className="card-face card-front">
                    <div className="card-content">
                      <div className="card-box-indicator">
                        Box {currentCard.box_level || 1} {getBoxIcon(currentCard.box_level || 1)}
                      </div>
                      <div className="card-text">
                        {currentCard.question}
                      </div>
                      {!isFlipped && !answered && (
                        <div className="card-hint">
                          <FlipIcon className="flip-icon" /> Tap to flip
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="card-face card-back">
                    <div className="card-content">
                      <div className="card-box-indicator">
                        Box {currentCard.box_level || 1} {getBoxIcon(currentCard.box_level || 1)}
                      </div>
                      <div className="card-text">
                        {currentCard.answer}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {isFlipped && !answered && (
                <div className="answer-controls">
                  <button
                    className="answer-button incorrect"
                    onClick={() => onAnswer(false)}
                  >
                    <ThumbDownIcon /> Incorrect
                  </button>
                  <button
                    className="answer-button correct"
                    onClick={() => onAnswer(true)}
                  >
                    <ThumbUpIcon /> Correct
                  </button>
                </div>
              )}

              {answered && (
                <div className="navigation-controls">
                  <button
                    className="next-button"
                    onClick={onNext}
                  >
                    {currentCardIndex < dueTodayCards.length - 1 ? (
                      <>Next Card <NavigateNextIcon /></>
                    ) : (
                      <>Finish <CheckCircleIcon /></>
                    )}
                  </button>
                </div>
              )}

              <div className="session-stats">
                <div className="stat-item">
                  <span className="stat-label">Correct:</span>
                  <span className="stat-value correct">{sessionStats.correct}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Incorrect:</span>
                  <span className="stat-value incorrect">{sessionStats.incorrect}</span>
                </div>
              </div>
            </>
          ) : (
            <div className="empty-session">
              <h3>No cards due for review today!</h3>
              <p>Great job keeping up with your studies.</p>
              <button className="create-flashcards-button" onClick={onClose}>
                Close
              </button>
            </div>
          )}
        </div>
      </div>
    </div>,
    document.body
  );
};

export default LeitnerModal;
