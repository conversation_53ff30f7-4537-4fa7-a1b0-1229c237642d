import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './Quizzes.scss';

// Material Design Icons
import CloseIcon from '@mui/icons-material/Close';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import DownloadIcon from '@mui/icons-material/Download';
import UploadIcon from '@mui/icons-material/Upload';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import TimerIcon from '@mui/icons-material/Timer';
import QuizIcon from '@mui/icons-material/Quiz';
import SchoolIcon from '@mui/icons-material/School';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import StarIcon from '@mui/icons-material/Star';
import RestartAltIcon from '@mui/icons-material/RestartAlt';

const Quizzes = ({ tool, subject, subjectId, chapterId, onClose }) => {
    // Debug props
    console.log('Quizzes component props:', { tool, subject, subjectId, chapterId });

    // Theme detection
    const [isDarkMode, setIsDarkMode] = useState(false);

    // Quiz state
    const [quizzes, setQuizzes] = useState([]);
    const [quizAttempts, setQuizAttempts] = useState([]); // Track quiz attempts
    const [newQuiz, setNewQuiz] = useState({
        title: '',
        description: '',
        difficulty: 'medium',
        timeLimit: 0, // 0 means no time limit
        questions: [{
            text: '',
            options: ['', '', '', ''],
            correctIndex: 0,
            explanation: '',
            points: 1
        }]
    });

    // Navigation state
    const [currentQuizIndex, setCurrentQuizIndex] = useState(null);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [userAnswers, setUserAnswers] = useState([]);
    const [showResults, setShowResults] = useState(false);
    const [showSummary, setShowSummary] = useState(false);
    const [quizStartTime, setQuizStartTime] = useState(null);
    const [timeRemaining, setTimeRemaining] = useState(null);

    // UI state
    const [loadingError, setLoadingError] = useState(null);
    const [successMessage, setSuccessMessage] = useState(null);
    const [showAIForm, setShowAIForm] = useState(false);
    const [showGeneratingModal, setShowGeneratingModal] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [confirmAction, setConfirmAction] = useState(null);
    const [confirmMessage, setConfirmMessage] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('newest');
    const [filterDifficulty, setFilterDifficulty] = useState('all');

    // Edit state
    const [editingQuiz, setEditingQuiz] = useState(null);
    const [showEditModal, setShowEditModal] = useState(false);

    // AI Generation state
    const [aiGenerating, setAiGenerating] = useState(false);
    const [aiQuizData, setAiQuizData] = useState(null);
    const [selectedQuestionTypes, setSelectedQuestionTypes] = useState(['multiple_choice']);
    const [selectedDifficulty, setSelectedDifficulty] = useState('medium');
    const [selectedChapters, setSelectedChapters] = useState([]);
    const [availableChapters, setAvailableChapters] = useState([]);
    const [loadingChapters, setLoadingChapters] = useState(false);

    // Refs
    const timerRef = useRef(null);
    const fileInputRef = useRef(null);

    // Theme detection
    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        checkDarkMode();
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    // Timer effect for quiz time limit
    useEffect(() => {
        if (timeRemaining !== null && timeRemaining > 0) {
            timerRef.current = setTimeout(() => {
                setTimeRemaining(prev => prev - 1);
            }, 1000);
        } else if (timeRemaining === 0) {
            // Time's up - auto submit
            setShowResults(true);
        }

        return () => {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }
        };
    }, [timeRemaining]);

    // Auto-clear messages
    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => setSuccessMessage(null), 3000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    useEffect(() => {
        if (loadingError) {
            const timer = setTimeout(() => setLoadingError(null), 5000);
            return () => clearTimeout(timer);
        }
    }, [loadingError]);

    // Load quizzes from database
    useEffect(() => {
        loadQuizzes();
        loadChapters();
        loadQuizAttempts();
    }, [subject, subjectId]);

    const loadQuizzes = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setLoadingError('Authentication required');
                return;
            }

            // Get subject ID from props
            if (!subjectId) {
                setLoadingError('Subject ID not found');
                return;
            }

            const response = await fetch(`http://localhost:5000/api/quizzes/${subjectId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.success) {
                setQuizzes(data.quizzes || []);
                setLoadingError(null);
            } else {
                throw new Error(data.error || 'Failed to load quizzes');
            }
        } catch (error) {
            console.error('Error loading quizzes:', error);
            setLoadingError('Failed to load quizzes from database.');
            setQuizzes([]);
        }
    };

    const loadChapters = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.error('No authentication token found');
                return;
            }

            // Get subject ID from props
            if (!subjectId) {
                console.error('Subject ID not found');
                return;
            }

            setLoadingChapters(true);

            // Try the Frontend server first (port 5001), then Backend (port 5000)
            let response;
            let data;

            try {
                // Try Frontend server first
                response = await fetch(`http://localhost:5001/api/subjects/${subjectId}/chapters`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    data = await response.json();
                    console.log('Chapters response from Frontend server:', data);
                } else {
                    throw new Error('Frontend server failed');
                }
            } catch (frontendError) {
                console.log('Frontend server failed, trying Backend server:', frontendError.message);

                // Fallback to Backend server
                response = await fetch(`http://localhost:5000/api/subjects/${subjectId}/chapters`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                data = await response.json();
                console.log('Chapters response from Backend server:', data);
            }

            if (data.success) {
                const chapters = data.chapters || [];
                console.log('Available chapters:', chapters);
                setAvailableChapters(chapters);
            } else {
                throw new Error(data.error || data.message || 'Failed to load chapters');
            }
        } catch (error) {
            console.error('Error loading chapters:', error);
            setAvailableChapters([]);
        } finally {
            setLoadingChapters(false);
        }
    };

    // Load quiz attempts from database
    const loadQuizAttempts = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            const response = await fetch('http://localhost:5000/api/quiz-attempts', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setQuizAttempts(data.attempts || []);
                }
            }
        } catch (error) {
            console.error('Error loading quiz attempts:', error);
        }
    };

    // Check if a quiz has been taken before
    const hasQuizBeenTaken = (quizId) => {
        return quizAttempts.some(attempt => attempt.quiz_id === quizId);
    };

    // Get best score for a quiz
    const getBestScore = (quizId) => {
        const attempts = quizAttempts.filter(attempt => attempt.quiz_id === quizId);
        if (attempts.length === 0) return null;
        return Math.max(...attempts.map(attempt => attempt.score || 0));
    };

    const resetQuizForm = () => {
        setNewQuiz({
            title: '',
            description: '',
            difficulty: 'medium',
            timeLimit: 0,
            questions: [{
                text: '',
                options: ['', '', '', ''],
                correctIndex: 0,
                explanation: '',
                points: 1
            }]
        });
        setShowAIForm(false);
        setShowGeneratingModal(false);
        setShowSuccessModal(false);
        setShowConfirmModal(false);
        setShowEditModal(false);
        setSuccessMessage(null);
        setAiQuizData(null);
        setEditingQuiz(null);
        setConfirmAction(null);
        setConfirmMessage('');
        setSelectedQuestionTypes(['multiple_choice']);
        setSelectedDifficulty('medium');
        setSelectedChapters([]);
    };



    const startQuiz = (index) => {
        setCurrentQuizIndex(index);
        setCurrentQuestionIndex(0);
        setUserAnswers(Array(quizzes[index].questions.length).fill(null));
        setShowResults(false);
        setQuizStartTime(Date.now());

        // Set timer if quiz has time limit
        if (quizzes[index].timeLimit > 0) {
            setTimeRemaining(quizzes[index].timeLimit * 60); // Convert minutes to seconds
        } else {
            setTimeRemaining(null);
        }
    };

    const cancelQuiz = () => {
        setCurrentQuizIndex(null);
        setShowResults(false);
        setShowSummary(false);
        setTimeRemaining(null);
        setQuizStartTime(null);
        if (timerRef.current) {
            clearTimeout(timerRef.current);
        }
    };

    const answerQuestion = (answerIndex) => {
        const newAnswers = [...userAnswers];
        newAnswers[currentQuestionIndex] = answerIndex;
        setUserAnswers(newAnswers);
    };

    const nextQuestion = () => {
        if (currentQuestionIndex < quizzes[currentQuizIndex].questions.length - 1) {
            setCurrentQuestionIndex(currentQuestionIndex + 1);
        } else {
            // Show summary page when reaching the last question
            setShowSummary(true);
        }
    };

    const prevQuestion = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex(currentQuestionIndex - 1);
        }
    };



    // Start editing a quiz
    const startEdit = (quiz) => {
        setEditingQuiz(quiz);
        setShowEditModal(true);
    };

    // Show confirmation modal for delete
    const deleteQuiz = (id) => {
        const quiz = quizzes.find(q => q.id === id);
        setConfirmMessage(`Are you sure you want to delete "${quiz?.title || 'this quiz'}"? This action cannot be undone.`);
        setConfirmAction(() => () => confirmDeleteQuiz(id));
        setShowConfirmModal(true);
    };

    // Actually delete the quiz after confirmation
    const confirmDeleteQuiz = async (id) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setLoadingError('Authentication required');
                return;
            }

            const response = await fetch(`http://localhost:5000/api/quizzes/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.success) {
                // Show success modal
                setShowConfirmModal(false);
                setSuccessMessage('Quiz deleted successfully!');
                setShowSuccessModal(true);

                // Auto-close success modal after 2 seconds
                setTimeout(() => {
                    setShowSuccessModal(false);
                    setSuccessMessage(null);
                }, 2000);

                if (currentQuizIndex !== null && quizzes[currentQuizIndex]?.id === id) {
                    cancelQuiz();
                }
                loadQuizzes(); // Reload quizzes from database
            } else {
                throw new Error(data.error || 'Failed to delete quiz');
            }

        } catch (error) {
            console.error('Error deleting quiz:', error);
            setLoadingError('Failed to delete quiz. Please try again.');
            setShowConfirmModal(false);
        }
    };

    // Handle confirmation modal actions
    const handleConfirmAction = () => {
        if (confirmAction) {
            confirmAction();
        }
    };

    const handleCancelAction = () => {
        setShowConfirmModal(false);
        setConfirmAction(null);
        setConfirmMessage('');
    };

    const exportQuizzes = () => {
        try {
            const data = JSON.stringify(quizzes, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${subject}-quizzes-${new Date().toISOString().slice(0, 10)}.json`;
            link.click();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error exporting quizzes:', error);
            setLoadingError('Failed to export quizzes.');
        }
    };

    const importQuizzes = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const importedQuizzes = JSON.parse(event.target.result);
                if (!Array.isArray(importedQuizzes)) {
                    throw new Error('Invalid file format - expected array of quizzes');
                }

                // Basic validation
                const validQuizzes = importedQuizzes.filter(quiz =>
                    quiz.id && quiz.title && Array.isArray(quiz.questions)
                );

                if (validQuizzes.length === 0 && importedQuizzes.length > 0) {
                    throw new Error('No valid quizzes found in import file');
                }

                setQuizzes(validQuizzes);
                setLoadingError(null);
                e.target.value = ''; // Reset file input
            } catch (error) {
                console.error('Error importing quizzes:', error);
                setLoadingError('Failed to import quizzes. Please check the file format.');
                e.target.value = ''; // Reset file input
            }
        };
        reader.onerror = () => {
            setLoadingError('Error reading file');
            e.target.value = ''; // Reset file input
        };
        reader.readAsText(file);
    };

    // Helper functions
    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };



    const getFilteredQuizzes = () => {
        let filtered = quizzes;

        // Search filter
        if (searchTerm) {
            filtered = filtered.filter(quiz =>
                quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                quiz.description?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Difficulty filter
        if (filterDifficulty !== 'all') {
            filtered = filtered.filter(quiz => quiz.difficulty === filterDifficulty);
        }

        // Sort
        filtered.sort((a, b) => {
            switch (sortBy) {
                case 'newest':
                    return new Date(b.createdAt) - new Date(a.createdAt);
                case 'oldest':
                    return new Date(a.createdAt) - new Date(b.createdAt);
                case 'title':
                    return a.title.localeCompare(b.title);
                case 'questions':
                    return b.questions.length - a.questions.length;
                default:
                    return 0;
            }
        });

        return filtered;
    };

    const calculateTotalScore = () => {
        if (!showResults || currentQuizIndex === null) return 0;

        let totalQuestions = 0;
        let correctAnswers = 0;

        quizzes[currentQuizIndex].questions.forEach((question, index) => {
            totalQuestions += 1; // Each question carries equal weight

            // Determine if the answer is correct
            let isCorrect = false;

            if (question.question_type === 'multiple_choice' || question.options) {
                // For multiple choice questions
                let correctAnswerIndex = question.correctIndex;

                // If correctIndex is not set, find it from correct_answer
                if (correctAnswerIndex === undefined && question.correct_answer !== undefined && Array.isArray(question.options)) {
                    correctAnswerIndex = question.options.findIndex(option =>
                        option.toLowerCase().trim() === question.correct_answer.toLowerCase().trim()
                    );
                }

                isCorrect = userAnswers[index] === correctAnswerIndex;
            } else if (question.question_type === 'short_answer') {
                // For short answer questions, consider any non-empty answer as attempted
                // (In a real scenario, you'd want AI to evaluate the answer)
                isCorrect = userAnswers[index] && userAnswers[index].trim().length > 0;
            }

            if (isCorrect) {
                correctAnswers += 1;
            }
        });

        return totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
    };

    const submitQuiz = async () => {
        setShowSummary(false);
        setShowResults(true);

        // Auto-save quiz results
        await saveQuizResults();
    };

    const saveQuizResults = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                console.error('No authentication token found');
                return;
            }

            const score = calculateTotalScore();
            const timeTaken = quizStartTime ? Math.floor((Date.now() - quizStartTime) / 1000) : 0;

            const response = await fetch('http://localhost:5000/api/quiz-results', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    quiz_id: quizzes[currentQuizIndex].id,
                    score: score,
                    time_taken: timeTaken,
                    answers: userAnswers
                })
            });

            if (response.ok) {
                console.log('Quiz results saved successfully');
            } else {
                console.error('Failed to save quiz results');
            }
        } catch (error) {
            console.error('Error saving quiz results:', error);
        }
    };

    const getAnsweredQuestionsCount = () => {
        return userAnswers.filter(answer => answer !== null && answer !== undefined).length;
    };

    // AI Generation Functions
    const generateQuizWithAI = async () => {
        if (!selectedQuestionTypes.length) {
            setLoadingError('Please select at least one question type');
            return;
        }

        if (selectedChapters.length === 0) {
            setLoadingError('Please select at least one chapter');
            return;
        }

        // Check if too many chapters are selected to prevent timeout (only for partial selection)
        const totalChapters = availableChapters.length;
        const isAllChaptersSelected = selectedChapters.length === totalChapters;

        if (!isAllChaptersSelected && selectedChapters.length > 5) {
            setLoadingError('Please select maximum 5 chapters to prevent timeout, or select all chapters for comprehensive quiz generation.');
            return;
        }

        // Close the form and show generating modal
        setShowAIForm(false);
        setShowGeneratingModal(true);
        setAiGenerating(true);
        setLoadingError(null);

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setLoadingError('Authentication required');
                return;
            }

            // Get subject ID from props
            if (!subjectId) {
                setLoadingError('Subject ID not found');
                return;
            }

            const requestData = {
                subject_id: subjectId,
                question_types: selectedQuestionTypes,
                difficulty: selectedDifficulty
            };

            // Add chapter_ids if specific chapters are selected
            if (selectedChapters.length > 0) {
                requestData.chapter_ids = selectedChapters.map(id => parseInt(id));
            }

            console.log('Sending quiz generation request:', requestData);

            const response = await fetch('http://localhost:5000/api/generate-quiz', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Error response:', errorText);

                // Try to parse error response
                let errorMessage = `HTTP error! status: ${response.status}`;
                try {
                    const errorData = JSON.parse(errorText);
                    if (errorData.error) {
                        errorMessage = errorData.error;
                    }
                } catch (e) {
                    // If not JSON, use the text as is
                    if (errorText) {
                        errorMessage = errorText;
                    }
                }

                throw new Error(errorMessage);
            }

            const data = await response.json();
            console.log('Quiz generation response:', data);
            if (data.success) {
                setAiQuizData(data.quiz);

                // Automatically save the generated quiz
                await saveAIGeneratedQuizAuto(data.quiz);
            } else {
                throw new Error(data.error || 'Failed to generate quiz');
            }

        } catch (error) {
            console.error('Error generating quiz:', error);

            // Provide more specific error messages
            let errorMessage = 'Failed to generate quiz. Please try again.';
            if (error.message.includes('No document content found')) {
                errorMessage = 'No document content found for this subject. Please upload a document first to generate quizzes.';
            } else if (error.message.includes('Subject not found')) {
                errorMessage = 'Subject not found. Please select a valid subject.';
            } else if (error.message.includes('Access denied')) {
                errorMessage = 'Access denied. You do not have permission to access this subject.';
            } else if (error.message.includes('Authentication required')) {
                errorMessage = 'Authentication required. Please log in again.';
            } else if (error.message) {
                errorMessage = error.message;
            }

            setLoadingError(errorMessage);
            setShowGeneratingModal(false);
        } finally {
            setAiGenerating(false);
        }
    };



    const saveAIGeneratedQuizAuto = async (quizData) => {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setLoadingError('Authentication required');
                return;
            }

            // Get subject ID from props
            if (!subjectId) {
                setLoadingError('Subject ID not found');
                return;
            }

            const response = await fetch('http://localhost:5000/api/quizzes', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    subject_id: subjectId,
                    title: quizData.title,
                    description: quizData.description,
                    difficulty: quizData.difficulty,
                    time_limit: quizData.time_limit || 0,
                    question_types: quizData.question_types,
                    ai_generated: true,
                    questions: quizData.questions
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.success) {
                // Show success modal
                setShowGeneratingModal(false);
                setShowSuccessModal(true);

                // Auto-close success modal after 2 seconds
                setTimeout(() => {
                    setShowSuccessModal(false);
                    resetQuizForm();
                }, 2000);

                loadQuizzes(); // Reload quizzes from database
            } else {
                throw new Error(data.error || 'Failed to save quiz');
            }

        } catch (error) {
            console.error('Error saving quiz:', error);
            setLoadingError('Failed to save quiz automatically. Please try again.');
            setShowGeneratingModal(false);
        }
    };



    return (
        <>
            <style>
                {`
                    .chapters-list::-webkit-scrollbar {
                        width: 6px;
                    }
                    .chapters-list::-webkit-scrollbar-track {
                        background: transparent;
                        border-radius: 3px;
                    }
                    .chapters-list::-webkit-scrollbar-thumb {
                        background: ${isDarkMode
                        ? 'linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%)'
                        : 'linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.15) 100%)'
                    };
                        border-radius: 3px;
                        transition: all 0.3s ease;
                        border: ${isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)'};
                    }
                    .chapters-list::-webkit-scrollbar-thumb:hover {
                        background: ${isDarkMode
                        ? 'linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.3) 100%)'
                        : 'linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.25) 100%)'
                    };
                        transform: scaleX(1.2);
                    }
                    .chapters-list::-webkit-scrollbar-corner {
                        background: transparent;
                    }
                `}
            </style>
            <div className={`advanced-quizzes-app ${isDarkMode ? 'theme-dark' : ''}`}>
                {/* Fixed Header */}
                <header className="quizzes-header fixed-header">
                    <div className="header-content">
                        <div className="header-left">
                            <h1 className="app-title">{subject} Quizzes</h1>
                            <p className="app-subtitle">Create, manage, and take interactive quizzes</p>
                        </div>
                        <div className="header-controls">
                            {showAIForm && (
                                <button
                                    className="control-button secondary close-modal-btn"
                                    onClick={() => resetQuizForm()}
                                    title="Close Quiz Generator"
                                >
                                    <span className="material-icons">close</span>
                                    Close
                                </button>
                            )}
                            <button
                                className="control-button secondary"
                                onClick={exportQuizzes}
                                title="Export quizzes"
                            >
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                                </svg>
                                Export
                            </button>
                            {/*<button
                            className="control-button secondary"
                            onClick={() => fileInputRef.current?.click()}
                            title="Import quizzes"
                        >
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                            </svg>
                            Import
                        </button>*/}
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept=".json"
                                onChange={importQuizzes}
                                style={{ display: 'none' }}
                            />
                            <button
                                className="control-button secondary close-quiz-tool-btn"
                                onClick={() => {
                                    // Close the entire quiz tool using the onClose prop from parent modal
                                    if (onClose) {
                                        onClose();
                                    } else {
                                        // Fallback for when not in modal
                                        if (window.history.length > 1) {
                                            window.history.back();
                                        } else {
                                            console.log('Close quiz tool');
                                        }
                                    }
                                }}
                                title="Close Quiz Tool"
                            >
                                <span className="material-icons">close</span>
                            </button>
                        </div>
                    </div>
                </header>

                {/* Messages */}
                <AnimatePresence>
                    {loadingError && (
                        <motion.div
                            className="message-banner error"
                            initial={{ opacity: 0, y: -50 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -50 }}
                            transition={{ duration: 0.3 }}
                        >
                            <div className="message-content">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                                </svg>
                                <span>{loadingError}</span>
                            </div>
                            <button
                                className="close-message"
                                onClick={() => setLoadingError(null)}
                            >
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                </svg>
                            </button>
                        </motion.div>
                    )}

                    {successMessage && (
                        <motion.div
                            className="message-banner success"
                            initial={{ opacity: 0, y: -50 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -50 }}
                            transition={{ duration: 0.3 }}
                        >
                            <div className="message-content">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" />
                                </svg>
                                <span>{successMessage}</span>
                            </div>
                            <button
                                className="close-message"
                                onClick={() => setSuccessMessage(null)}
                            >
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                </svg>
                            </button>
                        </motion.div>
                    )}
                </AnimatePresence>

                {/* Scrollable Content */}
                <div className="quizzes-content">
                    {currentQuizIndex === null ? (
                        <div className="quiz-manager">
                            {/* Search and Filter Bar */}
                            <div className="filter-bar">
                                {/*<div className="search-section">
                            <div className="search-input-container">
                                <svg className="search-icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
                                </svg>
                                <input
                                    type="text"
                                    placeholder="Search quizzes..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="search-input"
                                />
                                {searchTerm && (
                                    <button
                                        className="clear-search"
                                        onClick={() => setSearchTerm('')}
                                    >
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                        </svg>
                                    </button>
                                )}
                            </div>
                        </div>*/}

                                <div className="filter-controls">
                                    <select
                                        value={filterDifficulty}
                                        onChange={(e) => setFilterDifficulty(e.target.value)}
                                        className="filter-select"
                                    >
                                        <option value="all">All Difficulties</option>
                                        <option value="easy">Easy</option>
                                        <option value="medium">Medium</option>
                                        <option value="hard">Hard</option>
                                    </select>

                                    <select
                                        value={sortBy}
                                        onChange={(e) => setSortBy(e.target.value)}
                                        className="filter-select"
                                    >
                                        <option value="newest">Newest First</option>
                                        <option value="oldest">Oldest First</option>
                                        <option value="title">Title A-Z</option>
                                        <option value="questions">Most Questions</option>
                                    </select>
                                </div>

                                <div className="create-quiz-section">
                                    <button
                                        className="control-button primary create-quiz-btn"
                                        onClick={() => setShowAIForm(true)}
                                    >
                                        <AutoAwesomeIcon />
                                        Generate Quiz with AI
                                    </button>
                                </div>
                            </div>

                            <div className="quiz-list-container">
                                <h2>Available Quizzes ({getFilteredQuizzes().length})</h2>
                                {getFilteredQuizzes().length > 0 ? (
                                    <div className="quiz-grid">
                                        {getFilteredQuizzes().map((quiz, index) => (
                                            <motion.div
                                                key={quiz.id}
                                                className="quiz-card"
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                whileHover={{ y: -4 }}
                                            >
                                                <div className="quiz-card-header">
                                                    <div className="quiz-meta">
                                                        <span className={`difficulty-badge ${quiz.difficulty || 'medium'}`}>
                                                            {(quiz.difficulty || 'medium').toUpperCase()}
                                                        </span>
                                                        {quiz.timeLimit > 0 && (
                                                            <span className="time-badge">
                                                                <TimerIcon />
                                                                {quiz.timeLimit}m
                                                            </span>
                                                        )}
                                                        {hasQuizBeenTaken(quiz.id) && (
                                                            <span className="status-badge completed">
                                                                <CheckCircleIcon />
                                                                Completed
                                                            </span>
                                                        )}
                                                    </div>
                                                    <div className="quiz-actions-menu">
                                                        <button
                                                            className="menu-button"
                                                            onClick={() => startEdit(quiz)}
                                                            title="Edit quiz"
                                                        >
                                                            <EditIcon />
                                                        </button>
                                                        <button
                                                            className="menu-button delete"
                                                            onClick={() => deleteQuiz(quiz.id)}
                                                            title="Delete quiz"
                                                        >
                                                            <DeleteIcon />
                                                        </button>
                                                    </div>
                                                </div>

                                                <div className="quiz-content">
                                                    <h3 className="quiz-title">{quiz.title}</h3>
                                                    <p className="quiz-description">{quiz.description}</p>

                                                    <div className="quiz-stats">
                                                        <div className="stat-item">
                                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" />
                                                            </svg>
                                                            <span>{quiz.questions.length} questions</span>
                                                        </div>
                                                        <div className="stat-item">
                                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
                                                            </svg>
                                                            <span>{quiz.questions.reduce((sum, q) => sum + (q.points || 1), 0)} points</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="quiz-card-footer">
                                                    <button
                                                        className="start-quiz-btn"
                                                        onClick={() => startQuiz(quizzes.findIndex(q => q.id === quiz.id))}
                                                    >
                                                        <PlayArrowIcon />
                                                        Start Quiz
                                                    </button>
                                                </div>
                                            </motion.div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="empty-state">
                                        <div className="empty-icon">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
                                            </svg>
                                        </div>
                                        <h3>No quizzes found</h3>
                                        <p>
                                            {searchTerm || filterDifficulty !== 'all'
                                                ? 'Try adjusting your search or filters'
                                                : 'Create your first quiz to get started!'
                                            }
                                        </p>
                                        {(!searchTerm && filterDifficulty === 'all') && (
                                            <button
                                                className="control-button primary"
                                                onClick={() => setShowAIForm(true)}
                                            >
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z" />
                                                </svg>
                                                Generate Your First Quiz
                                            </button>
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* AI Quiz Generation Modal */}
                            <AnimatePresence>
                                {showAIForm && (
                                    <motion.div
                                        className="quiz-modal-overlay"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        transition={{ duration: 0.3 }}
                                        onClick={() => resetQuizForm()}
                                    >
                                        <motion.div
                                            className="quiz-modal-content"
                                            initial={{ opacity: 0, scale: 0.9, y: 20 }}
                                            animate={{ opacity: 1, scale: 1, y: 0 }}
                                            exit={{ opacity: 0, scale: 0.9, y: 20 }}
                                            transition={{ duration: 0.3 }}
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <div className="form-header">
                                                <div className="header-content">
                                                    <div className="ai-icon">
                                                        <AutoAwesomeIcon />
                                                    </div>
                                                    <div className="header-text">
                                                        <h2>Generate Quiz with AI</h2>
                                                        <p>Let Blueprint AI create a comprehensive quiz for you</p>
                                                    </div>
                                                </div>
                                                <button
                                                    className="close-form-btn"
                                                    onClick={() => resetQuizForm()}
                                                >
                                                    <CloseIcon />
                                                </button>
                                            </div>

                                            {!aiQuizData ? (
                                                <div className="ai-generation-form">
                                                    <div className="form-group">
                                                        <label>Question Types*</label>
                                                        <div className="checkbox-group">
                                                            <label className="checkbox-item">
                                                                <input
                                                                    type="checkbox"
                                                                    checked={selectedQuestionTypes.includes('multiple_choice')}
                                                                    onChange={(e) => {
                                                                        if (e.target.checked) {
                                                                            setSelectedQuestionTypes(prev => [...prev, 'multiple_choice']);
                                                                        } else {
                                                                            setSelectedQuestionTypes(prev => prev.filter(type => type !== 'multiple_choice'));
                                                                        }
                                                                    }}
                                                                />
                                                                <span>Multiple Choice</span>
                                                            </label>
                                                            <label className="checkbox-item">
                                                                <input
                                                                    type="checkbox"
                                                                    checked={selectedQuestionTypes.includes('short_answer')}
                                                                    onChange={(e) => {
                                                                        if (e.target.checked) {
                                                                            setSelectedQuestionTypes(prev => [...prev, 'short_answer']);
                                                                        } else {
                                                                            setSelectedQuestionTypes(prev => prev.filter(type => type !== 'short_answer'));
                                                                        }
                                                                    }}
                                                                />
                                                                <span>Short Answer</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div className="form-row">
                                                        <div className="form-group">
                                                            <label>Difficulty Level</label>
                                                            <select
                                                                value={selectedDifficulty}
                                                                onChange={(e) => setSelectedDifficulty(e.target.value)}
                                                                className="form-select"
                                                            >
                                                                <option value="easy">Easy</option>
                                                                <option value="medium">Medium</option>
                                                                <option value="hard">Hard</option>
                                                            </select>
                                                        </div>
                                                        <div className="form-group">
                                                            <label>Chapter Selection</label>
                                                            <div className="chapter-selection-info" style={{
                                                                marginBottom: '12px',
                                                                padding: '8px 12px',
                                                                borderRadius: '6px',
                                                                background: isDarkMode
                                                                    ? 'rgba(0, 122, 255, 0.1)'
                                                                    : 'rgba(0, 122, 255, 0.05)',
                                                                border: `1px solid ${isDarkMode ? 'rgba(0, 122, 255, 0.3)' : 'rgba(0, 122, 255, 0.2)'}`,
                                                                fontSize: '11px',
                                                                color: isDarkMode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)'
                                                            }}>
                                                                💡 <strong>Smart Processing:</strong> Select specific chapters (max 5) for focused quizzes, or select all chapters for comprehensive coverage of the entire document.
                                                            </div>
                                                            {loadingChapters ? (
                                                                <div className="loading-text">Loading chapters...</div>
                                                            ) : (
                                                                <div
                                                                    className="chapter-selection-container"
                                                                    style={{
                                                                        border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.08)',
                                                                        borderRadius: '12px',
                                                                        background: isDarkMode
                                                                            ? 'linear-gradient(145deg, rgba(28, 28, 30, 0.95) 0%, rgba(44, 44, 46, 0.95) 100%)'
                                                                            : 'linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 248, 248, 0.95) 100%)',
                                                                        backdropFilter: 'blur(20px) saturate(180%)',
                                                                        WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                                                                        overflow: 'hidden',
                                                                        maxHeight: '320px',
                                                                        display: 'flex',
                                                                        flexDirection: 'column',
                                                                        boxShadow: isDarkMode
                                                                            ? '0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                            : '0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
                                                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                        position: 'relative'
                                                                    }}
                                                                >
                                                                    <div
                                                                        className="chapter-selection-header"
                                                                        style={{
                                                                            display: 'flex',
                                                                            justifyContent: 'space-between',
                                                                            alignItems: 'center',
                                                                            padding: '12px 16px',
                                                                            background: isDarkMode
                                                                                ? 'linear-gradient(180deg, rgba(0, 122, 255, 0.15) 0%, rgba(0, 122, 255, 0.08) 100%)'
                                                                                : 'linear-gradient(180deg, rgba(0, 122, 255, 0.08) 0%, rgba(0, 122, 255, 0.04) 100%)',
                                                                            borderBottom: isDarkMode
                                                                                ? '1px solid rgba(255, 255, 255, 0.08)'
                                                                                : '1px solid rgba(0, 0, 0, 0.06)',
                                                                            flexShrink: 0,
                                                                            position: 'relative',
                                                                            backdropFilter: 'blur(10px)',
                                                                            WebkitBackdropFilter: 'blur(10px)'
                                                                        }}
                                                                    >
                                                                        <div className="selection-controls">
                                                                            <button
                                                                                type="button"
                                                                                className="select-all-btn"
                                                                                onClick={() => setSelectedChapters(availableChapters.map(ch => ch.id))}
                                                                                disabled={selectedChapters.length === availableChapters.length}
                                                                                style={{
                                                                                    padding: '6px 12px',
                                                                                    border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.08)',
                                                                                    borderRadius: '6px',
                                                                                    background: isDarkMode
                                                                                        ? 'linear-gradient(145deg, rgba(58, 58, 60, 0.8) 0%, rgba(44, 44, 46, 0.8) 100%)'
                                                                                        : 'linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 248, 248, 0.9) 100%)',
                                                                                    color: isDarkMode ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.8)',
                                                                                    fontSize: '11px',
                                                                                    fontWeight: '600',
                                                                                    cursor: selectedChapters.length === availableChapters.length ? 'not-allowed' : 'pointer',
                                                                                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                                    whiteSpace: 'nowrap',
                                                                                    backdropFilter: 'blur(10px)',
                                                                                    WebkitBackdropFilter: 'blur(10px)',
                                                                                    boxShadow: selectedChapters.length === availableChapters.length
                                                                                        ? 'none'
                                                                                        : isDarkMode
                                                                                            ? '0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                            : '0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
                                                                                    opacity: selectedChapters.length === availableChapters.length ? 0.5 : 1
                                                                                }}
                                                                                onMouseEnter={(e) => {
                                                                                    if (selectedChapters.length !== availableChapters.length) {
                                                                                        e.target.style.transform = 'translateY(-1px) scale(1.02)';
                                                                                        e.target.style.boxShadow = isDarkMode
                                                                                            ? '0 4px 16px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15)'
                                                                                            : '0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.9)';
                                                                                    }
                                                                                }}
                                                                                onMouseLeave={(e) => {
                                                                                    if (selectedChapters.length !== availableChapters.length) {
                                                                                        e.target.style.transform = 'translateY(0) scale(1)';
                                                                                        e.target.style.boxShadow = isDarkMode
                                                                                            ? '0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                            : '0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)';
                                                                                    }
                                                                                }}
                                                                            >
                                                                                Select All
                                                                            </button>
                                                                            <button
                                                                                type="button"
                                                                                className="deselect-all-btn"
                                                                                onClick={() => setSelectedChapters([])}
                                                                                disabled={selectedChapters.length === 0}
                                                                                style={{
                                                                                    padding: '6px 12px',
                                                                                    border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.08)',
                                                                                    borderRadius: '6px',
                                                                                    background: isDarkMode
                                                                                        ? 'linear-gradient(145deg, rgba(58, 58, 60, 0.8) 0%, rgba(44, 44, 46, 0.8) 100%)'
                                                                                        : 'linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 248, 248, 0.9) 100%)',
                                                                                    color: isDarkMode ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.8)',
                                                                                    fontSize: '11px',
                                                                                    fontWeight: '600',
                                                                                    cursor: selectedChapters.length === 0 ? 'not-allowed' : 'pointer',
                                                                                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                                    whiteSpace: 'nowrap',
                                                                                    backdropFilter: 'blur(10px)',
                                                                                    WebkitBackdropFilter: 'blur(10px)',
                                                                                    boxShadow: selectedChapters.length === 0
                                                                                        ? 'none'
                                                                                        : isDarkMode
                                                                                            ? '0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                            : '0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
                                                                                    opacity: selectedChapters.length === 0 ? 0.5 : 1
                                                                                }}
                                                                                onMouseEnter={(e) => {
                                                                                    if (selectedChapters.length !== 0) {
                                                                                        e.target.style.transform = 'translateY(-1px) scale(1.02)';
                                                                                        e.target.style.boxShadow = isDarkMode
                                                                                            ? '0 4px 16px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15)'
                                                                                            : '0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.9)';
                                                                                    }
                                                                                }}
                                                                                onMouseLeave={(e) => {
                                                                                    if (selectedChapters.length !== 0) {
                                                                                        e.target.style.transform = 'translateY(0) scale(1)';
                                                                                        e.target.style.boxShadow = isDarkMode
                                                                                            ? '0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                            : '0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)';
                                                                                    }
                                                                                }}
                                                                            >
                                                                                Clear All
                                                                            </button>
                                                                        </div>
                                                                        <div
                                                                            className="selection-count"
                                                                            style={{
                                                                                fontSize: '11px',
                                                                                fontWeight: '700',
                                                                                color: '#007AFF',
                                                                                background: isDarkMode
                                                                                    ? 'linear-gradient(135deg, rgba(0, 122, 255, 0.2) 0%, rgba(0, 122, 255, 0.15) 100%)'
                                                                                    : 'linear-gradient(135deg, rgba(0, 122, 255, 0.12) 0%, rgba(0, 122, 255, 0.08) 100%)',
                                                                                padding: '4px 10px',
                                                                                borderRadius: '8px',
                                                                                border: isDarkMode ? '1px solid rgba(0, 122, 255, 0.3)' : '1px solid rgba(0, 122, 255, 0.2)',
                                                                                backdropFilter: 'blur(10px)',
                                                                                WebkitBackdropFilter: 'blur(10px)',
                                                                                boxShadow: isDarkMode
                                                                                    ? '0 2px 8px rgba(0, 122, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                    : '0 2px 8px rgba(0, 122, 255, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
                                                                                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                                textShadow: isDarkMode ? '0 1px 2px rgba(0, 0, 0, 0.3)' : '0 1px 2px rgba(255, 255, 255, 0.8)'
                                                                            }}
                                                                        >
                                                                            {selectedChapters.length} of {availableChapters.length} selected
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        className="chapters-list"
                                                                        style={{
                                                                            flex: 1,
                                                                            overflowY: 'auto',
                                                                            overflowX: 'hidden',
                                                                            padding: '8px',
                                                                            minHeight: 0,
                                                                            background: isDarkMode
                                                                                ? 'linear-gradient(180deg, rgba(28, 28, 30, 0.3) 0%, rgba(44, 44, 46, 0.1) 100%)'
                                                                                : 'linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(248, 248, 248, 0.3) 100%)',
                                                                            scrollbarWidth: 'thin',
                                                                            scrollbarColor: isDarkMode
                                                                                ? 'rgba(255, 255, 255, 0.2) transparent'
                                                                                : 'rgba(0, 0, 0, 0.2) transparent'
                                                                        }}
                                                                    >
                                                                        {availableChapters.map((chapter) => (
                                                                            <label
                                                                                key={chapter.id}
                                                                                className="chapter-checkbox-item"
                                                                                style={{
                                                                                    display: 'flex',
                                                                                    alignItems: 'center',
                                                                                    gap: '12px',
                                                                                    padding: '10px 14px',
                                                                                    borderRadius: '8px',
                                                                                    cursor: 'pointer',
                                                                                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                                    marginBottom: '4px',
                                                                                    minHeight: '44px',
                                                                                    background: selectedChapters.includes(chapter.id)
                                                                                        ? isDarkMode
                                                                                            ? 'linear-gradient(135deg, rgba(0, 122, 255, 0.25) 0%, rgba(0, 122, 255, 0.15) 100%)'
                                                                                            : 'linear-gradient(135deg, rgba(0, 122, 255, 0.12) 0%, rgba(0, 122, 255, 0.08) 100%)'
                                                                                        : 'transparent',
                                                                                    border: selectedChapters.includes(chapter.id)
                                                                                        ? isDarkMode
                                                                                            ? '1px solid rgba(0, 122, 255, 0.4)'
                                                                                            : '1px solid rgba(0, 122, 255, 0.25)'
                                                                                        : isDarkMode
                                                                                            ? '1px solid transparent'
                                                                                            : '1px solid transparent',
                                                                                    boxShadow: selectedChapters.includes(chapter.id)
                                                                                        ? isDarkMode
                                                                                            ? '0 2px 12px rgba(0, 122, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                            : '0 2px 12px rgba(0, 122, 255, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
                                                                                        : 'none',
                                                                                    backdropFilter: selectedChapters.includes(chapter.id) ? 'blur(10px)' : 'none',
                                                                                    WebkitBackdropFilter: selectedChapters.includes(chapter.id) ? 'blur(10px)' : 'none',
                                                                                    position: 'relative',
                                                                                    overflow: 'hidden'
                                                                                }}
                                                                                onMouseEnter={(e) => {
                                                                                    if (!selectedChapters.includes(chapter.id)) {
                                                                                        e.target.style.background = isDarkMode
                                                                                            ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)'
                                                                                            : 'linear-gradient(135deg, rgba(0, 0, 0, 0.03) 0%, rgba(0, 0, 0, 0.01) 100%)';
                                                                                        e.target.style.transform = 'translateX(4px) scale(1.01)';
                                                                                        e.target.style.boxShadow = isDarkMode
                                                                                            ? '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                                                                                            : '0 4px 16px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.6)';
                                                                                    } else {
                                                                                        e.target.style.transform = 'translateX(4px) scale(1.01)';
                                                                                        e.target.style.boxShadow = isDarkMode
                                                                                            ? '0 4px 20px rgba(0, 122, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15)'
                                                                                            : '0 4px 20px rgba(0, 122, 255, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.9)';
                                                                                    }
                                                                                }}
                                                                                onMouseLeave={(e) => {
                                                                                    if (!selectedChapters.includes(chapter.id)) {
                                                                                        e.target.style.background = 'transparent';
                                                                                        e.target.style.transform = 'translateX(0) scale(1)';
                                                                                        e.target.style.boxShadow = 'none';
                                                                                    } else {
                                                                                        e.target.style.transform = 'translateX(0) scale(1)';
                                                                                        e.target.style.boxShadow = isDarkMode
                                                                                            ? '0 2px 12px rgba(0, 122, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                            : '0 2px 12px rgba(0, 122, 255, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)';
                                                                                    }
                                                                                }}
                                                                            >
                                                                                <input
                                                                                    type="checkbox"
                                                                                    checked={selectedChapters.includes(chapter.id)}
                                                                                    onChange={(e) => {
                                                                                        if (e.target.checked) {
                                                                                            setSelectedChapters(prev => [...prev, chapter.id]);
                                                                                        } else {
                                                                                            setSelectedChapters(prev => prev.filter(id => id !== chapter.id));
                                                                                        }
                                                                                    }}
                                                                                    style={{
                                                                                        width: '18px',
                                                                                        height: '18px',
                                                                                        border: selectedChapters.includes(chapter.id)
                                                                                            ? '2px solid #007AFF'
                                                                                            : isDarkMode
                                                                                                ? '2px solid rgba(255, 255, 255, 0.3)'
                                                                                                : '2px solid rgba(0, 0, 0, 0.2)',
                                                                                        borderRadius: '4px',
                                                                                        background: selectedChapters.includes(chapter.id)
                                                                                            ? 'linear-gradient(135deg, #007AFF 0%, #0056CC 100%)'
                                                                                            : isDarkMode
                                                                                                ? 'linear-gradient(145deg, rgba(58, 58, 60, 0.8) 0%, rgba(44, 44, 46, 0.8) 100%)'
                                                                                                : 'linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 248, 248, 0.9) 100%)',
                                                                                        cursor: 'pointer',
                                                                                        position: 'relative',
                                                                                        margin: 0,
                                                                                        appearance: 'none',
                                                                                        WebkitAppearance: 'none',
                                                                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                                        flexShrink: 0,
                                                                                        boxShadow: selectedChapters.includes(chapter.id)
                                                                                            ? isDarkMode
                                                                                                ? '0 2px 8px rgba(0, 122, 255, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                                                                                                : '0 2px 8px rgba(0, 122, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
                                                                                            : isDarkMode
                                                                                                ? '0 2px 6px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                                                                                                : '0 2px 6px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
                                                                                        transform: selectedChapters.includes(chapter.id) ? 'scale(1.1)' : 'scale(1)'
                                                                                    }}
                                                                                />
                                                                                {selectedChapters.includes(chapter.id) && (
                                                                                    <div style={{
                                                                                        position: 'absolute',
                                                                                        left: '17px',
                                                                                        top: '50%',
                                                                                        transform: 'translateY(-50%)',
                                                                                        color: 'white',
                                                                                        fontSize: '10px',
                                                                                        fontWeight: '900',
                                                                                        pointerEvents: 'none',
                                                                                        textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
                                                                                        zIndex: 1
                                                                                    }}>
                                                                                        ✓
                                                                                    </div>
                                                                                )}
                                                                                <span
                                                                                    className="chapter-info"
                                                                                    style={{
                                                                                        flex: 1,
                                                                                        minWidth: 0,
                                                                                        display: 'flex',
                                                                                        flexDirection: 'column'
                                                                                    }}
                                                                                >
                                                                                    <span
                                                                                        className="chapter-title"
                                                                                        style={{
                                                                                            fontSize: '13px',
                                                                                            fontWeight: selectedChapters.includes(chapter.id) ? '600' : '500',
                                                                                            color: selectedChapters.includes(chapter.id)
                                                                                                ? '#007AFF'
                                                                                                : isDarkMode
                                                                                                    ? 'rgba(255, 255, 255, 0.9)'
                                                                                                    : 'rgba(0, 0, 0, 0.85)',
                                                                                            lineHeight: '1.4',
                                                                                            overflow: 'hidden',
                                                                                            textOverflow: 'ellipsis',
                                                                                            whiteSpace: 'nowrap',
                                                                                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                                            textShadow: selectedChapters.includes(chapter.id)
                                                                                                ? isDarkMode
                                                                                                    ? '0 1px 2px rgba(0, 0, 0, 0.3)'
                                                                                                    : '0 1px 2px rgba(255, 255, 255, 0.8)'
                                                                                                : 'none'
                                                                                        }}
                                                                                    >
                                                                                        {chapter.chapter_number ? `${chapter.chapter_number}: ` : ''}{chapter.title || chapter.name}
                                                                                    </span>
                                                                                </span>
                                                                            </label>
                                                                        ))}
                                                                    </div>
                                                                    {availableChapters.length === 0 && (
                                                                        <div className="no-chapters-message">
                                                                            No chapters available for this subject.
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    <div className="info-section">
                                                        <div className="info-item">
                                                            <ErrorIcon />
                                                            <span>AI will generate up to 20 questions from your uploaded document content</span>
                                                        </div>
                                                        <div className="info-item">
                                                            <ErrorIcon />
                                                            <span>Questions will progress from easy to difficult automatically</span>
                                                        </div>
                                                    </div>

                                                    <div className="form-actions">
                                                        <button
                                                            className="cancel-button"
                                                            onClick={() => resetQuizForm()}
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            className="generate-button"
                                                            onClick={generateQuizWithAI}
                                                            disabled={!selectedQuestionTypes.length || selectedChapters.length === 0 || aiGenerating}
                                                        >
                                                            {aiGenerating ? (
                                                                <>
                                                                    <div className="loading-spinner"></div>
                                                                    Generating...
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <AutoAwesomeIcon />
                                                                    Generate Quiz
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="ai-quiz-preview">
                                                    <div className="preview-header">
                                                        <h3>Generated Quiz Preview</h3>
                                                        <div className="quiz-meta">
                                                            <span className={`difficulty-badge ${aiQuizData.difficulty}`}>
                                                                {aiQuizData.difficulty?.toUpperCase()}
                                                            </span>
                                                            <span className="question-count">
                                                                {aiQuizData.questions?.length} questions
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div className="preview-content">
                                                        <h4>{aiQuizData.title}</h4>
                                                        <p>{aiQuizData.description}</p>

                                                        <div className="questions-preview">
                                                            {aiQuizData.questions?.slice(0, 2).map((question, index) => (
                                                                <div key={index} className="question-preview">
                                                                    <div className="question-header">
                                                                        <strong>Q{index + 1}:</strong>
                                                                        <span className={`question-type ${question.question_type}`}>
                                                                            {question.question_type === 'multiple_choice' ? 'Multiple Choice' : 'Short Answer'}
                                                                        </span>
                                                                    </div>
                                                                    <div className="question-text">
                                                                        {question.question_text || question.text}
                                                                    </div>
                                                                    {question.question_type === 'multiple_choice' && question.options && (
                                                                        <div className="options-preview">
                                                                            {question.options.slice(0, 2).map((option, optIndex) => (
                                                                                <div key={optIndex} className="option-preview">
                                                                                    {String.fromCharCode(65 + optIndex)}. {option}
                                                                                </div>
                                                                            ))}
                                                                            {question.options.length > 2 && (
                                                                                <div className="more-options">
                                                                                    +{question.options.length - 2} more options...
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            ))}
                                                            {aiQuizData.questions?.length > 2 && (
                                                                <div className="more-questions">
                                                                    +{aiQuizData.questions.length - 2} more questions...
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    <div className="preview-actions">
                                                        <button
                                                            className="generate-new-button primary"
                                                            onClick={() => {
                                                                setAiQuizData(null);
                                                                setShowAIForm(true);
                                                            }}
                                                        >
                                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                                <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z" />
                                                            </svg>
                                                            Generate Another Quiz
                                                        </button>
                                                    </div>
                                                </div>
                                            )}
                                        </motion.div>
                                    </motion.div>
                                )}
                            </AnimatePresence>

                            {/* Generating Modal */}
                            <AnimatePresence>
                                {showGeneratingModal && (
                                    <motion.div
                                        className="quiz-modal-overlay"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <motion.div
                                            className="quiz-generating-modal"
                                            initial={{ opacity: 0, scale: 0.9 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            exit={{ opacity: 0, scale: 0.9 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <div className="generating-content">
                                                <div className="generating-icon">
                                                    <div className="loading-spinner"></div>
                                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z" />
                                                    </svg>
                                                </div>
                                                <h3>Generating Your Quiz</h3>
                                                <p>AI is analyzing your content and creating personalized questions...</p>
                                                <div className="progress-dots">
                                                    <span></span>
                                                    <span></span>
                                                    <span></span>
                                                </div>
                                            </div>
                                        </motion.div>
                                    </motion.div>
                                )}
                            </AnimatePresence>

                            {/* Success Modal */}
                            <AnimatePresence>
                                {showSuccessModal && (
                                    <motion.div
                                        className="quiz-modal-overlay"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <motion.div
                                            className="quiz-success-modal"
                                            initial={{ opacity: 0, scale: 0.9, y: 20 }}
                                            animate={{ opacity: 1, scale: 1, y: 0 }}
                                            exit={{ opacity: 0, scale: 0.9, y: 20 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <div className="success-content">
                                                <div className="success-icon">
                                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" />
                                                    </svg>
                                                </div>
                                                <h3>{successMessage || 'Quiz Created Successfully!'}</h3>
                                                <p>{successMessage?.includes('deleted') ? 'The quiz has been removed from your collection.' : 'Your AI-generated quiz has been saved to your collection.'}</p>
                                            </div>
                                        </motion.div>
                                    </motion.div>
                                )}
                            </AnimatePresence>

                            {/* Confirmation Modal */}
                            <AnimatePresence>
                                {showConfirmModal && (
                                    <motion.div
                                        className="quiz-modal-overlay"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        transition={{ duration: 0.3 }}
                                        onClick={handleCancelAction}
                                    >
                                        <motion.div
                                            className="quiz-confirm-modal"
                                            initial={{ opacity: 0, scale: 0.9, y: 20 }}
                                            animate={{ opacity: 1, scale: 1, y: 0 }}
                                            exit={{ opacity: 0, scale: 0.9, y: 20 }}
                                            transition={{ duration: 0.3 }}
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <div className="confirm-content">
                                                <div className="confirm-icon">
                                                    <span className="material-icons">warning</span>
                                                </div>
                                                <h3>Confirm Action</h3>
                                                <p>{confirmMessage}</p>
                                                <div className="confirm-actions">
                                                    <button
                                                        className="cancel-button"
                                                        onClick={handleCancelAction}
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        className="confirm-button"
                                                        onClick={handleConfirmAction}
                                                    >
                                                        Yes, Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </motion.div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    ) : (
                        <div className="quiz-player">
                            {showSummary ? (
                                <motion.div
                                    className="quiz-summary"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <div className="summary-header">
                                        <h2>Quiz Summary</h2>
                                        <p className="quiz-name">{quizzes[currentQuizIndex].title}</p>
                                    </div>

                                    <div className="summary-content">
                                        <div className="summary-stats">
                                            <div className="stat-item">
                                                <div className="stat-value">{quizzes[currentQuizIndex].questions.length}</div>
                                                <div className="stat-label">Total Questions</div>
                                            </div>
                                            <div className="stat-item">
                                                <div className="stat-value">{getAnsweredQuestionsCount()}</div>
                                                <div className="stat-label">Answered</div>
                                            </div>
                                            <div className="stat-item">
                                                <div className="stat-value">{quizzes[currentQuizIndex].questions.length - getAnsweredQuestionsCount()}</div>
                                                <div className="stat-label">Unanswered</div>
                                            </div>
                                        </div>

                                        <div className="questions-overview">
                                            <h3>Questions Overview</h3>
                                            <div className="questions-grid">
                                                {quizzes[currentQuizIndex].questions.map((question, index) => {
                                                    const isAnswered = userAnswers[index] !== null && userAnswers[index] !== undefined;
                                                    const hasContent = question.question_type === 'short_answer' ?
                                                        (userAnswers[index] && userAnswers[index].trim().length > 0) : isAnswered;

                                                    return (
                                                        <div
                                                            key={index}
                                                            className={`question-overview-item ${hasContent ? 'answered' : 'unanswered'}`}
                                                            onClick={() => {
                                                                setCurrentQuestionIndex(index);
                                                                setShowSummary(false);
                                                            }}
                                                        >
                                                            <div className="question-number">Q{index + 1}</div>
                                                            <div className="question-status">
                                                                {hasContent ? (
                                                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                                                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" />
                                                                    </svg>
                                                                ) : (
                                                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                                                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6Z" />
                                                                    </svg>
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>

                                        {getAnsweredQuestionsCount() < quizzes[currentQuizIndex].questions.length && (
                                            <div className="warning-message">
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                                                </svg>
                                                <span>You have {quizzes[currentQuizIndex].questions.length - getAnsweredQuestionsCount()} unanswered questions. You can still submit, but consider reviewing them first.</span>
                                            </div>
                                        )}
                                    </div>

                                    <div className="summary-actions">
                                        <button
                                            className="review-button secondary"
                                            onClick={() => {
                                                // Go to first unanswered question or first question
                                                const firstUnanswered = userAnswers.findIndex(answer => answer === null || answer === undefined);
                                                setCurrentQuestionIndex(firstUnanswered !== -1 ? firstUnanswered : 0);
                                                setShowSummary(false);
                                            }}
                                        >
                                            Review Questions
                                        </button>
                                        <button
                                            className="submit-button primary"
                                            onClick={submitQuiz}
                                        >
                                            Submit Quiz
                                        </button>
                                    </div>
                                </motion.div>
                            ) : showResults ? (
                                <motion.div
                                    className="quiz-results"
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <div className="results-header">
                                        <div className="results-title">
                                            <h2>Quiz Results</h2>
                                            <p className="quiz-name">{quizzes[currentQuizIndex].title}</p>
                                        </div>
                                        <div className={`score-display ${calculateTotalScore() >= 70 ? 'good' : 'bad'}`}>
                                            <div className="score-value">{calculateTotalScore()}%</div>
                                            <div className="score-label">Final Score</div>
                                        </div>
                                    </div>

                                    <div className="results-details">
                                        <div className="summary">
                                            <p>
                                                You answered {userAnswers.filter((a, i) => {
                                                    const question = quizzes[currentQuizIndex].questions[i];
                                                    if (question.question_type === 'multiple_choice' || question.options) {
                                                        let correctAnswerIndex = question.correctIndex;

                                                        // If correctIndex is not set, find it from correct_answer
                                                        if (correctAnswerIndex === undefined && question.correct_answer !== undefined && Array.isArray(question.options)) {
                                                            correctAnswerIndex = question.options.findIndex(option =>
                                                                option.toLowerCase().trim() === question.correct_answer.toLowerCase().trim()
                                                            );
                                                        }

                                                        return a === correctAnswerIndex;
                                                    } else {
                                                        // For short answer, we'll consider any non-empty answer as attempted
                                                        return a && a.trim().length > 0;
                                                    }
                                                }).length} out of {quizzes[currentQuizIndex].questions.length} questions correctly.
                                            </p>
                                        </div>

                                        <div className="answers-review">
                                            {quizzes[currentQuizIndex].questions.map((question, index) => {
                                                const isCorrect = (() => {
                                                    if (question.question_type === 'multiple_choice' || question.options) {
                                                        let correctAnswerIndex = question.correctIndex;

                                                        // If correctIndex is not set, find it from correct_answer
                                                        if (correctAnswerIndex === undefined && question.correct_answer !== undefined && Array.isArray(question.options)) {
                                                            correctAnswerIndex = question.options.findIndex(option =>
                                                                option.toLowerCase().trim() === question.correct_answer.toLowerCase().trim()
                                                            );
                                                        }

                                                        return userAnswers[index] === correctAnswerIndex;
                                                    } else {
                                                        // For short answer, we'll show it as answered if there's content
                                                        return userAnswers[index] && userAnswers[index].trim().length > 0;
                                                    }
                                                })();

                                                return (
                                                    <div
                                                        key={index}
                                                        className={`question-review ${isCorrect ? 'correct' : 'incorrect'}`}
                                                    >
                                                        <div className="question-text">
                                                            <strong>Question {index + 1}:</strong> {question.question_text || question.text}
                                                        </div>

                                                        {/* Multiple Choice Answer Display */}
                                                        {(question.question_type === 'multiple_choice' || question.options) && (
                                                            <>
                                                                <div className="user-answer">
                                                                    <strong>Your answer:</strong> {
                                                                        question.options && question.options[userAnswers[index]]
                                                                            ? question.options[userAnswers[index]]
                                                                            : 'No answer selected'
                                                                    }
                                                                </div>
                                                                {!isCorrect && (
                                                                    <div className="correct-answer">
                                                                        <strong>Correct answer:</strong> {
                                                                            question.correct_answer ||
                                                                            (question.options && question.options[question.correctIndex])
                                                                        }
                                                                    </div>
                                                                )}
                                                            </>
                                                        )}

                                                        {/* Short Answer Display */}
                                                        {question.question_type === 'short_answer' && (
                                                            <>
                                                                <div className="user-answer">
                                                                    <strong>Your answer:</strong> {
                                                                        userAnswers[index] && userAnswers[index].trim()
                                                                            ? userAnswers[index]
                                                                            : 'No answer provided'
                                                                    }
                                                                </div>
                                                                <div className="correct-answer">
                                                                    <strong>Expected answer:</strong> {question.correct_answer}
                                                                </div>
                                                            </>
                                                        )}

                                                        {question.explanation && (
                                                            <div className="explanation">
                                                                <strong>Explanation:</strong> {question.explanation}
                                                            </div>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>

                                    <div className="results-actions">
                                        <button
                                            className="restart-button"
                                            onClick={() => startQuiz(currentQuizIndex)}
                                        >
                                            Try Again
                                        </button>
                                        <button
                                            className="back-button"
                                            onClick={cancelQuiz}
                                        >
                                            Back to Quizzes
                                        </button>
                                    </div>
                                </motion.div>
                            ) : (
                                <motion.div
                                    className="quiz-container"
                                    key={currentQuestionIndex}
                                    initial={{ opacity: 0, x: 50 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    exit={{ opacity: 0, x: -50 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <div className="quiz-header">
                                        <div className="quiz-title-section">
                                            <h2>{quizzes[currentQuizIndex].title}</h2>
                                            {/*{quizzes[currentQuizIndex].description && (
                                        <p className="quiz-description">{quizzes[currentQuizIndex].description}</p>
                                    )}*/}
                                        </div>

                                        <div className="quiz-progress-section">
                                            <div className="progress-info">
                                                <span className="question-counter">
                                                    Question {currentQuestionIndex + 1} of {quizzes[currentQuizIndex].questions.length}
                                                </span>
                                                <div className="progress-bar">
                                                    <div
                                                        className="progress-fill"
                                                        style={{
                                                            width: `${((currentQuestionIndex + 1) / quizzes[currentQuizIndex].questions.length) * 100}%`
                                                        }}
                                                    ></div>
                                                </div>
                                            </div>

                                            {timeRemaining !== null && (
                                                <div className={`timer ${timeRemaining <= 60 ? 'warning' : ''} ${timeRemaining <= 30 ? 'danger' : ''}`}>
                                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                                                    </svg>
                                                    <span>{formatTime(timeRemaining)}</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <div className="question-container">
                                        <div className="question-text">
                                            {quizzes[currentQuizIndex].questions[currentQuestionIndex].question_text || quizzes[currentQuizIndex].questions[currentQuestionIndex].text}
                                        </div>

                                        {/* Multiple Choice Questions */}
                                        {(quizzes[currentQuizIndex].questions[currentQuestionIndex].question_type === 'multiple_choice' ||
                                            quizzes[currentQuizIndex].questions[currentQuestionIndex].options) && (
                                                <div className="options-container">
                                                    {(quizzes[currentQuizIndex].questions[currentQuestionIndex].options || []).map((option, index) => (
                                                        <div
                                                            key={index}
                                                            className={`option ${userAnswers[currentQuestionIndex] === index ? 'selected' : ''}`}
                                                            onClick={() => answerQuestion(index)}
                                                        >
                                                            <div className="option-letter">
                                                                {String.fromCharCode(65 + index)}
                                                            </div>
                                                            <div className="option-text">
                                                                {option}
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}

                                        {/* Short Answer Questions */}
                                        {quizzes[currentQuizIndex].questions[currentQuestionIndex].question_type === 'short_answer' && (
                                            <div className="short-answer-container">
                                                <textarea
                                                    className="short-answer-input"
                                                    placeholder="Type your answer here..."
                                                    value={userAnswers[currentQuestionIndex] || ''}
                                                    onChange={(e) => answerQuestion(e.target.value)}
                                                    rows={4}
                                                />
                                                <div className="answer-hint">
                                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                                                    </svg>
                                                    <span>Provide a clear and concise answer. You can write 1-3 sentences.</span>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    <div className="navigation">
                                        <button
                                            onClick={prevQuestion}
                                            disabled={currentQuestionIndex === 0}
                                            className="nav-button prev-button"
                                        >
                                            <NavigateBeforeIcon />
                                            Previous
                                        </button>
                                        <button
                                            onClick={nextQuestion}
                                            className={`nav-button next-button ${(() => {
                                                const currentQuestion = quizzes[currentQuizIndex].questions[currentQuestionIndex];
                                                const currentAnswer = userAnswers[currentQuestionIndex];

                                                if (currentQuestion.question_type === 'short_answer') {
                                                    return !currentAnswer || currentAnswer.trim().length === 0;
                                                } else {
                                                    return currentAnswer === null || currentAnswer === undefined;
                                                }
                                            })() ? 'disabled' : ''}`}
                                        >
                                            {currentQuestionIndex === quizzes[currentQuizIndex].questions.length - 1 ? (
                                                <>
                                                    <CheckCircleIcon />
                                                    Finish
                                                </>
                                            ) : (
                                                <>
                                                    Next
                                                    <NavigateNextIcon />
                                                </>
                                            )}
                                        </button>
                                    </div>
                                </motion.div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default Quizzes;