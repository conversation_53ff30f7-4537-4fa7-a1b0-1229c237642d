import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [token, setToken] = useState(null);
    const [loading, setLoading] = useState(true);

    // Initialize auth state from localStorage
    useEffect(() => {
        const storedToken = localStorage.getItem('token');
        const storedUserId = localStorage.getItem('user_id');
        const storedFullName = localStorage.getItem('user_fullName');
        const storedEmail = localStorage.getItem('user_email');

        if (storedToken && storedUserId) {
            // Reconstruct user object with all available data
            setUser({
                id: parseInt(storedUserId),
                token: storedToken,
                fullName: storedFullName || '',
                email: storedEmail || ''
            });
            setToken(storedToken);
        }
        setLoading(false);
    }, []);

    const login = (userData, authToken) => {
        console.log('authContext.jsx: login called with userData:', userData, 'authToken:', authToken);
        const user = {
            id: userData.id,
            fullName: userData.fullName,
            email: userData.email,
            token: authToken,
        };
        setUser(user);
        setToken(authToken);

        // Store all user data in localStorage
        localStorage.setItem('token', authToken);
        localStorage.setItem('user_id', userData.id);
        localStorage.setItem('user_fullName', userData.fullName || '');
        localStorage.setItem('user_email', userData.email || '');

        console.log('authContext.jsx: Stored user data in localStorage:', {
            id: userData.id,
            fullName: userData.fullName,
            email: userData.email
        });
    };

    const logout = () => {
        setUser(null);
        setToken(null);

        // Clear all user data from localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('user_id');
        localStorage.removeItem('user_fullName');
        localStorage.removeItem('user_email');

        console.log('authContext.jsx: Cleared user data from localStorage');
    };

    return (
        <AuthContext.Provider value={{ user, token, loading, login, logout }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => useContext(AuthContext);