import React, { useState } from 'react';
import "../styles/sidebar.scss";

import { useTheme } from '../contexts/ThemeContext.jsx';
import ProfileDropdown from "./ProfileDropdown.jsx";
import { UploadPopupWithOverlay } from "./upload.jsx"; // Import the UploadPopupWithOverlay component

import SunnyIcon from '@mui/icons-material/Sunny';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import SearchIcon from '@mui/icons-material/Search';
import NotificationsIcon from '@mui/icons-material/Notifications';
import CloseIcon from '@mui/icons-material/Close';
import CircleIcon from '@mui/icons-material/Circle';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const Header = () => {
    const { darkMode, toggleTheme } = useTheme();
    const [showNotifications, setShowNotifications] = useState(false);
    const [notifications, setNotifications] = useState([
        { id: 1, text: 'New message from <PERSON>', time: '10 min ago', read: false },
        { id: 2, text: 'Your report is ready', time: '1 hour ago', read: false },
        { id: 3, text: 'System update scheduled', time: '2 days ago', read: true },
    ]);
    const [showUploadPopup, setShowUploadPopup] = useState(false); // State for upload popup

    const toggleNotifications = () => {
        setShowNotifications(!showNotifications);
        // Mark all as read when opening
        if (!showNotifications) {
            setNotifications(notifications.map(n => ({ ...n, read: true })));
        }
    };

    const unreadCount = notifications.filter(n => !n.read).length;

    return (
        <header className={`main-header ${darkMode ? 'theme-dark' : ''}`}>
            <div className="header-left">
                <div className="search-container">
                    <SearchIcon className="search-icon" />
                    <input type="text" placeholder="Search..." className="search-input" />
                </div>
            </div>
            <div className="header-right">
                <div className="notification-wrapper">
                    <button className="notification-button" onClick={toggleNotifications}>
                        <NotificationsIcon />
                        {unreadCount > 0 && (
                            <span className="notification-badge">{unreadCount}</span>
                        )}
                    </button>

                    {showNotifications && (
                        <div className="notification-popup">
                            <div className="notification-header">
                                <h3>Notifications</h3>
                                <button className="close-btn" onClick={toggleNotifications}>
                                    <CloseIcon fontSize="small" />
                                </button>
                            </div>
                            <div className="notification-list">
                                {notifications.length > 0 ? (
                                    notifications.map(notification => (
                                        <div key={notification.id}
                                            className={`notification-item ${notification.read ? 'read' : 'unread'}`}>
                                            {!notification.read &&
                                                <CircleIcon className="unread-dot" fontSize="small" />}
                                            <div className="notification-content">
                                                <p className="notification-text">{notification.text}</p>
                                                <span className="notification-time">{notification.time}</span>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="empty-notifications">
                                        No new notifications
                                    </div>
                                )}
                            </div>
                            <div className="notification-footer">
                                <button className="view-all-btn">View All Notifications</button>
                            </div>
                        </div>
                    )}
                </div>

                {/* Theme toggle commented out as in your original code */}
                <button className="theme-toggle" onClick={toggleTheme}>
                    <span className="material-icons">
                        {darkMode ? <SunnyIcon /> : <DarkModeIcon />}
                    </span>
                </button>

                <button
                    className="upload"
                    onClick={() => setShowUploadPopup(true)}
                >
                    <span className="icon">
                        <CloudUploadIcon />
                    </span>
                    <span className="text">
                        Upload
                    </span>
                </button>

                {/* Render the UploadPopupWithOverlay when showUploadPopup is true */}
                {showUploadPopup && (
                    <UploadPopupWithOverlay onClose={() => setShowUploadPopup(false)} />
                )}
            </div>
        </header>
    );
};

export default Header;