import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';
import axios from 'axios';
import '../../styles/leitner.scss';

// Material Icons
import SchoolIcon from '@mui/icons-material/School';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import BarChartIcon from '@mui/icons-material/BarChart';
import InfoIcon from '@mui/icons-material/Info';
import CloseIcon from '@mui/icons-material/Close';
import FilterListIcon from '@mui/icons-material/FilterList';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import LooksOneIcon from '@mui/icons-material/LooksOne';
import LooksTwoIcon from '@mui/icons-material/LooksTwo';
import Looks3Icon from '@mui/icons-material/Looks3';
import Looks4Icon from '@mui/icons-material/Looks4';
import Looks5Icon from '@mui/icons-material/Looks5';
import SettingsIcon from '@mui/icons-material/Settings';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ConfettiExplosion from 'react-confetti-explosion';

// Components
import LeitnerBox from './LeitnerBox';
import LeitnerStudySession from './LeitnerStudySession';
import LeitnerStats from './LeitnerStats';
import LeitnerInfo from './LeitnerInfo';

const LeitnerSystem = ({ chapterId, subjectId, onBack }) => {
  // Use ThemeContext for dark mode
  const { darkMode } = useTheme();

  // State variables
  const [cards, setCards] = useState([]);
  // Track original cards loaded from the database to avoid re-saving them
  const [originalCards, setOriginalCards] = useState([]);
  const [boxes, setBoxes] = useState({
    1: [],
    2: [],
    3: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('boxes'); // 'boxes', 'study', 'stats', 'info', 'preview'
  const [selectedBox, setSelectedBox] = useState(null);
  const [isExploding, setIsExploding] = useState(false);
  const [streak, setStreak] = useState(0);
  const [previewCards, setPreviewCards] = useState([]);

  // Define loadCards function outside useEffect so it can be called from other functions
  const loadCards = async () => {
    if (!chapterId) {
      setError('No chapter ID provided');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Fetch flashcards for this chapter
      const response = await axios.get(`http://localhost:5001/api/chapters/${chapterId}/flashcards`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success) {
        const fetchedCards = response.data.flashcards || [];

        // Store original cards to avoid re-saving them
        setOriginalCards(fetchedCards.map(card => ({
          ...card,
          isModified: false // Mark cards from database as not modified
        })));
        setCards(fetchedCards);

        // Distribute cards into Leitner boxes based on box_level
        const newBoxes = {
          1: [],
          2: [],
          3: []
        };

        // Track the highest streak among all cards
        let highestStreak = 0;

        fetchedCards.forEach(card => {
          // Convert old 5-box system to new 3-box system
          let newBoxLevel = 1;
          const oldBoxLevel = card.box_level || 1;

          if (oldBoxLevel <= 1) {
            newBoxLevel = 1; // New & difficult cards
          } else if (oldBoxLevel <= 3) {
            newBoxLevel = 2; // Learning cards
          } else {
            newBoxLevel = 3; // Mastered cards
          }

          // Update the card's box_level
          newBoxes[newBoxLevel].push({ ...card, box_level: newBoxLevel });

          // Update highest streak
          if (card.leitner_streak && card.leitner_streak > highestStreak) {
            highestStreak = card.leitner_streak;
          }
        });

        setBoxes(newBoxes);

        // Set the streak based on the highest streak found
        if (highestStreak > 0) {
          setStreak(highestStreak);
        }

        setError(null);
      } else {
        throw new Error(response.data.message || 'Failed to load flashcards');
      }
    } catch (error) {
      console.error('Failed to load flashcards for Leitner system:', error);
      setError('Failed to load flashcards. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Load cards when component mounts or chapterId changes
  useEffect(() => {
    loadCards();
  }, [chapterId]);

  // Track mastery progress
  const [totalCards, setTotalCards] = useState(0);
  const [masteryPercentage, setMasteryPercentage] = useState(0);

  // Calculate mastery progress whenever boxes change
  useEffect(() => {
    if (Object.keys(boxes).length > 0) {
      // Count total cards
      const total = Object.values(boxes).flat().length;
      setTotalCards(total);

      // Calculate mastery percentage (cards in box 3)
      const mastery = total > 0 ? (boxes[3].length / total) * 100 : 0;
      setMasteryPercentage(mastery);

      console.log(`Mastery progress: ${boxes[3].length}/${total} cards (${mastery.toFixed(1)}%)`);

      // Show confetti when all cards are in box 3
      if (total > 0 && boxes[3].length === total && boxes[3].length > 0) {
        console.log('All cards mastered! Showing celebration confetti');
        setIsExploding(true);
        setTimeout(() => setIsExploding(false), 3000);
      }
    }
  }, [boxes]);

  // Save updated boxes to the server
  const saveBoxes = async () => {
    if (!chapterId) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Flatten all boxes into a single array of cards
      const allCards = Object.values(boxes).flat();

      // Step 1: Identify only existing cards with modified Leitner data
      const cardsToUpdate = [];

      for (const card of allCards) {
        // Skip cards without an ID since we can only update Leitner data for existing flashcards
        if (!card.id) {
          console.log('Skipping card without ID - cannot update Leitner data for non-existent flashcard');
          continue; // Skip to the next card
        }

        // Skip cards that were originally loaded from the database and haven't been modified
        const originalCard = originalCards.find(oc => oc.id === card.id);

        // Include card if:
        // 1. It has been modified (different from original)
        // 2. It has the isModified flag set
        if (originalCard && (
          card.isModified ||
          card.box_level !== originalCard.box_level ||
          card.leitner_streak !== originalCard.leitner_streak ||
          card.next_review_date !== originalCard.next_review_date)) {

          // Mark as modified so we know to save it
          card.isModified = true;

          // Create a minimal object with ONLY the ID and Leitner-specific fields
          // This will update only the Leitner system data and review dates
          const cardToSave = {
            id: card.id, // Keep the ID for identifying the flashcard
            box_level: card.box_level || 1,
            chapter_id: chapterId,
            subject_id: subjectId,
            // Convert any Date objects to ISO strings
            last_reviewed: card.last_reviewed ? (card.last_reviewed instanceof Date ? card.last_reviewed.toISOString() : card.last_reviewed) : null,
            next_review_date: card.next_review_date ? (card.next_review_date instanceof Date ? card.next_review_date.toISOString() : card.next_review_date) : null,
            // Include Leitner-specific fields
            leitner_streak: card.leitner_streak || 0,
            review_count: card.review_count || 0,
            success_rate: card.success_rate || 0
          };

          cardsToUpdate.push(cardToSave);
        }
      }

      // If no cards need to be updated, return success
      if (cardsToUpdate.length === 0) {
        console.log("No cards need to be saved - all cards are unchanged from database");
        return;
      }

      console.log(`Saving ${cardsToUpdate.length} modified Leitner system records to server...`);

      // Log detailed information about the cards being updated
      console.log('Cards to update:', cardsToUpdate.map(card => ({
        id: card.id,
        box_level: card.box_level,
        last_reviewed: card.last_reviewed,
        next_review_date: card.next_review_date,
        leitner_streak: card.leitner_streak
      })));

      setLoading(true);

      // Process cards in smaller batches to avoid payload size issues
      const BATCH_SIZE = 20; // Process 20 cards at a time
      const batches = [];

      for (let i = 0; i < cardsToUpdate.length; i += BATCH_SIZE) {
        batches.push(cardsToUpdate.slice(i, i + BATCH_SIZE));
      }

      console.log(`Processing ${cardsToUpdate.length} cards in ${batches.length} batches`);

      // Process each batch
      let successCount = 0;

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} cards)`);

        // Log the batch data for debugging
        console.log('Sending batch data to server:', JSON.stringify(batch, null, 2));

        // We don't need to create a separate minimal batch since our cardsToUpdate
        // already contains only the necessary Leitner-specific fields

        try {
          // Use the existing endpoint but with our batch that only contains Leitner data
          const response = await axios.post(
            `http://localhost:5001/api/chapters/${chapterId}/flashcards`,
            {
              flashcards: batch,
              update_leitner_only: true // Add a flag to indicate we only want to update Leitner data
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );

          if (response.data.success) {
            successCount += batch.length;
            console.log(`Successfully updated Leitner data for batch ${i + 1} (${batch.length} cards)`);
          } else {
            console.error(`Failed to update Leitner data for batch ${i + 1}:`, response.data.message);
          }
        } catch (batchError) {
          console.error(`Error updating Leitner data for batch ${i + 1}:`, batchError);

          // Try updating each card individually as a fallback
          console.log(`Trying fallback approach for batch ${i + 1}...`);

          for (const card of batch) {
            try {
              // Try to update just the Leitner system data for this card
              await axios.post(
                `http://localhost:5001/api/chapters/${chapterId}/flashcards`,
                {
                  flashcards: [card],
                  update_leitner_only: true
                },
                {
                  headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                  }
                }
              );

              successCount++;
              console.log(`Successfully updated Leitner data for card ${card.id} using fallback approach`);
            } catch (cardError) {
              console.error(`Error updating Leitner data for card ${card.id}:`, cardError);
            }
          }
        }

        console.log(`Processed batch ${i + 1} (${batch.length} cards), successfully updated ${successCount} cards`);
      }

      // Update originalCards to include the newly saved cards
      if (successCount > 0) {
        const updatedOriginalCards = [...originalCards];

        // Update the Leitner-specific fields in originalCards
        for (const card of cardsToUpdate) {
          const existingIndex = updatedOriginalCards.findIndex(c => c.id === card.id);
          if (existingIndex >= 0) {
            // Update only Leitner-specific fields
            updatedOriginalCards[existingIndex] = {
              ...updatedOriginalCards[existingIndex],
              box_level: card.box_level,
              last_reviewed: card.last_reviewed,
              next_review_date: card.next_review_date,
              leitner_streak: card.leitner_streak,
              review_count: card.review_count,
              success_rate: card.success_rate,
              isModified: false
            };
          }
        }

        setOriginalCards(updatedOriginalCards);

        console.log(`Leitner boxes saved successfully. Updated ${successCount} Leitner system records.`);

        // No need to reload cards since we've already updated our state
        setLoading(false);
      } else {
        throw new Error(`Failed to save any of the ${cardsToUpdate.length} Leitner system records`);
      }
    } catch (error) {
      console.error('Failed to save Leitner boxes:', error);
      setError('Failed to save changes. Please try again later.');
      setLoading(false);
    }
  };

  // Move a card to a different box
  const moveCard = (cardId, fromBox, toBox) => {
    if (toBox < 1 || toBox > 3) return;

    console.log(`Moving card ${cardId} from box ${fromBox} to box ${toBox}`);

    setBoxes(prevBoxes => {
      // Find the card in the fromBox
      const cardIndex = prevBoxes[fromBox].findIndex(card => card.id === cardId);
      if (cardIndex === -1) {
        console.error(`Card ${cardId} not found in box ${fromBox}`);
        return prevBoxes;
      }

      // Create a copy of the card and update its box_level
      const card = {
        ...prevBoxes[fromBox][cardIndex],
        box_level: toBox,
        // Update the last_reviewed timestamp
        last_reviewed: prevBoxes[fromBox][cardIndex].last_reviewed || new Date().toISOString(),
        // Mark as modified so it will be saved
        isModified: true
      };

      console.log(`Updated card data:`, {
        id: card.id,
        box_level: card.box_level,
        last_reviewed: card.last_reviewed,
        next_review_date: card.next_review_date,
        isModified: card.isModified
      });

      // Create new boxes with the card moved
      const newBoxes = { ...prevBoxes };
      newBoxes[fromBox] = newBoxes[fromBox].filter(c => c.id !== cardId);
      newBoxes[toBox] = [...newBoxes[toBox], card];

      // Log the box counts after moving
      console.log(`Box counts after move: Box 1: ${newBoxes[1].length}, Box 2: ${newBoxes[2].length}, Box 3: ${newBoxes[3].length}`);

      // Save changes to server
      setTimeout(() => {
        saveBoxes();
      }, 100); // Small delay to ensure state is updated before saving

      return newBoxes;
    });
  };

  // Start a study session for a specific box
  const startStudySession = (boxNumber) => {
    setSelectedBox(boxNumber);
    setView('study');
  };

  // Start a preview session for a specific box
  const startPreviewSession = (boxNumber) => {
    setPreviewCards(boxes[boxNumber]);
    setSelectedBox(boxNumber);
    setView('preview');
  };

  // Handle card review result
  const handleCardReview = (cardId, isCorrect) => {
    console.log(`Card ${cardId} reviewed as ${isCorrect ? 'correct' : 'incorrect'}`);

    // Find which box the card is in
    let fromBox = null;
    let card = null;

    for (const [boxNum, boxCards] of Object.entries(boxes)) {
      const foundCard = boxCards.find(c => c.id === cardId);
      if (foundCard) {
        fromBox = parseInt(boxNum);
        card = foundCard;
        break;
      }
    }

    if (fromBox === null || !card) {
      console.error(`Card ${cardId} not found in any box`);
      return;
    }

    console.log(`Found card in box ${fromBox}:`, {
      id: card.id,
      box_level: card.box_level,
      leitner_streak: card.leitner_streak || 0
    });

    // Update card review data
    const now = new Date();
    card.last_reviewed = now.toISOString(); // Convert to ISO string for server
    card.review_count = (card.review_count || 0) + 1;
    card.isModified = true; // Mark as modified so it will be saved
    console.log(`Updated review data: last_reviewed=${card.last_reviewed}, review_count=${card.review_count}`);

    // Calculate success rate
    const successCount = isCorrect ?
      ((card.success_rate || 0) * (card.review_count - 1) + 100) :
      ((card.success_rate || 0) * (card.review_count - 1));
    card.success_rate = card.review_count > 0 ? successCount / card.review_count : 0;
    console.log(`Updated success rate: ${card.success_rate.toFixed(2)}%`);

    // Update streak
    if (isCorrect) {
      // Increment card's streak
      card.leitner_streak = (card.leitner_streak || 0) + 1;
      console.log(`Increased leitner streak to ${card.leitner_streak}`);

      // Move to next box if correct (unless already in box 3)
      if (fromBox < 3) {
        // Calculate next review date based on box level
        const nextReviewDays = fromBox === 1 ? 1 : (fromBox === 2 ? 3 : 7);
        const nextReviewDate = new Date();
        nextReviewDate.setDate(nextReviewDate.getDate() + nextReviewDays);
        card.next_review_date = nextReviewDate.toISOString(); // Convert to ISO string for server
        console.log(`Set next review date to ${nextReviewDate.toDateString()} (${nextReviewDays} days from now)`);

        console.log(`Moving card from box ${fromBox} to box ${fromBox + 1}`);

        // Explicitly set the box_level before moving the card
        card.box_level = fromBox + 1;
        console.log(`Updated box_level to ${card.box_level}`);

        // Move the card to the next box
        moveCard(cardId, fromBox, fromBox + 1);

        // Update streak and show confetti for milestones
        setStreak(prev => {
          const newStreak = prev + 1;
          console.log(`Increased session streak to ${newStreak}`);
          if (newStreak % 3 === 0) {
            console.log(`Milestone reached! Showing confetti`);
            setIsExploding(true);
            setTimeout(() => setIsExploding(false), 2000);
          }
          return newStreak;
        });
      } else if (fromBox === 3) {
        // Card is already mastered, but we'll still update the streak
        // Set next review date for mastered cards (14 days)
        const nextReviewDate = new Date();
        nextReviewDate.setDate(nextReviewDate.getDate() + 14);
        card.next_review_date = nextReviewDate.toISOString(); // Convert to ISO string for server
        console.log(`Card already in box 3 (mastered). Set next review date to ${nextReviewDate.toDateString()} (14 days from now)`);

        // Make sure box_level is explicitly set to 3
        card.box_level = 3;
        console.log(`Ensured box_level is set to ${card.box_level}`);

        setStreak(prev => {
          const newStreak = prev + 1;
          console.log(`Increased session streak to ${newStreak}`);
          if (newStreak % 3 === 0) {
            console.log(`Milestone reached! Showing confetti`);
            setIsExploding(true);
            setTimeout(() => setIsExploding(false), 2000);
          }
          return newStreak;
        });

        // Save the updated card
        console.log(`Saving mastered card updates to database`);
        saveBoxes();
      }
    } else {
      // Reset card's streak on incorrect answer
      card.leitner_streak = 0;
      console.log(`Reset leitner streak to 0`);

      // Set next review date for tomorrow
      const nextReviewDate = new Date();
      nextReviewDate.setDate(nextReviewDate.getDate() + 1);
      card.next_review_date = nextReviewDate.toISOString(); // Convert to ISO string for server
      console.log(`Set next review date to ${nextReviewDate.toDateString()} (tomorrow)`);

      // Explicitly set the box_level to 1
      card.box_level = 1;
      console.log(`Updated box_level to ${card.box_level}`);

      // Move back to box 1 if incorrect
      if (fromBox > 1) {
        console.log(`Moving card from box ${fromBox} back to box 1`);
        moveCard(cardId, fromBox, 1);
      } else {
        // If already in box 1, just save the updated card
        console.log(`Card already in box 1, saving updates`);
        saveBoxes();
      }

      // Reset session streak on incorrect answer
      console.log(`Resetting session streak to 0`);
      setStreak(0);
    }
  };

  // Render the appropriate view
  const renderView = () => {
    switch (view) {
      case 'study':
        return (
          <LeitnerStudySession
            cards={selectedBox ? boxes[selectedBox] : []}
            boxNumber={selectedBox}
            onBack={() => setView('boxes')}
            onCardReview={handleCardReview}
          />
        );
      case 'stats':
        return (
          <LeitnerStats
            boxes={boxes}
            onBack={() => setView('boxes')}
          />
        );
      case 'info':
        return (
          <LeitnerInfo
            onBack={() => setView('boxes')}
          />
        );
      case 'preview':
        return (
          <div className="leitner-preview">
            <div className="preview-header">
              <h2>
                <VisibilityIcon style={{ marginRight: '0.5rem' }} />
                Box {selectedBox} Preview
              </h2>
              <p>Previewing {previewCards.length} cards</p>
            </div>

            <div className="preview-cards">
              {previewCards.map((card, index) => (
                <motion.div
                  key={card.id}
                  className="preview-card-full"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <div className="card-number">{index + 1}</div>
                  <div className="card-content">
                    <div className="card-question">
                      <h3>Question</h3>
                      <p>{card.question}</p>
                    </div>
                    <div className="card-answer">
                      <h3>Answer</h3>
                      <p>{card.answer}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="preview-actions">
              <button className="primary" onClick={() => startStudySession(selectedBox)}>
                <PlayArrowIcon />
                Study These Cards
              </button>
            </div>
          </div>
        );
      case 'boxes':
      default:
        return (
          <>
            <div className="leitner-goal-indicator">
              <h2>Mastery Goal</h2>
              <p>Move all cards to Box 3 to master this chapter!</p>
              <div className="progress-container">
                <div
                  className="progress-bar"
                  style={{
                    width: `${masteryPercentage}%`,
                    backgroundColor: masteryPercentage > 75
                      ? '#28CD41' // Green
                      : masteryPercentage > 50
                        ? '#007AFF' // Blue
                        : masteryPercentage > 25
                          ? '#FF9500' // Orange
                          : '#FF3B30' // Red
                  }}
                ></div>
              </div>
              <div className="progress-stats">
                <span>{boxes[3].length} of {totalCards} cards mastered ({masteryPercentage.toFixed(1)}%)</span>
                {masteryPercentage === 100 && (
                  <div className="mastery-complete">
                    <CheckCircleIcon style={{ color: '#28CD41' }} />
                    <span>All cards mastered!</span>
                  </div>
                )}
              </div>
            </div>

            <div className="leitner-boxes">
              <LeitnerBox
                boxNumber={1}
                cards={boxes[1]}
                icon={<LooksOneIcon className="box-icon" />}
                onStudy={() => startStudySession(1)}
                onPreview={() => startPreviewSession(1)}
              />
              <LeitnerBox
                boxNumber={2}
                cards={boxes[2]}
                icon={<LooksTwoIcon className="box-icon" />}
                onStudy={() => startStudySession(2)}
                onPreview={() => startPreviewSession(2)}
              />
              <LeitnerBox
                boxNumber={3}
                cards={boxes[3]}
                icon={<Looks3Icon className="box-icon" />}
                onStudy={() => startStudySession(3)}
                onPreview={() => startPreviewSession(3)}
              />
            </div>

            {streak > 0 && (
              <div className="streak-counter">
                <LocalFireDepartmentIcon className="streak-icon" />
                <span>{streak} card streak</span>
              </div>
            )}
          </>
        );
    }
  };

  return (
    <div className={`leitner-system ${darkMode ? 'theme-dark' : ''}`}>
      {/* Confetti effect for achievements */}
      {isExploding && (
        <div className="confetti-container">
          <ConfettiExplosion
            force={0.8}
            duration={2000}
            particleCount={100}
            width={1600}
          />
        </div>
      )}

      <div className="leitner-header">
        <h1>
          <SchoolIcon style={{ marginRight: '0.5rem' }} />
          Leitner System
        </h1>

        <div className="leitner-controls">
          {view === 'boxes' ? (
            <>
              <button onClick={() => setView('info')}>
                <InfoIcon className="icon" />
                About
              </button>
              <button onClick={() => setView('stats')}>
                <BarChartIcon className="icon" />
                Statistics
              </button>
              <button className="primary" onClick={() => startStudySession(1)}>
                <PlayArrowIcon className="icon" />
                Start Studying
              </button>
              <button onClick={onBack}>
                <ArrowBackIcon className="icon" />
                Back to Flashcards
              </button>
            </>
          ) : (
            <button onClick={() => setView('boxes')}>
              <ArrowBackIcon className="icon" />
              Back to Boxes
            </button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading Leitner system...</p>
        </div>
      ) : error ? (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      ) : (
        renderView()
      )}
    </div>
  );
};

export default LeitnerSystem;
