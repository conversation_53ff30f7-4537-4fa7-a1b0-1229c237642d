"""
Backend Cleanup Script

This script reorganizes the Backend directory according to the recommended structure.
It creates a tests directory, moves essential test files there, and removes unnecessary files.

Usage:
    python cleanup_backend.py

Note: Run this script from the Backend directory.
"""

import os
import shutil
from pathlib import Path

# Files to keep in the root directory
ESSENTIAL_FILES = [
    '.env',
    'app.py',
    'ai_assistant.py',
    'database.py',
    'document_processor.py',
    'models.py',
    'requirements.txt',
    'cleanup_backend.py',  # Keep this script
    'RECOMMENDED_STRUCTURE.md'  # Keep the recommendation document
]

# Test files to move to the tests directory
TEST_FILES_TO_KEEP = [
    'test_ai_response.py',
    'test_db_connection.py',
    'test_rescheduling.py'  # We'll keep one consolidated test file
]

# Directories to keep
ESSENTIAL_DIRS = [
    'uploads'
]

def create_tests_directory():
    """Create a tests directory if it doesn't exist"""
    if not os.path.exists('tests'):
        os.makedirs('tests')
        print("Created tests directory")

def move_test_files():
    """Move essential test files to the tests directory"""
    for test_file in TEST_FILES_TO_KEEP:
        if os.path.exists(test_file):
            shutil.copy2(test_file, os.path.join('tests', test_file))
            print(f"Moved {test_file} to tests directory")
        else:
            print(f"Warning: {test_file} not found")

def remove_unnecessary_files():
    """Remove unnecessary files from the root directory"""
    for item in os.listdir('.'):
        # Skip directories for now
        if os.path.isdir(item) and item not in ['tests'] + ESSENTIAL_DIRS:
            continue

        # Skip essential files
        if item in ESSENTIAL_FILES:
            continue

        # Skip test files that will be moved
        if item in TEST_FILES_TO_KEEP:
            continue

        # Remove the file if it's not essential
        if os.path.isfile(item):
            print(f"Removing file: {item}")
            try:
                os.remove(item)
            except Exception as e:
                print(f"Error removing {item}: {str(e)}")

def remove_unnecessary_directories():
    """Remove unnecessary directories"""
    for item in os.listdir('.'):
        if os.path.isdir(item) and item not in ['tests'] + ESSENTIAL_DIRS:
            print(f"Removing directory: {item}")
            try:
                shutil.rmtree(item)
            except Exception as e:
                print(f"Error removing directory {item}: {str(e)}")

def main():
    """Main function to reorganize the Backend directory"""
    print("Starting Backend cleanup...")
    print(f"Current working directory: {os.getcwd()}")

    # List all files and directories in the current directory
    print("\nFiles and directories in the current directory:")
    for item in os.listdir('.'):
        if os.path.isdir(item):
            print(f"Directory: {item}")
        else:
            print(f"File: {item}")

    # Warning and confirmation
    print("\n⚠️ WARNING: This script will permanently delete files and directories! ⚠️")
    print("The following files will be kept:")
    for file in ESSENTIAL_FILES:
        print(f"  - {file}")
    print("\nThe following directories will be kept:")
    for directory in ['tests'] + ESSENTIAL_DIRS:
        print(f"  - {directory}")

    confirmation = input("\nAre you sure you want to proceed? (yes/no): ")
    if confirmation.lower() != 'yes':
        print("Operation cancelled.")
        return

    # Create tests directory
    create_tests_directory()

    # Move test files
    move_test_files()

    # Remove unnecessary files
    remove_unnecessary_files()

    # Remove unnecessary directories
    remove_unnecessary_directories()

    print("\nBackend cleanup completed!")
    print("The backend directory has been reorganized according to the recommended structure.")

if __name__ == "__main__":
    main()
