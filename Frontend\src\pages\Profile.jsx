import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON><PERSON>,
    Person,
    Palette,
    Notifications,
    Security,
    Language,
    Keyboard,
    DataUsage,
    Cloud,
    Science,
    Psychology
} from '@mui/icons-material';

import "../styles/ProfileSettings.scss";

/**
 * Advanced Profile Settings Component
 * Allows users to customize their profile and application settings
 */
const AdvancedProfileSettings = ({ onClose }) => {
    // State for active tab and user preferences
    const [activeTab, setActiveTab] = useState('personalization');
    const [theme, setTheme] = useState('system');
    const [fontSize, setFontSize] = useState(16);
    const [aiModel, setAiModel] = useState('gpt-4');
    const [dataCollection, setDataCollection] = useState(true);

    // Theme detection with MutationObserver to detect theme changes
    const [isDarkMode, setIsDarkMode] = useState(false);

    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    return (
        <div className={`advanced-profile-settings ${isDarkMode ? 'theme-dark' : ''} open`}>
            <div className="settings-header">
                <button className="back-button" onClick={onClose} aria-label="Go back">
                    <ArrowBack />
                </button>
                <h2>Advanced Settings</h2>
            </div>

            <div className="settings-container">
                <div className="settings-sidebar">
                    <button
                        className={`sidebar-item ${activeTab === 'personalization' ? 'active' : ''}`}
                        onClick={() => setActiveTab('personalization')}
                    >
                        <Palette className="sidebar-icon" />
                        <span>Personalization</span>
                    </button>

                    <button
                        className={`sidebar-item ${activeTab === 'privacy' ? 'active' : ''}`}
                        onClick={() => setActiveTab('privacy')}
                    >
                        <Security className="sidebar-icon" />
                        <span>Privacy & Security</span>
                    </button>

                    <button
                        className={`sidebar-item ${activeTab === 'ai' ? 'active' : ''}`}
                        onClick={() => setActiveTab('ai')}
                    >
                        <Psychology className="sidebar-icon" />
                        <span>AI Preferences</span>
                    </button>

                    <button
                        className={`sidebar-item ${activeTab === 'accessibility' ? 'active' : ''}`}
                        onClick={() => setActiveTab('accessibility')}
                    >
                        <Person className="sidebar-icon" />
                        <span>Accessibility</span>
                    </button>

                    <button
                        className={`sidebar-item ${activeTab === 'notifications' ? 'active' : ''}`}
                        onClick={() => setActiveTab('notifications')}
                    >
                        <Notifications className="sidebar-icon" />
                        <span>Notifications</span>
                    </button>
                </div>

                <div className="settings-content">
                    {activeTab === 'personalization' && (
                        <div className="settings-section">
                            <h3>Theme Preferences</h3>
                            <div className="theme-options">
                                <div
                                    className={`theme-option ${theme === 'light' ? 'active' : ''}`}
                                    onClick={() => setTheme('light')}
                                >
                                    <div className="light-theme-preview"></div>
                                    <span>Light</span>
                                </div>
                                <div
                                    className={`theme-option ${theme === 'dark' ? 'active' : ''}`}
                                    onClick={() => setTheme('dark')}
                                >
                                    <div className="dark-theme-preview"></div>
                                    <span>Dark</span>
                                </div>
                                <div
                                    className={`theme-option ${theme === 'system' ? 'active' : ''}`}
                                    onClick={() => setTheme('system')}
                                >
                                    <div className="system-theme-preview"></div>
                                    <span>System</span>
                                </div>
                            </div>

                            <h3>Accent Color</h3>
                            <div className="color-palette">
                                {['#0055f3', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'].map(color => (
                                    <div
                                        key={color}
                                        className="color-option"
                                        style={{ backgroundColor: color }}
                                    ></div>
                                ))}
                            </div>
                        </div>
                    )}

                    {activeTab === 'ai' && (
                        <div className="settings-section">
                            <h3>AI Model Selection</h3>
                            <div className="radio-group">
                                <label className="radio-option">
                                    <input
                                        type="radio"
                                        name="ai-model"
                                        checked={aiModel === 'gpt-4'}
                                        onChange={() => setAiModel('gpt-4')}
                                    />
                                    <span>GPT-4 (Recommended)</span>
                                    <p className="option-description">Most advanced model with best performance</p>
                                </label>
                                <label className="radio-option">
                                    <input
                                        type="radio"
                                        name="ai-model"
                                        checked={aiModel === 'gpt-3.5'}
                                        onChange={() => setAiModel('gpt-3.5')}
                                    />
                                    <span>GPT-3.5</span>
                                    <p className="option-description">Faster response times, slightly less accurate</p>
                                </label>
                            </div>

                            <h3>Learning Style</h3>
                            <select className="dropdown-select">
                                <option>Visual Learner</option>
                                <option>Auditory Learner</option>
                                <option>Kinesthetic Learner</option>
                                <option>Reading/Writing Learner</option>
                            </select>

                            <h3>Response Length</h3>
                            <div className="slider-container">
                                <input
                                    type="range"
                                    min="50"
                                    max="500"
                                    defaultValue="200"
                                    className="slider"
                                />
                                <div className="slider-labels">
                                    <span>Concise</span>
                                    <span>Detailed</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'privacy' && (
                        <div className="settings-section">
                            <h3>Data Collection</h3>
                            <div className="toggle-option">
                                <label className="switch">
                                    <input
                                        type="checkbox"
                                        checked={dataCollection}
                                        onChange={(e) => setDataCollection(e.target.checked)}
                                    />
                                    <span className="slider round"></span>
                                </label>
                                <div className="toggle-label">
                                    <span>Share usage data to improve experience</span>
                                    <p className="option-description">
                                        Helps us improve our AI models and features (anonymous)
                                    </p>
                                </div>
                            </div>

                            <h3>Clear Learning History</h3>
                            <button className="danger-button">
                                Delete All Session Data
                            </button>

                            <h3>Export Data</h3>
                            <button className="secondary-button">
                                Download All My Data
                            </button>
                        </div>
                    )}

                    {activeTab === 'accessibility' && (
                        <div className="settings-section">
                            <h3>Text Size</h3>
                            <div className="slider-container">
                                <input
                                    type="range"
                                    min="12"
                                    max="24"
                                    value={fontSize}
                                    onChange={(e) => setFontSize(e.target.value)}
                                    className="slider"
                                />
                                <div className="slider-value">{fontSize}px</div>
                            </div>

                            <h3>Keyboard Shortcuts</h3>
                            <div className="shortcut-list">
                                <div className="shortcut-item">
                                    <span>New chat</span>
                                    <span className="shortcut-key">⌘ + N</span>
                                </div>
                                <div className="shortcut-item">
                                    <span>Focus search</span>
                                    <span className="shortcut-key">⌘ + K</span>
                                </div>
                                <div className="shortcut-item">
                                    <span>Toggle theme</span>
                                    <span className="shortcut-key">⌘ + T</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'notifications' && (
                        <div className="settings-section">
                            <h3>Notification Preferences</h3>
                            <div className="toggle-option">
                                <label className="switch">
                                    <input type="checkbox" defaultChecked />
                                    <span className="slider round"></span>
                                </label>
                                <span>Enable notifications</span>
                            </div>

                            <div className="toggle-option">
                                <label className="switch">
                                    <input type="checkbox" defaultChecked />
                                    <span className="slider round"></span>
                                </label>
                                <span>Learning reminders</span>
                            </div>

                            <div className="toggle-option">
                                <label className="switch">
                                    <input type="checkbox" />
                                    <span className="slider round"></span>
                                </label>
                                <span>New feature announcements</span>
                            </div>

                            <h3>Do Not Disturb</h3>
                            <div className="time-picker">
                                <select>
                                    {Array.from({ length: 24 }, (_, i) => (
                                        <option key={i} value={i}>{i}:00</option>
                                    ))}
                                </select>
                                <span>to</span>
                                <select>
                                    {Array.from({ length: 24 }, (_, i) => (
                                        <option key={i} value={i}>{i}:00</option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default AdvancedProfileSettings;