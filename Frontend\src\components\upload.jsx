import React, { useState, useEffect } from 'react';
import ReactD<PERSON> from 'react-dom';
import axios from 'axios';
import { useAuth } from '../../authContext';
import { useNavigate } from 'react-router-dom';
import '../styles/upload.scss';
import DeleteIcon from '@mui/icons-material/Delete';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DocumentProcessor from './DocumentProcessor';

const MessagePopup = ({ message, isError = false, onClose }) => {
    useEffect(() => {
        let timer;
        if (!isError) {
            timer = setTimeout(() => {
                onClose();
            }, 2000); // Auto-close after 2 seconds for success messages
        }
        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [isError, onClose]);

    // Determine if dark mode is active
    const isDarkMode = document.body.classList.contains('theme-dark');
    const themeClass = isDarkMode ? 'theme-dark' : '';

    return (
        <div className="message-popup-overlay">
            <div className="message-popup-container">
                <div className="popup-header">
                    <h2>{isError ? 'Error' : 'Success'}</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>
                <div className="popup-content">
                    <p>{message}</p>
                </div>
            </div>
        </div>
    );
};

const UploadPopup = ({ onClose }) => {
    const { user, token, logout } = useAuth();
    const navigate = useNavigate();
    const [currentStep, setCurrentStep] = useState(0);
    const [files, setFiles] = useState([]);
    const [uploadProgress, setUploadProgress] = useState({});
    const [isUploading, setIsUploading] = useState(false);
    const [availableSubjects, setAvailableSubjects] = useState([]);
    const [availableTools, setAvailableTools] = useState([]);
    const [newSubject, setNewSubject] = useState('');
    const [subjectError, setSubjectError] = useState('');
    const [messagePopup, setMessagePopup] = useState({ show: false, message: '', isError: false });
    const [showDocumentProcessor, setShowDocumentProcessor] = useState(false);
    const [uploadedDocumentId, setUploadedDocumentId] = useState(null);
    const [formData, setFormData] = useState({
        subjectId: '',
        fileType: '',
        studyTools: [],
        fileName: ''
    });
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

    // Log state changes for debugging
    useEffect(() => {
        console.log('formData.subjectId:', formData.subjectId, 'Type:', typeof formData.subjectId);
        console.log('availableSubjects:', availableSubjects);
    }, [formData.subjectId, availableSubjects]);

    // Step configuration
    const steps = [
        {
            id: 'subject-selection',
            title: 'Select or Create Subject',
            fields: [
                {
                    type: 'subject-selection',
                    required: true
                }
            ],
            validate: () => formData.subjectId || newSubject
        },
        {
            id: 'file-type',
            title: 'Select File Type',
            fields: [
                {
                    type: 'file-type-selector',
                    options: [
                        { value: 'pdf', label: 'PDF', icon: '📝' },
                        { value: 'audio', label: 'Audio', icon: '🎵' },
                        { value: 'video', label: 'Video', icon: '🎥' },
                        { value: 'youtube', label: 'YouTube Link', icon: '▶️' }
                    ],
                    required: true
                },
                {
                    type: 'file-upload',
                    required: true
                }
            ],
            validate: () => formData.fileType && (formData.fileType === 'youtube' ? formData.fileName : files.length > 0)
        },
        {
            id: 'study-tools',
            title: 'Select Study Tools',
            fields: [
                {
                    type: 'study-tools',
                    options: availableTools,
                    required: false
                }
            ],
            validate: () => true
        },
        {
            id: 'upload',
            title: 'Confirm and Upload',
            fields: [
                {
                    type: 'summary',
                    fields: [
                        {
                            label: 'Subject',
                            value: (() => {
                                const subject = availableSubjects.find(s => String(s.id) === String(formData.subjectId));
                                if (!subject && formData.subjectId) {
                                    console.error(`Subject not found for ID: ${formData.subjectId}, availableSubjects:`, availableSubjects);
                                    return 'Unknown subject';
                                }
                                return subject?.name || 'Not specified';
                            })()
                        },
                        { label: 'File Type', value: formData.fileType },
                        {
                            label: 'Study Tools',
                            value: formData.studyTools.length > 0
                                ? formData.studyTools.map(toolId => {
                                    const tool = availableTools.find(t => t.value === toolId);
                                    return tool ? tool.label : toolId;
                                }).join(', ')
                                : 'None'
                        },
                        { label: 'File/Link', value: formData.fileName }
                    ]
                },
                {
                    type: 'progress',
                    show: isUploading
                }
            ],
            validate: () => formData.subjectId
        }
    ];

    // Fetch subjects and study tools on mount
    useEffect(() => {
        if (!user || !token) {
            console.error('UploadPopup.jsx: No user or token. Redirecting to login.');
            navigate('/login', { replace: true });
            return;
        }

        const fetchData = async () => {
            try {
                // Fetch subjects
                const subjectsResponse = await axios.get(`${API_URL}/api/subjects`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                if (subjectsResponse.data.success) {
                    const subjects = subjectsResponse.data.subjects;
                    setAvailableSubjects(subjects);
                    console.log('Fetched subjects:', subjects);
                    // Log subject ID types for debugging
                    console.log('Subject ID types:', subjects.map(s => ({ id: s.id, type: typeof s.id })));
                }

                // Fetch study tools
                const toolsResponse = await axios.get(`${API_URL}/api/study-tools`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                if (toolsResponse.data.success) {
                    // Convert tool IDs to strings to ensure consistent comparison
                    setAvailableTools(toolsResponse.data.tools.map(tool => ({
                        value: String(tool.id),
                        label: tool.name,
                        description: tool.description
                    })));
                    console.log('Fetched tools:', toolsResponse.data.tools);
                }
            } catch (error) {
                console.error('UploadPopup.jsx: Error fetching data:', error);
                if (error.response?.status === 403) {
                    console.error('UploadPopup.jsx: Invalid token. Redirecting to login.');
                    logout();
                    navigate('/login', { replace: true });
                } else {
                    setMessagePopup({
                        show: true,
                        message: 'Failed to load data. Please try again.',
                        isError: true
                    });
                }
            }
        };

        fetchData();
    }, [user, token, navigate, logout]);

    // File handling
    const handleDragOver = (e) => {
        e.preventDefault();
        e.currentTarget.classList.add('drag-active');
    };

    const handleDragLeave = (e) => {
        e.currentTarget.classList.remove('drag-active');
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-active');
        const droppedFiles = Array.from(e.dataTransfer.files);
        setFiles(droppedFiles);
        setFormData(prev => ({ ...prev, fileName: droppedFiles[0]?.name || '' }));
    };

    const handleFileChange = (e) => {
        const selectedFiles = Array.from(e.target.files);
        setFiles(selectedFiles);
        setFormData(prev => ({ ...prev, fileName: selectedFiles[0]?.name || '' }));
    };

    const handleYouTubeLinkChange = (e) => {
        setFormData(prev => ({ ...prev, fileName: e.target.value }));
    };

    const removeFile = () => {
        setFiles([]);
        setFormData(prev => ({ ...prev, fileName: '' }));
    };

    // Subject handling
    const handleSubjectInputChange = (e) => {
        const inputValue = e.target.value;
        setNewSubject(inputValue);
        setSubjectError('');

        // Check if subject exists
        const existingSubject = availableSubjects.find(
            subject => subject.name.toLowerCase() === inputValue.toLowerCase()
        );

        if (existingSubject) {
            setSubjectError(`Subject "${inputValue}" already exists. Please select it from the list or choose a different name.`);
        }
    };

    const createSubject = async () => {
        if (!newSubject) {
            setMessagePopup({
                show: true,
                message: 'Please enter a subject name.',
                isError: true
            });
            return;
        }

        // Check if subject exists
        const existingSubject = availableSubjects.find(
            subject => subject.name.toLowerCase() === newSubject.toLowerCase()
        );

        if (existingSubject) {
            setSubjectError(`Subject "${newSubject}" already exists. Please select it from the list or choose a different name.`);
            return;
        }

        try {
            const response = await axios.post(`${API_URL}/api/subjects`, { name: newSubject }, {
                headers: { Authorization: `Bearer ${token}` }
            });
            if (response.data.success) {
                const newSubjectData = response.data.subject;
                setAvailableSubjects(prev => [...prev, newSubjectData]);
                setFormData(prev => ({ ...prev, subjectId: String(newSubjectData.id) }));
                setNewSubject('');
                setCurrentStep(1); // Move to next step
                console.log('Created subject:', newSubjectData);
            }
        } catch (error) {
            console.error('UploadPopup.jsx: Error creating subject:', error);
            setMessagePopup({
                show: true,
                message: 'Failed to create subject. Please try again.',
                isError: true
            });
        }
    };

    // Upload handling
    const simulateDelayedProgress = () => {
        let progress = 0;
        const totalDuration = 20000; // 20 seconds
        const increment = 100 / (totalDuration / 200); // Update every 200ms

        const interval = setInterval(() => {
            progress += increment;
            setUploadProgress({ 0: Math.min(progress, 100) });
            if (progress >= 100) clearInterval(interval);
        }, 200);
    };

    // Function to track real upload progress
    const trackUploadProgress = (progressEvent) => {
        if (progressEvent.lengthComputable) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress({ 0: percentCompleted });
            console.log(`Upload progress: ${percentCompleted}%`);
        }
    };

    const handleUpload = async () => {
        if (!formData.subjectId) {
            setMessagePopup({
                show: true,
                message: 'Please select or create a subject.',
                isError: true
            });
            return;
        }

        setIsUploading(true);
        setUploadProgress({ 0: 0 });

        try {
            const subjectId = formData.subjectId;
            // Find the names of the selected tools for better logging
            const selectedToolNames = formData.studyTools.map(toolId => {
                const tool = availableTools.find(t => t.value === toolId);
                return tool ? `${tool.label} (ID: ${tool.value})` : `Unknown tool (ID: ${toolId})`;
            });

            const payload = {
                subject_id: subjectId,
                file_type: formData.fileType,
                study_tools: JSON.stringify(formData.studyTools),
                ...(formData.fileType === 'youtube' ? { youtube_url: formData.fileName } : {})
            };

            console.log('Selected tools:', selectedToolNames);
            console.log('Uploading with payload:', payload);
            if (formData.fileType !== 'youtube') {
                console.log('File included:', files[0]);
            }

            let response;
            if (formData.fileType === 'youtube') {
                // Handle YouTube link
                response = await axios.post(`${API_URL}/api/files/upload`, payload, {
                    headers: { Authorization: `Bearer ${token}` }
                });
            } else {
                // Handle file upload
                const formDataObj = new FormData();
                formDataObj.append('file', files[0]);
                formDataObj.append('subject_id', subjectId);
                formDataObj.append('file_type', formData.fileType);
                formDataObj.append('study_tools', JSON.stringify(formData.studyTools));

                response = await axios.post(`${API_URL}/api/files/upload`, formDataObj, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'multipart/form-data'
                    },
                    onUploadProgress: trackUploadProgress
                });
            }

            console.log('Upload response:', response.data);
            setIsUploading(false);

            if (response.data.success) {
                console.log('Full API response:', response.data);

                // Get the subject ID from the response or use the one we sent
                const subjectId = response.data.subject?.id || formData.subjectId;

                // Show the document processor with the subject ID
                setUploadedDocumentId(subjectId);
                setShowDocumentProcessor(true);
                console.log('Using subject ID for document processor:', subjectId);
            } else {
                throw new Error(response.data.message || 'Upload failed');
            }
        } catch (error) {
            console.error('UploadPopup.jsx: Upload failed:', error);
            setIsUploading(false);
            if (error.response?.status === 403) {
                console.error('UploadPopup.jsx: Invalid token. Redirecting to login.');
                logout();
                navigate('/login', { replace: true });
            } else {
                setMessagePopup({
                    show: true,
                    message: `Upload failed: ${error.response?.data?.message || error.message}`,
                    isError: true
                });
            }
        }
    };

    const handleMessagePopupClose = () => {
        setMessagePopup({ show: false, message: '', isError: false });
        if (!messagePopup.isError) {
            onClose();
            navigate('/dashboard');
        }
    };

    // Navigation
    const nextStep = () => {
        if (!steps[currentStep].validate()) {
            setMessagePopup({
                show: true,
                message: 'Please complete all required fields.',
                isError: true
            });
            return;
        }
        if (currentStep === 0 && newSubject && !formData.subjectId) {
            createSubject();
        } else if (currentStep < steps.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    // Formatting
    const formatFileSize = (bytes) => {
        if (!bytes || bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Render fields
    const renderField = (field) => {
        switch (field.type) {
            case 'subject-selection':
                return (
                    <div className="form-group">
                        {availableSubjects.length === 0 ? (
                            <>
                                <label>Or create a new subject</label>
                                <input
                                    type="text"
                                    placeholder="Enter new subject name"
                                    value={newSubject}
                                    onChange={handleSubjectInputChange}
                                    className="macos-input"
                                />
                                {subjectError && <p className="error-message">{subjectError}</p>}
                            </>
                        ) : (
                            <>
                                <label>Select subject</label>
                                <select
                                    className="macos-select"
                                    value={formData.subjectId}
                                    onChange={(e) => {
                                        const selectedSubjectId = e.target.value;
                                        setFormData(prev => ({ ...prev, subjectId: selectedSubjectId }));
                                        setSubjectError('');
                                        setNewSubject('');
                                        console.log('Selected subject ID:', selectedSubjectId, 'Type:', typeof selectedSubjectId);
                                    }}
                                >
                                    <option value="">Select a subject</option>
                                    {availableSubjects.map(subject => (
                                        <option key={subject.id} value={subject.id}>{subject.name}</option>
                                    ))}
                                </select>
                                {!formData.subjectId && (
                                    <>
                                        <p>Or create a new subject:</p>
                                        <input
                                            type="text"
                                            placeholder="Enter new subject name"
                                            value={newSubject}
                                            onChange={handleSubjectInputChange}
                                            className="macos-input"
                                        />
                                        {subjectError && <p className="error-message">{subjectError}</p>}
                                    </>
                                )}
                            </>
                        )}
                    </div>
                );
            case 'file-type-selector':
                return (
                    <div className="file-type-selector">
                        <div className="options-grid">
                            {field.options.map(option => (
                                <button
                                    key={option.value}
                                    type="button"
                                    className={`macos-button ${formData.fileType === option.value ? 'active' : ''}`}
                                    onClick={() => setFormData(prev => ({ ...prev, fileType: option.value }))}
                                >
                                    <span className="option-icon">{option.icon}</span>
                                    {option.label}
                                </button>
                            ))}
                        </div>
                    </div>
                );
            case 'file-upload':
                return formData.fileType === 'youtube' ? (
                    <div className="form-group">
                        <label>YouTube Link</label>
                        <input
                            type="text"
                            placeholder="Enter YouTube URL"
                            value={formData.fileName}
                            onChange={handleYouTubeLinkChange}
                            className="macos-input"
                            required
                        />
                    </div>
                ) : (
                    <div
                        className="drop-zone"
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                    >
                        <input
                            type="file"
                            multiple
                            onChange={handleFileChange}
                            style={{ display: 'none' }}
                            id="file-input"
                            accept={formData.fileType === 'pdf' ? '.pdf' : formData.fileType === 'doc' ? '.doc,.docx' : formData.fileType === 'audio' ? 'audio/*' : formData.fileType === 'video' ? 'video/*' : '*'}
                        />
                        <div className="drop-area">
                            <h2>Drag and drop files here</h2>
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                            <p className="hint">or click to browse</p>
                        </div>
                        <button
                            className="macos-button"
                            onClick={() => document.getElementById('file-input').click()}
                        >
                            Browse Files
                        </button>
                    </div>
                );
            case 'study-tools':
                return (
                    <div className="form-group">
                        <p className="tool-selection-info">Select the study tools you want to use with this material:</p>
                        <div className="options-grid">
                            {field.options.map(option => (
                                <div key={option.value} className="tool-option-container">
                                    <button
                                        type="button"
                                        className={`macos-button tool-button ${formData.studyTools.includes(option.value) ? 'active' : ''}`}
                                        onClick={() => setFormData(prev => {
                                            const newTools = prev.studyTools.includes(option.value)
                                                ? prev.studyTools.filter(t => t !== option.value)
                                                : [...prev.studyTools, option.value];
                                            return { ...prev, studyTools: newTools };
                                        })}
                                        title={option.description}
                                    >
                                        {option.label}
                                        {formData.studyTools.includes(option.value) && (
                                            <span className="tool-selected-indicator">✓</span>
                                        )}
                                    </button>
                                    {option.description && (
                                        <p className="tool-description">{option.description}</p>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                );
            case 'summary':
                return (
                    <div className="upload-summary">
                        <h4 className="summary-title">Please confirm your selections:</h4>
                        {field.fields.map((item, index) => (
                            <div key={index} className="summary-item">
                                <span className="summary-label">{item.label}:</span>
                                <span className="summary-value">
                                    {item.label === 'Study Tools' && item.value !== 'None' ? (
                                        <div className="selected-tools-list">
                                            {item.value.split(', ').map((tool, i) => (
                                                <div key={i} className="selected-tool-item">
                                                    <span className="tool-check-icon">✓</span>
                                                    {tool}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        item.value
                                    )}
                                </span>
                            </div>
                        ))}
                    </div>
                );
            case 'progress':
                return field.show && (
                    <div className="upload-progress-container">
                        <div className="progress-header">
                            <span>Uploading file...</span>
                            <span>{Math.round(uploadProgress[0] || 0)}%</span>
                        </div>
                        <div className="progress-bar-container">
                            <div className="progress-bar" style={{ width: `${uploadProgress[0] || 0}%` }}></div>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    // Render buttons
    const renderButtons = () => {
        const isLastStep = currentStep === steps.length - 1;
        const isFirstStep = currentStep === 0;
        return (
            <div className="popup-footer">
                {!isFirstStep && (
                    <button className="prev" onClick={prevStep}>
                        <ArrowBackIcon /> Back
                    </button>
                )}
                {!isLastStep ? (
                    <button
                        className="macos-button primary"
                        onClick={nextStep}
                        disabled={isFirstStep && !formData.subjectId && !newSubject}
                    >
                        {isFirstStep && newSubject && !formData.subjectId ? (
                            <>Create New Subject <ArrowForwardIcon /></>
                        ) : (
                            <>Next <ArrowForwardIcon /></>
                        )}
                    </button>
                ) : (
                    <button
                        className="macos-button primary"
                        onClick={handleUpload}
                        disabled={isUploading}
                    >
                        {isUploading ? (
                            <>
                                <span className="spinner"></span> Uploading...
                            </>
                        ) : (
                            <>Complete Upload <ArrowForwardIcon /></>
                        )}
                    </button>
                )}
            </div>
        );
    };

    // Determine if dark mode is active
    const isDarkMode = document.body.classList.contains('theme-dark');

    return (
        <>
            <div className="upload-popup-container" style={{
                zIndex: 100000,
                position: 'relative',
                maxHeight: '90vh',
                overflowY: 'auto'
            }}>
                <div className="popup-header-main">
                    <h2>Upload Materials</h2>
                    <button className="close-button-popup" onClick={onClose}>×</button>
                </div>

                <div className="popup-content">
                    <div className="step-content">
                        <h3>{steps[currentStep].title}</h3>
                        {steps[currentStep].fields.map((field, index) => (
                            <div key={index}>{renderField(field)}</div>
                        ))}
                        {files.length > 0 && currentStep === 1 && formData.fileType !== 'youtube' && (
                            <div className="selected-files-preview">
                                <div className="file-card">
                                    <div className="file-icon">
                                        {steps[1].fields[0].options.find(opt => opt.value === formData.fileType)?.icon || '📄'}
                                    </div>
                                    <div className="file-info">
                                        <p className="file-name">{formData.fileName}</p>
                                        <p className="file-size">{files[0] && formatFileSize(files[0].size)}</p>
                                    </div>
                                    <button className="danger" onClick={removeFile}>
                                        <DeleteIcon />
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                {renderButtons()}
            </div>

            {messagePopup.show && (
                <MessagePopup
                    message={messagePopup.message}
                    isError={messagePopup.isError}
                    onClose={handleMessagePopupClose}
                />
            )}

            {showDocumentProcessor && (
                <DocumentProcessor
                    documentId={uploadedDocumentId}
                    documentName={formData.fileName}
                    selectedTools={formData.studyTools}
                    onClose={() => {
                        setShowDocumentProcessor(false);
                        onClose();
                        navigate('/dashboard');
                    }}
                />
            )}
        </>
    );
};

/**
 * Upload Popup with Overlay Component
 * Wraps the UploadPopup component with an overlay
 */
const UploadPopupWithOverlay = ({ onClose }) => {
    // Add overlay-active class to body when component mounts
    useEffect(() => {
        document.body.classList.add('overlay-active');

        // Remove overlay-active class from body when component unmounts
        return () => {
            document.body.classList.remove('overlay-active');
        };
    }, []);

    // Custom close handler to ensure cleanup
    const handleClose = () => {
        document.body.classList.remove('overlay-active');
        onClose();
    };

    // Handle click on overlay background to close
    const handleOverlayClick = (e) => {
        // Only close if clicking directly on the overlay, not its children
        if (e.target === e.currentTarget) {
            handleClose();
        }
    };

    // Create portal to render at document body level
    return ReactDOM.createPortal(
        <div
            className="message-popup-overlay"
            onClick={handleOverlayClick}
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                width: '100vw',
                height: '100vh',
                zIndex: 999999,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                backdropFilter: 'blur(5px)'
            }}
        >
            <UploadPopup onClose={handleClose} />
        </div>,
        document.body
    );
};

export { UploadPopup, UploadPopupWithOverlay };
export default UploadPopupWithOverlay;