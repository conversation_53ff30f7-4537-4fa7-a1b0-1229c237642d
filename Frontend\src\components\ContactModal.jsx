import React, { useState, useEffect, useRef } from 'react';
import '../styles/ContactModal.scss';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import HelpIcon from '@mui/icons-material/Help';
import BugReportIcon from '@mui/icons-material/BugReport';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import FeedbackIcon from '@mui/icons-material/Feedback';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import DeleteIcon from '@mui/icons-material/Delete';
import { useTheme } from '../contexts/ThemeContext';

const ContactModal = ({ isOpen, onClose, user }) => {
  const { darkMode } = useTheme();
  const [activeTab, setActiveTab] = useState('general');
  const [formData, setFormData] = useState({
    name: user?.fullName || '',
    email: user?.email || '',
    subject: '',
    message: '',
    priority: 'normal',
    category: 'general',
    attachments: []
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [characterCount, setCharacterCount] = useState(0);
  const modalRef = useRef(null);
  const fileInputRef = useRef(null);

  // Categories for the tabs
  const categories = [
    { id: 'general', label: 'General Inquiry', icon: <HelpIcon /> },
    { id: 'bug', label: 'Report a Bug', icon: <BugReportIcon /> },
    { id: 'feature', label: 'Feature Request', icon: <LightbulbIcon /> },
    { id: 'feedback', label: 'Feedback', icon: <FeedbackIcon /> }
  ];

  // Priority options
  const priorities = [
    { id: 'low', label: 'Low', color: '#28CD41' },
    { id: 'normal', label: 'Normal', color: '#007AFF' },
    { id: 'high', label: 'High', color: '#FF9500' },
    { id: 'urgent', label: 'Urgent', color: '#FF3B30' }
  ];

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Handle tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    setFormData(prev => ({
      ...prev,
      category: tabId
    }));
  };

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (name === 'message') {
      setCharacterCount(value.length);
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      // Limit to 3 files
      const newFiles = [...formData.attachments, ...files].slice(0, 3);
      setFormData(prev => ({
        ...prev,
        attachments: newFiles
      }));
    }
  };

  // Remove attachment
  const removeAttachment = (index) => {
    const newAttachments = [...formData.attachments];
    newAttachments.splice(index, 1);
    setFormData(prev => ({
      ...prev,
      attachments: newAttachments
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }
    
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.length < 10) {
      newErrors.message = 'Message must be at least 10 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      setIsSubmitting(true);
      
      // Simulate API call
      setTimeout(() => {
        console.log('Form submitted:', formData);
        setIsSubmitting(false);
        setIsSubmitted(true);
        
        // Reset form after 3 seconds
        setTimeout(() => {
          handleClose();
        }, 3000);
      }, 1500);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      // Reset form state after closing
      setTimeout(() => {
        setActiveTab('general');
        setFormData({
          name: user?.fullName || '',
          email: user?.email || '',
          subject: '',
          message: '',
          priority: 'normal',
          category: 'general',
          attachments: []
        });
        setErrors({});
        setIsSubmitted(false);
        setCharacterCount(0);
      }, 300);
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`contact-modal-overlay ${darkMode ? 'theme-dark' : ''}`}>
      <div className="contact-modal" ref={modalRef}>
        <div className="modal-header">
          <h2>Contact Us</h2>
          <button className="close-button" onClick={handleClose} disabled={isSubmitting}>
            <CloseIcon />
          </button>
        </div>
        
        {isSubmitted ? (
          <div className="success-message">
            <CheckCircleIcon className="success-icon" />
            <h3>Thank you for contacting us!</h3>
            <p>We've received your message and will get back to you soon.</p>
          </div>
        ) : (
          <>
            <div className="tabs-container">
              {categories.map(category => (
                <div
                  key={category.id}
                  className={`tab ${activeTab === category.id ? 'active' : ''}`}
                  onClick={() => handleTabChange(category.id)}
                >
                  <div className="tab-icon">{category.icon}</div>
                  <span>{category.label}</span>
                </div>
              ))}
            </div>
            
            <form onSubmit={handleSubmit} className="contact-form">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Your name"
                    className={errors.name ? 'error' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.name && <div className="error-message"><ErrorIcon fontSize="small" /> {errors.name}</div>}
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Your email address"
                    className={errors.email ? 'error' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.email && <div className="error-message"><ErrorIcon fontSize="small" /> {errors.email}</div>}
                </div>
              </div>
              
              <div className="form-group">
                <label htmlFor="subject">Subject</label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  placeholder="Subject of your message"
                  className={errors.subject ? 'error' : ''}
                  disabled={isSubmitting}
                />
                {errors.subject && <div className="error-message"><ErrorIcon fontSize="small" /> {errors.subject}</div>}
              </div>
              
              <div className="form-group">
                <label htmlFor="message">Message</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="How can we help you?"
                  rows="5"
                  className={errors.message ? 'error' : ''}
                  disabled={isSubmitting}
                ></textarea>
                {errors.message && <div className="error-message"><ErrorIcon fontSize="small" /> {errors.message}</div>}
                <div className="character-count">{characterCount} characters</div>
              </div>
              
              <div className="form-row">
                <div className="form-group priority-group">
                  <label>Priority</label>
                  <div className="priority-options">
                    {priorities.map(priority => (
                      <div
                        key={priority.id}
                        className={`priority-option ${formData.priority === priority.id ? 'active' : ''}`}
                        onClick={() => setFormData(prev => ({ ...prev, priority: priority.id }))}
                        style={{ 
                          '--priority-color': priority.color,
                          opacity: isSubmitting ? 0.7 : 1,
                          pointerEvents: isSubmitting ? 'none' : 'auto'
                        }}
                      >
                        <span className="priority-dot"></span>
                        <span className="priority-label">{priority.label}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="form-group attachment-group">
                  <label>Attachments ({formData.attachments.length}/3)</label>
                  <div className="attachment-container">
                    <button
                      type="button"
                      className="attachment-button"
                      onClick={() => fileInputRef.current.click()}
                      disabled={formData.attachments.length >= 3 || isSubmitting}
                    >
                      <AttachFileIcon /> Add File
                    </button>
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                      multiple
                      accept="image/*,.pdf,.doc,.docx,.txt"
                      disabled={formData.attachments.length >= 3 || isSubmitting}
                    />
                  </div>
                </div>
              </div>
              
              {formData.attachments.length > 0 && (
                <div className="attachments-list">
                  {formData.attachments.map((file, index) => (
                    <div key={index} className="attachment-item">
                      <span className="attachment-name">{file.name}</span>
                      <button
                        type="button"
                        className="remove-attachment"
                        onClick={() => removeAttachment(index)}
                        disabled={isSubmitting}
                      >
                        <DeleteIcon fontSize="small" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="form-actions">
                <button
                  type="button"
                  className="cancel-button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="submit-button"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="spinner"></div>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <SendIcon />
                      <span>Send Message</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
};

export default ContactModal;
