import { useState, useEffect, useCallback, useMemo, useRef, useTransition } from 'react';
import axios from 'axios';
import {
    DndContext,
    DragOverlay,
    useDraggable,
    useDroppable,
    closestCenter,
    PointerSensor,
    KeyboardSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { useTheme } from '../../../contexts/ThemeContext';
import './EisenhowerMatrix.scss';
import debounce from 'lodash/debounce';

// Custom modifier to align dragged element with cursor
const adjustTranslate = ({ transform, active }) => {
    if (!active?.rect || !active?.data?.current) return transform;

    const { width, height } = active.rect.current.initial;
    return {
        ...transform,
        x: transform.x - width / 2,
        y: transform.y - height / 2,
    };
};

const EisenhowerMatrix = ({ subject, onClose }) => {
    const { darkMode } = useTheme();
    const [tasks, setTasks] = useState({
        'Urgent-Important': [],
        'Not Urgent-Important': [],
        'Urgent-Not Important': [],
        'Not Urgent-Not Important': [],
    });
    const [newTask, setNewTask] = useState({
        task: '',
        due_date: '',
        priority: 'medium',
    });
    const [selectedQuadrant, setSelectedQuadrant] = useState('Urgent-Important');
    const [editMode, setEditMode] = useState({ taskId: null, quadrant: null });
    const [filter, setFilter] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [activeTask, setActiveTask] = useState(null);
    const [isPending, startTransition] = useTransition();
    const activeTaskRef = useRef(null);

    const token = localStorage.getItem('token');
    const subjectId = subject?.id;

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 2, // Reduced for quicker response
            },
        }),
        useSensor(KeyboardSensor)
    );

    const fetchTasks = useCallback(async () => {
        try {
            setLoading(true);
            setError('');
            const url = subjectId ? `/api/eisenhower?subjectId=${subjectId}` : '/api/eisenhower';
            const response = await axios.get(url, {
                headers: { Authorization: `Bearer ${token}` },
            });

            if (response.data.success) {
                const groupedTasks = {
                    'Urgent-Important': [],
                    'Not Urgent-Important': [],
                    'Urgent-Not Important': [],
                    'Not Urgent-Not Important': [],
                };
                response.data.tasks.forEach((task) => {
                    if (groupedTasks[task.quadrant]) {
                        groupedTasks[task.quadrant].push(task);
                    }
                });
                setTasks(groupedTasks);
            }
        } catch (error) {
            console.error('Error fetching tasks:', error);
            setError('Failed to load tasks');
        } finally {
            setLoading(false);
        }
    }, [subjectId, token]);

    useEffect(() => {
        fetchTasks();
    }, [fetchTasks]);

    const addTask = useCallback(async () => {
        if (!newTask.task.trim()) return;

        try {
            setLoading(true);
            const response = await axios.post(
                '/api/eisenhower',
                {
                    subject_id: subjectId,
                    task: newTask.task,
                    quadrant: selectedQuadrant,
                    priority: newTask.priority,
                    due_date: newTask.due_date || null,
                },
                { headers: { Authorization: `Bearer ${token}` } }
            );

            if (response.data.success) {
                setTasks((prev) => ({
                    ...prev,
                    [selectedQuadrant]: [...prev[selectedQuadrant], response.data.task],
                }));
                setNewTask({ task: '', due_date: '', priority: 'medium' });
                setShowAddForm(false);
            }
        } catch (error) {
            console.error('Error adding task:', error);
            setError('Failed to add task');
            await fetchTasks(); // Fallback to refresh tasks
        } finally {
            setLoading(false);
        }
    }, [newTask, selectedQuadrant, subjectId, token, fetchTasks]);

    const updateTask = useCallback(
        async (taskId, updates) => {
            try {
                setLoading(true);
                const response = await axios.put(`/api/eisenhower/${taskId}`, updates, {
                    headers: { Authorization: `Bearer ${token}` },
                });

                if (response.data.success) {
                    setTasks((prev) => {
                        const currentQuadrant = Object.keys(prev).find((q) =>
                            prev[q].some((t) => t.id === taskId)
                        );
                        if (!currentQuadrant) return prev;

                        const newQuadrant = updates.quadrant || currentQuadrant;
                        const updatedTask = { ...prev[currentQuadrant].find((t) => t.id === taskId), ...updates };

                        return {
                            ...prev,
                            [currentQuadrant]: prev[currentQuadrant].filter((t) => t.id !== taskId),
                            [newQuadrant]: [...prev[newQuadrant], updatedTask],
                        };
                    });
                }
            } catch (error) {
                console.error('Error updating task:', error);
                setError('Failed to update task');
                await fetchTasks(); // Fallback to refresh tasks
            } finally {
                setLoading(false);
            }
        },
        [token, fetchTasks]
    );

    const deleteTask = useCallback(
        async (taskId) => {
            try {
                setLoading(true);
                const response = await axios.delete(`/api/eisenhower/${taskId}`, {
                    headers: { Authorization: `Bearer ${token}` },
                });

                if (response.data.success) {
                    setTasks((prev) => {
                        const newTasks = { ...prev };
                        Object.keys(newTasks).forEach((quadrant) => {
                            newTasks[quadrant] = newTasks[quadrant].filter((t) => t.id !== taskId);
                        });
                        return newTasks;
                    });
                }
            } catch (error) {
                console.error('Error deleting task:', error);
                setError('Failed to delete task');
                await fetchTasks(); // Fallback to refresh tasks
            } finally {
                setLoading(false);
            }
        },
        [token, fetchTasks]
    );

    const toggleTaskComplete = useCallback(
        async (task) => {
            await updateTask(task.id, { completed: !task.completed });
        },
        [updateTask]
    );

    const startEditing = useCallback((task) => {
        setEditMode({ taskId: task.id, quadrant: task.quadrant });
        setNewTask({
            task: task.task,
            due_date: task.due_date || '',
            priority: task.priority || 'medium',
        });
        setSelectedQuadrant(task.quadrant);
        setShowAddForm(true);
    }, []);

    const saveEdit = useCallback(async () => {
        if (!newTask.task.trim()) return;

        await updateTask(editMode.taskId, {
            task: newTask.task,
            priority: newTask.priority,
            due_date: newTask.due_date || null,
            quadrant: selectedQuadrant,
        });

        setEditMode({ taskId: null, quadrant: null });
        setNewTask({ task: '', due_date: '', priority: 'medium' });
        setShowAddForm(false);
    }, [newTask, selectedQuadrant, editMode.taskId, updateTask]);

    const cancelEdit = useCallback(() => {
        setEditMode({ taskId: null, quadrant: null });
        setNewTask({ task: '', due_date: '', priority: 'medium' });
        setSelectedQuadrant('Urgent-Important');
        setShowAddForm(false);
    }, []);

    const handleDragStart = useCallback((event) => {
        const taskId = event.active.id;
        let taskToMove = null;
        Object.keys(tasks).forEach((quadrant) => {
            const task = tasks[quadrant].find((t) => t.id === taskId);
            if (task) taskToMove = task;
        });
        activeTaskRef.current = taskToMove;
        startTransition(() => {
            setActiveTask(taskToMove);
        });
    }, [tasks]);

    const handleDragEnd = useCallback(
        async (event) => {
            const { active, over } = event;
            startTransition(() => {
                setActiveTask(null);
            });
            activeTaskRef.current = null;

            if (!over) return;

            const taskId = active.id;
            const newQuadrant = over.id;

            let taskToMove = null;
            let currentQuadrant = null;

            Object.keys(tasks).forEach((quadrant) => {
                const task = tasks[quadrant].find((t) => t.id === taskId);
                if (task) {
                    taskToMove = task;
                    currentQuadrant = quadrant;
                }
            });

            if (taskToMove && currentQuadrant !== newQuadrant) {
                setTasks((prev) => ({
                    ...prev,
                    [currentQuadrant]: prev[currentQuadrant].filter((t) => t.id !== taskId),
                    [newQuadrant]: [...prev[newQuadrant], { ...taskToMove, quadrant: newQuadrant }],
                }));
                await updateTask(taskId, { quadrant: newQuadrant });
            }
        },
        [tasks, updateTask]
    );

    const debouncedSetSearchTerm = useMemo(
        () =>
            debounce((value) => {
                startTransition(() => {
                    setSearchTerm(value);
                });
            }, 300),
        []
    );

    const filteredTasks = useMemo(
        () => (quadrant) => {
            let quadTasks = tasks[quadrant] || [];
            if (searchTerm) {
                quadTasks = quadTasks.filter((task) =>
                    task.task.toLowerCase().includes(searchTerm.toLowerCase())
                );
            }
            switch (filter) {
                case 'completed':
                    return quadTasks.filter((task) => task.completed);
                case 'pending':
                    return quadTasks.filter((task) => !task.completed);
                default:
                    return quadTasks;
            }
        },
        [tasks, searchTerm, filter]
    );

    const getPriorityColor = useCallback((priority) => {
        switch (priority) {
            case 'high':
                return '#FF3B30';
            case 'medium':
                return '#FF9500';
            case 'low':
                return '#28CD41';
            default:
                return '#007AFF';
        }
    }, []);

    const getQuadrantColor = useCallback((quadrant) => {
        switch (quadrant) {
            case 'Urgent-Important':
                return '#FF3B30';
            case 'Not Urgent-Important':
                return '#28CD41';
            case 'Urgent-Not Important':
                return '#FF9500';
            case 'Not Urgent-Not Important':
                return '#8E8E93';
            default:
                return '#007AFF';
        }
    }, []);

    const DraggableTask = useCallback(
        ({ task }) => {
            const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
                id: task.id,
            });

            const style = {
                transform: CSS.Transform.toString(transform),
                transition: isDragging ? 'none' : 'transform 0.2s ease',
                opacity: isDragging ? 0 : 1, // Hide original during drag
                cursor: isDragging ? 'grabbing' : 'grab',
            };

            return (
                <div
                    ref={setNodeRef}
                    style={style}
                    {...attributes}
                    {...listeners}
                    className={`task-item ${task.completed ? 'completed' : ''}`}
                >
                    <div className="task-content">
                        <input
                            type="checkbox"
                            checked={task.completed}
                            onChange={(e) => {
                                e.stopPropagation();
                                toggleTaskComplete(task);
                            }}
                            className="task-checkbox"
                        />
                        <span className="task-text">{task.task}</span>
                        {task.due_date && (
                            <span className="due-date">
                                📅 {new Date(task.due_date).toLocaleDateString()}
                            </span>
                        )}
                        <span
                            className="priority-dot"
                            style={{ backgroundColor: getPriorityColor(task.priority) }}
                        ></span>
                    </div>
                    <div className="task-actions">
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                startEditing(task);
                            }}
                            className="edit-btn"
                            title="Edit task"
                        >
                            ✏️
                        </button>
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                deleteTask(task.id);
                            }}
                            className="delete-btn"
                            title="Delete task"
                        >
                            🗑️
                        </button>
                    </div>
                </div>
            );
        },
        [toggleTaskComplete, startEditing, deleteTask, getPriorityColor]
    );

    const DroppableQuadrant = useCallback(
        ({ quadrant, title, children }) => {
            const { setNodeRef, isOver } = useDroppable({ id: quadrant });

            return (
                <div
                    ref={setNodeRef}
                    className={`quadrant ${quadrant.toLowerCase().replace(/[^a-z]/g, '-')}${isOver ? ' drag-over' : ''
                        }`}
                    style={{ borderColor: getQuadrantColor(quadrant) }}
                >
                    <div className="quadrant-header" style={{ color: getQuadrantColor(quadrant) }}>
                        <h3>{title}</h3>
                        <span className="task-count">{filteredTasks(quadrant).length}</span>
                    </div>
                    <div className="tasks-container">{children}</div>
                </div>
            );
        },
        [getQuadrantColor, filteredTasks]
    );

    const [showAddForm, setShowAddForm] = useState(false);

    return (
        <div className={`eisenhower-matrix ${darkMode ? 'theme-dark' : ''}`}>
            {/* Fixed Header */}
            <div className="matrix-header-fixed">
                <div className="header-left">
                    <h2>Eisenhower Matrix</h2>
                    <span className="subject-name">{subject?.name || 'All Subjects'}</span>
                </div>
                <div className="header-controls">
                    <div className="search-box">
                        <input
                            type="text"
                            placeholder="Search tasks..."
                            onChange={(e) => debouncedSetSearchTerm(e.target.value)}
                            className="search-input"
                        />
                        <span className="search-icon">🔍</span>
                    </div>
                    <select
                        value={filter}
                        onChange={(e) => setFilter(e.target.value)}
                        className="filter-select"
                    >
                        <option value="all">All Tasks</option>
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                    </select>
                    <button
                        onClick={() => setShowAddForm(!showAddForm)}
                        className="add-task-btn"
                        disabled={loading}
                    >
                        + Add Task
                    </button>
                    {/* Close Button */}
                    {onClose && (
                        <button
                            onClick={onClose}
                            className="close-btn"
                            title="Close"
                            aria-label="Close Eisenhower Matrix"
                        >
                            ×
                        </button>
                    )}
                </div>
            </div>

            {/* Scrollable Content */}
            <div className="matrix-content">
                {error && (
                    <div className="error-message">
                        <span>{error}</span>
                        <button onClick={() => setError('')} className="close-error">
                            ×
                        </button>
                    </div>
                )}

                {showAddForm && (
                    <div className="add-task-form">
                        <div className="form-row">
                            <select
                                value={selectedQuadrant}
                                onChange={(e) => setSelectedQuadrant(e.target.value)}
                                disabled={editMode.taskId}
                                className="quadrant-select"
                            >
                                <option value="Urgent-Important">Urgent & Important</option>
                                <option value="Not Urgent-Important">Not Urgent & Important</option>
                                <option value="Urgent-Not Important">Urgent & Not Important</option>
                                <option value="Not Urgent-Not Important">Not Urgent & Not Important</option>
                            </select>
                            <input
                                type="text"
                                placeholder="Enter task description..."
                                value={newTask.task}
                                onChange={(e) => setNewTask({ ...newTask, task: e.target.value })}
                                className="task-input"
                            />
                            <select
                                value={newTask.priority}
                                onChange={(e) => setNewTask({ ...newTask, priority: e.target.value })}
                                className="priority-select"
                            >
                                <option value="low">Low Priority</option>
                                <option value="medium">Medium Priority</option>
                                <option value="high">High Priority</option>
                            </select>
                            <input
                                type="date"
                                value={newTask.due_date}
                                onChange={(e) => setNewTask({ ...newTask, due_date: e.target.value })}
                                className="date-input"
                            />
                        </div>
                        <div className="form-actions">
                            {editMode.taskId ? (
                                <>
                                    <button onClick={saveEdit} className="save-btn" disabled={loading}>
                                        {loading ? 'Saving...' : 'Save Changes'}
                                    </button>
                                    <button onClick={cancelEdit} className="cancel-btn">
                                        Cancel
                                    </button>
                                </>
                            ) : (
                                <>
                                    <button onClick={addTask} className="add-btn" disabled={loading}>
                                        {loading ? 'Adding...' : 'Add Task'}
                                    </button>
                                    <button onClick={cancelEdit} className="cancel-btn">
                                        Cancel
                                    </button>
                                </>
                            )}
                        </div>
                    </div>
                )}

                <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                    modifiers={[adjustTranslate, restrictToWindowEdges]}
                >
                    <div className="matrix-grid">
                        <DroppableQuadrant quadrant="Urgent-Important" title="🔥 Urgent & Important">
                            {filteredTasks('Urgent-Important').map((task) => (
                                <DraggableTask key={task.id} task={task} />
                            ))}
                        </DroppableQuadrant>
                        <DroppableQuadrant quadrant="Not Urgent-Important" title="🎯 Not Urgent & Important">
                            {filteredTasks('Not Urgent-Important').map((task) => (
                                <DraggableTask key={task.id} task={task} />
                            ))}
                        </DroppableQuadrant>
                        <DroppableQuadrant quadrant="Urgent-Not Important" title="⚡ Urgent & Not Important">
                            {filteredTasks('Urgent-Not Important').map((task) => (
                                <DraggableTask key={task.id} task={task} />
                            ))}
                        </DroppableQuadrant>
                        <DroppableQuadrant
                            quadrant="Not Urgent-Not Important"
                            title="🗂️ Not Urgent & Not Important"
                        >
                            {filteredTasks('Not Urgent-Not Important').map((task) => (
                                <DraggableTask key={task.id} task={task} />
                            ))}
                        </DroppableQuadrant>
                    </div>
                    <DragOverlay
                        dropAnimation={{
                            duration: 200,
                            easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
                        }}
                    >
                        {activeTask && (
                            <div className={`task-item drag-overlay ${activeTask.completed ? 'completed' : ''}`}>
                                <div className="task-content">
                                    <input
                                        type="checkbox"
                                        checked={activeTask.completed}
                                        disabled
                                        className="task-checkbox"
                                    />
                                    <span className="task-text">{activeTask.task}</span>
                                    {activeTask.due_date && (
                                        <span className="due-date">
                                            📅 {new Date(activeTask.due_date).toLocaleDateString()}
                                        </span>
                                    )}
                                    <span
                                        className="priority-dot"
                                        style={{ backgroundColor: getPriorityColor(activeTask.priority) }}
                                    ></span>
                                </div>
                                <div className="task-actions">
                                    <button className="edit-btn" disabled title="Edit task">
                                        ✏️
                                    </button>
                                    <button className="delete-btn" disabled title="Delete task">
                                        🗑️
                                    </button>
                                </div>
                            </div>
                        )}
                    </DragOverlay>
                </DndContext>

                {loading && (
                    <div className="loading-overlay">
                        <div className="loading-spinner"></div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default EisenhowerMatrix;