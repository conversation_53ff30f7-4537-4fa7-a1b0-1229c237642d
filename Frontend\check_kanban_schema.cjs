const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'blueprint',
    password: 'Lukind@1956',
    port: 5432,
});

async function checkKanbanSchema() {
    try {
        console.log('Connecting to database...');

        // Check if kanban_board table exists
        const tableExists = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'kanban_board'
            );
        `);

        console.log('Table exists:', tableExists.rows[0].exists);

        if (tableExists.rows[0].exists) {
            // Get table schema
            const schema = await pool.query(`
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = 'kanban_board'
                ORDER BY ordinal_position;
            `);

            console.log('\nKanban Board Table Schema:');
            console.log('==========================');
            schema.rows.forEach(row => {
                console.log(`${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
            });

            // Check sample data
            const sampleData = await pool.query('SELECT * FROM kanban_board LIMIT 3');
            console.log('\nSample data:');
            console.log('============');
            console.log(sampleData.rows);

            // Check total count
            const count = await pool.query('SELECT COUNT(*) FROM kanban_board');
            console.log(`\nTotal records: ${count.rows[0].count}`);

            // Also check if there are any constraints or indexes
            const constraints = await pool.query(`
                SELECT constraint_name, constraint_type
                FROM information_schema.table_constraints
                WHERE table_name = 'kanban_board' AND table_schema = 'public';
            `);

            console.log('\nTable Constraints:');
            console.log('==================');
            constraints.rows.forEach(row => {
                console.log(`${row.constraint_name}: ${row.constraint_type}`);
            });
        }

    } catch (error) {
        console.error('Error checking schema:', error);
        console.error('Full error:', error.message);
    } finally {
        await pool.end();
    }
}

checkKanbanSchema();
