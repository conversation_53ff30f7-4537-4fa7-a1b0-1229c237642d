// Import macOS variables
@use "../../../styles/variables" as *;

// Advanced Pomodoro Timer - macOS Inspired Design
.advanced-pomodoro-timer {
  max-width: 28rem;
  margin: 0 auto;
  padding: 1.5rem;
  border-radius: 1.25rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  position: relative;
  overflow-y: auto;

  // Light theme (default)
  background: linear-gradient(135deg, $window-background 0%, $secondary-background 100%);
  color: $primary-text;
  box-shadow:
    0 0.25rem 1rem rgba(0, 0, 0, 0.08),
    0 0.125rem 0.5rem rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba($border-color, 0.3);

  // Dark theme
  &.theme-dark {
    background: linear-gradient(135deg, $dark-window-background 0%, $dark-secondary-background 100%);
    color: $dark-primary-text;
    box-shadow:
      0 0.25rem 1rem rgba(0, 0, 0, 0.3),
      0 0.125rem 0.5rem rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba($dark-border-color, 0.5);
  }

  // Session-specific styling
  &.work {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.25rem;
      background: linear-gradient(90deg, $system-blue, lighten($system-blue, 10%));
      border-radius: 1.25rem 1.25rem 0 0;
    }
  }

  &.short-break {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.25rem;
      background: linear-gradient(90deg, $system-green, lighten($system-green, 10%));
      border-radius: 1.25rem 1.25rem 0 0;
    }
  }

  &.long-break {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.25rem;
      background: linear-gradient(90deg, $system-orange, lighten($system-orange, 10%));
      border-radius: 1.25rem 1.25rem 0 0;
    }
  }

  // Animation state
  &.animating {
    animation: pulse 1s ease-in-out;
  }

  @include transition(all, 0.3s, cubic-bezier(0.25, 0.1, 0.25, 1));
}

// Header Section
.timer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;

  .header-left {
    flex: 1;

    .timer-title {
      font-size: 1.75rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      letter-spacing: -0.02em;

      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .session-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.375rem 0.75rem;
      border-radius: 1rem;
      font-size: 0.875rem;
      font-weight: 600;

      // Light theme
      background: rgba($system-light-gray, 0.6);
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        background: rgba($dark-secondary-background, 0.8);
        color: $dark-secondary-text;
      }

      .session-icon {
        font-size: 1rem;
      }
    }
  }

  .header-controls {
    display: flex;
    gap: 0.5rem;
  }
}

// Icon Button
.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 0.75rem;
  cursor: pointer;
  @include transition();

  // Light theme
  background: rgba($system-light-gray, 0.4);
  color: $secondary-text;

  // Dark theme
  .theme-dark & {
    background: rgba($dark-secondary-background, 0.6);
    color: $dark-secondary-text;
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  &:hover {
    transform: scale(1.05);

    // Light theme
    background: rgba($system-light-gray, 0.6);

    // Dark theme
    .theme-dark & {
      background: rgba($dark-secondary-background, 0.8);
    }
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
    @include focus-ring;
  }
}

// Settings Panel
.settings-panel {
  animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;

      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .close-settings {
      @extend .icon-button;
    }
  }

  .settings-content {
    .settings-section {
      margin-bottom: 2rem;

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        margin: 0 0 1rem 0;
        color: $secondary-text;

        .theme-dark & {
          color: $dark-secondary-text;
        }
      }
    }
  }

  .settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(8rem, 1fr));
    gap: 1rem;
  }

  .setting-item {
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: $secondary-text;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    .input-group {
      position: relative;
      display: flex;
      align-items: center;

      input {
        width: 100%;
        padding: 0.75rem 2.5rem 0.75rem 0.75rem;
        border: 1px solid $border-color;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        @include transition();

        // Light theme
        background: $window-background;
        color: $primary-text;

        // Dark theme
        .theme-dark & {
          background: $dark-secondary-background;
          color: $dark-primary-text;
          border-color: $dark-border-color;
        }

        &:focus {
          outline: none;
          @include focus-ring;
          border-color: $system-blue;
        }

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        &[type=number] {
          -moz-appearance: textfield;
        }
      }

      .input-suffix {
        position: absolute;
        right: 0.75rem;
        font-size: 0.75rem;
        font-weight: 500;
        color: $tertiary-text;
        pointer-events: none;

        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }
    }
  }

  .toggle-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .toggle-item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    label {
      font-size: 0.875rem;
      font-weight: 500;
      color: $secondary-text;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    .toggle {
      position: relative;
      width: 3rem;
      height: 1.75rem;
      border: none;
      border-radius: 0.875rem;
      cursor: pointer;
      @include transition();

      // Light theme
      background: $system-light-gray;

      // Dark theme
      .theme-dark & {
        background: $dark-border-color;
      }

      &.active {
        background: $system-blue;

        .toggle-slider {
          transform: translateX(1.25rem);
          background: white;
        }
      }

      .toggle-slider {
        position: absolute;
        top: 0.125rem;
        left: 0.125rem;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        @include transition();

        // Light theme
        background: white;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);

        // Dark theme
        .theme-dark & {
          background: $dark-secondary-text;
        }
      }

      &:focus {
        outline: none;
        @include focus-ring;
      }
    }
  }

  // Audio Controls
  .audio-controls {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid $separator-color;

    .theme-dark & {
      border-top-color: $dark-separator-color;
    }

    .sound-selector {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      margin-top: 0.5rem;

      .sound-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border: 1px solid $border-color;
        border-radius: 0.5rem;
        cursor: pointer;
        @include transition();

        // Light theme
        background: $window-background;
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          background: $dark-secondary-background;
          color: $dark-secondary-text;
          border-color: $dark-border-color;
        }

        &.active {
          border-color: $system-blue;
          background: rgba($system-blue, 0.1);
          color: $system-blue;

          .theme-dark & {
            background: rgba($system-blue, 0.2);
          }
        }

        &:hover:not(.active) {
          // Light theme
          background: rgba($system-light-gray, 0.3);

          // Dark theme
          .theme-dark & {
            background: rgba($dark-border-color, 0.3);
          }
        }

        .sound-name {
          font-size: 0.875rem;
          font-weight: 500;
        }

        .test-sound-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2rem;
          height: 2rem;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          @include transition();

          // Light theme
          background: rgba($system-blue, 0.1);
          color: $system-blue;

          // Dark theme
          .theme-dark & {
            background: rgba($system-blue, 0.2);
          }

          svg {
            width: 0.875rem;
            height: 0.875rem;
          }

          &:hover {
            background: rgba($system-blue, 0.2);
            transform: scale(1.1);

            .theme-dark & {
              background: rgba($system-blue, 0.3);
            }
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }

    .volume-control {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-top: 0.5rem;

      .volume-icon {
        font-size: 1rem;
        opacity: 0.7;
      }

      .volume-slider {
        flex: 1;
        height: 0.25rem;
        border-radius: 0.125rem;
        outline: none;
        cursor: pointer;
        @include transition();

        // Light theme
        background: $system-light-gray;

        // Dark theme
        .theme-dark & {
          background: $dark-border-color;
        }

        &::-webkit-slider-thumb {
          appearance: none;
          width: 1rem;
          height: 1rem;
          border-radius: 50%;
          background: $system-blue;
          cursor: pointer;
          @include transition();

          &:hover {
            transform: scale(1.2);
          }
        }

        &::-moz-range-thumb {
          width: 1rem;
          height: 1rem;
          border-radius: 50%;
          background: $system-blue;
          cursor: pointer;
          border: none;
          @include transition();

          &:hover {
            transform: scale(1.2);
          }
        }

        &:focus {
          &::-webkit-slider-thumb {
            @include focus-ring;
          }

          &::-moz-range-thumb {
            @include focus-ring;
          }
        }
      }

      .test-volume-btn {
        padding: 0.375rem 0.75rem;
        border: 1px solid $border-color;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        @include transition();

        // Light theme
        background: $window-background;
        color: $secondary-text;

        // Dark theme
        .theme-dark & {
          background: $dark-secondary-background;
          color: $dark-secondary-text;
          border-color: $dark-border-color;
        }

        &:hover {
          background: $system-blue;
          color: white;
          border-color: $system-blue;
        }

        &:focus {
          outline: none;
          @include focus-ring;
        }
      }
    }
  }

  .settings-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid $separator-color;

    .theme-dark & {
      border-top-color: $dark-separator-color;
    }

    .save-button {
      width: 100%;
      padding: 0.875rem 1.5rem;
      border: none;
      border-radius: 0.75rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      @include transition();

      background: $system-blue;
      color: white;

      &:hover {
        background: darken($system-blue, 5%);
        transform: translateY(-0.0625rem);
      }

      &:active {
        transform: translateY(0);
      }

      &:focus {
        outline: none;
        @include focus-ring;
      }
    }
  }
}

// Timer Content
.timer-content {
  animation: slideIn 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

// Main Timer Display
.timer-main {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;

  .timer-circle {
    position: relative;
    width: 12rem;
    height: 12rem;

    .progress-ring {
      width: 100%;
      height: 100%;
      transform: rotate(-90deg);

      .progress-ring-background {
        fill: none;
        stroke-width: 0.5rem;

        // Light theme
        stroke: rgba($system-light-gray, 0.3);

        // Dark theme
        .theme-dark & {
          stroke: rgba($dark-border-color, 0.5);
        }
      }

      .progress-ring-progress {
        fill: none;
        stroke-width: 0.5rem;
        stroke-linecap: round;
        @include transition(stroke-dashoffset, 1s, ease-in-out);

        // Session-specific colors
        .work & {
          stroke: $system-blue;
        }

        .short-break & {
          stroke: $system-green;
        }

        .long-break & {
          stroke: $system-orange;
        }
      }
    }

    .timer-display {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;

      .time-remaining {
        font-size: 2.5rem;
        font-weight: 300;
        font-variant-numeric: tabular-nums;
        letter-spacing: -0.02em;
        margin-bottom: 0.25rem;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        .theme-dark & {
          color: $dark-primary-text;
        }

        .tick-indicator {
          font-size: 1rem;
          opacity: 0.3;
          @include transition(all, 0.1s, ease-out);

          &.active {
            opacity: 1;
            transform: scale(1.2);
            color: $system-blue;
          }
        }
      }

      &.tick-pulse {
        animation: tickPulse 0.1s ease-out;
      }

      .session-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: $tertiary-text;
        text-transform: uppercase;
        letter-spacing: 0.05em;

        .theme-dark & {
          color: $dark-tertiary-text;
        }
      }
    }
  }
}

// Timer Controls
.timer-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;

  .control-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    @include transition();

    svg {
      width: 1rem;
      height: 1rem;
    }

    &.primary {
      background: $system-blue;
      color: white;

      &:hover:not(:disabled) {
        background: darken($system-blue, 5%);
        transform: translateY(-0.0625rem);
      }

      &.pause {
        background: $system-orange;

        &:hover:not(:disabled) {
          background: darken($system-orange, 5%);
        }
      }
    }

    &.secondary {
      // Light theme
      background: rgba($system-light-gray, 0.6);
      color: $secondary-text;

      // Dark theme
      .theme-dark & {
        background: rgba($dark-secondary-background, 0.8);
        color: $dark-secondary-text;
      }

      &:hover:not(:disabled) {
        // Light theme
        background: rgba($system-light-gray, 0.8);

        // Dark theme
        .theme-dark & {
          background: rgba($dark-secondary-background, 1);
        }

        transform: translateY(-0.0625rem);
      }
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:focus {
      outline: none;
      @include focus-ring;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Statistics
.timer-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;

  .stat-item {
    text-align: center;
    padding: 1rem;
    border-radius: 0.75rem;

    // Light theme
    background: rgba($secondary-background, 0.6);

    // Dark theme
    .theme-dark & {
      background: rgba($dark-secondary-background, 0.6);
    }

    .stat-value {
      font-size: 1.25rem;
      font-weight: 700;
      font-variant-numeric: tabular-nums;
      margin-bottom: 0.25rem;

      .theme-dark & {
        color: $dark-primary-text;
      }
    }

    .stat-label {
      font-size: 0.75rem;
      font-weight: 500;
      color: $tertiary-text;
      text-transform: uppercase;
      letter-spacing: 0.05em;

      .theme-dark & {
        color: $dark-tertiary-text;
      }
    }
  }
}

// Notes Section
.notes-section {
  .notes-header {
    margin-bottom: 0.75rem;

    h3 {
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      color: $secondary-text;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }
  }

  .notes-textarea {
    width: 100%;
    padding: 0.875rem;
    border: 1px solid $border-color;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    min-height: 4rem;
    @include transition();

    // Light theme
    background: $window-background;
    color: $primary-text;

    // Dark theme
    .theme-dark & {
      background: $dark-secondary-background;
      color: $dark-primary-text;
      border-color: $dark-border-color;
    }

    &::placeholder {
      color: $placeholder-text;

      .theme-dark & {
        color: $dark-placeholder-text;
      }
    }

    &:focus {
      outline: none;
      @include focus-ring;
      border-color: $system-blue;
    }
  }
}

// Animations
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.02);
  }
}

@keyframes tickPulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.01);
  }

  100% {
    transform: scale(1);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .advanced-pomodoro-timer {
    max-width: 100%;
    margin: 0;
    padding: 1rem;
    border-radius: 1rem;

    .timer-header {
      margin-bottom: 1.5rem;

      .header-left {
        .timer-title {
          font-size: 1.5rem;
        }
      }
    }

    .timer-main {
      .timer-circle {
        width: 10rem;
        height: 10rem;

        .timer-display {
          .time-remaining {
            font-size: 2rem;
          }
        }
      }
    }

    .timer-controls {
      flex-wrap: wrap;
      gap: 0.75rem;

      .control-button {
        flex: 1;
        min-width: 0;
        padding: 0.75rem 1rem;

        &.primary {
          order: -1;
          flex-basis: 100%;
        }
      }
    }

    .timer-stats {
      gap: 0.75rem;

      .stat-item {
        padding: 0.75rem;

        .stat-value {
          font-size: 1rem;
        }

        .stat-label {
          font-size: 0.6875rem;
        }
      }
    }

    .settings-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
    }
  }
}

@media (max-width: 480px) {
  .advanced-pomodoro-timer {
    padding: 0.75rem;

    .timer-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;

      .header-left {
        .timer-title {
          font-size: 1.25rem;
        }

        .session-badge {
          font-size: 0.8125rem;
          padding: 0.25rem 0.5rem;
        }
      }

      .header-controls {
        align-self: flex-end;
      }
    }

    .timer-main {
      .timer-circle {
        width: 8rem;
        height: 8rem;

        .timer-display {
          .time-remaining {
            font-size: 1.5rem;
          }

          .session-label {
            font-size: 0.75rem;
          }
        }
      }
    }

    .timer-stats {
      grid-template-columns: 1fr;
      gap: 0.5rem;

      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
        padding: 0.75rem;

        .stat-value {
          font-size: 1.125rem;
        }

        .stat-label {
          font-size: 0.75rem;
          text-transform: none;
          letter-spacing: normal;
        }
      }
    }

    .settings-grid {
      grid-template-columns: 1fr;
    }

    .toggle-group {
      gap: 0.75rem;
    }
  }
}