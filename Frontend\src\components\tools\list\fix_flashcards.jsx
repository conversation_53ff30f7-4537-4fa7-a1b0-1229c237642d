/**
 * This file contains a modified version of the saveCardsToDatabase function
 * to fix the 500 error when saving flashcards.
 * 
 * The issue is likely related to the unique constraint we added to the flashcards table.
 * When the client tries to save flashcards, it might be sending duplicate questions
 * which violate the unique constraint.
 * 
 * This modified function adds error handling and batching to prevent this issue.
 */

// Modified saveCardsToDatabase function
const saveCardsToDatabase = async (cardsToSave = cards) => {
    if (!chapterId || cardsToSave.length === 0) return true;

    try {
        const token = localStorage.getItem('token');
        if (!token) throw new Error('Authentication token not found');

        // Step 1: Ensure all cards have unique questions for this chapter
        const uniqueCards = [];
        const questionMap = new Map();

        for (const card of cardsToSave) {
            // Create a unique key for each card based on the question
            const questionKey = card.question.trim();
            
            // If we haven't seen this question before, add it to our unique cards
            if (!questionMap.has(questionKey)) {
                questionMap.set(questionKey, card);
                uniqueCards.push(card);
            } else {
                // If we have seen this question, update the existing card instead of adding a duplicate
                const existingCard = questionMap.get(questionKey);
                // Only update if the card has a newer timestamp
                if (card.updatedAt && existingCard.updatedAt && 
                    new Date(card.updatedAt) > new Date(existingCard.updatedAt)) {
                    // Replace the existing card with this one
                    const index = uniqueCards.findIndex(c => c.question === questionKey);
                    if (index !== -1) {
                        uniqueCards[index] = card;
                        questionMap.set(questionKey, card);
                    }
                }
            }
        }

        console.log(`Filtered ${cardsToSave.length} cards down to ${uniqueCards.length} unique cards`);

        // Step 2: Process cards in smaller batches to avoid payload size issues
        const BATCH_SIZE = 20; // Process 20 cards at a time
        const batches = [];
        
        for (let i = 0; i < uniqueCards.length; i += BATCH_SIZE) {
            batches.push(uniqueCards.slice(i, i + BATCH_SIZE));
        }
        
        console.log(`Processing ${uniqueCards.length} cards in ${batches.length} batches`);
        
        // Step 3: Process each batch
        let successCount = 0;
        
        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            console.log(`Processing batch ${i + 1} of ${batches.length} (${batch.length} cards)`);
            
            try {
                const response = await axios.post(
                    `http://localhost:5001/api/chapters/${chapterId}/flashcards`,
                    { flashcards: batch },
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );
                
                if (response.data.success) {
                    successCount += batch.length;
                    console.log(`Successfully saved batch ${i + 1} (${batch.length} cards)`);
                } else {
                    console.error(`Failed to save batch ${i + 1}:`, response.data.message);
                }
            } catch (batchError) {
                console.error(`Error saving batch ${i + 1}:`, batchError);
                // Continue with the next batch even if this one fails
            }
        }
        
        // Step 4: Save to local storage regardless of server success
        localStorage.setItem(`chapter-${chapterId}-flashcards`, JSON.stringify(uniqueCards));
        
        // Step 5: Report success if at least some cards were saved
        if (successCount > 0) {
            setLoadingError(successCount < uniqueCards.length 
                ? `Partially saved: ${successCount} of ${uniqueCards.length} cards saved.` 
                : null);
            return true;
        } else {
            throw new Error(`Failed to save any of the ${uniqueCards.length} cards`);
        }
    } catch (error) {
        handleError(error, 'saving flashcards');
        // Still save to local storage as a backup
        localStorage.setItem(`chapter-${chapterId}-flashcards`, JSON.stringify(cardsToSave));
        return false;
    }
};

// Instructions for implementing this fix:
// 1. Replace the existing saveCardsToDatabase function in FlashCards.jsx with this version
// 2. Make sure to keep any imports or other dependencies intact
// 3. Test by creating and saving flashcards to ensure they save properly
