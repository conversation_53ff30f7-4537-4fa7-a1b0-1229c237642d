@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// Leitner Dashboard Card - Minimalistic Design
.leitner-dashboard-card {
  width: 100%;
  height: 220px;
  padding: 1rem;
  cursor: pointer;
  @include transition(all);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  // Light theme
  background: $window-background;
  border: 1px solid rgba($border-color, 0.3);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

  // Dark theme
  &.theme-dark {
    background: $dark-window-background;
    border: 1px solid rgba($dark-border-color, 0.3);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

    &.theme-dark {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }

    .mastery-circle {
      transform: scale(1.02);
    }
  }

  &:focus {
    outline: 2px solid $system-blue;
    outline-offset: 2px;
  }

  // Widget Header
  .widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .widget-title {
      font-size: 0.9rem;
      font-weight: 600;
      margin: 0;
      color: $secondary-text;
      letter-spacing: -0.1px;

      .theme-dark & {
        color: $dark-secondary-text;
      }
    }

    .mastery-percentage {
      font-size: 1.25rem;
      font-weight: 700;
      color: $primary-text;
      background: rgba($system-blue, 0.08);
      padding: 0.25rem 0.5rem;
      border-radius: 8px;
      min-width: 2rem;
      text-align: center;

      .theme-dark & {
        color: $dark-primary-text;
        background: rgba($system-blue, 0.15);
      }
    }
  }

  // Error Message
  .error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 8px;
    background: rgba($system-red, 0.1);
    border: 1px solid rgba($system-red, 0.2);
    margin-bottom: 1rem;

    &.theme-dark {
      background: rgba($system-red, 0.15);
      border: 1px solid rgba($system-red, 0.3);
    }

    svg {
      color: $system-red;
      flex-shrink: 0;

      &.theme-dark {
        color: color.adjust($system-red, $lightness: 10%);
      }
    }

    span {
      font-size: 0.85rem;
      color: $system-red;
      font-weight: 500;

      &.theme-dark {
        color: color.adjust($system-red, $lightness: 10%);
      }
    }
  }

  // Loading Container
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    flex: 1;
    color: $secondary-text;

    &.theme-dark {
      color: $dark-secondary-text;
    }

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid $border-color;
      border-top: 2px solid $system-blue;
      border-radius: 50%;
      animation: spin 1s linear infinite;

      &.theme-dark {
        border: 2px solid $dark-border-color;
        border-top: 2px solid $system-blue;
      }
    }

    span {
      font-size: 0.9rem;
      font-weight: 500;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  }

  // Leitner Content
  .leitner-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 1rem;

    .mastery-overview {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;

      .mastery-circle {
        flex-shrink: 0;
        @include transition(all);

        .progress-ring {
          width: 80px;
          height: 80px;

          .progress-circle {
            @include transition(all);
          }

          .progress-text {
            font-size: 0.8rem;
            font-weight: 700;
            fill: $primary-text;

            .theme-dark & {
              fill: $dark-primary-text;
            }
          }
        }
      }

      .box-indicators {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        flex: 1;

        .box-indicator {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .box-dot {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            position: relative;

            .box-count {
              font-size: 0.7rem;
              font-weight: 700;
              color: white;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            &.box-1 {
              background-color: #FF6B6B;
            }

            &.box-2 {
              background-color: #4ECDC4;
            }

            &.box-3 {
              background-color: #45B7D1;
            }
          }

          .box-label {
            font-size: 0.7rem;
            font-weight: 600;
            color: $tertiary-text;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            .theme-dark & {
              color: $dark-tertiary-text;
            }
          }
        }
      }
    }

    .study-action {
      .study-button {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        border-radius: 10px;
        @include transition(all);
        cursor: pointer;
        border: none;
        width: 100%;

        // Light theme
        background: rgba($system-blue, 0.08);
        border: 1px solid rgba($system-blue, 0.15);

        // Dark theme
        &.theme-dark {
          background: rgba($system-blue, 0.12);
          border: 1px solid rgba($system-blue, 0.2);
        }

        &:hover:not(.disabled) {
          background: rgba($system-blue, 0.12);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba($system-blue, 0.15);

          &.theme-dark {
            background: rgba($system-blue, 0.18);
            box-shadow: 0 4px 12px rgba($system-blue, 0.25);
          }
        }

        &.disabled {
          opacity: 0.6;
          cursor: not-allowed;
          background: rgba($tertiary-text, 0.05);
          border: 1px solid rgba($tertiary-text, 0.1);

          &.theme-dark {
            background: rgba($dark-tertiary-text, 0.05);
            border: 1px solid rgba($dark-tertiary-text, 0.1);
          }
        }

        .study-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: rgba($system-blue, 0.1);
          flex-shrink: 0;

          &.theme-dark {
            background: rgba($system-blue, 0.2);
          }

          svg {
            color: $system-blue;

            &.theme-dark {
              color: color.adjust($system-blue, $lightness: 10%);
            }
          }

          .disabled & {
            background: rgba($tertiary-text, 0.1);

            svg {
              color: $tertiary-text;

              &.theme-dark {
                color: $dark-tertiary-text;
              }
            }

            &.theme-dark {
              background: rgba($dark-tertiary-text, 0.1);
            }
          }
        }

        .study-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.125rem;
          text-align: left;

          .study-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: $primary-text;

            &.theme-dark {
              color: $dark-primary-text;
            }

            .disabled & {
              color: $tertiary-text;

              &.theme-dark {
                color: $dark-tertiary-text;
              }
            }
          }

          .study-count {
            font-size: 0.75rem;
            color: $secondary-text;
            font-weight: 500;

            &.theme-dark {
              color: $dark-secondary-text;
            }

            .disabled & {
              color: $tertiary-text;

              &.theme-dark {
                color: $dark-tertiary-text;
              }
            }
          }
        }

        .study-arrow {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          svg {
            color: $system-blue;

            &.theme-dark {
              color: color.adjust($system-blue, $lightness: 10%);
            }
          }
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    height: 200px;
    padding: 0.75rem;

    .widget-header {
      margin-bottom: 0.75rem;

      .widget-title {
        font-size: 0.85rem;
      }

      .mastery-percentage {
        font-size: 1.1rem;
        padding: 0.2rem 0.4rem;
      }
    }

    .leitner-content {
      gap: 0.75rem;

      .mastery-overview {
        gap: 0.75rem;

        .mastery-circle {
          .progress-ring {
            width: 70px;
            height: 70px;

            .progress-text {
              font-size: 0.75rem;
            }
          }
        }

        .box-indicators {
          gap: 0.375rem;

          .box-indicator {
            gap: 0.375rem;

            .box-dot {
              width: 18px;
              height: 18px;

              .box-count {
                font-size: 0.65rem;
              }
            }

            .box-label {
              font-size: 0.65rem;
            }
          }
        }
      }

      .study-action {
        .study-button {
          padding: 0.6rem;
          gap: 0.6rem;

          .study-icon {
            width: 28px;
            height: 28px;

            svg {
              width: 16px;
              height: 16px;
            }
          }

          .study-info {
            .study-label {
              font-size: 0.85rem;
            }

            .study-count {
              font-size: 0.7rem;
            }
          }

          .study-arrow {
            svg {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
  }
}