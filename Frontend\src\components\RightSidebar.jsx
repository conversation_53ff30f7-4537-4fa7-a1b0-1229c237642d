
import React, { useState, useEffect, useRef } from 'react';
import "../styles/sidebar.scss";
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import PauseIcon from '@mui/icons-material/Pause';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import aivoice from '../assets/aivoice.mp4';
import EnergySphere from "./EnergySphere.jsx";
import axios from 'axios';
import { useAuth } from '../../authContext.jsx';

const RightSidebar = ({ collapsed, toggleCollapse }) => {
    const [messages, setMessages] = useState([]);
    const [inputText, setInputText] = useState('');
    const [isRecording, setIsRecording] = useState(false);
    const [sphereMode, setSphereMode] = useState('idle');
    const [sphereEmotion, setSphereEmotion] = useState('neutral');
    const [isActive, setIsActive] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [chatHistory, setChatHistory] = useState([]);
    const [userName, setUserName] = useState('');
    const [isSpeaking, setIsSpeaking] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [transcript, setTranscript] = useState('');
    const [isListening, setIsListening] = useState(false);
    const [textToSpeechEnabled, setTextToSpeechEnabled] = useState(false); // Disabled by default
    const [selectedVoice, setSelectedVoice] = useState('female'); // 'female' or 'male'
    const [availableVoices, setAvailableVoices] = useState([]);
    const [voiceMenuOpen, setVoiceMenuOpen] = useState(false);
    const [silenceTimer, setSilenceTimer] = useState(null);
    const [lastSpeechTime, setLastSpeechTime] = useState(0);
    const [currentUtterance, setCurrentUtterance] = useState(null);

    const { user, token } = useAuth();
    // Use port 5000 for the backend API
    // Use http://127.0.0.1:5000 instead of localhost to avoid CORS issues
    const API_URL = 'http://127.0.0.1:5000';

    // Speech recognition reference
    const recognitionRef = useRef(null);

    const mediaRecorderRef = useRef(null);
    const audioChunksRef = useRef([]);
    const messagesEndRef = useRef(null);
    const inputRef = useRef(null);

    // Initialize user name from auth context
    useEffect(() => {
        if (user && user.fullName) {
            // Use the full name from the user object
            setUserName(user.fullName);
            console.log('Using fullName from user object:', user.fullName);
        } else if (user && user.email) {
            // Extract name from email if available
            const nameFromEmail = user.email.split('@')[0];
            setUserName(nameFromEmail);
            console.log('Using name from email:', nameFromEmail);
        } else {
            // Default name if no user info is available
            setUserName('Student');
            console.log('Using default name: Student');
        }
    }, [user]);

    // Add click outside handler to close voice selector
    useEffect(() => {
        const handleClickOutside = (event) => {
            // Close voice selector when clicking outside
            if (voiceMenuOpen && !event.target.closest('.voice-controls')) {
                setVoiceMenuOpen(false);
            }
        };

        // Add event listener
        document.addEventListener('mousedown', handleClickOutside);

        // Clean up
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [voiceMenuOpen]);

    // Initialize speech recognition and speech synthesis voices
    useEffect(() => {
        // Initialize speech recognition
        // Check if browser supports speech recognition
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('Speech recognition not supported in this browser');
        } else {
            // Create speech recognition instance
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();

            // Configure recognition
            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'en-US';

            // Set up event handlers
            recognition.onresult = (event) => {
                const current = event.resultIndex;
                const transcriptText = event.results[current][0].transcript;
                setTranscript(transcriptText);

                // Update input field with transcript
                setInputText(transcriptText);

                // Reset silence timer on new speech
                setLastSpeechTime(Date.now());

                // Clear any existing silence timer
                if (silenceTimer) {
                    clearTimeout(silenceTimer);
                }

                // Set a new silence timer (10 seconds)
                const timer = setTimeout(() => {
                    if (isListening && transcriptText.trim()) {
                        console.log('Auto-sending message after 10 seconds of silence');
                        handleSendVoiceMessage(transcriptText);
                    }
                }, 10000); // 10 seconds

                setSilenceTimer(timer);
            };

            recognition.onend = () => {
                // If recognition ends but we're still in listening mode,
                // restart it (unless we're speaking)
                if (isListening && !isSpeaking) {
                    recognition.start();
                } else {
                    setIsListening(false);

                    // Clear silence timer
                    if (silenceTimer) {
                        clearTimeout(silenceTimer);
                        setSilenceTimer(null);
                    }
                }
            };

            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                setIsListening(false);

                // Clear silence timer
                if (silenceTimer) {
                    clearTimeout(silenceTimer);
                    setSilenceTimer(null);
                }
            };

            // Save recognition instance to ref
            recognitionRef.current = recognition;
        }

        // Initialize speech synthesis voices
        const loadVoices = () => {
            // Get available voices
            const voices = window.speechSynthesis.getVoices();

            if (voices.length > 0) {
                console.log('Available voices:', voices);
                setAvailableVoices(voices);

                // Find good default voices
                const femaleVoice = voices.find(voice =>
                (voice.name.includes('Female') ||
                    voice.name.includes('Samantha') ||
                    voice.name.includes('Google UK English Female') ||
                    voice.name.includes('Microsoft Zira'))
                );

                const maleVoice = voices.find(voice =>
                (voice.name.includes('Male') ||
                    voice.name.includes('Daniel') ||
                    voice.name.includes('Google UK English Male') ||
                    voice.name.includes('Microsoft David'))
                );

                if (femaleVoice) {
                    console.log('Found female voice:', femaleVoice.name);
                }

                if (maleVoice) {
                    console.log('Found male voice:', maleVoice.name);
                }
            }
        };

        // Check if speechSynthesis is supported
        if ('speechSynthesis' in window) {
            // Load voices
            loadVoices();

            // Chrome loads voices asynchronously, so we need this event
            if (window.speechSynthesis.onvoiceschanged !== undefined) {
                window.speechSynthesis.onvoiceschanged = loadVoices;
            }
        } else {
            console.warn('Text-to-speech not supported in this browser');
        }

        // Clean up on unmount
        return () => {
            if (recognitionRef.current) {
                recognitionRef.current.stop();
            }

            if ('speechSynthesis' in window && window.speechSynthesis.speaking) {
                window.speechSynthesis.cancel();
            }
        };
    }, []);

    // Load initial welcome message and test API connection
    useEffect(() => {
        // Test API connection
        const testApiConnection = async () => {
            try {
                console.log('Testing API connection to:', `${API_URL}/api/ai/today_objectives`);
                const response = await axios.get(`${API_URL}/api/ai/today_objectives`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                console.log('API test response:', response.data);

                if (response.data.success) {
                    // API is working
                    if (messages.length === 0) {
                        // Use the user's name in the welcome message if available
                        const welcomeName = userName || 'there';
                        setMessages([
                            {
                                text: `Hello ${welcomeName}! I'm your AI learning assistant. How can I help you with your studies today?`,
                                sender: 'ai'
                            }
                        ]);
                    }
                } else {
                    // API test failed
                    setMessages([
                        {
                            text: "Connection to AI service failed. Please try again later.",
                            sender: 'ai'
                        }
                    ]);
                }
            } catch (error) {
                console.error('API connection test failed:', error);
                setMessages([
                    {
                        text: "Unable to connect to the AI service. Please check if the backend server is running on port 5000.",
                        sender: 'ai'
                    }
                ]);
            }
        };

        testApiConnection();
    }, [API_URL, messages.length, userName]);

    // Scroll to bottom of messages
    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, [messages]);

    // Enhanced text-to-speech function with teacher-like voices
    const speakText = (text) => {
        // Check if text-to-speech is enabled and supported
        if (!textToSpeechEnabled || !('speechSynthesis' in window)) {
            if (!('speechSynthesis' in window)) {
                console.warn('Text-to-speech not supported in this browser');
            }
            return;
        }

        // Stop any ongoing speech
        if (window.speechSynthesis.speaking) {
            window.speechSynthesis.cancel();
        }

        // Preprocess text to improve speech quality
        // Extract bold text for emphasis
        const boldTexts = [];
        let processedText = text.replace(/\*\*(.*?)\*\*/g, (match, p1) => {
            boldTexts.push(p1);
            return p1;
        });

        // Create utterance
        const utterance = new SpeechSynthesisUtterance(processedText);

        // Configure voice parameters for more natural speech
        utterance.lang = 'en-US';
        utterance.rate = 0.95; // Slightly slower for teacher-like clarity
        utterance.pitch = selectedVoice === 'female' ? 1.05 : 0.95; // Slightly higher pitch for female voice
        utterance.volume = 1.0;

        // Select the appropriate voice based on user preference
        if (availableVoices.length > 0) {
            let selectedVoiceObj = null;

            if (selectedVoice === 'female') {
                // Try to find a good female voice in this order of preference
                selectedVoiceObj = availableVoices.find(voice => voice.name.includes('Google UK English Female')) ||
                    availableVoices.find(voice => voice.name.includes('Microsoft Zira')) ||
                    availableVoices.find(voice => voice.name.includes('Samantha')) ||
                    availableVoices.find(voice => voice.name.includes('Female'));
            } else {
                // Try to find a good male voice in this order of preference
                selectedVoiceObj = availableVoices.find(voice => voice.name.includes('Google UK English Male')) ||
                    availableVoices.find(voice => voice.name.includes('Microsoft David')) ||
                    availableVoices.find(voice => voice.name.includes('Daniel')) ||
                    availableVoices.find(voice => voice.name.includes('Male'));
            }

            // If we found a voice, use it
            if (selectedVoiceObj) {
                utterance.voice = selectedVoiceObj;
                console.log(`Using ${selectedVoice} voice: ${selectedVoiceObj.name}`);
            } else {
                console.warn(`No suitable ${selectedVoice} voice found, using default voice`);
            }
        }

        // Add pauses at punctuation for more natural speech
        processedText = processedText.replace(/\./g, '. ');
        processedText = processedText.replace(/\,/g, ', ');
        processedText = processedText.replace(/\:/g, ': ');
        processedText = processedText.replace(/\;/g, '; ');

        // Set event handlers
        utterance.onstart = () => {
            setIsSpeaking(true);
            setSphereMode('speaking');
        };

        utterance.onend = () => {
            setIsSpeaking(false);
            setSphereMode('idle');
        };

        utterance.onerror = (event) => {
            console.error('Speech synthesis error:', event);
            setIsSpeaking(false);
            setSphereMode('idle');
        };

        // Break long text into sentences for more natural pauses
        const sentences = processedText.split(/(?<=[.!?])\s+/);

        // Speak each sentence with a slight pause between
        sentences.forEach((sentence, index) => {
            if (sentence.trim().length === 0) return;

            // Check if this sentence contains any of the bold texts
            // If so, we'll emphasize it by slightly adjusting the pitch and rate
            let shouldEmphasize = false;
            let emphasizedSentence = sentence;

            for (const boldText of boldTexts) {
                if (sentence.includes(boldText)) {
                    shouldEmphasize = true;
                    // We could mark it for emphasis, but we'll just adjust the properties
                    break;
                }
            }

            const sentenceUtterance = new SpeechSynthesisUtterance(emphasizedSentence);

            // Copy all properties from the main utterance
            sentenceUtterance.voice = utterance.voice;

            // Adjust properties for emphasis if needed
            if (shouldEmphasize) {
                // Slow down slightly and increase volume for emphasis
                sentenceUtterance.rate = utterance.rate * 0.9;
                sentenceUtterance.volume = 1.0;
                // Adjust pitch slightly based on voice type
                sentenceUtterance.pitch = selectedVoice === 'female' ?
                    utterance.pitch * 1.05 : utterance.pitch * 1.05;
            } else {
                sentenceUtterance.rate = utterance.rate;
                sentenceUtterance.pitch = utterance.pitch;
                sentenceUtterance.volume = utterance.volume;
            }

            // Only set events for first and last sentence
            if (index === 0) {
                sentenceUtterance.onstart = utterance.onstart;
            }

            if (index === sentences.length - 1) {
                sentenceUtterance.onend = utterance.onend;
                sentenceUtterance.onerror = utterance.onerror;
            }

            // Add to speech queue
            window.speechSynthesis.speak(sentenceUtterance);

            // Add a tiny pause between sentences for more natural speech
            if (index < sentences.length - 1) {
                const pauseUtterance = new SpeechSynthesisUtterance('.');
                pauseUtterance.volume = 0;
                pauseUtterance.rate = 2.0; // Fast to minimize pause
                window.speechSynthesis.speak(pauseUtterance);
            }
        });
    };

    // Handle sending a message
    const handleSendMessage = async () => {
        if (inputText.trim() === '') return;

        // Add user message
        const userMessage = inputText.trim();
        const newMessages = [...messages, { text: userMessage, sender: 'user' }];
        setMessages(newMessages);
        setInputText('');

        // Update chat history for context
        const updatedHistory = [...chatHistory, { role: 'user', content: userMessage }];
        setChatHistory(updatedHistory);

        // Show AI thinking and responding
        setSphereMode('processing');
        setIsLoading(true);

        // Focus back on input field for better UX
        if (inputRef.current) {
            inputRef.current.focus();
        }

        try {
            // Log API request details for debugging
            console.log('Sending request to:', `${API_URL}/chat`);
            console.log('With token:', token ? 'Token exists' : 'No token');
            console.log('With data:', {
                message: userMessage,
                history: updatedHistory.slice(-6),
                user_name: userName
            });

            // Call the AI assistant chat API
            const response = await axios.post(`${API_URL}/api/ai/chat`,
                {
                    message: userMessage,
                    history: updatedHistory.slice(-6) // Send last 6 messages for context
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                }
            );

            // AI is speaking
            setSphereMode('speaking');

            if (response.data.success) {
                const aiResponse = response.data.response;

                // Add AI response to messages
                setMessages([...newMessages, {
                    text: aiResponse,
                    sender: 'ai'
                }]);

                // Update chat history with AI response
                setChatHistory([...updatedHistory, { role: 'assistant', content: aiResponse }]);

                // Set emotion based on response content
                if (aiResponse.includes('sorry') || aiResponse.includes('unfortunately')) {
                    setSphereEmotion('sad');
                } else if (aiResponse.includes('great') || aiResponse.includes('excellent') || aiResponse.includes('congratulations')) {
                    setSphereEmotion('happy');
                } else {
                    setSphereEmotion('neutral');
                }

                // Speak the AI response if text-to-speech is enabled and response is not too long
                if (textToSpeechEnabled && aiResponse.length < 1000) {  // Limit to avoid very long responses
                    speakRealisticText(aiResponse);
                }
            } else {
                // Handle error
                setMessages([...newMessages, {
                    text: "I'm sorry, I couldn't process your request. Please try again later.",
                    sender: 'ai'
                }]);
                setSphereEmotion('sad');
            }
        } catch (error) {
            console.error('Error sending message:', error);

            // Get detailed error message if available
            let errorMessage = "I'm sorry, there was an error connecting to the AI service. Please try again later.";

            if (error.response && error.response.data) {
                const responseData = error.response.data;
                if (responseData.message) {
                    errorMessage = responseData.message;
                } else if (responseData.error) {
                    errorMessage = responseData.error;
                }
                console.log('Server error details:', responseData);
            }

            // If backend is unavailable, provide a fallback response
            if (error.code === 'ERR_NETWORK' || !error.response) {
                errorMessage = "I'm sorry, I can't connect to the AI service right now. Please check if the backend server is running on port 5000.";

                // Provide a fallback response for testing
                const fallbackResponses = [
                    "I recommend breaking your study sessions into 25-minute focused blocks with 5-minute breaks in between. This technique, called the Pomodoro Technique, can help maintain concentration and prevent burnout.",
                    "When learning new material, try the SQ3R method: Survey, Question, Read, Recite, and Review. First, scan the material to get an overview, then formulate questions, read actively to answer them, recite the information in your own words, and finally review what you've learned.",
                    "Active recall is one of the most effective study techniques. Instead of passively re-reading notes, test yourself by trying to recall information from memory. This strengthens neural pathways and improves long-term retention.",
                    "Consider using spaced repetition to review material at increasing intervals. This technique leverages the psychological spacing effect to help move information from short-term to long-term memory more efficiently.",
                    "Creating mind maps can help you visualize connections between concepts and organize information in a way that mirrors how your brain naturally works. This can make complex topics easier to understand and remember."
                ];

                // Select a random fallback response
                const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];

                setMessages([...newMessages, {
                    text: `${errorMessage}\n\nHere's a study tip while you wait:\n\n${randomResponse}`,
                    sender: 'ai'
                }]);

                return;
            }

            // Show error message
            setMessages([...newMessages, {
                text: errorMessage,
                sender: 'ai'
            }]);
            setSphereEmotion('sad');
        } finally {
            // Return to idle state after a short delay
            setTimeout(() => {
                setSphereMode('idle');
                setIsLoading(false);
            }, 1000);
        }
    };

    // Toggle speech recognition for voice input
    const toggleSpeechRecognition = () => {
        // Don't allow starting speech recognition if AI is speaking
        if (isSpeaking) {
            console.log('Cannot start listening while AI is speaking');
            return;
        }

        if (isListening) {
            // Stop listening
            if (recognitionRef.current) {
                recognitionRef.current.stop();
            }
            setIsListening(false);

            // Clear any silence timer
            if (silenceTimer) {
                clearTimeout(silenceTimer);
                setSilenceTimer(null);
            }

            // If we have a transcript, send it as a message
            if (transcript.trim()) {
                handleSendVoiceMessage(transcript);
            }
        } else {
            // Start listening
            if (recognitionRef.current) {
                setTranscript('');
                setInputText('');
                setIsListening(true);
                setSphereMode('listening');

                // Initialize the last speech time
                setLastSpeechTime(Date.now());

                // Start recognition
                recognitionRef.current.start();
            } else {
                alert('Speech recognition is not supported in your browser.');
            }
        }
    };

    // Handle sending a voice message
    const handleSendVoiceMessage = async (voiceText) => {
        if (!voiceText || voiceText.trim() === '') return;

        // Add user voice message
        const userMessage = voiceText.trim();
        const newMessages = [...messages, {
            text: userMessage,
            sender: 'user',
            isVoiceMessage: true
        }];
        setMessages(newMessages);

        // Clear input and transcript
        setInputText('');
        setTranscript('');

        // Update chat history for context
        const updatedHistory = [...chatHistory, { role: 'user', content: userMessage }];
        setChatHistory(updatedHistory);

        // Show AI processing
        setSphereMode('processing');
        setIsLoading(true);

        try {
            // Send the transcribed voice message to the AI assistant
            const response = await axios.post(`${API_URL}/api/ai/chat`,
                {
                    message: userMessage,
                    history: updatedHistory.slice(-6), // Send last 6 messages for context
                    is_voice_message: true // Flag that this is a voice message
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                }
            );

            // AI is speaking
            setSphereMode('speaking');

            if (response.data.success) {
                const aiResponse = response.data.response;

                // Add AI response to messages
                setMessages([...newMessages, {
                    text: aiResponse,
                    sender: 'ai'
                }]);

                // Update chat history with AI response
                setChatHistory([...updatedHistory, { role: 'assistant', content: aiResponse }]);

                // Set emotion based on response content
                if (aiResponse.includes('sorry') || aiResponse.includes('unfortunately')) {
                    setSphereEmotion('sad');
                } else if (aiResponse.includes('great') || aiResponse.includes('excellent')) {
                    setSphereEmotion('happy');
                } else {
                    setSphereEmotion('neutral');
                }

                // Speak the AI response with realistic voice if text-to-speech is enabled
                if (textToSpeechEnabled) {
                    speakRealisticText(aiResponse);
                }
            } else {
                // Handle error
                setMessages([...newMessages, {
                    text: "I'm sorry, I couldn't process your voice message. Please try again later.",
                    sender: 'ai'
                }]);
                setSphereEmotion('sad');
            }
        } catch (error) {
            console.error('Error processing voice message:', error);

            // Get detailed error message if available
            let errorMessage = "I'm sorry, there was an error processing your voice message. Please try again later.";

            if (error.response && error.response.data) {
                const responseData = error.response.data;
                if (responseData.message) {
                    errorMessage = responseData.message;
                } else if (responseData.error) {
                    errorMessage = responseData.error;
                }
                console.log('Server error details:', responseData);
            }

            // If backend is unavailable, provide a fallback response
            if (error.code === 'ERR_NETWORK' || !error.response) {
                errorMessage = "I'm sorry, I can't connect to the AI service right now. Please check if the backend server is running on port 5000.";

                // Provide a fallback response for voice messages
                const voiceResponse = "Here are some general study tips: Break your study sessions into 25-minute focused blocks with 5-minute breaks. Use active recall by testing yourself instead of just re-reading notes. Create mind maps to visualize connections between concepts. Review material at spaced intervals to improve retention.";

                setMessages([...newMessages, {
                    text: `${errorMessage}\n\n${voiceResponse}`,
                    sender: 'ai'
                }]);

                return;
            }

            // Show error message
            setMessages([...newMessages, {
                text: errorMessage,
                sender: 'ai'
            }]);
            setSphereEmotion('sad');
        } finally {
            // Return to idle state after a short delay
            setTimeout(() => {
                setSphereMode('idle');
                setIsLoading(false);
            }, 1000);
        }
    };

    // Helper function to test voices
    const testSelectedVoice = () => {
        // Only test if text-to-speech is enabled
        if (!textToSpeechEnabled) {
            // If user tries to test voice while TTS is disabled, enable it first
            setTextToSpeechEnabled(true);
            // Wait for state to update before testing
            setTimeout(() => {
                const testText = selectedVoice === 'female' ?
                    "Hello, I'm your female teacher assistant. How can I help you today?" :
                    "Hello, I'm your male teacher assistant. How can I help you today?";

                speakRealisticText(testText);
            }, 100);
            return;
        }

        const testText = selectedVoice === 'female' ?
            "Hello, I'm your female teacher assistant. How can I help you today?" :
            "Hello, I'm your male teacher assistant. How can I help you today?";

        speakRealisticText(testText);
    };

    // Function to pause speech
    const pauseSpeech = () => {
        if (isSpeaking && !isPaused && window.speechSynthesis.speaking) {
            window.speechSynthesis.pause();
            setIsPaused(true);
            setSphereMode('idle');
        }
    };

    // Function to resume speech
    const resumeSpeech = () => {
        if (isSpeaking && isPaused && window.speechSynthesis.paused) {
            window.speechSynthesis.resume();
            setIsPaused(false);
            setSphereMode('speaking');
        }
    };

    // Function to stop speech
    const stopSpeech = () => {
        if (isSpeaking && window.speechSynthesis.speaking) {
            window.speechSynthesis.cancel();
            setIsSpeaking(false);
            setIsPaused(false);
            setCurrentUtterance(null);
            setSphereMode('idle');
        }
    };

    // Function to toggle pause/resume
    const togglePauseSpeech = () => {
        if (isPaused) {
            resumeSpeech();
        } else {
            pauseSpeech();
        }
    };

    // Realistic text-to-speech function using backend API
    const speakRealisticText = async (text) => {
        // Check if text-to-speech is enabled
        if (!textToSpeechEnabled) {
            return;
        }

        // Reset pause state
        setIsPaused(false);

        // Set speaking state
        setIsSpeaking(true);
        setSphereMode('speaking');

        // Stop listening if active
        if (isListening) {
            if (recognitionRef.current) {
                recognitionRef.current.stop();
            }
            setIsListening(false);

            // Clear any silence timer
            if (silenceTimer) {
                clearTimeout(silenceTimer);
                setSilenceTimer(null);
            }
        }

        try {
            // Preprocess text to improve speech quality
            let processedText = text.replace(/\*\*(.*?)\*\*/g, (match, p1) => {
                return p1; // Remove ** markers but keep the text
            });

            // Call our backend TTS endpoint for realistic voice
            const response = await axios.post(`${API_URL}/text-to-speech`, {
                text: processedText,
                voice_type: selectedVoice // 'female' or 'male'
            });

            if (response.data.success) {
                // In a real implementation with actual audio data:
                // 1. Create an audio element
                const audio = new Audio();

                // 2. Set up event handlers
                audio.onended = () => {
                    setIsSpeaking(false);
                    setIsPaused(false);
                    setCurrentUtterance(null);
                    setSphereMode('idle');
                };

                audio.onerror = (error) => {
                    console.error('Audio playback error:', error);
                    setIsSpeaking(false);
                    setIsPaused(false);
                    setCurrentUtterance(null);
                    setSphereMode('idle');
                };

                // 3. Set the source (in a real implementation, this would be a data URL)
                // For now, we'll use the Web Speech API as a fallback since our endpoint returns mock data

                // Fallback to Web Speech API for the demo
                useBrowserTTS(text);
            } else {
                console.error('Error getting speech audio:', response.data.error);
                setIsSpeaking(false);
                setSphereMode('idle');

                // Fallback to browser TTS
                useBrowserTTS(text);
            }
        } catch (error) {
            console.error('Error in realistic text-to-speech:', error);
            setIsSpeaking(false);
            setSphereMode('idle');

            // Fallback to browser TTS
            useBrowserTTS(text);
        }
    };

    // Browser TTS implementation with pause/resume support
    const useBrowserTTS = (text) => {
        // Cancel any ongoing speech
        if (window.speechSynthesis.speaking) {
            window.speechSynthesis.cancel();
        }

        // Preprocess text
        let processedText = text.replace(/\*\*(.*?)\*\*/g, (match, p1) => {
            return p1;
        });

        // Create utterance
        const utterance = new SpeechSynthesisUtterance(processedText);

        // Configure voice parameters
        utterance.lang = 'en-US';
        utterance.rate = 0.95;
        utterance.pitch = selectedVoice === 'female' ? 1.05 : 0.95;
        utterance.volume = 1.0;

        // Select voice
        if (availableVoices.length > 0) {
            let selectedVoiceObj = null;

            if (selectedVoice === 'female') {
                selectedVoiceObj = availableVoices.find(voice =>
                    voice.name.includes('Google UK English Female') ||
                    voice.name.includes('Microsoft Zira') ||
                    voice.name.includes('Samantha') ||
                    voice.name.includes('Female')
                );
            } else {
                selectedVoiceObj = availableVoices.find(voice =>
                    voice.name.includes('Google UK English Male') ||
                    voice.name.includes('Microsoft David') ||
                    voice.name.includes('Daniel') ||
                    voice.name.includes('Male')
                );
            }

            if (selectedVoiceObj) {
                utterance.voice = selectedVoiceObj;
            }
        }

        // Set event handlers
        utterance.onstart = () => {
            setIsSpeaking(true);
            setSphereMode('speaking');
        };

        utterance.onend = () => {
            setIsSpeaking(false);
            setIsPaused(false);
            setCurrentUtterance(null);
            setSphereMode('idle');
        };

        utterance.onerror = (event) => {
            console.error('Speech synthesis error:', event);
            setIsSpeaking(false);
            setIsPaused(false);
            setCurrentUtterance(null);
            setSphereMode('idle');
        };

        // Store the utterance for pause/resume functionality
        setCurrentUtterance(utterance);

        // Speak the text
        window.speechSynthesis.speak(utterance);
    };

    // Format message text to render ** ** as bold
    const formatMessageText = (text) => {
        if (!text) return '';

        // Replace **text** with <strong>text</strong> for bold formatting
        // Using a regex with non-greedy matching to handle multiple bold sections
        return text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // Also convert newlines to <br> tags for proper line breaks
            .replace(/\n/g, '<br>');
    };

    // Handle key press (Enter to send)
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            handleSendMessage();
        }
    };

    return (
        <div className={`sidebar right-sidebar ${collapsed ? 'collapsed' : ''}`}>
            <div className="sidebar-header">
                <h3>AI Assistant</h3>
                <button className="toggle-btn" onClick={toggleCollapse}>
                    <CloseIcon />
                </button>
            </div>

            <div className="chat-area">
                {/*<div className="energy-sphere-container">
                    {/*<div className="ai-voice-video-container">
                        <video
                            src={aivoice}
                            autoPlay
                            loop
                            muted
                            playsInline
                            className="ai-voice-video"
                        />
                    </div>
                    {/*<EnergySphere
                        mode={sphereMode}
                        emotion={sphereEmotion}
                        active={isActive}
                        size={200}
                        barCount={72}
                        particleCount={120}
                        speed={1.2}
                    />
                </div>*/}

                <div className="chat-messages">
                    {messages.map((message, index) => (
                        <div key={index} className={`message ${message.sender}`}>
                            <div className="message-content">
                                <p dangerouslySetInnerHTML={{ __html: formatMessageText(message.text) }}></p>
                                {message.audio && (
                                    <div className="audio-player">
                                        <audio controls src={message.audio}></audio>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                    {isLoading && (
                        <div className="message ai loading">
                            <div className="message-content">
                                <div className="typing-indicator">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    )}
                    <div ref={messagesEndRef} />
                </div>

                <div className="chat-bottom-container">
                    <div className="chat-controls">
                        <button
                            className={`voice-btn ${isListening ? 'listening' : ''}`}
                            onClick={toggleSpeechRecognition}
                            title={isListening ? "Stop listening" : "Start voice input"}
                            disabled={isSpeaking}
                        >
                            {isListening ? <MicOffIcon /> : <MicIcon />}
                        </button>

                        {/* Speech control buttons - only show when AI is speaking */}
                        {isSpeaking && (
                            <div className="speech-controls">
                                <button
                                    className={`speech-control-btn ${isPaused ? 'paused' : ''}`}
                                    onClick={togglePauseSpeech}
                                    title={isPaused ? "Resume speech" : "Pause speech"}
                                >
                                    {isPaused ? <PlayArrowIcon /> : <PauseIcon />}
                                </button>
                                <button
                                    className="speech-control-btn stop"
                                    onClick={stopSpeech}
                                    title="Stop speech"
                                >
                                    <StopIcon />
                                </button>
                            </div>
                        )}

                        <div className="voice-controls">
                            <button
                                className={`tts-btn ${textToSpeechEnabled ? 'enabled' : ''}`}
                                onClick={() => {
                                    // If turning off TTS while speaking, stop the speech
                                    if (textToSpeechEnabled && isSpeaking) {
                                        stopSpeech();
                                    }
                                    setTextToSpeechEnabled(!textToSpeechEnabled);
                                }}
                                title={textToSpeechEnabled ? "Disable text-to-speech (stops current speech)" : "Enable text-to-speech"}
                            >
                                {textToSpeechEnabled ? <VolumeUpIcon /> : <VolumeOffIcon />}
                            </button>

                            {/* Voice selector toggle button - only show when TTS is enabled */}
                            {textToSpeechEnabled && (
                                <button
                                    className="voice-toggle-btn"
                                    onClick={() => setVoiceMenuOpen(!voiceMenuOpen)}
                                    title="Select voice"
                                    style={{
                                        background: 'transparent',
                                        border: 'none',
                                        color: '#007AFF',
                                        fontSize: '0.8rem',
                                        cursor: 'pointer',
                                        padding: '4px 8px',
                                        borderRadius: '4px',
                                        marginLeft: '4px'
                                    }}
                                >
                                    {selectedVoice === 'female' ? 'F' : 'M'}
                                </button>
                            )}

                            {textToSpeechEnabled && voiceMenuOpen && (
                                <div className="voice-selector">
                                    <div className="voice-selector-label">Voice:</div>
                                    <button
                                        className={`voice-toggle ${selectedVoice === 'female' ? 'selected' : ''}`}
                                        onClick={() => {
                                            setSelectedVoice('female');
                                            // Test the voice after a short delay to allow state update
                                            setTimeout(() => testSelectedVoice(), 100);
                                            setVoiceMenuOpen(false);
                                        }}
                                        title="Use female teacher voice"
                                    >
                                        Female
                                    </button>
                                    <button
                                        className={`voice-toggle ${selectedVoice === 'male' ? 'selected' : ''}`}
                                        onClick={() => {
                                            setSelectedVoice('male');
                                            // Test the voice after a short delay to allow state update
                                            setTimeout(() => testSelectedVoice(), 100);
                                            setVoiceMenuOpen(false);
                                        }}
                                        title="Use male teacher voice"
                                    >
                                        Male
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="chat-input">
                        <input
                            ref={inputRef}
                            type="text"
                            placeholder={isListening ? "Listening..." : "Ask me anything about your studies..."}
                            value={isListening ? transcript : inputText}
                            onChange={(e) => setInputText(e.target.value)}
                            onKeyDown={handleKeyPress}
                            disabled={isListening}
                        />
                        <button
                            className="send-btn"
                            onClick={handleSendMessage}
                            disabled={(!inputText.trim() && !isListening) || isLoading}
                        >
                            <SendIcon />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RightSidebar;
