import React, { useState, useEffect } from 'react';
import { usePomodoroContext } from '../contexts/pomodoroContext';
import '../styles/MiniTimer.scss';

/**
 * MiniTimer Component
 * A floating mini timer that shows the current Pomodoro session
 */
const MiniTimer = () => {
    const {
        isRunning,
        isWorkPhase,
        timeLeft,
        workDuration,
        breakDuration,
        formatTime,
        toggleTimer,
        toggleMiniTimer,
        showMiniTimer,
        showPomodoroPopupAndCloseMini
    } = usePomodoroContext();

    // Theme detection with MutationObserver to detect theme changes
    const [isDarkMode, setIsDarkMode] = useState(false);

    useEffect(() => {
        const checkDarkMode = () => {
            setIsDarkMode(document.body.classList.contains('theme-dark'));
        };

        // Initial check
        checkDarkMode();

        // Set up observer to detect theme changes
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    if (!showMiniTimer) return null;

    return (
        <div className={`mini-timer ${!isDarkMode ? 'theme-light' : ''}`}>
            <div className="mini-timer-header">
                <span>{isWorkPhase ? 'Study' : 'Break'} Time</span>
                <div className="mini-timer-controls">
                    <button
                        className="mini-timer-button"
                        onClick={showPomodoroPopupAndCloseMini}
                        aria-label="Open full Pomodoro timer"
                        title="Open full timer"
                    >
                        🔍
                    </button>
                    <button
                        className="mini-timer-button"
                        onClick={toggleTimer}
                        aria-label={isRunning ? 'Pause timer' : 'Start timer'}
                    >
                        {isRunning ? '⏸️' : '▶️'}
                    </button>
                    <button
                        className="mini-timer-button"
                        onClick={toggleMiniTimer}
                        aria-label="Close mini timer"
                    >
                        ✖️
                    </button>
                </div>
            </div>
            <div className="mini-timer-body">
                <div className="mini-timer-time">{formatTime(timeLeft)}</div>
                <div className="mini-timer-progress">
                    <div
                        className={`mini-timer-progress-bar ${isWorkPhase ? 'work' : 'break'}`}
                        style={{
                            width: `${(timeLeft / (isWorkPhase ? workDuration : breakDuration)) * 100}%`
                        }}
                        aria-valuemin="0"
                        aria-valuemax="100"
                        aria-valuenow={(timeLeft / (isWorkPhase ? workDuration : breakDuration)) * 100}
                        role="progressbar"
                    ></div>
                </div>
            </div>
        </div>
    );
};

export default MiniTimer;
