import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../authContext';
import { useNavigate } from 'react-router-dom';
import '../styles/StudyPlan.scss';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';

const StudyPlanList = () => {
    const { user, token } = useAuth();
    const navigate = useNavigate();
    const [studyPlans, setStudyPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    // Use the correct port for the backend server
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

    useEffect(() => {
        const fetchStudyPlans = async () => {
            if (!user || !token) return;

            try {
                setLoading(true);
                console.log('Fetching study plans for user:', user.id);
                console.log('Using API URL:', API_URL);

                // Make the actual API call to get study plans
                const response = await axios.get(
                    `${API_URL}/api/get_study_plans/${user.id}`,
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );

                console.log('Study plans response:', response.data);

                if (response.data.success) {
                    setStudyPlans(response.data.study_plans || []);
                } else {
                    throw new Error(response.data.error || 'Failed to fetch study plans');
                }
            } catch (error) {
                console.error('Error fetching study plans:', error);
                setError(error.message || 'Failed to load study plans. Please try again.');
            } finally {
                setLoading(false);
            }
        };

        fetchStudyPlans();
    }, [user, token, API_URL]);

    const handleViewPlan = (planId) => {
        navigate(`/study-plan/${planId}`);
    };

    const handleDeletePlan = async (planId, event) => {
        // Stop event propagation to prevent navigating to the plan details
        event.stopPropagation();

        // Confirm deletion
        const confirmDelete = window.confirm('Are you sure you want to delete this study plan? This action cannot be undone.');
        if (!confirmDelete) return;

        try {
            setLoading(true);
            console.log('Deleting study plan:', planId);

            // Make API call to delete the study plan
            const response = await axios.delete(
                `${API_URL}/api/delete_study_plan/${planId}`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log('Delete response:', response.data);

            if (response.data.success) {
                // Remove the deleted plan from the state
                setStudyPlans(prevPlans => prevPlans.filter(plan => plan.id !== planId));
            } else {
                throw new Error(response.data.error || 'Failed to delete study plan');
            }
        } catch (error) {
            console.error('Error deleting study plan:', error);
            alert(`Error deleting study plan: ${error.message || 'Unknown error'}`);
        } finally {
            setLoading(false);
        }
    };

    const formatDate = (dateString) => {
        const options = { year: 'numeric', month: 'short', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    const calculateDaysLeft = (endDate) => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const end = new Date(endDate);
        end.setHours(0, 0, 0, 0);

        const diffTime = end - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays;
    };

    if (loading) {
        return (
            <div className="study-plans-container loading">
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                    <CircularProgress />
                </Box>
            </div>
        );
    }

    if (error) {
        return (
            <div className="study-plans-container error">
                <h2>Error Loading Study Plans</h2>
                <p>{error}</p>
                <button
                    className="macos-button"
                    onClick={() => window.location.reload()}
                >
                    Try Again
                </button>
            </div>
        );
    }

    if (studyPlans.length === 0) {
        return (
            <div className="study-plans-container empty">
                <h2>No Study Plans Found</h2>
                <p>You haven't created any study plans yet. Upload a document and create a study plan to get started.</p>
                <button
                    className="macos-button primary"
                    onClick={() => navigate('/upload')}
                >
                    Upload Document
                </button>
            </div>
        );
    }

    return (
        <div className="study-plans-container">
            <h2>Your Study Plans</h2>
            <div className="study-plans-grid">
                {studyPlans.map(plan => {
                    const daysLeft = calculateDaysLeft(plan.end_date);
                    const isActive = daysLeft >= 0;

                    return (
                        <div
                            key={plan.id}
                            className={`study-plan-card ${!isActive ? 'completed' : ''}`}
                        >
                            <div className="plan-header">
                                <h3>{plan.name}</h3>
                                <div className="progress-indicator">
                                    <CircularProgress
                                        variant="determinate"
                                        value={plan.progress.percentage}
                                        size={40}
                                        thickness={4}
                                        sx={{
                                            color: plan.progress.percentage >= 100 ? '#4caf50' : '#2196f3',
                                            backgroundColor: 'rgba(0,0,0,0.1)',
                                            borderRadius: '50%'
                                        }}
                                    />
                                    <div className="progress-label">
                                        {plan.progress.percentage}%
                                    </div>
                                </div>
                            </div>

                            <div className="plan-details">
                                <div className="detail-item">
                                    <MenuBookIcon />
                                    <span>{plan.curriculum_title}</span>
                                </div>
                                <div className="detail-item">
                                    <CalendarTodayIcon />
                                    <span>{formatDate(plan.start_date)} - {formatDate(plan.end_date)}</span>
                                </div>
                                <div className="detail-item">
                                    <AccessTimeIcon />
                                    <span>{plan.daily_study_time_minutes} minutes daily</span>
                                </div>
                            </div>

                            <div className="plan-status">
                                {isActive ? (
                                    <div className="days-left">
                                        <span className="days">{daysLeft}</span>
                                        <span className="label">days left</span>
                                    </div>
                                ) : (
                                    <div className="completed-label">Completed</div>
                                )}

                                <div className="objectives-count">
                                    <span className="completed">{plan.progress.completed_objectives}</span>
                                    <span className="separator">/</span>
                                    <span className="total">{plan.progress.total_objectives}</span>
                                    <span className="label">objectives</span>
                                </div>
                            </div>

                            <div className="plan-actions">
                                <button
                                    className="view-button"
                                    onClick={() => handleViewPlan(plan.id)}
                                >
                                    <VisibilityIcon /> View Plan
                                </button>
                                <button
                                    className="delete-button"
                                    onClick={(e) => handleDeletePlan(plan.id, e)}
                                >
                                    <DeleteIcon /> Delete
                                </button>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default StudyPlanList;
