

├── .github/                  # CI/CD workflows (GitHub Actions)
│   └── workflows/
│       ├── backend-tests.yml
│       └── frontend-deploy.yml
├── backend/                  # Django/FastAPI backend
│   ├── core/                 # Main app
│   │   ├── models/           # Database models
│   │   │   ├── user.py
│   │   │   ├── documents.py  # Notes/PDFs
│   │   │   └── ai_requests.py
│   │   ├── serializers/      # API data transformers
│   │   ├── views/            # API endpoints
│   │   ├── services/         # Business logic
│   │   │   ├── ai_summarizer.py
│   │   │   └── math_solver.py
│   │   └── utils/            # OCR, file processing
│   ├── ml_models/            # Custom AI models (SymPy, Tesseract)
│   ├── requirements.txt      # Python dependencies
│   └── manage.py
├── frontend/                 # React.js/Flutter
│   ├── web/                  # React frontend
│   │   ├── public/           # Static assets
│   │   ├── src/
│   │   │   ├── components/   # Reusable UI (NoteEditor, MathSolver)
│   │   │   ├── pages/        # Next.js-style routing
│   │   │   ├── hooks/        # Custom hooks (e.g., useAIChat)
│   │   │   └── stores/       # Zustand/Redux state
│   │   └── package.json
│   └──
├── ai_services/              # Isolated AI microservices
│   ├── summarizer/           # GPT-4/Claude API wrapper
│   │   ├── Dockerfile
│   │   └── main.py
│   └── ocr/                  # Tesseract/Whisper
│       └── Dockerfile
├── infrastructure/           # DevOps
│   ├── docker-compose.yml    # Multi-container setup
│   ├── nginx/                # Reverse proxy config
│   └── terraform/            # Cloud provisioning (AWS/GCP)
├── docs/                     # Project documentation
│   ├── API.md                # Swagger/OpenAPI specs
│   └── ARCHITECTURE.md
└── .env.example              # Environment variables

















-- Add chapter_number column to the chapters table as VARCHAR to store "Chapter X" format
ALTER TABLE chapters 
ADD COLUMN chapter_number VARCHAR(50);

-- Create an index on chapter_number for better query performance
CREATE INDEX idx_chapters_chapter_number ON chapters(chapter_number);

-- Update existing records (optional - if you want to set default values)
-- This example sets chapter numbers in "Chapter X" format for each subject
WITH numbered_chapters AS (
  SELECT 
    id,
    subject_id,
    'Chapter ' || ROW_NUMBER() OVER (PARTITION BY subject_id ORDER BY id) AS new_chapter_number
  FROM chapters
)
UPDATE chapters
SET chapter_number = numbered_chapters.new_chapter_number
FROM numbered_chapters
WHERE chapters.id = numbered_chapters.id;