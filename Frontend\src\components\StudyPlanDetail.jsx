import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../authContext';
import { useParams, useNavigate } from 'react-router-dom';
import '../styles/StudyPlan.scss';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import ListAltIcon from '@mui/icons-material/ListAlt';
import FilterListIcon from '@mui/icons-material/FilterList';
import SortIcon from '@mui/icons-material/Sort';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

const StudyPlanDetail = () => {
    const { planId } = useParams();
    const { user, token } = useAuth();
    const navigate = useNavigate();
    const [studyPlan, setStudyPlan] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [activeDate, setActiveDate] = useState(null);
    const [viewMode, setViewMode] = useState('daily'); // 'daily', 'all', or 'chapters'
    const [expandedChapters, setExpandedChapters] = useState({});
    const [allObjectives, setAllObjectives] = useState([]);
    const [sortBy, setSortBy] = useState('chapter'); // 'chapter', 'difficulty', or 'completion'
    const [filterCompleted, setFilterCompleted] = useState('all'); // 'all', 'completed', 'incomplete'
    // Use the correct API URL for study plan details
    const API_URL = 'http://localhost:5001';

    useEffect(() => {
        const fetchStudyPlanDetails = async () => {
            if (!user || !token || !planId) return;

            try {
                setLoading(true);
                console.log('Fetching study plan details for plan ID:', planId);
                console.log('Using API URL:', API_URL);

                // Make the actual API call to get study plan details
                const response = await axios.get(
                    `${API_URL}/api/get_study_plan_details/${planId}`,
                    {
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }
                );

                console.log('Study plan details response:', response.data);

                if (response.data.success) {
                    setStudyPlan(response.data.study_plan);

                    // Set active date to today or first available date
                    const today = new Date();
                    const todayStr = today.toISOString().split('T')[0];

                    if (response.data.study_plan.schedule && response.data.study_plan.schedule.length > 0) {
                        const todaySchedule = response.data.study_plan.schedule.find(day => day.date === todayStr);

                        if (todaySchedule) {
                            setActiveDate(todayStr);
                        } else {
                            // Sort dates chronologically
                            const sortedDates = [...response.data.study_plan.schedule].sort((a, b) =>
                                new Date(a.date) - new Date(b.date)
                            );
                            setActiveDate(sortedDates[0].date);
                        }

                        // Process all objectives for the "All Objectives" view
                        const objectives = [];
                        response.data.study_plan.schedule.forEach(day => {
                            day.items.forEach(item => {
                                objectives.push({
                                    ...item,
                                    scheduled_date: day.date
                                });
                            });
                        });
                        setAllObjectives(objectives);
                    }
                } else {
                    throw new Error(response.data.error || 'Failed to fetch study plan details');
                }
            } catch (error) {
                console.error('Error fetching study plan details:', error);
                setError(error.message || 'Failed to load study plan details. Please try again.');
            } finally {
                setLoading(false);
            }
        };

        fetchStudyPlanDetails();
    }, [planId, user, token, API_URL]);

    const handleToggleObjective = async (itemId, isCompleted) => {
        if (!user || !token) return;

        try {
            console.log('Updating objective:', itemId, 'to completed:', !isCompleted);

            // Make the actual API call to update the objective
            const response = await axios.put(
                `${API_URL}/api/update_schedule_item/${itemId}`,
                { is_completed: !isCompleted },
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log('Update response:', response.data);

            if (response.data.success) {
                // Update local state with the response data
                setStudyPlan(prevPlan => {
                    const updatedSchedule = prevPlan.schedule.map(day => {
                        if (day.date === activeDate) {
                            const updatedItems = day.items.map(item => {
                                if (item.id === itemId) {
                                    return {
                                        ...item,
                                        is_completed: !isCompleted,
                                        completion_date: !isCompleted ? new Date().toISOString() : null
                                    };
                                }
                                return item;
                            });
                            return { ...day, items: updatedItems };
                        }
                        return day;
                    });
                    return { ...prevPlan, schedule: updatedSchedule };
                });

                // Also update the allObjectives state
                setAllObjectives(prevObjectives => {
                    return prevObjectives.map(obj => {
                        if (obj.id === itemId) {
                            return {
                                ...obj,
                                is_completed: !isCompleted,
                                completion_date: !isCompleted ? new Date().toISOString() : null
                            };
                        }
                        return obj;
                    });
                });
            } else {
                throw new Error(response.data.error || 'Failed to update objective');
            }
        } catch (error) {
            console.error('Error updating objective:', error);
            // Show error message to user
            alert(`Error updating objective: ${error.message || 'Unknown error'}`);
        }
    };

    const formatDate = (dateString) => {
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    const calculateProgress = () => {
        if (!studyPlan) return 0;

        let totalItems = 0;
        let completedItems = 0;

        studyPlan.schedule.forEach(day => {
            totalItems += day.items.length;
            completedItems += day.items.filter(item => item.is_completed).length;
        });

        return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
    };

    // Function to sort objectives based on the selected sort criteria
    const getSortedObjectives = () => {
        if (!allObjectives.length) return [];

        const objectives = [...allObjectives];

        switch (sortBy) {
            case 'chapter':
                // Sort by chapter number
                return objectives.sort((a, b) => {
                    // Extract chapter numbers from chapter names
                    const getChapterNumber = (item) => {
                        const chapter = item.chapter?.name || '';
                        const match = chapter.match(/Chapter\s+(\d+)/i);
                        return match ? parseInt(match[1]) : 0;
                    };

                    return getChapterNumber(a) - getChapterNumber(b);
                });

            case 'difficulty':
                // Sort by difficulty level (highest to lowest)
                return objectives.sort((a, b) =>
                    (b.objective?.difficulty_level || 0) - (a.objective?.difficulty_level || 0)
                );

            case 'completion':
                // Sort by completion status (incomplete first)
                return objectives.sort((a, b) => {
                    if (a.is_completed === b.is_completed) {
                        // If completion status is the same, sort by scheduled date
                        return new Date(a.scheduled_date) - new Date(b.scheduled_date);
                    }
                    return a.is_completed ? 1 : -1;
                });

            case 'date':
                // Sort by scheduled date
                return objectives.sort((a, b) =>
                    new Date(a.scheduled_date) - new Date(b.scheduled_date)
                );

            default:
                return objectives;
        }
    };

    // Function to filter objectives based on completion status
    const getFilteredObjectives = () => {
        const sorted = getSortedObjectives();

        switch (filterCompleted) {
            case 'completed':
                return sorted.filter(item => item.is_completed);

            case 'incomplete':
                return sorted.filter(item => !item.is_completed);

            default:
                return sorted;
        }
    };

    const toggleChapter = (chapterId) => {
        setExpandedChapters(prev => ({
            ...prev,
            [chapterId]: !prev[chapterId]
        }));
    };

    if (loading) {
        return (
            <div className="study-plan-detail-container loading">
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                    <CircularProgress />
                </Box>
            </div>
        );
    }

    if (error) {
        return (
            <div className="study-plan-detail-container error">
                <h2>Error Loading Study Plan</h2>
                <p>{error}</p>
                <button
                    className="macos-button"
                    onClick={() => window.location.reload()}
                >
                    Try Again
                </button>
                <button
                    className="macos-button secondary"
                    onClick={() => navigate('/study-plans')}
                >
                    <ArrowBackIcon /> Back to Study Plans
                </button>
            </div>
        );
    }

    if (!studyPlan) {
        return (
            <div className="study-plan-detail-container not-found">
                <h2>Study Plan Not Found</h2>
                <p>The requested study plan could not be found.</p>
                <button
                    className="macos-button"
                    onClick={() => navigate('/study-plans')}
                >
                    <ArrowBackIcon /> Back to Study Plans
                </button>
            </div>
        );
    }

    const progress = calculateProgress();
    const activeDaySchedule = studyPlan.schedule.find(day => day.date === activeDate);

    return (
        <div className="study-plan-detail-container">
            <div className="header-section">
                <button
                    className="back-button"
                    onClick={() => navigate('/study-plans')}
                >
                    <ArrowBackIcon /> Back to Study Plans
                </button>
                <h1>{studyPlan.name}</h1>
                <div className="plan-meta">
                    <div className="meta-item">
                        <CalendarTodayIcon />
                        <span>{formatDate(studyPlan.start_date)} - {formatDate(studyPlan.end_date)}</span>
                    </div>
                    <div className="meta-item">
                        <AccessTimeIcon />
                        <span>{studyPlan.daily_study_time_minutes} minutes daily</span>
                    </div>
                </div>
                <div className="progress-section">
                    <div className="progress-circle">
                        <CircularProgress
                            variant="determinate"
                            value={progress}
                            size={80}
                            thickness={4}
                            sx={{
                                color: progress >= 100 ? '#4caf50' : '#2196f3',
                                backgroundColor: 'rgba(0,0,0,0.1)',
                                borderRadius: '50%'
                            }}
                        />
                        <div className="progress-label">
                            {progress}%
                        </div>
                    </div>
                    <div className="progress-text">
                        <h3>Overall Progress</h3>
                        <p>Keep going! You're doing great.</p>
                    </div>
                </div>
            </div>

            <div className="view-mode-selector">
                <button
                    className={`view-mode-button ${viewMode === 'daily' ? 'active' : ''}`}
                    onClick={() => setViewMode('daily')}
                >
                    <CalendarTodayIcon /> Daily View
                </button>
                <button
                    className={`view-mode-button ${viewMode === 'all' ? 'active' : ''}`}
                    onClick={() => setViewMode('all')}
                >
                    <ListAltIcon /> All Objectives
                </button>
                <button
                    className={`view-mode-button ${viewMode === 'chapters' ? 'active' : ''}`}
                    onClick={() => setViewMode('chapters')}
                >
                    <MenuBookIcon /> Chapters View
                </button>
            </div>

            {viewMode === 'daily' ? (
                <div className="schedule-section">
                    <div className="date-selector">
                        <h3>Study Schedule</h3>
                        <div className="date-buttons">
                            {studyPlan.schedule.map(day => (
                                <button
                                    key={day.date}
                                    className={`date-button ${day.date === activeDate ? 'active' : ''}`}
                                    onClick={() => setActiveDate(day.date)}
                                >
                                    {new Date(day.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="daily-schedule">
                        <h3>{formatDate(activeDate)}</h3>

                        {activeDaySchedule && activeDaySchedule.items.length > 0 ? (
                            <div className="objectives-list">
                                {activeDaySchedule.items.map(item => (
                                    <div
                                        key={item.id}
                                        className={`objective-item ${item.is_completed ? 'completed' : ''}`}
                                        onClick={() => handleToggleObjective(item.id, item.is_completed)}
                                    >
                                        <div className="check-icon">
                                            {item.is_completed ?
                                                <CheckCircleIcon className="completed-icon" /> :
                                                <RadioButtonUncheckedIcon className="incomplete-icon" />
                                            }
                                        </div>
                                        <div className="objective-content">
                                            <div className="objective-text">{item.objective.text}</div>
                                            <div className="objective-meta">
                                                <span className="subject">{item.subject?.name}</span>
                                                <span className="chapter">{item.chapter?.name}</span>
                                                <span className="time">{item.objective.estimated_time_minutes} min</span>
                                                <span className={`difficulty level-${item.objective.difficulty_level}`}>
                                                    Level {item.objective.difficulty_level}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="no-objectives">
                                <p>No objectives scheduled for this day.</p>
                            </div>
                        )}
                    </div>
                </div>
            ) : viewMode === 'all' ? (
                <div className="all-objectives-section">
                    <div className="all-objectives-controls">
                        <h3>All Objectives</h3>
                        <div className="controls">
                            <div className="filter-control">
                                <label><FilterListIcon /> Filter:</label>
                                <select
                                    value={filterCompleted}
                                    onChange={(e) => setFilterCompleted(e.target.value)}
                                >
                                    <option value="all">All</option>
                                    <option value="completed">Completed</option>
                                    <option value="incomplete">Incomplete</option>
                                </select>
                            </div>
                            <div className="sort-control">
                                <label><SortIcon /> Sort by:</label>
                                <select
                                    value={sortBy}
                                    onChange={(e) => setSortBy(e.target.value)}
                                >
                                    <option value="chapter">Chapter</option>
                                    <option value="difficulty">Difficulty</option>
                                    <option value="completion">Completion Status</option>
                                    <option value="date">Scheduled Date</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="all-objectives-list">
                        {getFilteredObjectives().length > 0 ? (
                            getFilteredObjectives().map(item => (
                                <div
                                    key={item.id}
                                    className={`objective-item ${item.is_completed ? 'completed' : ''}`}
                                    onClick={() => handleToggleObjective(item.id, item.is_completed)}
                                >
                                    <div className="check-icon">
                                        {item.is_completed ?
                                            <CheckCircleIcon className="completed-icon" /> :
                                            <RadioButtonUncheckedIcon className="incomplete-icon" />
                                        }
                                    </div>
                                    <div className="objective-content">
                                        <div className="objective-text">{item.objective.text}</div>
                                        <div className="objective-meta">
                                            <span className="subject">{item.subject?.name}</span>
                                            <span className="chapter">{item.chapter?.name}</span>
                                            <span className="time">{item.objective.estimated_time_minutes} min</span>
                                            <span className={`difficulty level-${item.objective.difficulty_level}`}>
                                                Level {item.objective.difficulty_level}
                                            </span>
                                            <span className="date">
                                                {new Date(item.scheduled_date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="no-objectives">
                                <p>No objectives match the current filter.</p>
                            </div>
                        )}
                    </div>
                </div>
            ) : (
                <div className="chapters-section">
                    <h3>Chapters and Objectives</h3>
                    {studyPlan.chapters && studyPlan.chapters.length > 0 ? (
                        <div className="chapters-list">
                            {studyPlan.chapters.map(chapter => {
                                // Calculate completion percentage for this chapter
                                const totalObjectives = chapter.objectives?.length || 0;
                                const completedObjectives = chapter.objectives?.filter(obj => obj.is_completed)?.length || 0;
                                const completionPercentage = totalObjectives > 0
                                    ? Math.round((completedObjectives / totalObjectives) * 100)
                                    : 0;

                                // Check if this chapter is expanded
                                const isExpanded = expandedChapters[chapter.id] === true;

                                return (
                                    <div key={chapter.id} className="chapter-item">
                                        <div
                                            className="chapter-header"
                                            onClick={() => toggleChapter(chapter.id)}
                                        >
                                            <div className="chapter-header-content">
                                                <h4>
                                                    {chapter.chapter_number
                                                        ? isExpanded
                                                            ? `${chapter.chapter_number}: ${chapter.title || chapter.name}`
                                                            : chapter.chapter_number
                                                        : chapter.name
                                                    }
                                                </h4>
                                                <div className="chapter-meta">
                                                    <span className="objectives-count">{totalObjectives} objectives</span>
                                                    <span className="completion-percentage">{completionPercentage}% complete</span>
                                                </div>
                                            </div>
                                            <div className="expand-icon">
                                                {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                                            </div>
                                        </div>

                                        {isExpanded && chapter.objectives && chapter.objectives.length > 0 ? (
                                            <div className="chapter-objectives">
                                                {chapter.objectives.map(objective => (
                                                    <div
                                                        key={objective.id}
                                                        className={`objective-item ${objective.is_completed ? 'completed' : ''}`}
                                                        onClick={() => handleToggleObjective(objective.schedule_item_id, objective.is_completed)}
                                                    >
                                                        <div className="check-icon">
                                                            {objective.is_completed ?
                                                                <CheckCircleIcon className="completed-icon" /> :
                                                                <RadioButtonUncheckedIcon className="incomplete-icon" />
                                                            }
                                                        </div>
                                                        <div className="objective-content">
                                                            <div className="objective-text">{objective.text}</div>
                                                            <div className="objective-meta">
                                                                <span className="time">{objective.estimated_time_minutes} min</span>
                                                                <span className={`difficulty level-${objective.difficulty_level}`}>
                                                                    Level {objective.difficulty_level}
                                                                </span>
                                                                <span className="date">
                                                                    {new Date(objective.scheduled_date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : isExpanded ? (
                                            <p className="no-objectives">No objectives for this chapter.</p>
                                        ) : null}
                                    </div>
                                );
                            })}
                        </div>
                    ) : (
                        <p className="no-chapters">No chapters found for this study plan.</p>
                    )}
                </div>
            )}
        </div>
    );
};

export default StudyPlanDetail;
