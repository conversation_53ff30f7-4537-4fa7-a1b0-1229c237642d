@use "variables" as *;
@use "sass:color";
@use "scrollbar" as *;

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Progress Card - macOS style
.progress-card {
    background-color: $window-background;
    border: 1px solid $border-color;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    //z-index: 1;
    border-radius: 12px;
    padding: .5rem !important;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
    width: 100%;
    height: 100%;
    margin: 0;
    position: relative;
    overflow: hidden;

    // Dark theme
    .theme-dark & {
        background-color: $dark-window-background;
        border-color: $dark-border-color;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.5) 50%,
                rgba(255, 255, 255, 0) 100%);
        z-index: 1;
    }

    &:hover {
        transform: translateY(-4px) scale(1.01);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba($system-blue, 0.3), 0 2px 12px rgba(0, 0, 0, 0.08);
    }

    .card-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: $primary-text;
        margin-bottom: 16px;
        text-align: center;
        letter-spacing: 0.2px;
        position: relative;

        // Dark theme
        .theme-dark & {
            color: $dark-primary-text;
        }

        &::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: $system-blue;
            border-radius: 2px;
            opacity: 0.7;
        }
    }

    .progress-circle {
        position: relative;
        width: 100%;
        height: fit-content;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 1.5rem auto;

        .progress-ring {
            aspect-ratio: 1 / 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: auto;
            //width: 100%;
            //height: fit-content;
            border-radius: 50%;
            filter: drop-shadow(0 0 10px rgba($system-blue, 0.2));
            transform: rotate(0deg);

            // Outer border
            .progress-ring-outer-border {
                fill: none;
                stroke: rgba($system-blue, 0.3);
                stroke-width: 1;
            }

            // Inner border
            .progress-ring-inner-border {
                fill: none;
                stroke: rgba($system-blue, 0.3);
                stroke-width: 1;
            }

            // Clear center
            .progress-ring-center {
                fill: transparent;
            }

            // Segmented background
            .progress-ring-segment {
                fill: rgba($system-blue, 0.05);
                stroke: rgba($system-blue, 0.1);
                stroke-width: 0.5;

                &:nth-child(odd) {
                    fill: rgba($system-blue, 0.03);
                }
            }

            // Main progress fill
            .progress-ring-fill {
                fill: none;
                stroke: $system-blue;
                stroke-width: 8;
                transition: stroke-dashoffset 1.2s cubic-bezier(0.2, 0.8, 0.2, 1);
                stroke-linecap: round;

            }
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            font-weight: 600;
            color: $system-blue;
            letter-spacing: 0.5px;
            background: transparent;
            aspect-ratio: 1 /1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            width: 7rem;
            background: $window-background;

            .theme-dark & {
                background: $dark-window-background;
            }

            &::after {
                content: '';
                font-size: 14px;
                font-weight: 500;
                margin-left: 2px;
                margin-top: -8px;
                opacity: 0.8;
            }
        }

        .progress-label {
            position: absolute;
            bottom: -24px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 13px;
            font-weight: 500;
            color: $secondary-text;
            letter-spacing: 0.5px;
            white-space: nowrap;

            // Dark theme
            .theme-dark & {
                color: $dark-secondary-text;
            }
        }

        .tooltip {
            position: absolute;
            top: -40px;
            left: 50%;
            transform: translateX(-50%);
            background: $window-background;
            color: $primary-text;
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 8px;
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
            border: 1px solid $border-color;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            z-index: 10;

            // Dark theme
            .theme-dark & {
                background: $dark-window-background;
                color: $dark-primary-text;
                border-color: $dark-border-color;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            &::after {
                content: '';
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                border-width: 6px;
                border-style: solid;
                border-color: $window-background transparent transparent transparent;

                // Dark theme
                .theme-dark & {
                    border-color: $dark-window-background transparent transparent transparent;
                }
            }
        }

        &:hover .tooltip {
            opacity: 1;
            top: -45px;
        }

        // Different color states based on progress
        &.low-progress {
            .progress-ring-fill {
                stroke: $system-red;
                filter: drop-shadow(0 0 3px rgba($system-red, 0.5));
            }

            .progress-text {
                color: $system-red !important;
            }
        }

        &.medium-progress {
            .progress-ring-fill {
                stroke: $system-orange;
                filter: drop-shadow(0 0 3px rgba($system-orange, 0.5));
            }

            .progress-text {
                color: $system-orange;
            }
        }

        &.high-progress {
            .progress-ring-fill {
                stroke: $system-green;
                filter: drop-shadow(0 0 3px rgba($system-green, 0.5));
            }

            .progress-text {
                color: $system-green;
            }
        }
    }
}

// Modal Overlay - macOS style
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);

    // Dark theme
    &.theme-dark {
        background: rgba(0, 0, 0, 0.4);
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }
}

// Modal Window - macOS style
.modal-window {
    background: rgba($window-background, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.05);
    width: 600px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    animation: slideIn 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
    position: relative;

    // Dark theme
    &.theme-dark {
        background: rgba($dark-window-background, 0.95);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.5) 50%,
                rgba(255, 255, 255, 0) 100%);
        z-index: 1;
    }

    @keyframes slideIn {
        from {
            transform: translateY(20px) scale(0.98);
            opacity: 0;
        }

        to {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
    }

    .modal-header {
        display: flex;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid $separator-color;
        position: relative;

        // Dark theme
        .theme-dark & {
            border-bottom-color: $dark-separator-color;
        }

        .traffic-lights {
            display: flex;
            gap: 8px;
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);

            .traffic-light {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: none;
                cursor: pointer;
                transition: all 0.2s ease;

                &.red {
                    background: $system-red;

                    &:hover {
                        background: darken($system-red, 5%);
                    }
                }

                &.yellow {
                    background: $system-yellow;

                    &:hover {
                        background: darken($system-yellow, 5%);
                    }
                }

                &.green {
                    background: $system-green;

                    &:hover {
                        background: darken($system-green, 5%);
                    }
                }

                &:disabled {
                    opacity: 0.4;
                    cursor: default;
                }
            }
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: $primary-text;
            flex: 1;
            text-align: center;
            margin: 0;
            letter-spacing: 0.2px;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }

    .modal-content {
        padding: 24px;

        .summary-section {
            margin-bottom: 28px;

            h3 {
                font-size: 16px;
                font-weight: 600;
                margin: 0 0 16px;
                color: $primary-text;
                position: relative;

                // Dark theme
                .theme-dark & {
                    color: $dark-primary-text;
                }

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: -8px;
                    width: 40px;
                    height: 2px;
                    background: $system-blue;
                    border-radius: 2px;
                    opacity: 0.7;
                }
            }

            p {
                font-size: 14px;
                color: $secondary-text;
                margin: 0 0 16px;
                line-height: 1.5;

                // Dark theme
                .theme-dark & {
                    color: $dark-secondary-text;
                }

                .streak-note {
                    color: $system-green;
                    margin-left: 8px;
                    font-weight: 500;
                    display: inline-flex;
                    align-items: center;

                    &::before {
                        content: '•';
                        margin-right: 4px;
                        font-size: 18px;
                    }
                }
            }

            .progress-bar {
                position: relative;
                height: 8px;
                background: rgba($system-blue, 0.1);
                border-radius: 4px;
                overflow: hidden;
                margin: 24px 0;

                .progress-fill {
                    height: 100%;
                    background: $system-blue;
                    transition: width 1s cubic-bezier(0.2, 0.8, 0.2, 1);
                    border-radius: 4px;

                    @keyframes fill {
                        from {
                            width: 0;
                        }
                    }

                    animation: fill 1s cubic-bezier(0.2, 0.8, 0.2, 1);
                }

                // Different color states based on progress
                &.low-progress .progress-fill {
                    background: $system-red;
                }

                &.medium-progress .progress-fill {
                    background: $system-orange;
                }

                &.high-progress .progress-fill {
                    background: $system-green;
                }

                .tooltip {
                    position: absolute;
                    top: -30px;
                    left: var(--tooltip-position, 50%);
                    transform: translateX(-50%);
                    background: $window-background;
                    color: $primary-text;
                    font-size: 12px;
                    padding: 4px 10px;
                    border-radius: 6px;
                    opacity: 0;
                    pointer-events: none;
                    transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
                    border: 1px solid $border-color;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    backdrop-filter: blur(20px);
                    z-index: 10;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 100%;
                        left: 50%;
                        transform: translateX(-50%);
                        border-width: 4px;
                        border-style: solid;
                        border-color: $window-background transparent transparent transparent;
                    }
                }

                &:hover .tooltip {
                    opacity: 1;
                    top: -35px;
                }
            }
        }

        .objectives-list {
            h3 {
                font-size: 16px;
                font-weight: 600;
                margin: 0 0 16px;
                color: $primary-text;
                position: relative;

                // Dark theme
                .theme-dark & {
                    color: $dark-primary-text;
                }

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: -8px;
                    width: 40px;
                    height: 2px;
                    background: $system-blue;
                    border-radius: 2px;
                    opacity: 0.7;
                }
            }

            .objective-items {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                    font-size: 14px;
                    color: $primary-text;
                    margin-bottom: 10px;
                    padding: 12px 16px;
                    border-radius: 8px;
                    background: $secondary-background;
                    border: 1px solid transparent;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                    cursor: pointer;
                    transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
                    display: flex;
                    align-items: center;
                    position: relative;
                    overflow: hidden;

                    // Dark theme
                    .theme-dark & {
                        color: $dark-primary-text;
                        background: $dark-secondary-background;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                    }

                    &::before {
                        content: '';
                        position: absolute;
                        left: 0;
                        top: 0;
                        bottom: 0;
                        width: 3px;
                        background: $system-blue;
                        opacity: 0;
                        transition: opacity 0.2s ease;
                    }

                    &.completed {
                        background: rgba($system-green, 0.05);
                        border-color: rgba($system-green, 0.2);

                        &::before {
                            background: $system-green;
                            opacity: 1;
                        }
                    }

                    &.pending {
                        opacity: 0.8;
                    }

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
                        border-color: $border-color;

                        &::before {
                            opacity: 1;
                        }
                    }

                    &:focus {
                        outline: none;
                        box-shadow: 0 0 0 3px rgba($system-blue, 0.3), 0 1px 3px rgba(0, 0, 0, 0.05);
                    }

                    strong {
                        font-weight: 600;
                        flex: 1;
                    }

                    .objective-details {
                        font-size: 12px;
                        color: $tertiary-text;
                        margin-left: 8px;
                        display: flex;
                        align-items: center;

                        // Dark theme
                        .theme-dark & {
                            color: $dark-tertiary-text;
                        }
                    }

                    &::before {
                        content: '•';
                        margin-right: 4px;
                        font-size: 14px;
                    }
                }

                .completed-icon {
                    margin-left: 12px;
                    color: $system-green;
                    font-size: 16px;
                    opacity: 0;
                    transition: opacity 0.2s ease;
                }

                &.completed .completed-icon {
                    opacity: 1;
                }
            }
        }
    }
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid $separator-color;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    // Dark theme
    .theme-dark & {
        border-top-color: $dark-separator-color;
    }

    .close-button,
    .action-button {
        background: $system-blue;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 18px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
        letter-spacing: 0.3px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            background: lighten($system-blue, 5%);
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            background: darken($system-blue, 5%);
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    }

    .secondary-button {
        background: $secondary-background;
        color: $secondary-text;
        border: 1px solid $border-color;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
            background: $dark-secondary-background;
            color: $dark-secondary-text;
            border-color: $dark-border-color;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            background: lighten($secondary-background, 2%);
            color: $primary-text;
        }

        &:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            background: darken($secondary-background, 2%);
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba($system-blue, 0.15);
        }
    }
}