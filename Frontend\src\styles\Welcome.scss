@use "variables" as *;
@use "sass:color";

// Common mixins and variables
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
    transition: $property $duration $timing;
}

@mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

@mixin hover-lift {
    &:hover {
        transform: translateY(-2px);
    }

    &:active {
        transform: translateY(0);
    }
}

// Theme mode classes
// These classes will be applied to the body element
// Light theme variables are already defined in _variables.scss
// Dark theme overrides are applied using the .theme-dark parent selector

// Main container - macOS style
.steps-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    border-radius: 12px;
    font-family: -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue", Helvetica, Arial, sans-serif;

    // Light theme styles
    background-color: white;
    border: 1px solid $light-accent-color-2;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);

    // Dark theme styles
    .theme-dark & {
        background-color: $dark-background;
        border: 1px solid $dark-accent-color-2;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
}

// Progress track - macOS style
.progress-track {
    height: 6px;
    border-radius: 6px;
    margin-bottom: 2rem;
    overflow: hidden;

    // Light theme styles
    background: $light-accent-color-2;

    // Dark theme styles
    .theme-dark & {
        background: $dark-accent-color-2;
    }
}

.progress-bar {
    height: 100%;
    background: $system-blue !important;
    border-radius: 6px;
    @include transition(width);
}

// Step indicators - macOS style
.step-indicators {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2.5rem;
}

.step-indicator {
    flex: 1;
    text-align: center;
    position: relative;
    cursor: pointer;
    padding-top: 28px;
    background: none !important;
    border: none;
    display: flex;
    justify-content: center;
    @include transition;

    &:hover {
        .step-number {
            transform: translateX(-50%) scale(1.05);

            // Light theme hover
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);

            // Dark theme hover
            .theme-dark & {
                box-shadow: 0 2px 8px rgba(0, 122, 255, 0.4);
            }
        }
    }

    &.active {
        .step-number {
            background: $color-primary;
            color: white;

            // Light theme active
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);

            // Dark theme active
            .theme-dark & {
                box-shadow: 0 2px 8px rgba(0, 122, 255, 0.5);
            }
        }

        .step-label {
            // Light theme active
            color: $light-primary-text;

            // Dark theme active
            .theme-dark & {
                color: $dark-primary-text;
            }

            font-weight: 500;
        }
    }
}

.step-number {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 26px;
    height: 26px;
    border-radius: 50%;
    @include flex-center;
    font-size: 13px;
    font-weight: 500;
    @include transition;

    // Light theme styles
    background: $light-accent-color-2;
    color: $light-secondary-text;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    // Dark theme styles
    .theme-dark & {
        background: $dark-accent-color-2;
        color: $dark-secondary-text;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
}

.step-label {
    font-size: 13px;
    font-weight: 400;
    @include transition;

    // Light theme styles
    color: $light-secondary-text;

    // Dark theme styles
    .theme-dark & {
        color: $dark-secondary-text;
    }
}

// Step panels - macOS style
.step-panel {
    padding: 0 1.5rem;
    min-height: 220px;
    display: flex;
    flex-direction: column;

    h2 {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
        text-align: center;
        font-weight: 600;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    p {
        margin-bottom: 2rem;
        text-align: center;
        font-size: 0.95rem;
        line-height: 1.4;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-secondary-text;
        }
    }

    input {
        // Light theme
        background-color: $light-background;
        color: $light-primary-text !important;

        // Dark theme
        .theme-dark & {
            background-color: color.adjust($dark-background, $lightness: 5%);
            color: $dark-primary-text !important;
        }
    }
}

// Options grids - macOS style
.language-options,
.role-options,
.goal-options,
.tool-options,
.chronotype-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 14px;
    margin-bottom: 2.5rem;

    button {
        padding: 14px 10px;
        border-radius: 8px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        font-weight: 500;
        @include transition;

        // Light theme
        background: white;
        border: 1px solid $light-accent-color-2;
        color: $light-primary-text;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        // Dark theme
        .theme-dark & {
            background: color.adjust($dark-background, $lightness: 5%);
            border: 1px solid $dark-accent-color-2;
            color: $dark-primary-text;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        &:hover {
            border-color: $color-primary;
            transform: translateY(-2px);

            // Light theme hover
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

            // Dark theme hover
            .theme-dark & {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }
        }

        &:active {
            transform: translateY(0);
        }

        &.selected {
            border-color: $color-primary;

            // Light theme selected
            background: rgba(0, 122, 255, 0.1);
            color: $color-primary;
            box-shadow: 0 2px 6px rgba(0, 122, 255, 0.15);

            // Dark theme selected
            .theme-dark & {
                background: rgba(0, 122, 255, 0.2);
                color: color.adjust($color-primary, $lightness: 15%);
                box-shadow: 0 2px 6px rgba(0, 122, 255, 0.3);
            }
        }
    }
}

// Navigation buttons - macOS style
.nav-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1.5rem;

    // Light theme
    border-top: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
        border-top: 1px solid $dark-accent-color-2;
    }

    .nav-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        width: fit-content;
        font-size: 0.95rem;
        @include transition;

        &.primary {
            background-color: $color-primary;
            color: white;
            border: none;
            float: right !important;
            margin: auto 0 auto auto;

            // Light theme
            box-shadow: 0 2px 5px rgba(0, 122, 255, 0.2);

            // Dark theme
            .theme-dark & {
                box-shadow: 0 2px 5px rgba(0, 122, 255, 0.4);
            }

            &:hover {
                background-color: color.adjust($color-primary, $lightness: -5%);
                transform: translateY(-2px);

                // Light theme hover
                box-shadow: 0 4px 10px rgba(0, 122, 255, 0.25);

                // Dark theme hover
                .theme-dark & {
                    box-shadow: 0 4px 10px rgba(0, 122, 255, 0.5);
                }
            }

            &:active {
                transform: translateY(0);

                // Light theme active
                box-shadow: 0 2px 5px rgba(0, 122, 255, 0.2);

                // Dark theme active
                .theme-dark & {
                    box-shadow: 0 2px 5px rgba(0, 122, 255, 0.4);
                }
            }

            &:disabled {
                background-color: color.adjust($color-primary, $lightness: 20%, $saturation: -30%);
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
                opacity: 0.7;
            }
        }

        &.prev {
            // Light theme
            background-color: $light-background;
            border: 1px solid $light-accent-color-2;
            color: $light-secondary-text;

            // Dark theme
            .theme-dark & {
                background-color: color.adjust($dark-background, $lightness: 5%);
                border: 1px solid $dark-accent-color-2;
                color: $dark-secondary-text;
            }

            &:hover {
                transform: translateY(-2px);

                // Light theme hover
                background-color: color.adjust($light-background, $lightness: -2%);
                color: $light-primary-text;
                border-color: color.adjust($light-accent-color-2, $lightness: -5%);
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

                // Dark theme hover
                .theme-dark & {
                    background-color: color.adjust($dark-background, $lightness: 8%);
                    color: $dark-primary-text;
                    border-color: color.adjust($dark-accent-color-2, $lightness: 5%);
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                }
            }

            &:active {
                transform: translateY(0);
                box-shadow: none;
            }
        }
    }
}

// Flag emoji
.flag {
    font-size: 1.8rem;
    margin-bottom: 10px;
}

// Tool, goal, and role icons and names
.tool-icon,
.goal-icon,
.role-icon {
    font-size: 1.8rem;
    margin-bottom: 10px;
    display: block;
}

.tool-name,
.goal-name,
.role-name {
    font-size: 0.9rem;
    display: block;
}

// Chronotype styling
.chronotype-options button {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 1.8rem;

    .chronotype-label {
        font-size: 0.9rem;
        margin-top: 10px;
    }
}

// Field input with suggestions - macOS style
.field-input {
    position: relative;
    margin-bottom: 2.5rem;

    input {
        width: 100%;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 1rem;
        @include transition;

        // Light theme
        border: 1px solid $light-accent-color-2;
        background-color: $light-background;
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
            border: 1px solid $dark-accent-color-2;
            background-color: color.adjust($dark-background, $lightness: 5%);
            color: $dark-primary-text;
        }

        &:focus {
            outline: none;
            border-color: $color-primary;

            // Light theme focus
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);

            // Dark theme focus
            .theme-dark & {
                box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
            }
        }

        &::placeholder {
            // Light theme placeholder
            color: $light-secondary-text;

            // Dark theme placeholder
            .theme-dark & {
                color: $dark-secondary-text;
            }

            opacity: 0.7;
        }
    }
}

.suggestions {
    position: absolute;
    width: 100%;
    max-height: 220px;
    overflow-y: auto;
    border-radius: 8px;
    margin-top: 4px;
    z-index: 10;

    // Light theme
    background: white;
    border: 1px solid $light-accent-color-2;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);

    // Dark theme
    .theme-dark & {
        background: color.adjust($dark-background, $lightness: 5%);
        border: 1px solid $dark-accent-color-2;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        border-radius: 8px;

        // Light theme scrollbar track
        background: $light-background;

        // Dark theme scrollbar track
        .theme-dark & {
            background: color.adjust($dark-background, $lightness: 3%);
        }
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 8px;

        // Light theme scrollbar thumb
        background: $light-accent-color-2;

        // Dark theme scrollbar thumb
        .theme-dark & {
            background: $dark-accent-color-2;
        }

        &:hover {
            // Light theme scrollbar thumb hover
            background: color.adjust($light-accent-color-2, $lightness: -10%);

            // Dark theme scrollbar thumb hover
            .theme-dark & {
                background: color.adjust($dark-accent-color-2, $lightness: 10%);
            }
        }
    }
}

.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    text-align: left;
    @include transition(all, 0.15s);
    font-size: 0.95rem;

    // Light theme
    color: $light-primary-text;

    // Dark theme
    .theme-dark & {
        color: $dark-primary-text;
    }

    &:hover {
        // Light theme hover
        background: rgba(0, 122, 255, 0.05);

        // Dark theme hover
        .theme-dark & {
            background: rgba(0, 122, 255, 0.1);
        }
    }

    &:active {
        // Light theme active
        background: rgba(0, 122, 255, 0.1);

        // Dark theme active
        .theme-dark & {
            background: rgba(0, 122, 255, 0.15);
        }
    }
}

// Premium container - macOS style
.premium-container {
    border-radius: 16px;
    margin-bottom: 2rem;
    overflow: hidden;

    // Light theme
    background: white;
    border: 1px solid $light-accent-color-2;
    color: $light-primary-text;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);

    // Dark theme
    .theme-dark & {
        background: color.adjust($dark-background, $lightness: 3%);
        border: 1px solid $dark-accent-color-2;
        color: $dark-primary-text;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    }
}

// Premium header
.premium-header {
    padding: 1.5rem;
    text-align: center;
    position: relative;

    // Light theme
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(0, 122, 255, 0.02));

    // Dark theme
    .theme-dark & {
        background: linear-gradient(135deg, rgba(0, 122, 255, 0.15), rgba(0, 122, 255, 0.05));
    }

    h3 {
        font-size: 1.5rem;
        margin: 0.75rem 0 0;
        font-weight: 600;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-primary-text;
        }
    }
}

.premium-badge {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    letter-spacing: 0.05rem;
    margin-bottom: 0.5rem;

    // Light theme
    background: $system-blue;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);

    // Dark theme
    .theme-dark & {
        background: $system-blue;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 122, 255, 0.5);
    }
}

// Premium features
.premium-features {
    padding: 1rem 1.5rem 1.5rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;

    // Light theme
    background: white;

    // Dark theme
    .theme-dark & {
        background: color.adjust($dark-background, $lightness: 3%);
    }
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .feature-icon {
        font-size: 1.5rem;
        flex-shrink: 0;
    }

    .feature-text {
        h4 {
            font-size: 1rem;
            margin: 0;
            font-weight: 600;

            // Light theme
            color: $light-primary-text;

            // Dark theme
            .theme-dark & {
                color: $dark-primary-text;
            }
        }
    }
}

// Premium actions
.premium-actions {
    display: flex;
    flex-direction: column;
}

// Modal styles
body.modal-open {
    overflow: hidden;
}

// Modal overlay
.modal-overlay-welcome {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999999;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

// Pricing modal
.pricing-modal {
    width: 90%;
    max-width: 500px;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    animation: modalFadeIn 0.3s ease-out;

    // Light theme
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);

    // Dark theme
    .theme-dark & {
        background: $dark-background;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;

    // Light theme
    background: rgba(0, 0, 0, 0.05);
    color: $light-secondary-text;

    // Dark theme
    .theme-dark & {
        background: rgba(255, 255, 255, 0.1);
        color: $dark-secondary-text;
    }

    &:hover {
        // Light theme
        background: rgba(0, 0, 0, 0.1);

        // Dark theme
        .theme-dark & {
            background: rgba(255, 255, 255, 0.15);
        }
    }
}

.modal-header {
    padding: 2rem 2rem 1rem;
    text-align: center;

    // Light theme
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.08), rgba(0, 122, 255, 0.03));

    // Dark theme
    .theme-dark & {
        background: linear-gradient(135deg, rgba(0, 122, 255, 0.2), rgba(0, 122, 255, 0.08));
    }

    h3 {
        font-size: 1.5rem;
        margin: 0.75rem 0 0.5rem;
        font-weight: 600;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    p {
        font-size: 0.95rem;
        margin: 0;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-secondary-text;
        }
    }
}

// Pricing details
.pricing-details {
    padding: 1.5rem;
    text-align: center;

    // Light theme
    background: white;

    // Dark theme
    .theme-dark & {
        background: color.adjust($dark-background, $lightness: 3%);
    }
}

.price-tag {
    display: inline-flex;
    align-items: flex-start;
    margin-bottom: 0.5rem;

    .currency {
        font-size: 1.5rem;
        font-weight: 600;
        margin-top: 0.3rem;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    .amount {
        font-size: 3.5rem;
        font-weight: 700;
        line-height: 1;

        // Light theme
        color: $light-primary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-primary-text;
        }
    }

    .period {
        font-size: 1rem;
        margin-top: 0.8rem;
        margin-left: 0.2rem;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-secondary-text;
        }
    }
}

.price-note {
    font-size: 0.9rem;
    margin: 0;

    // Light theme
    color: $light-secondary-text;

    // Dark theme
    .theme-dark & {
        color: $dark-secondary-text;
    }
}

// Pricing features
.pricing-features {
    padding: 1.5rem 2rem;

    // Light theme
    background: white;
    border-top: 1px solid $light-accent-color-2;

    // Dark theme
    .theme-dark & {
        background: color.adjust($dark-background, $lightness: 3%);
        border-top: 1px solid $dark-accent-color-2;
    }
}

.pricing-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;

    &:last-child {
        margin-bottom: 0;
    }

    .feature-check {
        flex-shrink: 0;
        margin-right: 0.75rem;
        font-weight: 600;

        // Light theme
        color: $system-blue;

        // Dark theme
        .theme-dark & {
            color: $system-blue;
        }
    }

    p {
        margin: 0;
        font-size: 0.95rem;
        line-height: 1.4;

        // Light theme
        color: $light-secondary-text;

        // Dark theme
        .theme-dark & {
            color: $dark-secondary-text;
        }
    }
}

.subscribe-button {
    width: 100%;
    padding: 1rem;
    background: $system-blue;
    color: white;
    border: none;
    border-radius: 0;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    @include transition;

    // Light theme
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);

    // Dark theme
    .theme-dark & {
        box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
    }

    &:hover {
        background-color: color.adjust($system-blue, $lightness: -5%);

        // Light theme hover
        box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);

        // Dark theme hover
        .theme-dark & {
            box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
        }
    }

    &:active {
        background-color: color.adjust($system-blue, $lightness: -10%);
    }
}

// Premium buttons
.premium-button {
    width: 100%;
    padding: 1rem;
    background: $system-blue;
    color: white;
    border: none;
    border-radius: 0;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    @include transition;

    // Light theme
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);

    // Dark theme
    .theme-dark & {
        box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
    }

    &:hover {
        background-color: color.adjust($system-blue, $lightness: -5%);

        // Light theme hover
        box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);

        // Dark theme hover
        .theme-dark & {
            box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
        }
    }

    &:active {
        background-color: color.adjust($system-blue, $lightness: -10%);
    }
}

.continue-free-button {
    width: 100%;
    padding: 1rem;
    background: transparent;
    border: none;
    border-radius: 0;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
    @include transition;

    // Light theme
    color: $light-secondary-text;

    // Dark theme
    .theme-dark & {
        color: $dark-secondary-text;
    }

    &:hover {
        // Light theme hover
        background-color: rgba(0, 0, 0, 0.02);
        color: $light-primary-text;

        // Dark theme hover
        .theme-dark & {
            background-color: rgba(255, 255, 255, 0.05);
            color: $dark-primary-text;
        }
    }

    &:active {
        // Light theme active
        background-color: rgba(0, 0, 0, 0.05);

        // Dark theme active
        .theme-dark & {
            background-color: rgba(255, 255, 255, 0.08);
        }
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

// Media Queries for Responsive Design
@media (max-width: 768px) {
    .steps-container {
        max-width: 90%;
        padding: 1.5rem;
    }

    .step-indicators {
        margin-bottom: 2rem;
    }

    .step-indicator {
        padding-top: 24px;

        .step-number {
            width: 22px;
            height: 22px;
            font-size: 12px;
        }

        .step-label {
            font-size: 12px;
        }
    }

    .language-options,
    .role-options,
    .goal-options,
    .tool-options,
    .chronotype-options {
        grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
        gap: 10px;

        button {
            padding: 12px 8px;
            font-size: 0.85rem;
        }
    }

    .nav-buttons {
        .nav-button {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
        }
    }

    .premium-container {
        margin-bottom: 1.5rem;
    }

    .premium-header {
        padding: 1.25rem;

        h3 {
            font-size: 1.3rem;
        }
    }

    .premium-features {
        padding: 1rem;
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .premium-button,
    .continue-free-button {
        padding: 0.9rem;
        font-size: 0.9rem;
    }

    // Modal responsive styles
    .pricing-modal {
        width: 95%;
    }

    .modal-header {
        padding: 1.5rem 1.5rem 1rem;

        h3 {
            font-size: 1.3rem;
        }

        p {
            font-size: 0.9rem;
        }
    }

    .pricing-details {
        padding: 1.25rem;
    }

    .pricing-features {
        padding: 1.25rem 1.5rem;
    }

    .pricing-feature {
        margin-bottom: 0.75rem;

        p {
            font-size: 0.9rem;
        }
    }

    .subscribe-button {
        padding: 0.9rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .steps-container {
        max-width: 95%;
        padding: 1rem;
        margin: 1rem auto;
    }

    .step-panel {
        padding: 0 0.75rem;

        h2 {
            font-size: 1.5rem;
        }

        p {
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }
    }

    .language-options,
    .role-options,
    .goal-options,
    .tool-options,
    .chronotype-options {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
        margin-bottom: 1.5rem;
    }

    .nav-buttons {
        padding-top: 1rem;

        .nav-button {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
    }

    .field-input input {
        padding: 10px 14px;
        font-size: 0.95rem;
    }

    .suggestion-item {
        padding: 10px 14px;
        font-size: 0.9rem;
    }

    .premium-container {
        margin-bottom: 1rem;
    }

    .premium-header {
        padding: 1rem;

        h3 {
            font-size: 1.2rem;
            margin: 0.5rem 0 0;
        }
    }

    .premium-badge {
        padding: 0.3rem 0.7rem;
        font-size: 0.75rem;
    }

    .premium-features {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .feature-item {
        .feature-icon {
            font-size: 1.3rem;
        }

        .feature-text {
            h4 {
                font-size: 0.9rem;
            }
        }
    }

    .premium-button,
    .continue-free-button {
        padding: 0.8rem;
        font-size: 0.85rem;
    }

    // Modal responsive styles for small screens
    .pricing-modal {
        width: 95%;
        max-width: 100%;
    }

    .modal-header {
        padding: 1.25rem 1rem 0.75rem;

        h3 {
            font-size: 1.2rem;
        }

        p {
            font-size: 0.85rem;
        }
    }

    .pricing-details {
        padding: 1rem;
    }

    .price-tag {
        .currency {
            font-size: 1.2rem;
        }

        .amount {
            font-size: 2.8rem;
        }

        .period {
            font-size: 0.85rem;
        }
    }

    .price-note {
        font-size: 0.8rem;
    }

    .pricing-features {
        padding: 1rem 1.25rem;
    }

    .pricing-feature {
        margin-bottom: 0.6rem;

        p {
            font-size: 0.85rem;
        }
    }

    .subscribe-button {
        padding: 0.8rem;
        font-size: 0.85rem;
    }

    .chronotype-options button {
        font-size: 1.5rem;

        .chronotype-label {
            font-size: 0.8rem;
            margin-top: 8px;
        }
    }

    .tool-icon,
    .goal-icon,
    .role-icon {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }

    .tool-name,
    .goal-name,
    .role-name {
        font-size: 0.8rem;
    }
}