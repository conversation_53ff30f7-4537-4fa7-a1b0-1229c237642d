# 🚀 Development Environment Setup

This guide explains how to start all services (Frontend + Backend) with a single command.

## 📋 Prerequisites

1. **Node.js** (v16 or higher)
2. **Python** (v3.8 or higher)
3. **PostgreSQL** database running

## 🛠️ Installation

First, install all dependencies:

```bash
# Install frontend dependencies
cd Frontend
npm install

# Install backend dependencies (if needed)
cd ../Backend
pip install -r requirements.txt
```

## 🎯 Quick Start Options

### Option 1: Single Command (Recommended)
```bash
cd Frontend
npm run dev
```

This will start:
- ✅ **Vite Frontend** on `http://localhost:5173`
- ✅ **Python Backend** on `http://localhost:5000`
- ✅ **Node.js Server** on `http://localhost:5001`

### Option 2: Alternative with npm-run-all
```bash
cd Frontend
npm run dev:alt
```

### Option 3: Custom Starter Script
```bash
cd Frontend
npm run dev:custom
```

### Option 4: Platform-specific Scripts

**Windows:**
```cmd
cd Frontend
start-dev.bat
```

**macOS/Linux:**
```bash
cd Frontend
./start-dev.sh
```

## 🔧 Individual Services

If you need to start services individually:

```bash
# Frontend only
npm run dev:frontend

# Backend only  
npm run dev:backend

# Node.js server only
npm run dev:server
```

## 📊 Service Details

| Service | Port | URL | Purpose |
|---------|------|-----|---------|
| Vite Frontend | 5173 | http://localhost:5173 | React app with hot reload |
| Python Backend | 5000 | http://localhost:5000 | Flask API server |
| Node.js Server | 5001 | http://localhost:5001 | Express.js API server |

## 🎨 Console Output

The development command shows colored output:
- 🔵 **VITE** (Cyan) - Frontend messages
- 🟡 **PYTHON** (Yellow) - Backend messages  
- 🟢 **NODE** (Green) - Server messages

## 🛑 Stopping Services

Press `Ctrl+C` in the terminal to stop all services at once.

## 🔍 Troubleshooting

### Python Backend Not Starting
- Ensure Python is installed and in PATH
- Check if `../Backend/app.py` exists
- Install Python dependencies: `pip install -r ../Backend/requirements.txt`

### Node.js Server Not Starting
- Ensure Node.js is installed
- Check if `server.js` exists in Frontend directory
- Run `npm install` to install dependencies

### Port Conflicts
- Make sure ports 5173, 5000, and 5001 are not in use
- Kill any existing processes using these ports

### Database Connection Issues
- Ensure PostgreSQL is running
- Check database credentials in backend configuration
- Verify database 'blueprint' exists

## 📝 Package.json Scripts

```json
{
  "scripts": {
    "dev": "Start all services with colored output",
    "dev:frontend": "Start only Vite frontend",
    "dev:backend": "Start only Python backend", 
    "dev:server": "Start only Node.js server",
    "dev:alt": "Alternative using npm-run-all",
    "dev:custom": "Custom starter with enhanced logging"
  }
}
```

## 🎉 Success!

When everything is working, you should see:
- Frontend accessible at http://localhost:5173
- API endpoints responding at http://localhost:5000 and http://localhost:5001
- Hot reload working for frontend changes
- All services logging to the same terminal with color coding
