import React from 'react';
import LinkIcon from '@mui/icons-material/Link';
import '../../styles/SettingsPanels.scss';

const CrossSubjectPanel = ({ settings }) => {
  const { crossSubjectLinking, setCrossSubjectLinking, isDarkMode } = settings;
  
  // Sample subjects for visualization
  const subjects = [
    { id: 1, name: 'Mathematics', color: '#007AFF' },
    { id: 2, name: 'Physics', color: '#FF9500' },
    { id: 3, name: 'Chemistry', color: '#FF3B30' },
    { id: 4, name: 'Biology', color: '#28CD41' },
    { id: 5, name: 'History', color: '#FFCC00' }
  ];
  
  return (
    <div className={`detail-panel linking-panel ${isDarkMode ? 'theme-dark' : ''}`}>
      <div className="panel-header">
        <h2>Cross-Subject Linking</h2>
        <p className="panel-description">
          Enable or disable the AI's ability to connect concepts across different subjects.
        </p>
      </div>
      
      <div className="settings-group">
        <div className="setting-item toggle-setting">
          <div className="toggle-container">
            <div className="toggle-label">
              <LinkIcon />
              <span>Cross-Subject Connections</span>
            </div>
            <div className="toggle-description">
              <p>
                When enabled, the AI will identify and explain connections between concepts 
                across different subjects, helping you build a more integrated understanding.
              </p>
            </div>
            <label className="toggle-switch">
              <input 
                type="checkbox" 
                checked={crossSubjectLinking} 
                onChange={() => setCrossSubjectLinking(!crossSubjectLinking)}
              />
              <span className="toggle-slider"></span>
              <span className="toggle-status">{crossSubjectLinking ? 'Enabled' : 'Disabled'}</span>
            </label>
          </div>
        </div>
      </div>
      
      <div className="subject-linking-visualization">
        <div className="subjects-network">
          {subjects.map((subject, index) => (
            <div 
              key={subject.id}
              className="subject-node"
              style={{
                backgroundColor: subject.color,
                transform: `translate(${Math.cos(index * (2 * Math.PI / subjects.length)) * 120}px, ${Math.sin(index * (2 * Math.PI / subjects.length)) * 120}px)`
              }}
            >
              <span>{subject.name}</span>
            </div>
          ))}
          
          {crossSubjectLinking && (
            <div className="subject-connections">
              {subjects.map((subject, i) => (
                subjects.map((targetSubject, j) => {
                  if (i < j) {
                    return (
                      <div 
                        key={`${i}-${j}`}
                        className="connection-line"
                        style={{
                          left: '50%',
                          top: '50%',
                          width: `${Math.sqrt(
                            Math.pow(Math.cos(j * (2 * Math.PI / subjects.length)) * 120 - Math.cos(i * (2 * Math.PI / subjects.length)) * 120, 2) +
                            Math.pow(Math.sin(j * (2 * Math.PI / subjects.length)) * 120 - Math.sin(i * (2 * Math.PI / subjects.length)) * 120, 2)
                          )}px`,
                          transform: `rotate(${Math.atan2(
                            Math.sin(j * (2 * Math.PI / subjects.length)) * 120 - Math.sin(i * (2 * Math.PI / subjects.length)) * 120,
                            Math.cos(j * (2 * Math.PI / subjects.length)) * 120 - Math.cos(i * (2 * Math.PI / subjects.length)) * 120
                          )}rad)`,
                          transformOrigin: '0 0',
                          opacity: Math.random() * 0.5 + 0.25
                        }}
                      ></div>
                    );
                  }
                  return null;
                })
              ))}
            </div>
          )}
        </div>
      </div>
      
      <div className="info-box">
        <p>
          Cross-subject linking helps you see how concepts in different fields relate to each other. 
          For example, how calculus is used in physics, or how chemistry connects to biology.
        </p>
      </div>
    </div>
  );
};

export default CrossSubjectPanel;
