import { useState, useEffect } from 'react';

const Mnemonic = ({ tool, subject }) => {
    const [mnemonics, setMnemonics] = useState([]);
    const [newMnemonic, setNewMnemonic] = useState({
        concept: '',
        mnemonic: '',
        explanation: ''
    });
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        // Load saved mnemonics from localStorage
        const savedMnemonics = JSON.parse(localStorage.getItem('mnemonics') || '[]');
        setMnemonics(savedMnemonics);
    }, []);

    useEffect(() => {
        // Save mnemonics to localStorage when they change
        localStorage.setItem('mnemonics', JSON.stringify(mnemonics));
    }, [mnemonics]);

    const addMnemonic = () => {
        if (newMnemonic.concept.trim() && newMnemonic.mnemonic.trim()) {
            const mnemonic = {
                ...newMnemonic,
                id: Date.now(),
                subject,
                createdAt: new Date().toISOString()
            };

            setMnemonics(prev => [...prev, mnemonic]);
            setNewMnemonic({ concept: '', mnemonic: '', explanation: '' });
        }
    };

    const removeMnemonic = (id) => {
        setMnemonics(prev => prev.filter(m => m.id !== id));
    };

    const filteredMnemonics = mnemonics.filter(mnemonic => {
        const matchesSubject = mnemonic.subject === subject;
        const matchesSearch = mnemonic.concept.toLowerCase().includes(searchTerm.toLowerCase()) ||
            mnemonic.mnemonic.toLowerCase().includes(searchTerm.toLowerCase()) ||
            mnemonic.explanation.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesSubject && (searchTerm === '' || matchesSearch);
    });

    return (
        <div className="mnemonic">
            <h2>Mnemonic: {subject}</h2>

            <div className="search-bar">
                <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search mnemonics..."
                />
            </div>

            <div className="mnemonics-list">
                {filteredMnemonics.length > 0 ? (
                    <ul>
                        {filteredMnemonics.map(mnemonic => (
                            <li key={mnemonic.id}>
                                <div className="mnemonic-card">
                                    <div className="concept">
                                        <h3>{mnemonic.concept}</h3>
                                        <button onClick={() => removeMnemonic(mnemonic.id)}>×</button>
                                    </div>
                                    <div className="mnemonic-text">
                                        <p><strong>Mnemonic:</strong> {mnemonic.mnemonic}</p>
                                    </div>
                                    {mnemonic.explanation && (
                                        <div className="explanation">
                                            <p><strong>Explanation:</strong> {mnemonic.explanation}</p>
                                        </div>
                                    )}
                                    <div className="meta">
                                        <small>{new Date(mnemonic.createdAt).toLocaleDateString()}</small>
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <p>No mnemonics yet. Create one below!</p>
                )}
            </div>

            <div className="add-mnemonic">
                <h3>Create New Mnemonic</h3>

                <div className="form-group">
                    <label>Concept/Term:</label>
                    <input
                        type="text"
                        value={newMnemonic.concept}
                        onChange={(e) => setNewMnemonic({ ...newMnemonic, concept: e.target.value })}
                        placeholder="Enter concept or term to remember..."
                    />
                </div>

                <div className="form-group">
                    <label>Mnemonic:</label>
                    <textarea
                        value={newMnemonic.mnemonic}
                        onChange={(e) => setNewMnemonic({ ...newMnemonic, mnemonic: e.target.value })}
                        placeholder="Enter your memory aid (acronym, rhyme, image, etc.)..."
                        rows="3"
                    />
                </div>

                <div className="form-group">
                    <label>Explanation (optional):</label>
                    <textarea
                        value={newMnemonic.explanation}
                        onChange={(e) => setNewMnemonic({ ...newMnemonic, explanation: e.target.value })}
                        placeholder="Explain how the mnemonic works..."
                        rows="2"
                        import { useState, useEffect, useCallback } from 'react';
                    import { motion, AnimatePresence } from 'framer-motion';
                    import { FiSearch, FiPlus, FiTrash2, FiEdit2, FiClock, FiBookmark, FiX } from 'react-icons/fi';
                    import { FaBrain } from 'react-icons/fa';
                    import { debounce } from 'lodash';
                    import Markdown from 'react-markdown';
                    import remarkGfm from 'remark-gfm';

                    const Mnemonic = ({ tool, subject }) => {
                    const [mnemonics, setMnemonics] = useState([]);
                    const [newMnemonic, setNewMnemonic] = useState({
                    concept: '',
                    mnemonic: '',
                    explanation: '',
                    tags: []
                });
                    const [searchTerm, setSearchTerm] = useState('');
                    const [isEditing, setIsEditing] = useState(null);
                    const [activeTab, setActiveTab] = useState('all');
                    const [newTag, setNewTag] = useState('');
                    const [isExpanded, setIsExpanded] = useState({});
                    const [isLoading, setIsLoading] = useState(true);

                    // Load mnemonics from localStorage
                    useEffect(() => {
                    const loadMnemonics = () => {
                    try {
                    const saved = localStorage.getItem('mnemonics');
                    if (saved) {
                    const parsed = JSON.parse(saved);
                    setMnemonics(parsed);

                    // Initialize expanded state
                    const expandedState = {};
                    parsed.forEach(m => {
                    expandedState[m.id] = false;
                });
                    setIsExpanded(expandedState);
                }
                } catch (error) {
                    console.error('Failed to load mnemonics:', error);
                } finally {
                    setIsLoading(false);
                }
                };

                    loadMnemonics();
                }, []);

                    // Save mnemonics to localStorage with debounce
                    useEffect(() => {
                    const saveMnemonics = debounce(() => {
                    localStorage.setItem('mnemonics', JSON.stringify(mnemonics));
                }, 500);

                    saveMnemonics();
                    return () => saveMnemonics.cancel();
                }, [mnemonics]);

                    const addMnemonic = useCallback(() => {
                    if (newMnemonic.concept.trim() && newMnemonic.mnemonic.trim()) {
                    const mnemonic = {
                    ...newMnemonic,
                    id: Date.now(),
                    subject,
                    createdAt: new Date().toISOString(),
                    lastEdited: new Date().toISOString(),
                    favorite: false
                };

                    setMnemonics(prev => [mnemonic, ...prev]);
                    setNewMnemonic({
                    concept: '',
                    mnemonic: '',
                    explanation: '',
                    tags: []
                });

                    // Auto-expand new mnemonic
                    setIsExpanded(prev => ({ ...prev, [mnemonic.id]: true }));
                }
                }, [newMnemonic, subject]);

                    const updateMnemonic = useCallback(() => {
                    if (isEditing && newMnemonic.concept.trim() && newMnemonic.mnemonic.trim()) {
                    setMnemonics(prev => prev.map(m =>
                    m.id === isEditing ? {
                    ...m,
                    ...newMnemonic,
                    lastEdited: new Date().toISOString()
                } : m
                    ));
                    setIsEditing(null);
                    setNewMnemonic({ concept: '', mnemonic: '', explanation: '', tags: [] });
                }
                }, [isEditing, newMnemonic]);

                    const removeMnemonic = useCallback((id) => {
                    setMnemonics(prev => prev.filter(m => m.id !== id));
                }, []);

                    const toggleFavorite = useCallback((id) => {
                    setMnemonics(prev => prev.map(m =>
                    m.id === id ? { ...m, favorite: !m.favorite } : m
                    ));
                }, []);

                    const toggleExpand = useCallback((id) => {
                    setIsExpanded(prev => ({ ...prev, [id]: !prev[id] }));
                }, []);

                    const addTag = useCallback(() => {
                    if (newTag.trim() && !newMnemonic.tags.includes(newTag.trim())) {
                    setNewMnemonic(prev => ({
                    ...prev,
                    tags: [...prev.tags, newTag.trim()]
                }));
                    setNewTag('');
                }
                }, [newMnemonic.tags, newTag]);

                    const removeTag = useCallback((tagToRemove) => {
                    setNewMnemonic(prev => ({
                    ...prev,
                    tags: prev.tags.filter(tag => tag !== tagToRemove)
                }));
                }, []);

                    const startEditing = useCallback((mnemonic) => {
                    setIsEditing(mnemonic.id);
                    setNewMnemonic({
                    concept: mnemonic.concept,
                    mnemonic: mnemonic.mnemonic,
                    explanation: mnemonic.explanation || '',
                    tags: mnemonic.tags || []
                });
                }, []);

                    const cancelEditing = useCallback(() => {
                    setIsEditing(null);
                    setNewMnemonic({ concept: '', mnemonic: '', explanation: '', tags: [] });
                }, []);

                    const filteredMnemonics = mnemonics.filter(mnemonic => {
                    const matchesSubject = mnemonic.subject === subject;
                    const matchesSearch = searchTerm === '' ||
                    mnemonic.concept.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    mnemonic.mnemonic.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    mnemonic.explanation?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    mnemonic.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

                    const matchesTab = activeTab === 'all' ||
                    (activeTab === 'favorites' && mnemonic.favorite);

                    return matchesSubject && matchesSearch && matchesTab;
                });

                    const sortedMnemonics = [...filteredMnemonics].sort((a, b) => {
                    if (a.favorite !== b.favorite) return a.favorite ? -1 : 1;
                    return new Date(b.createdAt) - new Date(a.createdAt);
                });

                    const uniqueTags = [...new Set(mnemonics.flatMap(m => m.tags || []))];

                    if (isLoading) {
                    return (
                    <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                    );
                }

                    return (
                    <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    className="max-w-4xl mx-auto p-4 md:p-6"
                >
                    <div className="flex items-center mb-6">
                        <FaBrain className="text-indigo-600 text-3xl mr-3" />
                        <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
                            Mnemonics for <span className="text-indigo-600">{subject}</span>
                        </h1>
                    </div>

                    {/* Search and Filter Bar */}
                    <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
                        <div className="relative mb-4">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FiSearch className="text-gray-400" />
                            </div>
                            <input
                                type="text"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Search mnemonics by term, content, or tags..."
                                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            />
                        </div>

                        <div className="flex flex-wrap items-center gap-3">
                            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                                <button
                                    onClick={() => setActiveTab('all')}
                                    className={`px-3 py-1 text-sm rounded-md ${activeTab === 'all' ? 'bg-white shadow-sm text-indigo-600' : 'text-gray-600'}`}
                                >
                                    All
                                </button>
                                <button
                                    onClick={() => setActiveTab('favorites')}
                                    className={`px-3 py-1 text-sm rounded-md ${activeTab === 'favorites' ? 'bg-white shadow-sm text-indigo-600' : 'text-gray-600'}`}
                                >
                                    Favorites
                                </button>
                            </div>

                            {uniqueTags.length > 0 && (
                                <select
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    value={searchTerm}
                                    className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-indigo-500 focus:border-indigo-500"
                                >
                                    <option value="">All Tags</option>
                                    {uniqueTags.map(tag => (
                                        <option key={tag} value={tag}>{tag}</option>
                                    ))}
                                </select>
                            )}
                        </div>
                    </div>

                    {/* Mnemonics List */}
                    <div className="mb-8">
                        <AnimatePresence>
                            {sortedMnemonics.length > 0 ? (
                                <ul className="space-y-4">
                                    {sortedMnemonics.map(mnemonic => (
                                        <motion.li
                                            key={mnemonic.id}
                                            layout
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, x: -20 }}
                                            transition={{ duration: 0.2 }}
                                            className="bg-white rounded-lg shadow-sm overflow-hidden"
                                        >
                                            <div className="p-4">
                                                <div className="flex justify-between items-start">
                                                    <div className="flex-1 min-w-0">
                                                        <h3 className="text-lg font-semibold text-gray-800 truncate">
                                                            {mnemonic.concept}
                                                        </h3>
                                                        <div className="flex items-center text-sm text-gray-500 mt-1">
                                                            <FiClock className="mr-1" />
                                                            <span>
                                                        {new Date(mnemonic.createdAt).toLocaleDateString('en-US', {
                                                            year: 'numeric',
                                                            month: 'short',
                                                            day: 'numeric'
                                                        })}
                                                    </span>
                                                            {mnemonic.lastEdited && (
                                                                <span className="ml-2 text-xs text-gray-400">
                                                            (edited)
                                                        </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex space-x-2 ml-2">
                                                        <button
                                                            onClick={() => toggleFavorite(mnemonic.id)}
                                                            className={`p-1 rounded-full ${mnemonic.favorite ? 'text-yellow-500' : 'text-gray-400 hover:text-gray-600'}`}
                                                        >
                                                            <FiBookmark />
                                                        </button>
                                                        <button
                                                            onClick={() => startEditing(mnemonic)}
                                                            className="p-1 text-gray-500 hover:text-indigo-600 rounded-full"
                                                        >
                                                            <FiEdit2 size={16} />
                                                        </button>
                                                        <button
                                                            onClick={() => removeMnemonic(mnemonic.id)}
                                                            className="p-1 text-gray-500 hover:text-red-600 rounded-full"
                                                        >
                                                            <FiTrash2 size={16} />
                                                        </button>
                                                    </div>
                                                </div>

                                                {mnemonic.tags?.length > 0 && (
                                                    <div className="mt-2 flex flex-wrap gap-2">
                                                        {mnemonic.tags.map(tag => (
                                                            <span
                                                                key={tag}
                                                                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800"
                                                            >
                                                        {tag}
                                                    </span>
                                                        ))}
                                                    </div>
                                                )}

                                                <div className="mt-3">
                                                    <div className="prose prose-sm max-w-none">
                                                        <Markdown remarkPlugins={[remarkGfm]}>
                                                            {mnemonic.mnemonic}
                                                        </Markdown>
                                                    </div>
                                                </div>

                                                {mnemonic.explanation && isExpanded[mnemonic.id] && (
                                                    <motion.div
                                                        initial={{ opacity: 0, height: 0 }}
                                                        animate={{ opacity: 1, height: 'auto' }}
                                                        transition={{ duration: 0.2 }}
                                                        className="mt-3 bg-gray-50 p-3 rounded-lg"
                                                    >
                                                        <h4 className="text-sm font-medium text-gray-700 mb-1">Explanation</h4>
                                                        <div className="prose prose-sm max-w-none">
                                                            <Markdown remarkPlugins={[remarkGfm]}>
                                                                {mnemonic.explanation}
                                                            </Markdown>
                                                        </div>
                                                    </motion.div>
                                                )}

                                                {mnemonic.explanation && (
                                                    <button
                                                        onClick={() => toggleExpand(mnemonic.id)}
                                                        className="mt-2 text-sm text-indigo-600 hover:text-indigo-800 flex items-center"
                                                    >
                                                        {isExpanded[mnemonic.id] ? 'Hide explanation' : 'Show explanation'}
                                                    </button>
                                                )}
                                            </div>
                                        </motion.li>
                                    ))}
                                </ul>
                            ) : (
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    className="bg-white rounded-lg shadow-sm p-8 text-center"
                                >
                                    <FiBookmark className="mx-auto text-4xl text-gray-400 mb-3" />
                                    <h3 className="text-lg font-medium text-gray-700 mb-1">No mnemonics found</h3>
                                    <p className="text-gray-500">
                                        {searchTerm ? 'Try a different search term' : 'Create your first mnemonic below'}
                                    </p>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>

                    {/* Add/Edit Mnemonic Form */}
                    <motion.div
                        layout
                        className="bg-white rounded-lg shadow-sm overflow-hidden"
                    >
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-800">
                                {isEditing ? 'Edit Mnemonic' : 'Create New Mnemonic'}
                            </h3>
                        </div>
                        <div className="p-4">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Concept/Term</label>
                                    <input
                                        type="text"
                                        value={newMnemonic.concept}
                                        onChange={(e) => setNewMnemonic({ ...newMnemonic, concept: e.target.value })}
                                        placeholder="What do you want to remember?"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Mnemonic</label>
                                    <textarea
                                        value={newMnemonic.mnemonic}
                                        onChange={(e) => setNewMnemonic({ ...newMnemonic, mnemonic: e.target.value })}
                                        placeholder="Create your memory aid (acronym, rhyme, image, etc.)..."
                                        rows="3"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                    <p className="mt-1 text-xs text-gray-500">
                                        Supports Markdown formatting (lists, bold, links, etc.)
                                    </p>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Explanation (optional)</label>
                                    <textarea
                                        value={newMnemonic.explanation}
                                        onChange={(e) => setNewMnemonic({ ...newMnemonic, explanation: e.target.value })}
                                        placeholder="Explain how the mnemonic works..."
                                        rows="2"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Tags (optional)</label>
                                    <div className="flex">
                                        <input
                                            type="text"
                                            value={newTag}
                                            onChange={(e) => setNewTag(e.target.value)}
                                            onKeyDown={(e) => e.key === 'Enter' && addTag()}
                                            placeholder="Add tags to categorize..."
                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-indigo-500 focus:border-indigo-500"
                                        />
                                        <button
                                            onClick={addTag}
                                            className="px-3 py-2 bg-indigo-600 text-white rounded-r-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                        >
                                            <FiPlus />
                                        </button>
                                    </div>
                                    {newMnemonic.tags.length > 0 && (
                                        <div className="mt-2 flex flex-wrap gap-2">
                                            {newMnemonic.tags.map(tag => (
                                                <span
                                                    key={tag}
                                                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800"
                                                >
                                            {tag}
                                                    <button
                                                        onClick={() => removeTag(tag)}
                                                        className="ml-1.5 inline-flex text-indigo-600 hover:text-indigo-900"
                                                    >
                                                <FiX size={12} />
                                            </button>
                                        </span>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                <div className="flex justify-end space-x-3 pt-2">
                                    {isEditing && (
                                        <button
                                            onClick={cancelEditing}
                                            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                                        >
                                            Cancel
                                        </button>
                                    )}
                                    <button
                                        onClick={isEditing ? updateMnemonic : addMnemonic}
                                        disabled={!newMnemonic.concept.trim() || !newMnemonic.mnemonic.trim()}
                                        className={`px-4 py-2 rounded-lg text-white ${(!newMnemonic.concept.trim() || !newMnemonic.mnemonic.trim()) ? 'bg-indigo-300 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}
                                    >
                                        {isEditing ? 'Update Mnemonic' : 'Save Mnemonic'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
                );
                };

                export default Mnemonic; />
                </div>

                <button onClick={addMnemonic}>Save Mnemonic</button>
            </div>
        </div>
    );
};

export default Mnemonic;