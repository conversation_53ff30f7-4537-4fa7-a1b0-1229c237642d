@use "sass:color";

// macOS-inspired color palette
$system-blue: #007AFF;
$system-green: #28CD41;
$system-red: #FF3B30;
$system-orange: #FF9500;
$system-yellow: #FFCC00;
$system-gray: #8E8E93;
$system-light-gray: #E5E5EA;
$system-dark-gray: #636366;
$system-gold: #fbb034;

// Background colors
$window-background: #FFFFFF;
$secondary-background: #F2F2F7;

// Text colors
$primary-text: #000000;
$secondary-text: #3C3C43;
$tertiary-text: #8E8E93;
$placeholder-text: #C7C7CC;

// Border colors
$border-color: #C6C6C8;
$separator-color: #D1D1D6;

// Dark mode colors
$dark-window-background: #1C1C1E;
$dark-secondary-background: #2C2C2E;
$dark-primary-text: #FFFFFF;
$dark-secondary-text: #EBEBF5;
$dark-tertiary-text: #AEAEB2;
$dark-placeholder-text: #636366;
$dark-border-color: #38383A;
$dark-separator-color: #444446;

// Common mixins
@mixin transition($property: all, $duration: 0.3s, $timing: cubic-bezier(0.25, 0.1, 0.25, 1)) {
  transition: $property $duration $timing;
}

@mixin focus-ring {
  box-shadow: 0 0 0 3px rgba($system-blue, 0.3);
}

































// Light Theme Variables
$light-background: #F8F9FA; // Soft White
$light-primary-text: #1d1d1d; // Dark Gray
$light-secondary-text: #666666; // Medium Gray
$light-link-color: #FFD700; // Gold
$light-accent-color-1: #06304A; // Deep Blue
$light-accent-color-2: #bdbdbd; // Light Gray for button hovers/borders
$light-button-background: #E9ECEF; // Light Gray
$light-button-text: #06304A; // Deep Blue

$box-shadow: inset 0 1px 0 rgba(255, 255, 255, .6), 0 22px 70px 4px rgba(136, 136, 136, 0.56), 0 0 0 1px rgba(0, 0, 0, 0.0);

// Dark Theme Variables
$dark-background: #121212; // Dark Gray/Black
$dark-primary-text: #FFFFFF; // White
$dark-secondary-text: #C4C4C4; // Light Gray
$dark-link-color: #FFD700; // Gold
$dark-accent-color-1: #06304A; // Deep Blue
$dark-accent-color-2: #212529; // Darker Gray for button hovers/borders
$dark-button-background: #212529; // Darker Gray
$dark-button-text: #FFD700; // Gold

$text-light: white;
$text-dark: #050505;

$color-primary: #007aff; // Apple's blue (e.g., buttons, links)
$color-secondary: color.adjust($color-primary, $lightness: -10%); // Accent purple
$color-success: #2aa147; // Success green
$color-error: #ff3b30; // Error red
$color-warning: #ffcc00; // Warning yellow
$border-color: #c0c0c0; // Neutral gray (for placeholders, secondary text)
$color-success-op: rgba(52, 199, 89, 0.8);
$color-primary-op: rgb(0, 122, 255);
$input-bg: rgba(0, 122, 255, 0.14);

// Layout Variables
$sidebar-width: 250px;
$sidebar-collapsed-width: 80px;
$header-height: 70px;
$transition-speed: 0.3s;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}