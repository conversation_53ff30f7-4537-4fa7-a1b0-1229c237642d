import pg from 'pg';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const { Pool } = pg;
dotenv.config();

async function updateFlashcardsTableForAI() {
    const pool = new Pool({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || 'Lukind@1956',
        database: process.env.DB_NAME || 'blueprint',
        port: process.env.DB_PORT || 5432,
    });

    try {
        console.log('Checking if ai_generated column exists in flashcards table...');

        // Check if ai_generated column exists
        const aiGeneratedCheck = await pool.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'flashcards'
            AND column_name = 'ai_generated'
        `);

        if (aiGeneratedCheck.rows.length === 0) {
            console.log('Adding ai_generated column to flashcards table...');
            await pool.query(`
                ALTER TABLE flashcards
                ADD COLUMN ai_generated BOOLEAN DEFAULT FALSE
            `);
            console.log('ai_generated column added successfully');
        } else {
            console.log('ai_generated column already exists');
        }

        console.log('Flashcards table update for AI generation completed successfully');
    } catch (error) {
        console.error('Error updating flashcards table for AI generation:', error.message, error.stack);
    } finally {
        await pool.end();
        console.log('Database connection closed');
    }
}

updateFlashcardsTableForAI();
