"""
AI Assistant module for BlueprintAI

This module provides functionality for an AI assistant that can:
1. Access user-specific study data
2. Get today's objectives and upcoming objectives
3. Update objective display dates
4. Update study plan time_per_day
5. Process natural language requests using Gemini API

The AI assistant is designed to help users manage their study plans and objectives
through natural language interaction.
"""

from flask import Blueprint, request, jsonify
from models import User, Subject, Chapter, Objective, StudyPlan
from database import db, pg_conn
import google.generativeai as genai
import os
import json
import logging
import traceback
from datetime import datetime, timedelta, date
from functools import wraps
import jwt
from sqlalchemy import func
import psycopg2
import psycopg2.extras

# Create Blueprint for AI assistant routes
ai_assistant_bp = Blueprint('ai_assistant', __name__)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Authentication decorator (copied from app.py to maintain consistency)
def authenticate_token(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')

        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

        if not token:
            return jsonify({'success': False, 'message': 'Token is missing'}), 401

        try:
            # Get JWT secret key from environment
            jwt_secret = os.environ.get('JWT_SECRET')
            if not jwt_secret:
                logging.error("JWT_SECRET environment variable not set")
                return jsonify({'success': False, 'message': 'Server configuration error'}), 500

            # Decode the token
            payload = jwt.decode(token, jwt_secret, algorithms=['HS256'])
            # Use 'userId' key to match app.py implementation
            current_user_id = payload['userId']
            logging.info(f"Successfully authenticated user with ID: {current_user_id}")

            # Pass the current user ID to the wrapped function
            return f(current_user_id=current_user_id, *args, **kwargs)

        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': 'Token has expired'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': 'Invalid token'}), 401

    return decorated

# Initialize Gemini API
def init_gemini_api():
    """Initialize the Gemini API with the API key from environment variables"""
    try:
        # Get API key from environment variables
        api_key = os.environ.get('GOOGLE_API_KEY')

        # Log API key status (without revealing the key)
        if not api_key:
            logging.error("GOOGLE_API_KEY environment variable not set")
            # Use the hardcoded key from the .env file as a fallback
            api_key = "AIzaSyAdAJod78n6xaHYSGtZhO8BBVGigsyIwLo"
            logging.warning("Using fallback API key - not recommended for production")
        else:
            logging.info("Using GOOGLE_API_KEY from environment variables")

        # Configure Gemini with the API key
        genai.configure(api_key=api_key)
        logging.info("Configured Gemini API with key")

        # Define model priority list from newest to oldest
        model_names = [
            'gemini-2.0-flash',  # Latest model (2024)
            'gemini-1.5-flash',  # Alternative model
            'gemini-1.5-pro',    # Alternative model
            'gemini-pro'         # Older model
        ]

        # Try models in order until one works
        model = None
        for model_name in model_names:
            try:
                model = genai.GenerativeModel(model_name)
                logging.info(f"Successfully initialized {model_name} model")
                return model
            except Exception as e:
                logging.warning(f"Failed to initialize {model_name} model: {str(e)}")
                continue

        if model is None:
            logging.error("Failed to initialize any Gemini model")
            return None

    except Exception as e:
        logging.error(f"Error configuring Gemini API: {str(e)}")
        return None

# Initialize Gemini model
gemini_model = init_gemini_api()

# Helper Functions
def get_user_study_context(user_id):
    """
    Get user-specific study data including subjects, chapters, objectives, and study plans

    Args:
        user_id: The ID of the user

    Returns:
        Dictionary with user study context
    """
    try:
        # Get user information
        user = User.query.get(user_id)
        if not user:
            return {"error": "User not found"}

        # Get all subjects for the user
        subjects_data = []

        # Use direct SQL query to get subjects for the user
        # This avoids the ORM relationship that might be causing issues
        try:
            # Create a cursor from the pg_conn connection
            if pg_conn:
                pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

                # Query to get subjects for the user directly (subjects now have user_id)
                pg_cursor.execute("""
                    SELECT id, name
                    FROM subjects
                    WHERE user_id = %s
                """, (user_id,))

                # Make sure to commit or rollback after each query to prevent transaction errors
                pg_conn.commit()

                subjects_query = pg_cursor.fetchall()
                pg_cursor.close()
            else:
                # Fallback to ORM if pg_conn is not available
                logging.warning("PostgreSQL connection not available, falling back to ORM")
                subjects_query = db.session.query(Subject).filter(
                    Subject.user_id == user_id
                ).all()
        except Exception as e:
            logging.error(f"Error querying subjects: {str(e)}")
            logging.error(traceback.format_exc())
            subjects_query = []

        for subject in subjects_query:
            # Handle different types of results (ORM objects, dict, DictRow)
            try:
                # First try dictionary access (works for dict and DictRow)
                subject_id = subject['id']
                subject_name = subject['name']
            except (TypeError, KeyError):
                # If that fails, try attribute access (works for ORM objects)
                subject_id = subject.id
                subject_name = subject.name

            subject_data = {
                "id": subject_id,
                "name": subject_name,
                "chapters": []
            }

            # Get chapters for this subject
            chapters = Chapter.query.filter_by(subject_id=subject_id).all()
            for chapter in chapters:
                # Use title instead of name to match the database schema
                try:
                    chapter_name = chapter.title  # Try to use title first
                except AttributeError:
                    try:
                        chapter_name = chapter['title']  # Try dictionary access for title
                    except (KeyError, TypeError):
                        try:
                            chapter_name = chapter.name  # Fall back to name if title doesn't exist
                        except AttributeError:
                            chapter_name = chapter.get('name', 'Unknown Chapter')  # Last resort

                chapter_data = {
                    "id": chapter.id if hasattr(chapter, 'id') else chapter['id'],
                    "name": chapter_name,
                    "chapter_number": chapter.chapter_number if hasattr(chapter, 'chapter_number') else chapter.get('chapter_number', None),
                    "objectives": []
                }

                # Get objectives for this chapter
                objectives = Objective.query.filter_by(chapter_id=chapter.id).all()
                for objective in objectives:
                    objective_data = {
                        "id": objective.id,
                        "text": objective.objective,
                        "display_date": objective.display_date.isoformat() if objective.display_date else None,
                        "completed": objective.completed
                    }
                    chapter_data["objectives"].append(objective_data)

                subject_data["chapters"].append(chapter_data)

            subjects_data.append(subject_data)

        # Get study plans for the user
        study_plans = StudyPlan.query.filter_by(user_id=user_id).all()
        study_plans_data = []

        for plan in study_plans:
            plan_data = {
                "id": plan.id,
                "name": plan.name,
                "subject_id": plan.subject_id,
                "start_date": plan.start_date.isoformat(),
                "end_date": plan.end_date.isoformat(),
                "daily_study_time_minutes": plan.daily_study_time_minutes,
                "preferred_days": plan.preferred_days,
                "unavailable_periods": plan.unavailable_periods
            }
            study_plans_data.append(plan_data)

        # Compile all data
        user_context = {
            "user": {
                "id": user.id,
                "name": user.full_name,
                "email": user.email
            },
            "subjects": subjects_data,
            "study_plans": study_plans_data
        }

        return user_context

    except Exception as e:
        logging.error(f"Error getting user study context: {str(e)}")
        logging.error(traceback.format_exc())
        return {"error": f"Error getting user study context: {str(e)}"}

def get_today_objectives(user_id):
    """
    Get objectives scheduled for today for a given user

    Args:
        user_id: The ID of the user

    Returns:
        Dictionary with today's objectives
    """
    try:
        today = date.today()

        # Use direct SQL query to avoid ORM relationship issues
        if pg_conn:
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Query objectives with today's display_date
            pg_cursor.execute("""
                SELECT o.id, o.objective, o.completed,
                       c.id as chapter_id, c.title as chapter_name, c.chapter_number,
                       s.id as subject_id, s.name as subject_name
                FROM objectives o
                JOIN chapters c ON o.chapter_id = c.id
                JOIN subjects s ON c.subject_id = s.id
                WHERE s.user_id = %s AND o.display_date = %s
            """, (user_id, today))

            # Make sure to commit or rollback after each query to prevent transaction errors
            pg_conn.commit()

            objectives_query_result = pg_cursor.fetchall()
            pg_cursor.close()

            # Convert to format similar to SQLAlchemy result
            objectives_query = []
            for row in objectives_query_result:
                objective = type('Objective', (), {
                    'id': row['id'],
                    'objective': row['objective'],
                    'completed': row['completed']
                })
                chapter = type('Chapter', (), {
                    'id': row['chapter_id'],
                    'name': row['chapter_name'],
                    'chapter_number': row['chapter_number']
                })
                subject = type('Subject', (), {
                    'id': row['subject_id'],
                    'name': row['subject_name']
                })
                objectives_query.append((objective, chapter, subject))
        else:
            # Fallback to SQLAlchemy but with updated query
            objectives_query = db.session.query(
                Objective, Chapter, Subject
            ).join(
                Chapter, Objective.chapter_id == Chapter.id
            ).join(
                Subject, Chapter.subject_id == Subject.id
            ).filter(
                Subject.user_id == user_id,
                Objective.display_date == today
            ).all()

        objectives_data = []
        for objective, chapter, subject in objectives_query:
            # Use try-except to handle both attribute and dictionary access
            try:
                objective_data = {
                    "id": objective.id,
                    "text": objective.objective,
                    "completed": objective.completed,
                    "chapter": {
                        "id": chapter.id,
                        "name": chapter.title if hasattr(chapter, 'title') else (chapter['title'] if isinstance(chapter, dict) and 'title' in chapter else 'Unknown Chapter'),
                        "chapter_number": chapter.chapter_number
                    },
                    "subject": {
                        "id": subject.id,
                        "name": subject.name
                    }
                }
            except (AttributeError, TypeError):
                # If attribute access fails, try dictionary access
                objective_data = {
                    "id": objective['id'],
                    "text": objective['objective'],
                    "completed": objective['completed'],
                    "chapter": {
                        "id": chapter['id'],
                        "name": chapter['title'] if 'title' in chapter else (chapter.get('name', 'Unknown Chapter')),
                        "chapter_number": chapter['chapter_number']
                    },
                    "subject": {
                        "id": subject['id'],
                        "name": subject['name']
                    }
                }
            objectives_data.append(objective_data)

        return {
            "success": True,
            "date": today.isoformat(),
            "objectives": objectives_data
        }

    except Exception as e:
        logging.error(f"Error getting today's objectives: {str(e)}")
        logging.error(traceback.format_exc())
        return {"success": False, "error": f"Error getting today's objectives: {str(e)}"}

def get_upcoming_objectives(user_id, days=7):
    """
    Get upcoming objectives for a given user

    Args:
        user_id: The ID of the user
        days: Number of days to look ahead (default: 7)

    Returns:
        Dictionary with upcoming objectives grouped by date
    """
    try:
        today = date.today()
        end_date = today + timedelta(days=days)

        # Use direct SQL query to avoid ORM relationship issues
        if pg_conn:
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Query objectives with display_date in the upcoming days
            pg_cursor.execute("""
                SELECT o.id, o.objective, o.completed, o.display_date,
                       c.id as chapter_id, c.title as chapter_name, c.chapter_number,
                       s.id as subject_id, s.name as subject_name
                FROM objectives o
                JOIN chapters c ON o.chapter_id = c.id
                JOIN subjects s ON c.subject_id = s.id
                WHERE s.user_id = %s
                  AND o.display_date > %s
                  AND o.display_date <= %s
                ORDER BY o.display_date
            """, (user_id, today, end_date))

            # Make sure to commit or rollback after each query to prevent transaction errors
            pg_conn.commit()

            objectives_query_result = pg_cursor.fetchall()
            pg_cursor.close()

            # Convert to format similar to SQLAlchemy result
            objectives_query = []
            for row in objectives_query_result:
                objective = type('Objective', (), {
                    'id': row['id'],
                    'objective': row['objective'],
                    'completed': row['completed'],
                    'display_date': row['display_date']
                })
                chapter = type('Chapter', (), {
                    'id': row['chapter_id'],
                    'name': row['chapter_name'],
                    'chapter_number': row['chapter_number']
                })
                subject = type('Subject', (), {
                    'id': row['subject_id'],
                    'name': row['subject_name']
                })
                objectives_query.append((objective, chapter, subject))
        else:
            # Fallback to SQLAlchemy but with updated query
            objectives_query = db.session.query(
                Objective, Chapter, Subject
            ).join(
                Chapter, Objective.chapter_id == Chapter.id
            ).join(
                Subject, Chapter.subject_id == Subject.id
            ).filter(
                Subject.user_id == user_id,
                Objective.display_date > today,
                Objective.display_date <= end_date
            ).order_by(
                Objective.display_date
            ).all()

        # Group objectives by date
        objectives_by_date = {}
        for objective, chapter, subject in objectives_query:
            # Handle both attribute and dictionary access for display_date
            try:
                date_str = objective.display_date.isoformat()
            except AttributeError:
                date_str = objective['display_date'].isoformat()

            if date_str not in objectives_by_date:
                objectives_by_date[date_str] = []

            # Use try-except to handle both attribute and dictionary access
            try:
                objective_data = {
                    "id": objective.id,
                    "text": objective.objective,
                    "completed": objective.completed,
                    "chapter": {
                        "id": chapter.id,
                        "name": chapter.title if hasattr(chapter, 'title') else (chapter['title'] if isinstance(chapter, dict) and 'title' in chapter else 'Unknown Chapter'),
                        "chapter_number": chapter.chapter_number
                    },
                    "subject": {
                        "id": subject.id,
                        "name": subject.name
                    }
                }
            except (AttributeError, TypeError):
                # If attribute access fails, try dictionary access
                objective_data = {
                    "id": objective['id'],
                    "text": objective['objective'],
                    "completed": objective['completed'],
                    "chapter": {
                        "id": chapter['id'],
                        "name": chapter['title'] if 'title' in chapter else (chapter.get('name', 'Unknown Chapter')),
                        "chapter_number": chapter['chapter_number']
                    },
                    "subject": {
                        "id": subject['id'],
                        "name": subject['name']
                    }
                }
            objectives_by_date[date_str].append(objective_data)

        return {
            "success": True,
            "start_date": today.isoformat(),
            "end_date": end_date.isoformat(),
            "objectives_by_date": objectives_by_date
        }

    except Exception as e:
        logging.error(f"Error getting upcoming objectives: {str(e)}")
        logging.error(traceback.format_exc())
        return {"success": False, "error": f"Error getting upcoming objectives: {str(e)}"}

def direct_update_objective_display_date(objective_id, new_date):
    """Direct SQL update of an objective's display_date using raw SQL"""
    try:
        # Convert new_date to a date object if it's a string
        if isinstance(new_date, str):
            new_date = datetime.fromisoformat(new_date).date()

        # Log the update attempt
        logging.info(f"Attempting direct SQL update of objective ID {objective_id} to display_date {new_date}")

        # Try using the existing PostgreSQL connection first
        if pg_conn:
            try:
                # Use the existing connection
                logging.info(f"Using existing PostgreSQL connection for updating objective {objective_id}")
                cursor = pg_conn.cursor()

                # Execute UPDATE statement with explicit casting
                logging.info(f"Executing SQL: UPDATE objectives SET display_date = {new_date}::DATE WHERE id = {objective_id}")
                cursor.execute("""
                    UPDATE objectives
                    SET display_date = %s::DATE
                    WHERE id = %s
                """, (new_date, objective_id))

                # Commit the transaction
                pg_conn.commit()
                logging.info(f"Transaction committed for objective {objective_id}")

                # Verify the update
                cursor.execute("""
                    SELECT id, display_date FROM objectives WHERE id = %s
                """, (objective_id,))

                result = cursor.fetchone()

                if result:
                    logging.info(f"Direct SQL update successful: Objective ID {objective_id} now has display_date {result[1]}")
                    success = True
                else:
                    logging.error(f"Direct SQL update verification failed: Could not find objective ID {objective_id}")
                    success = False

                # Close the cursor (but not the connection since we're reusing it)
                cursor.close()
                logging.info(f"Cursor closed after updating objective {objective_id}")

                return success

            except Exception as pg_e:
                logging.error(f"Error in direct database update: {str(pg_e)}")
                logging.error(traceback.format_exc())

        # If pg_conn is not available or the first attempt failed, try SQLAlchemy
        try:
            # Execute raw SQL through SQLAlchemy
            logging.info(f"Using SQLAlchemy to update objective {objective_id} to display_date {new_date}")
            result = db.session.execute(
                "UPDATE objectives SET display_date = :new_date WHERE id = :objective_id",
                {"new_date": new_date, "objective_id": objective_id}
            )

            # Commit the transaction
            db.session.commit()
            logging.info(f"SQLAlchemy transaction committed for objective {objective_id}")

            # Verify the update
            verify = db.session.execute(
                "SELECT id, display_date FROM objectives WHERE id = :objective_id",
                {"objective_id": objective_id}
            ).fetchone()

            if verify:
                logging.info(f"Direct SQL update via SQLAlchemy successful: Objective ID {objective_id} now has display_date {verify.display_date}")
                return True
            else:
                logging.error(f"Direct SQL update via SQLAlchemy verification failed: Could not find objective ID {objective_id}")
                return False

        except Exception as sa_e:
            logging.error(f"Error in direct SQL update via SQLAlchemy: {str(sa_e)}")
            logging.error(traceback.format_exc())
            db.session.rollback()
            return False

    except Exception as e:
        logging.error(f"Unexpected error in direct_update_objective_display_date: {str(e)}")
        logging.error(traceback.format_exc())
        return False


def update_objectives_display_date(user_id, objective_ids, new_date):
    """
    Update the display_date of objectives

    Args:
        user_id: The ID of the user
        objective_ids: List of objective IDs to update
        new_date: New display date (string in ISO format or date object)

    Returns:
        Dictionary with success status and updated objectives
    """
    try:
        # Convert string date to date object if needed
        if isinstance(new_date, str):
            new_date = datetime.fromisoformat(new_date).date()

        logging.info(f"Starting update_objectives_display_date for user {user_id}, objectives {objective_ids}, new_date {new_date}")

        # Verify that the objectives belong to the user
        valid_objectives = []
        for objective_id in objective_ids:
            # Use direct SQL query to avoid ORM relationship issues
            if pg_conn:
                pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

                # Query to check if the objective belongs to the user
                pg_cursor.execute("""
                    SELECT o.id, o.objective, o.completed
                    FROM objectives o
                    JOIN chapters c ON o.chapter_id = c.id
                    JOIN subjects s ON c.subject_id = s.id
                    WHERE s.user_id = %s AND o.id = %s
                """, (user_id, objective_id))

                # Make sure to commit or rollback after each query to prevent transaction errors
                pg_conn.commit()

                result = pg_cursor.fetchone()
                pg_cursor.close()

                if result:
                    # Create an Objective-like object
                    objective_query = type('Objective', (), {
                        'id': result['id'],
                        'objective': result['objective'],
                        'completed': result['completed']
                    })
                    logging.info(f"Found valid objective ID {objective_id} for user {user_id}")
                else:
                    objective_query = None
                    logging.warning(f"Objective ID {objective_id} not found or does not belong to user {user_id}")
            else:
                # Fallback to SQLAlchemy but with updated query
                objective_query = db.session.query(
                    Objective
                ).join(
                    Chapter, Objective.chapter_id == Chapter.id
                ).join(
                    Subject, Chapter.subject_id == Subject.id
                ).filter(
                    Subject.user_id == user_id,
                    Objective.id == objective_id
                ).first()

                if objective_query:
                    logging.info(f"Found valid objective ID {objective_id} for user {user_id} via SQLAlchemy")
                else:
                    logging.warning(f"Objective ID {objective_id} not found or does not belong to user {user_id} via SQLAlchemy")

            if objective_query:
                valid_objectives.append(objective_query)

        if not valid_objectives:
            logging.error(f"No valid objectives found for user {user_id} with IDs {objective_ids}")
            return {
                "success": False,
                "error": "No valid objectives found for the user"
            }

        logging.info(f"Found {len(valid_objectives)} valid objectives for user {user_id}")

        # Update the display_date for valid objectives
        updated_objectives = []

        # Check if we're using PostgreSQL direct connection
        if pg_conn:
            logging.info(f"Using PostgreSQL direct connection to update {len(valid_objectives)} objectives to date {new_date}")
            pg_cursor = pg_conn.cursor()

            # Update each objective with a direct SQL UPDATE statement
            for objective in valid_objectives:
                try:
                    # Get the objective ID
                    objective_id = objective.id if hasattr(objective, 'id') else objective['id']

                    # Log the SQL statement we're about to execute
                    logging.info(f"Executing SQL: UPDATE objectives SET display_date = {new_date} WHERE id = {objective_id}")

                    # Execute UPDATE statement with explicit casting to DATE type
                    pg_cursor.execute("""
                        UPDATE objectives
                        SET display_date = %s::DATE
                        WHERE id = %s
                    """, (new_date, objective_id))

                    # Verify the update was successful by querying the updated row
                    pg_cursor.execute("""
                        SELECT id, display_date FROM objectives WHERE id = %s
                    """, (objective_id,))

                    updated_row = pg_cursor.fetchone()
                    if updated_row:
                        logging.info(f"Verification: Objective ID {objective_id} now has display_date {updated_row[1]}")
                    else:
                        logging.error(f"Verification failed: Could not find objective ID {objective_id} after update")

                    # Add to updated_objectives list
                    objective_text = objective.objective if hasattr(objective, 'objective') else objective['objective']
                    updated_objectives.append({
                        "id": objective_id,
                        "text": objective_text,
                        "display_date": new_date.isoformat()
                    })
                except Exception as e:
                    logging.error(f"Error updating objective {objective_id}: {str(e)}")
                    logging.error(traceback.format_exc())

            # Commit the transaction with additional error handling
            try:
                logging.info(f"Attempting to commit transaction for {len(updated_objectives)} objectives")
                pg_conn.commit()
                logging.info(f"Successfully committed transaction for {len(updated_objectives)} objectives")

                # Double-check that the changes were committed by querying one of the objectives
                if updated_objectives:
                    verify_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
                    objective_id = updated_objectives[0]['id']
                    verify_cursor.execute("""
                        SELECT id, display_date FROM objectives WHERE id = %s
                    """, (objective_id,))

                    verify_row = verify_cursor.fetchone()
                    if verify_row:
                        logging.info(f"Post-commit verification: Objective ID {objective_id} has display_date {verify_row['display_date']}")
                    else:
                        logging.error(f"Post-commit verification failed: Could not find objective ID {objective_id}")

                    verify_cursor.close()
            except Exception as commit_error:
                logging.error(f"Error during commit: {str(commit_error)}")
                logging.error(traceback.format_exc())
                pg_conn.rollback()
                raise
            finally:
                pg_cursor.close()
                logging.info(f"Cursor closed after committing updates for {len(updated_objectives)} objectives")
        else:
            # Using SQLAlchemy ORM with direct SQL as fallback
            logging.info(f"Using SQLAlchemy ORM to update {len(valid_objectives)} objectives to date {new_date}")

            # First try to use the ORM approach
            try:
                for objective in valid_objectives:
                    # Set the display_date attribute
                    try:
                        objective_id = objective.id if hasattr(objective, 'id') else objective['id']
                        objective_text = objective.objective if hasattr(objective, 'objective') else objective['objective']

                        # Try ORM update first
                        try:
                            objective.display_date = new_date
                            logging.info(f"Updated objective ID {objective_id} to display_date {new_date} via ORM")
                        except AttributeError:
                            # If ORM update fails, try direct SQL update
                            logging.info(f"ORM update failed for objective ID {objective_id}, trying direct SQL")
                            db.session.execute(
                                "UPDATE objectives SET display_date = :new_date WHERE id = :objective_id",
                                {"new_date": new_date, "objective_id": objective_id}
                            )
                            logging.info(f"Updated objective ID {objective_id} to display_date {new_date} via direct SQL")

                        # Add to updated_objectives list
                        updated_objectives.append({
                            "id": objective_id,
                            "text": objective_text,
                            "display_date": new_date.isoformat()
                        })
                    except Exception as obj_error:
                        logging.error(f"Error updating objective: {str(obj_error)}")
                        logging.error(traceback.format_exc())

                # Commit the changes
                db.session.commit()
                logging.info(f"Successfully committed updates for {len(updated_objectives)} objectives via SQLAlchemy")

                # Verify the updates
                if updated_objectives:
                    objective_id = updated_objectives[0]['id']
                    verify_result = db.session.execute(
                        "SELECT id, display_date FROM objectives WHERE id = :objective_id",
                        {"objective_id": objective_id}
                    ).fetchone()

                    if verify_result:
                        logging.info(f"Post-commit verification: Objective ID {objective_id} has display_date {verify_result.display_date}")
                    else:
                        logging.error(f"Post-commit verification failed: Could not find objective ID {objective_id}")

            except Exception as orm_error:
                # If ORM approach fails, try direct SQL update as a last resort
                logging.error(f"ORM update failed: {str(orm_error)}")
                logging.error(traceback.format_exc())

                try:
                    logging.info("Falling back to raw SQL update")
                    # Roll back any partial changes
                    db.session.rollback()

                    # Update each objective with raw SQL
                    for objective in valid_objectives:
                        objective_id = objective.id if hasattr(objective, 'id') else objective['id']
                        objective_text = objective.objective if hasattr(objective, 'objective') else objective['objective']

                        db.session.execute(
                            "UPDATE objectives SET display_date = :new_date WHERE id = :objective_id",
                            {"new_date": new_date, "objective_id": objective_id}
                        )

                        logging.info(f"Updated objective ID {objective_id} to display_date {new_date} via fallback SQL")

                        # Add to updated_objectives list
                        updated_objectives.append({
                            "id": objective_id,
                            "text": objective_text,
                            "display_date": new_date.isoformat()
                        })

                    # Commit the changes
                    db.session.commit()
                    logging.info(f"Successfully committed updates for {len(updated_objectives)} objectives via fallback SQL")
                except Exception as sql_error:
                    logging.error(f"Fallback SQL update failed: {str(sql_error)}")
                    logging.error(traceback.format_exc())
                    db.session.rollback()
                    raise

        # Final verification and logging
        logging.info(f"Successfully updated {len(updated_objectives)} objectives to date {new_date}")

        return {
            "success": True,
            "message": f"Updated display date for {len(updated_objectives)} objectives",
            "updated_objectives": updated_objectives
        }

    except Exception as e:
        if 'db' in locals() and hasattr(db, 'session'):
            db.session.rollback()
        logging.error(f"Error updating objectives display date: {str(e)}")
        logging.error(traceback.format_exc())
        return {"success": False, "error": f"Error updating objectives display date: {str(e)}"}

def update_study_time_per_day(user_id, study_plan_id, new_time_minutes):
    """
    Update the daily_study_time_minutes of a study plan

    Args:
        user_id: The ID of the user
        study_plan_id: The ID of the study plan
        new_time_minutes: New daily study time in minutes

    Returns:
        Dictionary with success status and updated study plan
    """
    try:
        # Verify that the study plan belongs to the user
        study_plan = StudyPlan.query.filter_by(id=study_plan_id, user_id=user_id).first()

        if not study_plan:
            return {
                "success": False,
                "error": "Study plan not found or does not belong to the user"
            }

        # Update the daily_study_time_minutes
        study_plan.daily_study_time_minutes = new_time_minutes

        # Commit the changes
        db.session.commit()

        return {
            "success": True,
            "message": f"Updated daily study time for study plan {study_plan_id}",
            "study_plan": {
                "id": study_plan.id,
                "name": study_plan.name,
                "daily_study_time_minutes": study_plan.daily_study_time_minutes
            }
        }

    except Exception as e:
        db.session.rollback()
        logging.error(f"Error updating study time per day: {str(e)}")
        logging.error(traceback.format_exc())
        return {"success": False, "error": f"Error updating study time per day: {str(e)}"}

def chat_with_ai(user_id, message, history=None):
    """
    Process a chat message with Gemini AI and return a response

    Args:
        user_id: The ID of the user
        message: The message from the user
        history: Optional chat history for context

    Returns:
        Dictionary with AI response and any actions taken
    """
    try:
        if not gemini_model:
            return {
                "success": False,
                "error": "Gemini model is not initialized",
                "message": "The AI service is not properly configured."
            }

        # Get user study context for the AI
        user_context = get_user_study_context(user_id)
        if "error" in user_context:
            return {
                "success": False,
                "error": user_context["error"],
                "message": "Unable to retrieve your study information."
            }

        # Get today's objectives for context
        today_objectives = get_today_objectives(user_id)
        today_obj_count = len(today_objectives.get('objectives', []))

        # Get flashcard stats for context
        flashcard_stats = get_flashcard_stats(user_id)

        # Create a detailed system prompt for Gemini with enhanced context
        system_prompt = f"""
You are Blueprint Assistant, an advanced AI tutor developed for BlueprintAI. Your purpose is to provide accurate, intelligent, and engaging responses to educational queries, fostering deep understanding and critical thinking. You also have access to the user's study data and can help manage their study plans, objectives, and study time.

USER INFORMATION:
- Name: {user_context['user']['name']}
- Email: {user_context['user']['email']}

STUDY CONTEXT:
- The user has {len(user_context['subjects'])} subjects
- The user has {len(user_context['study_plans'])} study plans

TODAY'S OBJECTIVES:
{json.dumps(today_objectives.get('objectives', []), indent=2)}

FLASHCARD INFORMATION:
{json.dumps(flashcard_stats.get('stats', {"total_count": 0}), indent=2)}

**Understanding the User**:
- I understand your name is {user_context['user']['name']}. I'll aim to weave your name into our conversations naturally.
- I'll tailor responses to your academic level, learning style, and goals when possible.

**My Approach to Helping You Learn**:
- Explain complex concepts clearly with analogies and real-world examples.
- Provide step-by-step problem-solving guidance with Socratic questioning.
- Offer personalized study strategies like time management and note-taking.
- Manage study objectives by rescheduling them or updating study time.

**Study Plan Management Capabilities**:
1. When a user says objectives are "too many" or "too few":
   - Ask how many objectives they want for today.
   - Reschedule excess objectives to future dates or pull objectives from tomorrow to today.
   - Follow these rescheduling rules:
     - Aim for 3-10 objectives per day, but respect the user's explicit choice.
     - If tomorrow has 10 objectives, reschedule to the next available day.
   - Provide a detailed summary of rescheduled objectives.
2. When a user directly specifies a number of objectives (e.g., "I want 5 objectives today"):
   - Adjust today's objectives to match the requested number.
   - Reschedule excess objectives or pull from tomorrow as needed.
3. When a user asks about objectives:
   - List today's objectives in a numbered format with chapter information.
   - Show upcoming objectives for specified days.
4. When a user wants to adjust study time:
   - Update their daily study time in the study plan.
   - Provide a confirmation message.

**Study Tools Guidance Capabilities**:
1. When a user asks about flashcards:
   - Provide statistics about their flashcards (total count, by subject, by difficulty).
   - Offer best practices for creating effective flashcards.
   - Suggest improvements to their flashcard study habits.
2. When a user asks about other study tools:
   - Provide guidance on how to effectively use tools like Kanban boards, Cornell notes, mind maps, etc.
   - Offer best practices for each tool type.
   - Suggest which tool might be best for different learning scenarios.
3. Important: Do NOT create content for the user's tools. Instead:
   - Guide them on how to create their own effective content.
   - Provide examples of good vs. poor content.
   - Explain the principles behind effective study tool design.

**Response Guidelines**:
- Be concise, friendly, and helpful.
- Use numbered format for objectives (Objective 1, 2, 3) with chapter info at the end.
- Use **bold text** for key terms or main points.
- Avoid repetitive greetings like "Hello {user_context['user']['name']}".
- Indicate actions taken (e.g., rescheduling, updating study time).
- Do not create study plans, objectives, study tools, or events.
"""
        # Format the request for the Gemini API
        conversation_text = f"System Instructions: {system_prompt}\n\n"
        if history and len(history) > 0:
            for entry in history[-5:]:
                role = entry.get('role', '')
                content_text = entry.get('content', '')
                if role == 'user':
                    conversation_text += f"User: {content_text}\n\n"
                elif role == 'assistant':
                    conversation_text += f"Assistant: {content_text}\n\n"
        conversation_text += f"User: {message}"

        content = [{"parts": [{"text": conversation_text}]}]
        response = gemini_model.generate_content(content)
        ai_response = response.text.strip()
        actions_taken = []

        # Handle objectives-related requests
        import re
        is_too_many = ("too many" in message.lower() or "too much" in message.lower()) and "objective" in message.lower()
        is_too_few = "too few" in message.lower() and "objective" in message.lower()

        # More flexible regex pattern for detecting rescheduling requests
        want_objectives_match = re.search(r'(?:i want|i\'d like|give me|reschedule to|keep|leave|set|make it|change to|only|show me|let\'s do|can i have|please|just|reduce to|increase to) (\d+) objectives?(?:[ \.,]|$|for|today|for today|for me|instead)', message.lower())

        # Also check for simple number patterns like "I want 2" after being asked how many objectives
        is_number_response = history and len(history) >= 2 and "how many objectives" in history[-1].get('content', '').lower() and (message.isdigit() or re.search(r'(\d+)', message))

        logging.info(f"Checking for rescheduling request: is_too_many={is_too_many}, is_too_few={is_too_few}, want_objectives_match={bool(want_objectives_match)}, is_number_response={is_number_response}")

        if is_too_many or is_too_few:
            logging.info(f"User indicated they have too many/few objectives. Prompting for count.")
            ai_response = "How many objectives would you like to have for today?"
            actions_taken.append("prompted_for_objective_count")
        elif want_objectives_match or is_number_response:
            # Extract the number from the message
            if want_objectives_match:
                num_objectives = int(want_objectives_match.group(1))
            elif message.isdigit():
                num_objectives = int(message)
            else:
                # Try to extract a number from the message
                number_match = re.search(r'(\d+)', message)
                if number_match:
                    num_objectives = int(number_match.group(1))
                else:
                    # Fallback to a default value
                    num_objectives = 3
                    logging.warning(f"Could not extract a number from message: {message}, using default value: {num_objectives}")
            logging.info(f"User requested {num_objectives} objectives for today. Current count: {today_obj_count}")

            MAX_OBJECTIVES_PER_DAY = 10
            MIN_OBJECTIVES_PER_DAY = 3

            if num_objectives < MIN_OBJECTIVES_PER_DAY:
                logging.info(f"User requested fewer than minimum ({MIN_OBJECTIVES_PER_DAY}) objectives: {num_objectives}")
                ai_response += f"\n\nYou've requested {num_objectives} objectives, fewer than the recommended {MIN_OBJECTIVES_PER_DAY}. I'll adjust as requested."
            elif num_objectives > MAX_OBJECTIVES_PER_DAY:
                logging.info(f"User requested more than maximum ({MAX_OBJECTIVES_PER_DAY}) objectives: {num_objectives}")
                ai_response += f"\n\nYou've requested {num_objectives} objectives, more than the recommended {MAX_OBJECTIVES_PER_DAY}. I'll adjust as requested."

            if num_objectives != today_obj_count:
                logging.info(f"Need to reschedule: requested={num_objectives}, current={today_obj_count}")
                rescheduled_objectives = []
                confirmation = ""

                if num_objectives < today_obj_count:
                    # Moving objectives from today to future dates
                    objectives_to_move = today_obj_count - num_objectives
                    logging.info(f"Need to move {objectives_to_move} objectives from today to future dates")

                    # Get the IDs of objectives to move (last N objectives)
                    objective_ids = [obj['id'] for obj in today_objectives.get('objectives', [])][-objectives_to_move:]
                    logging.info(f"Objectives to move: {objective_ids}")

                    # Get upcoming objectives to check capacity
                    upcoming_objectives = get_upcoming_objectives(user_id, 14)
                    objectives_by_date = upcoming_objectives.get('objectives_by_date', {})

                    # Start with tomorrow and find suitable days
                    current_date = date.today() + timedelta(days=1)
                    remaining_objectives = objectives_to_move

                    logging.info(f"Starting to distribute {remaining_objectives} objectives to future dates")

                    # Track successful updates
                    successful_updates = []

                    while remaining_objectives > 0:
                        date_str = current_date.isoformat()
                        existing_objectives = len(objectives_by_date.get(date_str, []))
                        available_slots = MAX_OBJECTIVES_PER_DAY - existing_objectives

                        logging.info(f"Checking date {date_str}: has {existing_objectives} objectives, {available_slots} slots available")

                        if available_slots > 0:
                            objectives_to_add = min(available_slots, remaining_objectives)
                            start_idx = objectives_to_move - remaining_objectives
                            end_idx = start_idx + objectives_to_add
                            objectives_for_date = objective_ids[start_idx:end_idx]

                            logging.info(f"Will add {objectives_to_add} objectives to {date_str}: {objectives_for_date}")

                            # Use direct update function for each objective
                            for objective_id in objectives_for_date:
                                logging.info(f"Calling direct_update_objective_display_date for objective {objective_id} to date {date_str}")
                                update_success = direct_update_objective_display_date(objective_id, date_str)

                                if update_success:
                                    logging.info(f"Successfully updated objective {objective_id} to {date_str}")

                                    # Get objective text for confirmation message
                                    objective_text = next((obj['text'] for obj in today_objectives.get('objectives', []) if obj['id'] == objective_id), "Unknown objective")

                                    # Add to successful updates
                                    successful_updates.append({
                                        "id": objective_id,
                                        "text": objective_text,
                                        "display_date": date_str
                                    })

                                    # Decrement remaining objectives
                                    remaining_objectives -= 1
                                else:
                                    logging.error(f"Failed to update objective {objective_id} to {date_str}")

                        current_date += timedelta(days=1)

                    if successful_updates:
                        logging.info(f"Successfully rescheduled {len(successful_updates)} objectives")
                        actions_taken.append("rescheduled_objectives")
                        rescheduled_objectives.extend(successful_updates)

                        confirmation = f"\n\nI've rescheduled {len(successful_updates)} objectives:\n"
                        for obj in successful_updates:
                            date_obj = datetime.fromisoformat(obj['display_date']).date()
                            date_formatted = date_obj.strftime("%A, %B %d")
                            confirmation += f"- \"{obj['text']}\" → {date_formatted}\n"
                    else:
                        logging.warning(f"No objectives were rescheduled despite needing to move {objectives_to_move}")

                elif num_objectives > today_obj_count:
                    # Moving objectives from tomorrow to today
                    objectives_needed = num_objectives - today_obj_count
                    logging.info(f"Need to move {objectives_needed} objectives from tomorrow to today")

                    # Get tomorrow's objectives
                    tomorrow = (date.today() + timedelta(days=1)).isoformat()
                    upcoming_objectives = get_upcoming_objectives(user_id, 1)
                    tomorrow_objectives = upcoming_objectives.get('objectives_by_date', {}).get(tomorrow, [])

                    logging.info(f"Found {len(tomorrow_objectives)} objectives for tomorrow")

                    if len(tomorrow_objectives) >= objectives_needed:
                        # Get the IDs of objectives to move
                        objective_ids = [obj['id'] for obj in tomorrow_objectives][:objectives_needed]
                        logging.info(f"Will move these objectives from tomorrow to today: {objective_ids}")

                        # Use direct update function for each objective
                        successful_updates = []
                        today_date = date.today()

                        for objective_id in objective_ids:
                            logging.info(f"Calling direct_update_objective_display_date for objective {objective_id} to today")
                            update_success = direct_update_objective_display_date(objective_id, today_date)

                            if update_success:
                                logging.info(f"Successfully updated objective {objective_id} to today")

                                # Get objective text for confirmation message
                                objective_text = next((obj['text'] for obj in tomorrow_objectives if obj['id'] == objective_id), "Unknown objective")

                                # Add to successful updates
                                successful_updates.append({
                                    "id": objective_id,
                                    "text": objective_text,
                                    "display_date": today_date.isoformat()
                                })
                            else:
                                logging.error(f"Failed to update objective {objective_id} to today")

                        if successful_updates:
                            logging.info(f"Successfully moved {len(successful_updates)} objectives to today")
                            actions_taken.append("rescheduled_objectives")
                            rescheduled_objectives.extend(successful_updates)

                            confirmation = f"\n\nI've moved {len(successful_updates)} objectives from tomorrow to today:\n"
                            for obj in successful_updates:
                                date_formatted = "Today"
                                confirmation += f"- \"{obj['text']}\" → {date_formatted}\n"
                        else:
                            logging.error(f"Failed to move any objectives to today")
                            return {
                                "success": False,
                                "error": "Failed to reschedule objectives",
                                "message": "There was an error rescheduling your objectives."
                            }
                    else:
                        logging.info(f"Not enough objectives for tomorrow. Have {len(tomorrow_objectives)}, need {objectives_needed}")
                        ai_response += f"\n\nI couldn't find enough objectives for tomorrow to meet your request of {num_objectives}. You have {today_obj_count} today and {len(tomorrow_objectives)} tomorrow."

                # Verify database state
                logging.info(f"Verifying database state after rescheduling")
                updated_today_objectives = get_today_objectives(user_id)
                updated_count = len(updated_today_objectives.get('objectives', []))
                logging.info(f"After rescheduling: expected={num_objectives}, actual={updated_count}")

                if updated_today_objectives.get('success') and updated_count != num_objectives:
                    logging.error(f"Database verification failed: Expected {num_objectives} objectives, found {updated_count}")
                    return {
                        "success": False,
                        "error": "Database verification failed",
                        "message": "The schedule was not updated correctly. Please try again."
                    }

                ai_response += confirmation

        # Handle other requests (today's objectives, upcoming objectives, study time)
        if ("what" in message.lower() and "objectives" in message.lower() and "today" in message.lower()) or \
           ("show" in message.lower() and "objectives" in message.lower() and "today" in message.lower()):
            if today_objectives.get('success') and today_objectives.get('objectives'):
                subject_name = today_objectives.get('objectives', [])[0]['subject']['name']
                objectives_text = f"\n\nHere are your objectives for today in {subject_name}:\n\n"
                for i, obj in enumerate(today_objectives.get('objectives', []), 1):
                    objectives_text += f"**Objective {i}**: {obj['text']}\n"
                chapter_info = today_objectives.get('objectives', [])[0]['chapter']['chapter_number'] or "Unknown Chapter"
                chapter_title = today_objectives.get('objectives', [])[0]['chapter']['name'] or "Unknown Chapter"
                objectives_text += f"\nAll these objectives are from {chapter_info} of \"{chapter_title}.\" Let me know if you'd like more details or changes!"
                ai_response = f"{ai_response}\n{objectives_text}"
                actions_taken.append("provided_today_objectives")
            else:
                ai_response += "\n\nYou don't have any objectives scheduled for today. Would you like me to check upcoming objectives?"
                actions_taken.append("no_objectives_today")

        # Handle flashcard-related requests
        flashcard_request = (
            ("flashcard" in message.lower()) or
            ("flash card" in message.lower()) or
            ("flash-card" in message.lower())
        )

        flashcard_stats_request = (
            flashcard_request and
            any(word in message.lower() for word in ["stats", "statistics", "count", "how many", "summary"])
        )

        flashcard_best_practices_request = (
            flashcard_request and
            any(word in message.lower() for word in ["best", "practice", "tip", "advice", "how to", "guide", "help"])
        )

        flashcard_content_request = (
            flashcard_request and
            any(word in message.lower() for word in ["show", "list", "display", "see", "view", "content", "what are", "my"])
        )

        flashcard_learning_request = (
            flashcard_request and
            any(word in message.lower() for word in ["learn", "study", "practice", "review", "test", "quiz", "help me learn"])
        )

        # Extract subject or chapter information from the message
        subject_id = None
        chapter_id = None

        # Get user context to find subjects and chapters
        if user_context and "subjects" in user_context:
            # Check if any subject name is mentioned in the message
            for subject in user_context["subjects"]:
                if subject["name"].lower() in message.lower():
                    subject_id = subject["id"]
                    logging.info(f"Found subject ID {subject_id} ({subject['name']}) mentioned in message")

                    # Check if any chapter from this subject is mentioned
                    for chapter in subject.get("chapters", []):
                        chapter_name = chapter.get("name", "")
                        chapter_number = chapter.get("chapter_number", "")

                        if (chapter_name and chapter_name.lower() in message.lower()) or \
                           (chapter_number and chapter_number.lower() in message.lower()):
                            chapter_id = chapter["id"]
                            logging.info(f"Found chapter ID {chapter_id} ({chapter_name}) mentioned in message")
                            break

                    break

        if flashcard_stats_request:
            # Get flashcard stats
            stats = get_flashcard_stats(user_id)
            if stats.get('success') and stats.get('stats'):
                total_count = stats['stats']['total_count']
                by_subject = stats['stats'].get('by_subject', [])
                by_difficulty = stats['stats'].get('by_difficulty', {})
                by_type = stats['stats'].get('by_type', {})

                stats_text = f"\n\n**Your Flashcard Statistics**\n\n"
                stats_text += f"You have a total of **{total_count} flashcards**.\n\n"

                if by_subject:
                    stats_text += "**Flashcards by Subject:**\n"
                    for subject in by_subject:
                        stats_text += f"- {subject['subject_name']}: {subject['card_count']} cards\n"
                    stats_text += "\n"

                if by_difficulty:
                    difficult_count = by_difficulty.get('True', 0)
                    normal_count = by_difficulty.get('False', 0)
                    stats_text += f"**Difficulty Level:**\n"
                    stats_text += f"- Difficult cards: {difficult_count}\n"
                    stats_text += f"- Normal cards: {normal_count}\n\n"

                if by_type:
                    stats_text += f"**Card Types:**\n"
                    for card_type, count in by_type.items():
                        stats_text += f"- {card_type}: {count} cards\n"

                ai_response += stats_text
                actions_taken.append("provided_flashcard_stats")
            else:
                ai_response += "\n\nI couldn't find any flashcards in your account. Would you like some guidance on creating effective flashcards?"
                actions_taken.append("no_flashcards_found")

        elif flashcard_content_request:
            # Get flashcards content
            flashcards_result = get_user_flashcards(
                user_id,
                subject_id,
                chapter_id,
                limit=10,  # Limit to 10 cards to avoid overwhelming the user
                include_content=True
            )

            if flashcards_result.get('success') and flashcards_result.get('flashcards'):
                flashcards = flashcards_result['flashcards']
                count = len(flashcards)

                # Group by subject/chapter for better organization
                cards_by_group = {}
                for card in flashcards:
                    group_key = (card.get('subject_name', 'Unknown'), card.get('chapter_title', 'Unknown'))
                    if group_key not in cards_by_group:
                        cards_by_group[group_key] = []
                    cards_by_group[group_key].append(card)

                content_text = f"\n\n**Your Flashcards ({count} shown)**\n\n"

                for (subject_name, chapter_title), cards in cards_by_group.items():
                    content_text += f"**Subject: {subject_name} - Chapter: {chapter_title}**\n\n"

                    for i, card in enumerate(cards, 1):
                        card_type = card.get('type', 'basic')
                        question = card.get('question', 'No question')
                        answer = card.get('answer', 'No answer')

                        # Format based on card type
                        if card_type == 'cloze':
                            # For cloze cards, show the cloze text with blanks
                            cloze_text = question.replace("{{", "____").replace("}}", "____")
                            content_text += f"**Card {i}** (Cloze):\n"
                            content_text += f"- Text: {cloze_text}\n"
                            content_text += f"- Answer: {answer}\n\n"
                        else:
                            # For basic cards, show question and answer
                            content_text += f"**Card {i}** ({card_type}):\n"
                            content_text += f"- Question: {question}\n"
                            content_text += f"- Answer: {answer}\n\n"

                if count == 0:
                    content_text = "\n\nI couldn't find any flashcards matching your request. Would you like to see flashcards from a different subject or chapter?"
                    actions_taken.append("no_matching_flashcards")
                else:
                    content_text += "\nThese are just a few of your flashcards. Would you like to study them or see more?"
                    actions_taken.append("provided_flashcard_content")

                ai_response += content_text
            else:
                ai_response += "\n\nI couldn't find any flashcards in your account. Would you like some guidance on creating effective flashcards?"
                actions_taken.append("no_flashcards_found")

        elif flashcard_learning_request:
            # Get flashcards for learning
            prioritize_difficult = "difficult" in message.lower()

            learning_result = get_flashcards_for_learning(
                user_id,
                subject_id,
                chapter_id,
                count=5,  # Start with 5 cards for a learning session
                prioritize_difficult=prioritize_difficult
            )

            if learning_result.get('success') and learning_result.get('flashcards'):
                flashcards = learning_result['flashcards']
                count = len(flashcards)

                learning_text = f"\n\n**Let's Study Your Flashcards ({count} cards)**\n\n"
                learning_text += "I'll show you the questions one by one. Try to recall the answers before revealing them.\n\n"

                for i, card in enumerate(flashcards, 1):
                    card_type = card.get('type', 'basic')
                    question = card.get('question', 'No question')
                    answer = card.get('answer', 'No answer')
                    subject_name = card.get('subject_name', 'Unknown Subject')
                    chapter_title = card.get('chapter_title', 'Unknown Chapter')

                    # Format based on card type
                    if card_type == 'cloze':
                        # For cloze cards, show the cloze text with blanks
                        cloze_text = question.replace("{{", "____").replace("}}", "____")
                        learning_text += f"**Card {i}** (From {subject_name} - {chapter_title}):\n"
                        learning_text += f"{cloze_text}\n\n"
                        learning_text += f"**Answer:** {answer}\n\n"
                    else:
                        # For basic cards, show question and answer
                        learning_text += f"**Card {i}** (From {subject_name} - {chapter_title}):\n"
                        learning_text += f"**Question:** {question}\n\n"
                        learning_text += f"**Answer:** {answer}\n\n"

                if count == 0:
                    learning_text = "\n\nI couldn't find any flashcards for you to study. Would you like some guidance on creating effective flashcards?"
                    actions_taken.append("no_flashcards_for_learning")
                else:
                    learning_text += "\nTo effectively learn this material:\n"
                    learning_text += "1. Try to recall the answer before looking at it\n"
                    learning_text += "2. Rate your confidence in your answer (high, medium, low)\n"
                    learning_text += "3. For cards you struggled with, create connections to things you already know\n"
                    learning_text += "4. Review difficult cards more frequently\n\n"
                    learning_text += "Would you like to study more cards or focus on a specific subject?"
                    actions_taken.append("provided_flashcard_learning")

                ai_response += learning_text
            else:
                ai_response += "\n\nI couldn't find any flashcards for you to study. Would you like some guidance on creating effective flashcards?"
                actions_taken.append("no_flashcards_for_learning")

        elif flashcard_best_practices_request:
            # Get flashcard best practices
            best_practices = get_tool_best_practices("flashcards")
            if best_practices.get('success') and best_practices.get('best_practices'):
                practices = best_practices['best_practices']

                practices_text = f"\n\n**{practices['title']}**\n\n"
                for i, practice in enumerate(practices['practices'], 1):
                    practices_text += f"{i}. {practice}\n"

                if 'examples' in practices:
                    practices_text += "\n**Examples:**\n"
                    for example in practices['examples']:
                        practices_text += f"- Good: \"{example['good']}\"\n"
                        practices_text += f"  Bad: \"{example['bad']}\"\n"

                # Get flashcard stats to provide personalized advice
                stats = get_flashcard_stats(user_id)
                if stats.get('success') and stats.get('stats') and stats['stats']['total_count'] > 0:
                    total_count = stats['stats']['total_count']
                    by_type = stats['stats'].get('by_type', {})

                    # Add personalized advice based on their current flashcards
                    practices_text += f"\n\n**Personalized Advice Based on Your {total_count} Flashcards:**\n"

                    # Check if they have a good mix of card types
                    if len(by_type) == 1 and 'basic' in by_type:
                        practices_text += "- Try creating some cloze deletion cards to test your knowledge in a different way\n"

                    # Add more personalized advice here based on their stats
                    practices_text += "- Review your cards regularly using spaced repetition for better retention\n"
                    practices_text += "- Consider marking difficult cards to prioritize them in your study sessions\n"

                ai_response += practices_text
                actions_taken.append("provided_flashcard_best_practices")
            else:
                ai_response += "\n\nI couldn't retrieve the best practices for flashcards. Would you like some general study advice instead?"
                actions_taken.append("failed_to_get_best_practices")

        # Handle study tool best practices requests
        tool_types = ["kanban", "cornell_notes", "mindmap"]
        requested_tool = None

        for tool in tool_types:
            tool_name = tool.replace("_", " ")
            if tool_name in message.lower() or tool in message.lower():
                requested_tool = tool
                break

        if requested_tool and any(word in message.lower() for word in ["best", "practice", "tip", "advice", "how to", "guide", "help"]):
            # Get best practices for the requested tool
            best_practices = get_tool_best_practices(requested_tool)
            if best_practices.get('success') and best_practices.get('best_practices'):
                practices = best_practices['best_practices']

                practices_text = f"\n\n**{practices['title']}**\n\n"
                for i, practice in enumerate(practices['practices'], 1):
                    practices_text += f"{i}. {practice}\n"

                if 'examples' in practices:
                    practices_text += "\n**Examples:**\n"
                    for example in practices['examples']:
                        practices_text += f"- Good: \"{example['good']}\"\n"
                        practices_text += f"  Bad: \"{example['bad']}\"\n"

                ai_response += practices_text
                actions_taken.append(f"provided_{requested_tool}_best_practices")
            else:
                ai_response += f"\n\nI couldn't retrieve the best practices for {requested_tool.replace('_', ' ')}. Would you like some general study advice instead?"
                actions_taken.append("failed_to_get_best_practices")

        if ("what" in message.lower() and "objectives" in message.lower() and "upcoming" in message.lower()) or \
           ("show" in message.lower() and "objectives" in message.lower() and "upcoming" in message.lower()) or \
           ("what" in message.lower() and "objectives" in message.lower() and "next" in message.lower()) or \
           ("show" in message.lower() and "objectives" in message.lower() and "next" in message.lower()):
            days = 7
            if "week" in message.lower():
                days = 7
            elif "month" in message.lower():
                days = 30
            elif "days" in message.lower():
                days_match = re.search(r'(\d+)\s*days', message.lower())
                if days_match:
                    days = int(days_match.group(1))
            upcoming_objectives = get_upcoming_objectives(user_id, days)
            if upcoming_objectives.get('success') and upcoming_objectives.get('objectives_by_date'):
                all_subjects = set(obj['subject']['name'] for _, objectives in upcoming_objectives.get('objectives_by_date', {}).items() for obj in objectives)
                subject_list = ", ".join(all_subjects)
                objectives_text = f"\n\nHere are your upcoming objectives for the next {days} days in {subject_list}:\n"
                for date_str, objectives in upcoming_objectives.get('objectives_by_date', {}).items():
                    date_obj = datetime.fromisoformat(date_str).date()
                    date_formatted = date_obj.strftime("%A, %B %d")
                    objectives_text += f"\n**{date_formatted}:**\n"
                    objectives_by_chapter = {}
                    for obj in objectives:
                        chapter_key = (obj['chapter']['chapter_number'], obj['chapter']['name'], obj['subject']['name'])
                        if chapter_key not in objectives_by_chapter:
                            objectives_by_chapter[chapter_key] = []
                        objectives_by_chapter[chapter_key].append(obj)
                    for (chapter_number, chapter_name, subject_name), chapter_objectives in objectives_by_chapter.items():
                        chapter_info = chapter_number or "Unknown Chapter"
                        chapter_title = chapter_name or "Unknown Chapter"
                        objectives_text += f"From {chapter_info} of \"{chapter_title}\" ({subject_name}):\n"
                        for i, obj in enumerate(chapter_objectives, 1):
                            objectives_text += f"**Objective {i}**: {obj['text']}\n"
                        objectives_text += "\n"
                    objectives_text += "Let me know if you'd like more details or changes!"
                    ai_response = f"{ai_response}\n{objectives_text}"
                    actions_taken.append("provided_upcoming_objectives")
            else:
                ai_response += f"\n\nNo objectives scheduled for the next {days} days. Want help creating some?"
                actions_taken.append("no_upcoming_objectives")

        if ("update" in message.lower() and "study time" in message.lower()) or \
           ("change" in message.lower() and "study time" in message.lower()):
            time_match = re.search(r'(\d+)\s*(minutes|mins|min)', message.lower())
            if time_match:
                new_time = int(time_match.group(1))
                if user_context['study_plans']:
                    study_plan_id = user_context['study_plans'][0]['id']
                    update_result = update_study_time_per_day(user_id, study_plan_id, new_time)
                    if update_result.get('success'):
                        actions_taken.append("updated_study_time")
                        ai_response += f"\n\nI've updated your daily study time to {new_time} minutes."
                    else:
                        ai_response += "\n\nFailed to update study time. Please try again."

        return {
            "success": True,
            "response": ai_response,
            "actions_taken": actions_taken
        }

    except Exception as e:
        logging.error(f"Error processing chat message: {str(e)}")
        logging.error(traceback.format_exc())
        return {
            "success": False,
            "error": f"Error processing chat message: {str(e)}",
            "message": "There was an error processing your chat message."
        }

# API Endpoints
@ai_assistant_bp.route('/api/ai/user_context', methods=['GET'])
@authenticate_token
def api_get_user_context(current_user_id):
    """API endpoint to get user study context"""
    return jsonify(get_user_study_context(current_user_id))

@ai_assistant_bp.route('/api/ai/today_objectives', methods=['GET'])
@authenticate_token
def api_get_today_objectives(current_user_id):
    """API endpoint to get today's objectives"""
    return jsonify(get_today_objectives(current_user_id))

@ai_assistant_bp.route('/api/ai/upcoming_objectives', methods=['GET'])
@authenticate_token
def api_get_upcoming_objectives(current_user_id):
    """API endpoint to get upcoming objectives"""
    days = request.args.get('days', default=7, type=int)
    return jsonify(get_upcoming_objectives(current_user_id, days))

@ai_assistant_bp.route('/api/ai/update_objectives_date', methods=['POST'])
@authenticate_token
def api_update_objectives_date(current_user_id):
    """API endpoint to update objectives display date"""
    data = request.json
    if not data or 'objective_ids' not in data or 'new_date' not in data:
        return jsonify({
            "success": False,
            "error": "Missing required fields: objective_ids, new_date"
        }), 400

    return jsonify(update_objectives_display_date(
        current_user_id,
        data['objective_ids'],
        data['new_date']
    ))

@ai_assistant_bp.route('/api/ai/update_study_time', methods=['POST'])
@authenticate_token
def api_update_study_time(current_user_id):
    """API endpoint to update study plan time per day"""
    data = request.json
    if not data or 'study_plan_id' not in data or 'new_time_minutes' not in data:
        return jsonify({
            "success": False,
            "error": "Missing required fields: study_plan_id, new_time_minutes"
        }), 400

    return jsonify(update_study_time_per_day(
        current_user_id,
        data['study_plan_id'],
        data['new_time_minutes']
    ))



def get_user_flashcards(user_id, subject_id=None, chapter_id=None, limit=20, include_content=True, filter_by_difficulty=None, filter_by_type=None):
    """
    Get flashcards for a specific user, optionally filtered by subject, chapter, difficulty, or type

    Args:
        user_id: The ID of the user
        subject_id: Optional subject ID to filter by
        chapter_id: Optional chapter ID to filter by
        limit: Maximum number of flashcards to return (default: 20)
        include_content: Whether to include the full question and answer content (default: True)
        filter_by_difficulty: Optional filter for difficult cards (True/False)
        filter_by_type: Optional filter for card type (e.g., 'basic', 'cloze')

    Returns:
        Dictionary with flashcards data or error message
    """
    try:
        # Build the query based on provided filters
        if pg_conn:
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Start building the query
            select_clause = "f.id, f.subject_id, f.chapter_id, s.name as subject_name, c.title as chapter_title, c.chapter_number, f.type, f.difficult, f.box_level, f.last_reviewed, f.next_review_date, f.color, f.review_count, f.success_rate, f.tags, f.created_at, f.updated_at"

            # Include content only if requested (to reduce payload size for statistics-only requests)
            if include_content:
                select_clause = "f.*, " + select_clause

            query = f"""
                SELECT {select_clause}
                FROM flashcards f
                JOIN subjects s ON f.subject_id = s.id
                JOIN chapters c ON f.chapter_id = c.id
                WHERE f.user_id = %s
            """
            params = [user_id]

            if subject_id:
                query += " AND f.subject_id = %s"
                params.append(subject_id)

            if chapter_id:
                query += " AND f.chapter_id = %s"
                params.append(chapter_id)

            if filter_by_difficulty is not None:
                query += " AND f.difficult = %s"
                params.append(filter_by_difficulty)

            if filter_by_type:
                query += " AND f.type = %s"
                params.append(filter_by_type)

            query += " ORDER BY f.created_at DESC LIMIT %s"
            params.append(limit)

            # Execute the query
            pg_cursor.execute(query, params)

            # Commit to prevent transaction errors
            pg_conn.commit()

            # Fetch results
            flashcards = pg_cursor.fetchall()
            pg_cursor.close()

            # Process the results
            result_flashcards = []
            for card in flashcards:
                card_dict = dict(card)

                # Convert datetime objects to strings for JSON serialization
                if card_dict.get('created_at'):
                    card_dict['created_at'] = card_dict['created_at'].isoformat()
                if card_dict.get('updated_at'):
                    card_dict['updated_at'] = card_dict['updated_at'].isoformat()
                if card_dict.get('last_reviewed'):
                    card_dict['last_reviewed'] = card_dict['last_reviewed'].isoformat() if card_dict['last_reviewed'] else None
                if card_dict.get('next_review_date'):
                    card_dict['next_review_date'] = card_dict['next_review_date'].isoformat() if card_dict['next_review_date'] else None

                # Convert tags string to array if present
                if card_dict.get('tags'):
                    card_dict['tags'] = card_dict['tags'].split(',') if card_dict['tags'] else []

                result_flashcards.append(card_dict)
        else:
            # Fallback to SQLAlchemy if pg_conn is not available
            logging.warning("PostgreSQL connection not available, falling back to raw SQL via SQLAlchemy")

            # Start building the query
            select_clause = "f.id, f.subject_id, f.chapter_id, s.name as subject_name, c.title as chapter_title, c.chapter_number, f.type, f.difficult, f.box_level, f.last_reviewed, f.next_review_date, f.color, f.review_count, f.success_rate, f.tags, f.created_at, f.updated_at"

            # Include content only if requested (to reduce payload size for statistics-only requests)
            if include_content:
                select_clause = "f.*, " + select_clause

            query = f"""
                SELECT {select_clause}
                FROM flashcards f
                JOIN subjects s ON f.subject_id = s.id
                JOIN chapters c ON f.chapter_id = c.id
                WHERE f.user_id = :user_id
            """
            params = {"user_id": user_id}

            if subject_id:
                query += " AND f.subject_id = :subject_id"
                params["subject_id"] = subject_id

            if chapter_id:
                query += " AND f.chapter_id = :chapter_id"
                params["chapter_id"] = chapter_id

            if filter_by_difficulty is not None:
                query += " AND f.difficult = :difficult"
                params["difficult"] = filter_by_difficulty

            if filter_by_type:
                query += " AND f.type = :type"
                params["type"] = filter_by_type

            query += " ORDER BY f.created_at DESC LIMIT :limit"
            params["limit"] = limit

            # Execute the query
            result = db.session.execute(query, params)

            # Process the results
            result_flashcards = []
            for row in result:
                card_dict = dict(row)

                # Convert datetime objects to strings for JSON serialization
                if card_dict.get('created_at'):
                    card_dict['created_at'] = card_dict['created_at'].isoformat()
                if card_dict.get('updated_at'):
                    card_dict['updated_at'] = card_dict['updated_at'].isoformat()
                if card_dict.get('last_reviewed'):
                    card_dict['last_reviewed'] = card_dict['last_reviewed'].isoformat() if card_dict['last_reviewed'] else None
                if card_dict.get('next_review_date'):
                    card_dict['next_review_date'] = card_dict['next_review_date'].isoformat() if card_dict['next_review_date'] else None

                # Convert tags string to array if present
                if card_dict.get('tags'):
                    card_dict['tags'] = card_dict['tags'].split(',') if card_dict['tags'] else []

                result_flashcards.append(card_dict)

        return {
            "success": True,
            "flashcards": result_flashcards,
            "count": len(result_flashcards)
        }

    except Exception as e:
        logging.error(f"Error fetching user flashcards: {str(e)}")
        logging.error(traceback.format_exc())
        return {
            "success": False,
            "error": f"Error fetching flashcards: {str(e)}"
        }

def get_flashcards_for_learning(user_id, subject_id=None, chapter_id=None, count=5, prioritize_difficult=False):
    """
    Get a set of flashcards optimized for learning, with priority given to cards due for review

    Args:
        user_id: The ID of the user
        subject_id: Optional subject ID to filter by
        chapter_id: Optional chapter ID to filter by
        count: Number of flashcards to return (default: 5)
        prioritize_difficult: Whether to prioritize difficult cards (default: False)

    Returns:
        Dictionary with flashcards for learning
    """
    try:
        # Build a query that prioritizes cards due for review or marked as difficult
        if pg_conn:
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Start building the query
            query = """
                SELECT f.*, s.name as subject_name, c.title as chapter_title, c.chapter_number
                FROM flashcards f
                JOIN subjects s ON f.subject_id = s.id
                JOIN chapters c ON f.chapter_id = c.id
                WHERE f.user_id = %s
            """
            params = [user_id]

            if subject_id:
                query += " AND f.subject_id = %s"
                params.append(subject_id)

            if chapter_id:
                query += " AND f.chapter_id = %s"
                params.append(chapter_id)

            # Order by priority factors
            if prioritize_difficult:
                query += """
                    ORDER BY
                        f.difficult DESC,
                        (CASE WHEN f.next_review_date <= CURRENT_DATE THEN 1 ELSE 0 END) DESC,
                        f.box_level ASC,
                        f.success_rate ASC,
                        RANDOM()
                    LIMIT %s
                """
            else:
                query += """
                    ORDER BY
                        (CASE WHEN f.next_review_date <= CURRENT_DATE THEN 1 ELSE 0 END) DESC,
                        f.box_level ASC,
                        f.difficult DESC,
                        f.success_rate ASC,
                        RANDOM()
                    LIMIT %s
                """
            params.append(count)

            # Execute the query
            pg_cursor.execute(query, params)

            # Commit to prevent transaction errors
            pg_conn.commit()

            # Fetch results
            flashcards = pg_cursor.fetchall()
            pg_cursor.close()

            # Process the results
            result_flashcards = []
            for card in flashcards:
                card_dict = dict(card)

                # Convert datetime objects to strings for JSON serialization
                if card_dict.get('created_at'):
                    card_dict['created_at'] = card_dict['created_at'].isoformat()
                if card_dict.get('updated_at'):
                    card_dict['updated_at'] = card_dict['updated_at'].isoformat()
                if card_dict.get('last_reviewed'):
                    card_dict['last_reviewed'] = card_dict['last_reviewed'].isoformat() if card_dict['last_reviewed'] else None
                if card_dict.get('next_review_date'):
                    card_dict['next_review_date'] = card_dict['next_review_date'].isoformat() if card_dict['next_review_date'] else None

                # Convert tags string to array if present
                if card_dict.get('tags'):
                    card_dict['tags'] = card_dict['tags'].split(',') if card_dict['tags'] else []

                result_flashcards.append(card_dict)
        else:
            # Fallback to SQLAlchemy
            logging.warning("PostgreSQL connection not available, falling back to raw SQL via SQLAlchemy")

            # Start building the query
            query = """
                SELECT f.*, s.name as subject_name, c.title as chapter_title, c.chapter_number
                FROM flashcards f
                JOIN subjects s ON f.subject_id = s.id
                JOIN chapters c ON f.chapter_id = c.id
                WHERE f.user_id = :user_id
            """
            params = {"user_id": user_id}

            if subject_id:
                query += " AND f.subject_id = :subject_id"
                params["subject_id"] = subject_id

            if chapter_id:
                query += " AND f.chapter_id = :chapter_id"
                params["chapter_id"] = chapter_id

            # Order by priority factors
            if prioritize_difficult:
                query += """
                    ORDER BY
                        f.difficult DESC,
                        (CASE WHEN f.next_review_date <= CURRENT_DATE THEN 1 ELSE 0 END) DESC,
                        f.box_level ASC,
                        f.success_rate ASC,
                        RANDOM()
                    LIMIT :count
                """
            else:
                query += """
                    ORDER BY
                        (CASE WHEN f.next_review_date <= CURRENT_DATE THEN 1 ELSE 0 END) DESC,
                        f.box_level ASC,
                        f.difficult DESC,
                        f.success_rate ASC,
                        RANDOM()
                    LIMIT :count
                """
            params["count"] = count

            # Execute the query
            result = db.session.execute(query, params)

            # Process the results
            result_flashcards = []
            for row in result:
                card_dict = dict(row)

                # Convert datetime objects to strings for JSON serialization
                if card_dict.get('created_at'):
                    card_dict['created_at'] = card_dict['created_at'].isoformat()
                if card_dict.get('updated_at'):
                    card_dict['updated_at'] = card_dict['updated_at'].isoformat()
                if card_dict.get('last_reviewed'):
                    card_dict['last_reviewed'] = card_dict['last_reviewed'].isoformat() if card_dict['last_reviewed'] else None
                if card_dict.get('next_review_date'):
                    card_dict['next_review_date'] = card_dict['next_review_date'].isoformat() if card_dict['next_review_date'] else None

                # Convert tags string to array if present
                if card_dict.get('tags'):
                    card_dict['tags'] = card_dict['tags'].split(',') if card_dict['tags'] else []

                result_flashcards.append(card_dict)

        return {
            "success": True,
            "flashcards": result_flashcards,
            "count": len(result_flashcards)
        }

    except Exception as e:
        logging.error(f"Error fetching flashcards for learning: {str(e)}")
        logging.error(traceback.format_exc())
        return {
            "success": False,
            "error": f"Error fetching flashcards for learning: {str(e)}"
        }

def get_flashcard_stats(user_id):
    """
    Get statistics about a user's flashcards

    Args:
        user_id: The ID of the user

    Returns:
        Dictionary with flashcard statistics or error message
    """
    try:
        if pg_conn:
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # Get total count of flashcards
            count_query = """
                SELECT COUNT(*) as total_count
                FROM flashcards
                WHERE user_id = %s
            """

            # Get count by subject
            subject_query = """
                SELECT s.name as subject_name, COUNT(*) as card_count
                FROM flashcards f
                JOIN subjects s ON f.subject_id = s.id
                WHERE f.user_id = %s
                GROUP BY s.name
                ORDER BY card_count DESC
            """

            # Get count by difficulty
            difficulty_query = """
                SELECT difficult, COUNT(*) as count
                FROM flashcards
                WHERE user_id = %s
                GROUP BY difficult
            """

            # Get count by type
            type_query = """
                SELECT type, COUNT(*) as count
                FROM flashcards
                WHERE user_id = %s
                GROUP BY type
            """

            # Execute queries
            pg_cursor.execute(count_query, [user_id])
            count_result = pg_cursor.fetchone()
            pg_conn.commit()

            pg_cursor.execute(subject_query, [user_id])
            subject_result = pg_cursor.fetchall()
            pg_conn.commit()

            pg_cursor.execute(difficulty_query, [user_id])
            difficulty_result = pg_cursor.fetchall()
            pg_conn.commit()

            pg_cursor.execute(type_query, [user_id])
            type_result = pg_cursor.fetchall()
            pg_conn.commit()

            pg_cursor.close()

            # Process results
            total_count = count_result['total_count'] if count_result else 0
            subjects = [dict(row) for row in subject_result]
            difficulties = {str(row['difficult']): row['count'] for row in difficulty_result}
            types = {row['type']: row['count'] for row in type_result}
        else:
            # Fallback to SQLAlchemy
            logging.warning("PostgreSQL connection not available, falling back to raw SQL via SQLAlchemy")

            # Get total count of flashcards
            count_query = """
                SELECT COUNT(*) as total_count
                FROM flashcards
                WHERE user_id = :user_id
            """

            # Get count by subject
            subject_query = """
                SELECT s.name as subject_name, COUNT(*) as card_count
                FROM flashcards f
                JOIN subjects s ON f.subject_id = s.id
                WHERE f.user_id = :user_id
                GROUP BY s.name
                ORDER BY card_count DESC
            """

            # Get count by difficulty
            difficulty_query = """
                SELECT difficult, COUNT(*) as count
                FROM flashcards
                WHERE user_id = :user_id
                GROUP BY difficult
            """

            # Get count by type
            type_query = """
                SELECT type, COUNT(*) as count
                FROM flashcards
                WHERE user_id = :user_id
                GROUP BY type
            """

            # Execute queries
            count_result = db.session.execute(count_query, {"user_id": user_id}).fetchone()
            subject_result = db.session.execute(subject_query, {"user_id": user_id}).fetchall()
            difficulty_result = db.session.execute(difficulty_query, {"user_id": user_id}).fetchall()
            type_result = db.session.execute(type_query, {"user_id": user_id}).fetchall()

            # Process results
            total_count = count_result['total_count'] if count_result else 0
            subjects = [dict(row) for row in subject_result]
            difficulties = {str(row['difficult']): row['count'] for row in difficulty_result}
            types = {row['type']: row['count'] for row in type_result}

        return {
            "success": True,
            "stats": {
                "total_count": total_count,
                "by_subject": subjects,
                "by_difficulty": difficulties,
                "by_type": types
            }
        }

    except Exception as e:
        logging.error(f"Error fetching flashcard stats: {str(e)}")
        logging.error(traceback.format_exc())
        return {
            "success": False,
            "error": f"Error fetching flashcard stats: {str(e)}"
        }

def get_tool_best_practices(tool_type):
    """
    Get best practices for creating effective study tools

    Args:
        tool_type: The type of study tool (flashcards, kanban, etc.)

    Returns:
        Dictionary with best practices for the specified tool type
    """
    best_practices = {
        "flashcards": {
            "title": "Best Practices for Creating Effective Flashcards",
            "practices": [
                "Keep questions clear and concise",
                "Focus on one concept per card",
                "Use images and diagrams for visual concepts",
                "Include mnemonics for difficult-to-remember information",
                "Create cards that test application, not just recall",
                "Use cloze deletion for fill-in-the-blank style cards",
                "Review cards regularly using spaced repetition",
                "Tag cards by topic for better organization",
                "Include context on the answer side when helpful",
                "Create bidirectional cards for key concepts"
            ],
            "examples": [
                {
                    "good": "What is the function of mitochondria?",
                    "bad": "Tell me about cell organelles and their functions"
                },
                {
                    "good": "Define 'opportunity cost' in economics",
                    "bad": "What are some important economic concepts?"
                }
            ]
        },
        "kanban": {
            "title": "Best Practices for Using Kanban Boards",
            "practices": [
                "Limit work in progress (WIP)",
                "Break down complex tasks into smaller items",
                "Use clear, actionable task descriptions",
                "Set deadlines for time-sensitive tasks",
                "Review and update the board regularly",
                "Use color coding for different types of tasks",
                "Include acceptance criteria for completion",
                "Focus on flow, not just individual tasks",
                "Identify and resolve bottlenecks",
                "Celebrate completed tasks"
            ]
        },
        "cornell_notes": {
            "title": "Best Practices for Cornell Notes",
            "practices": [
                "Divide your page into three sections: notes, cues, and summary",
                "Take notes in the largest section during class/reading",
                "Write cues/questions in the left column after taking notes",
                "Write a summary at the bottom after completing notes",
                "Use abbreviations and symbols to take notes faster",
                "Focus on key concepts rather than writing everything",
                "Leave space for adding details later",
                "Review notes within 24 hours of taking them",
                "Use the cue column for active recall practice",
                "Connect new information to what you already know"
            ]
        },
        "mindmap": {
            "title": "Best Practices for Mind Mapping",
            "practices": [
                "Start with a central concept in the middle",
                "Use single keywords or simple phrases on branches",
                "Use colors to organize and categorize information",
                "Add images to enhance memory and association",
                "Create hierarchical branches from general to specific",
                "Use curved rather than straight lines for connections",
                "Make the map personal and meaningful to you",
                "Keep revising and expanding your mind map",
                "Use arrows to show cross-connections between branches",
                "Don't overcrowd - create sub-maps for complex topics"
            ]
        }
    }

    # Return best practices for the requested tool type, or a general message if not found
    if tool_type in best_practices:
        return {
            "success": True,
            "tool_type": tool_type,
            "best_practices": best_practices[tool_type]
        }
    else:
        return {
            "success": True,
            "tool_type": tool_type,
            "best_practices": {
                "title": "General Study Tool Best Practices",
                "practices": [
                    "Focus on active recall rather than passive review",
                    "Use spaced repetition to improve long-term retention",
                    "Connect new information to existing knowledge",
                    "Break complex topics into smaller, manageable chunks",
                    "Test yourself regularly on the material",
                    "Teach concepts to others to solidify understanding",
                    "Use multiple modalities (visual, auditory, kinesthetic)",
                    "Review and revise your study tools regularly",
                    "Focus on understanding, not just memorization",
                    "Apply concepts to real-world examples"
                ]
            }
        }

# API Endpoints for the new functions
@ai_assistant_bp.route('/api/ai/flashcards', methods=['GET'])
@authenticate_token
def api_get_user_flashcards(current_user_id):
    """API endpoint to get user flashcards"""
    subject_id = request.args.get('subject_id', type=int)
    chapter_id = request.args.get('chapter_id', type=int)
    limit = request.args.get('limit', default=20, type=int)
    include_content = request.args.get('include_content', default='true').lower() == 'true'

    # Handle difficulty filter
    difficulty_param = request.args.get('difficult')
    filter_by_difficulty = None
    if difficulty_param is not None:
        filter_by_difficulty = difficulty_param.lower() == 'true'

    # Handle type filter
    filter_by_type = request.args.get('type')

    return jsonify(get_user_flashcards(
        current_user_id,
        subject_id,
        chapter_id,
        limit,
        include_content,
        filter_by_difficulty,
        filter_by_type
    ))

@ai_assistant_bp.route('/api/ai/flashcards/learning', methods=['GET'])
@authenticate_token
def api_get_flashcards_for_learning(current_user_id):
    """API endpoint to get flashcards optimized for learning"""
    subject_id = request.args.get('subject_id', type=int)
    chapter_id = request.args.get('chapter_id', type=int)
    count = request.args.get('count', default=5, type=int)
    prioritize_difficult = request.args.get('prioritize_difficult', default='false').lower() == 'true'

    return jsonify(get_flashcards_for_learning(
        current_user_id,
        subject_id,
        chapter_id,
        count,
        prioritize_difficult
    ))

@ai_assistant_bp.route('/api/ai/flashcard_stats', methods=['GET'])
@authenticate_token
def api_get_flashcard_stats(current_user_id):
    """API endpoint to get flashcard statistics"""
    return jsonify(get_flashcard_stats(current_user_id))

@ai_assistant_bp.route('/api/ai/tool_best_practices', methods=['GET'])
@authenticate_token
def api_get_tool_best_practices(current_user_id):
    """API endpoint to get best practices for study tools"""
    # current_user_id is required by the authenticate_token decorator but not used in this function
    # since best practices are not user-specific
    tool_type = request.args.get('tool_type', default='general')

    # Log the request for auditing purposes
    logging.info(f"User {current_user_id} requested best practices for tool type: {tool_type}")

    return jsonify(get_tool_best_practices(tool_type))

@ai_assistant_bp.route('/api/ai/chat', methods=['POST'])
@authenticate_token
def api_chat_with_ai(current_user_id):
    """API endpoint to chat with the AI assistant"""
    data = request.json
    if not data or 'message' not in data:
        return jsonify({
            "success": False,
            "error": "Missing required field: message"
        }), 400

    history = data.get('history', [])

    return jsonify(chat_with_ai(
        current_user_id,
        data['message'],
        history
    ))
