import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import LeitnerModal from './LeitnerModal.jsx';

// Global modal state
let globalModalState = {
  isOpen: false,
  loading: false,
  error: null,
  dueTodayCards: [],
  currentCardIndex: 0,
  isFlipped: false,
  answered: false,
  sessionStats: { correct: 0, incorrect: 0 },
  onClose: null,
  onFlip: null,
  onAnswer: null,
  onNext: null,
  onRetry: null
};

let setGlobalModalState = null;

// Global functions to control the modal
export const openLeitnerModal = (modalProps) => {
  console.log('🎯 [MODAL MANAGER] Opening Leitner modal with props:', modalProps);
  globalModalState = { ...globalModalState, ...modalProps, isOpen: true };
  if (setGlobalModalState) {
    setGlobalModalState({ ...globalModalState });
  }
};

export const closeLeitnerModal = () => {
  console.log('🎯 [MODAL MANAGER] Closing Leitner modal');
  globalModalState = { ...globalModalState, isOpen: false };
  if (setGlobalModalState) {
    setGlobalModalState({ ...globalModalState });
  }
};

export const updateLeitnerModal = (updates) => {
  console.log('🎯 [MODAL MANAGER] Updating Leitner modal:', updates);
  globalModalState = { ...globalModalState, ...updates };
  if (setGlobalModalState) {
    setGlobalModalState({ ...globalModalState });
  }
};

// Modal Manager Component
const LeitnerModalManager = () => {
  const [modalState, setModalState] = useState(globalModalState);

  useEffect(() => {
    setGlobalModalState = setModalState;
    return () => {
      setGlobalModalState = null;
    };
  }, []);

  console.log('🎯 [MODAL MANAGER] Rendering with state:', modalState);

  if (!modalState.isOpen) {
    return null;
  }

  return createPortal(
    <LeitnerModal
      isOpen={modalState.isOpen}
      onClose={modalState.onClose || closeLeitnerModal}
      loading={modalState.loading}
      error={modalState.error}
      dueTodayCards={modalState.dueTodayCards}
      currentCardIndex={modalState.currentCardIndex}
      isFlipped={modalState.isFlipped}
      answered={modalState.answered}
      sessionStats={modalState.sessionStats}
      onFlip={modalState.onFlip}
      onAnswer={modalState.onAnswer}
      onNext={modalState.onNext}
      onRetry={modalState.onRetry}
    />,
    document.body
  );
};

export default LeitnerModalManager;
