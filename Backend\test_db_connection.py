import os
import sys
import psycopg2
import urllib.parse
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get database connection parameters from environment variables
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_USER = os.getenv('DB_USER', 'postgres')
DB_PASSWORD = os.getenv('DB_PASSWORD', '')
DB_NAME = os.getenv('DB_NAME', 'blueprint')

# URL encode the password to handle special characters
DB_PASSWORD_ENCODED = urllib.parse.quote_plus(DB_PASSWORD)

print("Database Connection Test")
print("=======================")
print(f"Host: {DB_HOST}")
print(f"User: {DB_USER}")
print(f"Password: {'*' * len(DB_PASSWORD)} (length: {len(DB_PASSWORD)})")
print(f"Database: {DB_NAME}")
print(f"Connection string: postgresql://{DB_USER}:{DB_PASSWORD_ENCODED}@{DB_HOST}/{DB_NAME}")
print("=======================")

try:
    # Connect to the database
    print("Attempting to connect to the database...")
    conn = psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        dbname=DB_NAME
    )
    
    # Create a cursor
    cur = conn.cursor()
    
    # Execute a simple query
    print("Executing test query...")
    cur.execute("SELECT 1")
    
    # Fetch the result
    result = cur.fetchone()
    print(f"Query result: {result}")
    
    # Check if tables exist
    print("\nChecking for required tables...")
    required_tables = ['users', 'curricula', 'subjects', 'chapters', 'objectives', 'study_plans', 'schedule_items']
    
    for table in required_tables:
        cur.execute(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table}')")
        exists = cur.fetchone()[0]
        print(f"Table '{table}': {'EXISTS' if exists else 'MISSING'}")
    
    # Close the cursor and connection
    cur.close()
    conn.close()
    print("\nDatabase connection test successful!")
    
except Exception as e:
    print(f"\nError connecting to the database: {str(e)}")
    print("Please check your database credentials and connection settings.")
    sys.exit(1)
